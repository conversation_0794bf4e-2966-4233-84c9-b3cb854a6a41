##### 镜像设置

npm config set registry https://registry.npm.taobao.org/

##### 依赖安装

pnpm i

##### 本地执行

pnpm run dev

##### 打包构建

pnpm run build

## qiankun 子应用

ui 库使用的 element plus 以及 skynet-pandora-ui

- skynet-pandora-ui 是基于 element plus 二次开发的组件库，文档地址：http://turing.iflytek.com:2230/skynet-pandora-ui/
- element plus，文档地址：https://element-plus.org/zh-CN/component/overview.html


#### 面包屑适配

`面包屑是根据meta的路径统一获取的，因此适配面包屑需要遵循以下规则`

1. 所有路由配置需meta: { label: '面包屑名称' }
2. 如果有二级面包屑（如列表管理/详情）需要配置如下两点
   1). 列表页内容在<page-wrapper route-name="详情页的路由名称"></page-wrapper>内部写
   2). 详情页路由需要配置成列表页的子级路由
   3). 详情页路由名称name格式为 "列表页路由name::xxx"
   4). 如果有三级页面，那么中间那个页面需要通过params传参，并且路由设置成'detail/:id格式'  参考mock-demo
   ``` javascript
      {
         path: "/mock-demo",
         name: "mockDemo",
         meta: { label: 'demo列表' },
         component: () => import("@/views/mock/list.vue"),
         children: [
            {
              path: "detail/:id",
              name: "mockDemo::detail", // mockDemo为列表页路由名称以::隔开
              meta: { label: '详情' },
              component: () => import("@/views/mock/detail.vue"),
              children: [
                  {
                     path: "detail2",
                     name: "mockDemo::detail::detail2",
                     meta: { label: '详情页的详情页' },
                     component: () => import("@/views/mock/detail2.vue"),
                  },
               ]
            },
         ]
     }
   ```