

# Vite + Vue 3 + TypeScript + Element-plus的通用管理端框架

`公共的逻辑代码部分已实现，开发着重于业务开发即可`

## 一、技术栈

| 技术栈        | 官网地址                                                     |
| ------------- | ------------------------------------------------------------ |
| Vite          | https://v3.vitejs.dev/                                       |
| Vue3          | https://cn.vuejs.org/guide/introduction.htmlElement-plus https://element-plus.org/zh-CN/#/zh-CN |
| typescript    | https://www.tslang.cn/docs/handbook/basic-types.html#google_vignette |
| Element-plus  | https://element-plus.org/zh-CN/guide/design.html             |
| echarts       | https://echarts.apache.org/examples/zh/index.html            |
| pinia         | https://pinia.vuejs.org/zh/core-concepts/state.html          |
| vue-i18n      | https://vue-i18n.intlify.dev/api/general.html                |
| sass          | https://www.sass.hk/guide/                                   |
| lodash        | https://www.lodashjs.com/                                    |
| turing-plugin | 参考同级目录下的文件 [turing-plugin使用说明.pdf]()           |



## 二、命令
npm run dev 或 vite
npm run build 或 vite build 




## 三、开发前阅读

公共组件、公共方法、公共样式使用已封装的，无需自行封装重复的（方便后期统一维护）。若有改动，请在已有组件/方法进行调整




## 四、公共组件使用说明
### 1.  myForm

#### 1.1 使用demo

``` html
 <my-form
   ref="formRef"
   labelWidth="120px"
   :rules="rules"
   :ruleForm="ruleForm"
   :formItems="formItems"> 
 </my-form>
 <div class="inline-btns">
   <my-button type="primary" @click="handleConfirm">提交</my-button>
   <my-button @click="handleClear">取消</my-button>
 </div>
```
``` javascript
/* 表单的初始值定义 */
const defaultForm = {
  id: undefined,
  name: "",
  type: '',
  city: '',
  date: '',
  age: '',
  isEnd: false,
  desc: '', 
  file: null,
  category: ''
};

/* 表单model绑定值的值 */
let ruleForm = ref<any>(assign({}, defaultForm));

/* 表单校验规则rules */
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  category: [{ required: true, message: "请选择敏感分类", trigger: "change" }]
});

/* 表单项 */
const formItems = ref<any>({
  name: { 
    label: "名称", 
    type: "input",
    attrs: { maxlength: 30 }
  },
  type: {
    label: "类型",
    type: "select",
    options: [],
    attrs: { clearable: false },
    events: { change: () =>{} }
  },
  city: { 
    label: "城市", 
    type: "select", 
    options: [], 
    attrs: { multiple: true },
  },
  date: { 
    label: "日期", 
    type: "datePicker"
  },
  age: { 
    label: "年龄", 
    type: "inputNumber", 
    attrs: {min: 1, max: 100, step: 1} 
  },
  isEnd: { 
    label: "开关", 
    type: "switch" 
  },
 desc: { 
    label: "描述", 
    type: "textarea", 
    attrs: { maxlength: 50  }
  },
  // 可以通过插槽自定义表单项；
  file: { 
    label: "文件", 
    type: "slot", 
    slotName: 'file'
  },
  category: { 
    type: "cascader", 
    label: '分类', 
    // 当表单项为下拉列表、级联选择框、单选组时，options为选项值
    options: [], 
    // 表单项的提示信息
    tooltip: '这是提示信息',
    // 表单项禁用，可根据form的属性进行控制
    disabled: (form) => form.type === 1,
    // 表单项隐藏，可根据form的属性进行控制
    hidden: (form) => form.type === 2,
    // 表单项的属性集合(继承element对应的表单项支持的所有属性）
    attrs: {
      props: {
        value: "code",
        label: "name",
        children: "subCategory"
      }
    },
    // 表单项 的事件集合
    evenets: {
      change: () => {}
    }
  }
});

/*表单提交*/
const formRef = ref<any>(null)
// 点击提交触发
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
      if (valid) {
        submit(ruleForm.value)
      }
    })
};
// 点击取消触发
const handleClear = () => {
  formRef.value.resetForm();
}
// 提交后的实际操作
const submit = (form) => {
  console.log('实际提交的表单信息', form)
  const params: any = {}
  // 隐藏的数据不传，如果有部分隐藏字段需要传，加上条件即可，以实际需求为准
  // 这里的逻辑可自行写
  for(let key in form) {
    if (!formItems.value[key]?.hidden || !formItems.value[key]?.hidden(form) || key === 'category') {
      params[key] = form[key]
    }
  }
  // 分类传参转换
  if (params.category) {
    const [category, subCategory] = form.category
    params.category = category
    params.subCategory = subCategory
  }
  // 如果有其他需要转换的字段，在这里进行转换即可
  // 最后进行接口调用
}

```
#### 1.2  API 介绍

`继承el-form的所有属性，仅列举常用属性`

|属性名|说明|类型|默认值|
|-|-|-|-|
|model|表单数据对象|object|—|
|rules|表单验证规则|object|—|
|label-width|标签的长度|string/number|—|
|formItems|表单项(具体属性如下：)|object|—|

#### 1.3  formItems 属性介绍
|属性名|说明|取值|
|-|-|-|
|type|表单项的类型|input、textarea、inputNumberr、radio、checkbox、switch、select、cascader、datePicker、daterange、datetimerange、file、slot【可按需添加】|
|label|表单项名称|—|
|tooltip|表单项的提示信息，鼠标移入icon显示提示信息|—|
|options|当表单项为下拉列表、级联选择框、单选组时，options为选项值|[{value: 1, label: '选项1', tooltip: '提示信息', disabled: false}]（value和label为必选项，其他的的为可选项）|
|disabled|表单项是否禁用|flase|
|hidden|表单项是否隐藏|false|
|attrs|可扩展的属性|继承element-plus所有支持的属性|
|events|表单项的事件集合|继承element-plus所有支持的事件|



### 2.  myQuery

#### 2.1 使用demo

``` html
<my-query 
  :queryItems="queryItems" 
  @search="events.searchQuery"
  @reset="events.resetQuery"/>
```
``` javascript
/* 查询项 */
const queryItems = ref<any>({
  appId: { 
    label: "应用", 
    modelValue: "", 
    type: "select", 
    options: [], 
    attrs: {
      clearable: false,
      remote: true,
      remoteMethod: function(search: string) {
        if (this.remote) {
          const debounceFun = debounce(getAppOptions, 300)
          debounceFun(search)  
        }
      }
    },
    events: {
      change: events.appChange
    }
  },
  strategyId: { 
    label: "审核策略", 
    modelValue: "", 
    type: "select", 
    options: [], 
    attrs: {
      clearable: false
    }
  },
  keywords: { 
    label: "关键词", 
    modelValue: "", 
    type: "input",  
    attrs: {
      placehodler: '请根据关键词进行检索'’
    }
  }
});

/* 给options进行赋值 */
const getAppOptions = async (search ? : string) => {
  queryItems.value.appId.options = await api.getAppOptions(search)
  queryItems.value.appId.attrs.remote = result.total > api.MAX_TOTAL // 如果下拉列表数据总数大于规定的数量，则支持远程搜索获取下拉列表的值
}
const getStrategyOptions = async () => {
  queryItems.value.strategyId.options = await api.getStrategyOptions()
}

const getOptions = async () => {
  getAppOptions()
  getStrategyOptions()
}

onMounted (() => {
  getOptions()
}
```
####  2.2  API介绍
|属性名|说明|默认值|
|-|-|-|
|queryItems|查询项的集合|见下方queryItems属性说明|
|searchBtn|查询重置按钮相关配置|见下方searchBtn属性说明|
|refreshBtn|刷新按钮相关配置|见下方refreshBtn属性说明|
|supportFold|查询项过多是否支持“展开收起”功能|false|

#### queryItems属性介绍
|属性名|说明|默认值|
|-|-|-|
|type|查询项类型|input、inputNumber、select、cascader、radioButton、datePicker、daterange、datetimerange【可按需添加】|
|label|查询项名称|不赋值则不显示label名称|
|modelValue|查询项绑定的值|—|
|width|查询项的宽度|不同的查询项默认宽度如下：input：240px select：150px cascader: 160px|
|hidden|是否隐藏当前查询项|false(可根据权限设置隐藏逻辑)|
|options|当表单项为下拉列表、级联选择框、单选组时，options为选项值|[{value: '', lable: ''}]|
|showAppend|文本框是否为后置搜索按钮的风格|false|
|quick|当type为dateQuick时，需要设置快速按钮的相关属性|见下放quick属性说明|
|attrs|扩展属性|继承element-plus所有支持的属性|
|events|扩展事件|继承element-plus所有支持的事件|

#### searchBtn属性
|属性名|说明|默认值|
|-|-|-|
|show|是否展示|false|
|right|是否固定在右侧线上|false|
|searchName|查询按钮文本内容|"查询"|
|resetName|重置按钮文本内容|"清除"|

#### refreshBtn属性
|属性名|说明|默认值|
|-|-|-|
|show|是否展示|false|

#### quick属性
|属性名|说明|默认值|
|-|-|-|
|modelValue|单选按钮组绑定值|-|
|options|单选按钮集合|value/label形式的数组|
|attrs|单选按钮组的属性集合|继承el-radio-group所有的属性|
|withTime|是否包含时间|false，默认仅选中日期，如果传withTime:true则同时选中时间|


### 3.  myOperation

#### 3.1  使用demo

``` html
<my-operation :selectedTotal="selectedTotal">
  <template #buttonGroup>
    <my-button type="primary" @click="events.add">新增</my-button>
    <my-button type="primary" plain @click="events.import">导入</my-button>
  </template>
</my-operation>
```
#### 3.2  API说明

|属性|说明|取值|
|-|-|-|
|selectedTotal|如果表格支持选择，selectedTotal代表当前选中的表格项|—|



### 4. myPagination

#### 4.1  使用demo

``` html
<my-pagination
  :pageNum="page.pageNum"
  :pageSize="page.pageSize"
  :pageSizes="page.pageSizes"
  :total="total"
  @current-change="currentChange"
  @size-change="sizeChange"
/>
```
``` javascript
const page = reactive({
  pageNum: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40, 50],
  total: 0,
});
const currentChange = (val: number) => {
  page.pageNum = val;
  loadData()
};
const sizeChange = (val: number) => {
  page.pageSize = val;
  loadData()
};
const loadData = () => {
 // 获取列表数据
}
```
#### 4.2  API介绍
继承el-pagination的所有属性和方法

### 5. myTable

`表格组件`


#### API
```
继承el-table的所有的属性
```

|属性名|说明|默认值|
|-|-|-|
|columns|表格项集合|见下方columns属性说明|
|tableData|表格数据|-|
|operations|操作项集合|见下方operations属性说明|
|tableHeight|设置表格的高度，通过设置表格高度可固定表头|-|
|withSelection|是否显示多选框|false|
|withOrder|是否显示序号|true|
|selectable|多选框是否可选【根据行数据的某个字段去区分，如状态为失败的数据禁选，则设置selectable的值为状态不等于“失败”】|true|
|page|设置当前表格的页数和每页查询的条数（如果表格有分页，需要设置page属性，否则翻页时不能计算出叠加的序号。如果表格没有分页需求，无需设置）|-|
|show-overflow-tooltip|设置所有表格项超出显示省略号|true|

#### columns属性集合

```
继承el-table-column的所有的属性
```

|属性名|说明|默认值|
|-|-|-|
|prop|表格项字段名|-|
|label|表格项名称|-|
|custom|自定义表格项|-|
|customRender|自定义表格项的一些属性以及事件集合|-|
|show-overflow-tooltip|表格项是否超出隐藏显示省略号|true|
|withCopy|是否显示复制图标|false|
|copySuccessText|复制成功后的文本|'已复制'|
|slotName|插槽名称|-|

#### operations集合

|属性名|说明|默认值|
|-|-|-|
|type|操作项类型|-|
|label|操作项名称|-|
|btnType|操作项按钮的类型|primary/warning/danger/success/info 默认值为primary|
|exist|是否显示当前操作项|true|
|disabled|是否禁用当前操作项|false|
|disabledTips|操作项禁用的原因提示信息|-|
|collapsed|操作按钮是否放在“更多”里面|false|







### 6. TablePage

`TablePage组件是用来展示列表页的组件，包括搜索栏、操作栏、表格栏、分页栏。由my-query、my-operation、my-table、my-pagination这四个部分共同组成`

`其中查询栏和操作栏部分通过插槽的形式传。如果没有写#query的插槽，那就没有查询栏和操作栏。如果没有分页栏，设置属性withPagination = false即可`

`组件内部已集成列表接口调用的逻辑，父组件无需再传tableData。组件内部通过接口调用生成`

#### 6.1  使用demo

``` html
<table-page
   ref="myTableRef"
   :query="query"
   :columns="columns"
   :operations="operations"
   :loadDataApi="getTemplateList"
   :transformListData="transformListData"
   with-selection
   :selectable="(row: any) =>row.status !== 1"
   @operation="handleOperation"
   @selection-change="handleSelectionChange">
   <!-- 查询 + 操作插槽内容 -->
   <template #query>
     <div class="flexBetweenStart">
       <my-query 
         :queryItems="queryItems"
         :refresh-btn="{ show: true }"
         @search="events.searchQuery"
         @reset="events.resetQuery"/>
       <my-operation :selectedTotal="selectedIds.length">
         <template #buttonGroup>
           <my-button type="add" @click="events.add">新建模版</my-button>
         </template>
       </my-operation>
     </div>
     <!-- 表格项的插槽内容 -->
     <template #customText="scope">
       <el-icon><Service /></el-icon>
       <span style="display:inline-block; color: red; margin-left: 5px;">{{ scope.row.customText }</span>
     </template> 
</my-table>
```
``` javascript
/*js部分代码参考框架中demo*/
```


#### 6.2  API介绍 

`继承el-table的所有属性 这里只列出常用的属性`

|属性名|说明|默认值|
|-|-|-|
|columns|表格项|-|
|operations|表格操作项|-|
|query|表格列表接口的查询条件|-|
|loadDataApi|表格列表查询接口|-|
|loadImmediately|表格列表接口是否在my-table加载完成后直接调用【如果希望在父组件某个时刻调用，需要将此值设置为false】|true|
|transformListData| 处理接口返回的数据【将接口返回的数据格式按照业务需要转换成需要的格式】 |-|
|transformQuery|处理接口请求参数【将query的参数转换成后台接口需要的格式】|-|
|withPagination|是否含有分页栏|true|
|defaultPageSizes|分页的pageSizes|[10, 20, 30, 40, 50]|
|withSelection|表格是否支持多选|false|
|selectable|多选框是否禁用【根据行数据的某个字段取区分，如状态为失败的数据禁选】|true|
|withOrder|表格是否展示序号| true                 |

#### 6.3  columns 属性
|属性名|说明|取值|
|-|-|-|
|prop|列表项字段名称|—|
|label|列表项名称|—|
|width|列表项的宽度|—|
|min-width|列表项的最小宽度|—|
|align|列表项的对齐方式|left/right/center|
|fixed|列表项是否固定|left/right|
|show-overflow-tooltip|列表项内容超出是否省略号展示并且tooltip展示|默认值为true，如果想取消则赋值false|
|widtCopy|列表项是否含有复制按钮|—|
|custom|是否为自定义项|status、tagStatus、editInput、switch、link|
|customRender|自定义项用到的属性以及方法放在这里写，具体的可参考代码|—|
|slotName|自定义插槽内容|—|


### 7. myButton

#### 7.1 使用demo

``` html
<my-button type="priamry" plian>导入</my-button>
<my-button type="add">新建</my-button>
<my-button type="import">导入</my-button>
<my-button type="export">导出</my-button>
<my-button type="delete">删除</my-button>
```
#### 7.2  API介绍 

`继承el-button所有的属性，这里只列举常用属性`

|属性名|说明|取值|
|-|-|-|
|type|按钮类型|继承el-button的所有类型，额外定义类型有'add'、'import'、'export'、'delete'|
|time|一定时间内按钮仅触发一次，防止多次触                      发|默认值500ms|



### 8. TooltipButton

#### 8.1  组件介绍

`含有tooltip提示的按钮`

#### 8.2  使用demo

``` html
<TooltipButton
  type="export"
  :handle="导出"
  :clickable="selectedIds.length > 0"
  @click="events.batchExport">
 批量导出
</TooltipButton>
```
#### 8.3  API介绍 

`继承el-button所有的属性，这里只列举常用的`

|属性名|说明|取值|
|-|-|-|
|type|按钮类型|继承el-button的所有类型，额外定义类型有'add'、'import'、'export'、'delete'|
|time|一定时间内按钮仅触发一次，防止多次触发|默认值500ms|
|handle|操作的名称|——|
|clickable|是否支持点击|true/false|
|tips|当不支持点击时弹出来的tip提示信息|默认值为"请选择数据"|



### 9. mySelect

#### 9.1  使用demo

``` html
<my-select
   v-model="ruleForm.appId"
   placeholder="请输入关联应用"
   value-in-label
   :options="[
      {value: 1, lable: '选项1'},
      {value: 2, lable: '选项2'},
      {value: 3, lable: '选项3'}
   ]"
   :remote="true"
   :remoteMethod="appRemote"
    @change="appIdChange"
    style="width: 100%"
/>
```
#### 9.2  API介绍

`继承el-select所有属性，这里只列举部分自定义或常用的属性`

|属性名|说明|取值|
|-|-|-|
|v-model|绑定的值|-|
|options|下拉列表项|格式为[{value: 1, label: '选项1'}]|
|defaultProps|options的默认属性值|默认值为{value: 'value', label: 'label'}|
|valueInLabel|change时是否将绑定值的其他属性带上|false|
|useVirtual|是否开启虚拟列表|默认值为false|



### 10. myDialog

对弹窗组件进行封装，方便后期统一维护。具体的属性和用法详见代码myDialog.vue



### 11. myDrawer

对抽屉组件进行封装，方便后期统一维护。具体的属性和用法详见代码myDrawer.vue

### 12. PageWrapper

为了匹配面包屑路径使用的外围组件, 详见七、面包屑匹配

### 13. FullscreenPage

全屏组件：有些三级页面要求全屏展示，因此封装一个公共的组件

#### API

| 属性名 | 属性说明       | 默认值 |
| ------ | -------------- | ------ |
| title  | 全屏组件的标题 | -      |

#### **Slot**

| 插槽名称 | 插槽说明           |      |
| -------- | ------------------ | ---- |
| header   | 自定义头部内容     |      |
| 默认插槽 | 全屏组件的内容部分 |      |



### 14. 其他组件

其他的组件详见组件源码以及使用，这里就不一一列举



# 五、公共方法介绍

## 1. utils介绍

### 1.1   helper.ts
公共函数定义
### 1.2   constants.ts
公共的常量定义
### 1.3   validate.ts
公共的校验方法定义
### 1.4   app-tip.ts
`全局confirm确认框、message消息提示框、Notification消息通知框的封装`
`为了全局使用方法，在useCtx.ts的hooks中已将confirm、message、notification 挂载到当前组件实例上，因此可以通过当前组件实例引用方法`

``` javascript
// 获取当前实例
import useCtx from '@/hooks/useCtx'
const { $app } = useCtx()

/* confirm确认框的使用 */
// confirm提示框
 $app.$confirm({
    title: '退出提示',
    message: '有数据变更未保存，确定要离开当前页面吗？',
    cancelButtonText: '留在页面',
    confirmButtonText: '离开页面',
    customClass: 'four-text-btn'
 }).then(() => {
   // 确认后执行操作
 })
// alert提示框
$app.$confirm({
  type: 'success',
  title: '提示',
  message: '导入成功',
  showCancelButton: false
}).then(() => {
  // 确认后执行操作
})
// 删除提示框
$app.$deleteConfirm({
  title: '确认删除当前任务',
}).then(() => {
   // 确认后执行操作
})

/* message消息提示框的使用 */
$app.$message.success('这是一条success提示信息')
$app.$message.warning('这是一条warning提示信息')
$app.$message.error('这是一条error提示信息')
$app.$message.success('这是一条success提示信息')
// 或：
$app.$message({
  type: 'success',
  message: '这是一条success提示信息'
  // 继承el-message所有的属性
})

/* notification消息通知看的使用 */
$app.notification({
 title: '消息提示',
 message: '这是一条消息提示框',
 type: 'success'
 // 继承el-notification所有的属性
})
```

### 1.5   i18n.ts

##### 1.5.1  介绍

`国际化语言的封装`

##### 1.5.2  使用步骤
- main.ts 中全局注册
``` javascript
import i18n from '@/utils/i18n'
app.use(i18n)
```
- 在html模版中直接使用：
``` html
<div>{{ $t('title.or') }}</div>
```
- 在js中使用
``` javascript
import i18n from '@/utils/i18n'
const $t: any = i18n.global.t
const FAST_DATE = [
  {value:1, label: $t('time.today')},
  {value:7, label: $t('time.nearDay', {n: 7})},
]
```
##### 1.5.2  定义说明

`所在目录：src/lang`

- langConfig中定义语言类型

- zh_CN.json为语言类型对应的json文件

  

json文件内容的定义以及被引用规则：

``` json
{ 
  "title": { // 标题相关的文字
    'or': "或者"
  },
  "time": {  // 时间相关的文字
    'today': "今天"
  },
  "placeholder": {  // placeholder相关的文字
    "pleaseInput": "请输入"
  },
  "btn": {  // 按钮相关的文字
    "search": "查询"
  },
  "tip": {  // 提示相关的文字
    "warnTip": "温馨提示"
  },
  "common": {  // 公共的文字
    "keyword": "关键词"
  }
}
```
引用规则：
``` javascript
$t('title.or')
$t('time.today')
$t('placeholder.pleaseInput')
$t('btn.today')
$t('tip.warnTip')
$t('common.keyword')
```
具体的定义根据实际情况而定
如果需要添加适配的语言，在langConfig中添加对应的语言项，然后添加对应的json文件即可

## 2. hooks介绍
### 2.1  useCtx.ts
`用于获取当前组件实例，并且将常用的方法挂载到当前实例，在组件中方便使用`
`组件中使用如下：`

``` javascript
import useCtx from "@/hooks/useCtx";
const { $app, proxy} = useCtx();
$app.$router.push({name: 'detail'})
$app.$message.succss('操作成功！')
proxy.$refs.addDialog.openDialog()
```



## 3. store介绍

`pinia：vue3的状态管理库`
#### 3.1  index.ts 
pinia的入口文件

#### 3.2  authority.ts
权限相关数据存储，如左侧菜单栏、用户信息等

#### 3.3  api.ts
系统中使用频繁的接口，一般为一些公用的下拉列表接口

## 4. api介绍
`接口定义`

# 六、公共样式介绍
## 1.  styles介绍
`系统中样式文件定义`

#### 1.1  index.scss
入口文件
#### 1.2  reset.scss
css初始化
#### 1.3  common.scss
系统中涉及到的公共样式定义
#### 1.4  element.scss
elemnent-plus样式覆盖
#### 1.5  mixin.scss
css函数定义
#### 1.6  vars.scss
css变量定义
#### 1.7  theme.scss
主题变量定义：可通过修改element-plus对应的变量取值而达到主题自定义的效果

## 2. 样式变量的引用
``` css
// index.scss文件中引入
@import '@/styles/reset.scss';
@import '@/styles/element.scss';
@import '@/styles/common.scss';
@import '@/styles/iconfont/iconfont.css'
```
``` javascript
// main.ts引入index.scss
import '@/styles/index.scss'
```
`为了能够在组件<style lang="scss" scoped></style>直接使用minxin.scss以及vars.scss中定义的css函数或变量，需要在vite.config.ts添加如下配置：`

``` javascript
// vite.config.ts
export default defineConfig({
  css: {
    // 添加css预处理器配置
    preprocessorOptions: {
      scss: {
        // additionalData的内容会在每个scss文件的开头自动注入，这样就可以全局使用scss了
        additionalData: '@use "@/styles/vars.scss" as *; @use "@/styles/mixin.scss" as *; @use "@/styles/theme.scss" as *;'
      }
    }
  }
})
```


## 3. 样式变量或函数的使用

``` css
.link-text {
  color: $primary-color;
}
.flexBetween {
  @include flexBetween()
}
```



## 4. 系统样式书写规范与技巧

#### 1.1  避免样式冲突
1. index.html中添加class类base-app-frame【名称根据实际项目为准，建议以项目名称命名】
   `注意：添加的父类不要放在body下面，如果系统作为qiankun子应用嵌入到主应用上，body是没有挂载过去的，会导致所有.base-app-frame下面的样式全部失效`
``` html
<body>
    <div id="app" class="base-app-frame"></div>
    <script type="module" src="/src/main.ts"></script>
</dody>
```
2. 所有的公共样式都在.base-app-frame {}下面写
``` css
// common.scss
.base-app-frame {
  min-width: 1200px;
  // 全文用到的一些公共样式
  .link-text {
    color: $primary-color;
    font-size: 14px;
    cursor: pointer;
  }
  ...
}
// element.scss
.base-app-frame {
  // layout
  .el-header,
  .el-main {
    padding: 0;
  }
  ...
}
```
3. 关于一些弹框，浮框类的组件挂载到#app或者父节点下，默认是挂载在body下面的
``` javascript
export const $message = (options: any = {}, type: string) => {
  const isStr = typeof(options) === 'string'
  options = isStr ? {message: options } : options // 如果传的string,则直接当做message使用
  ElMessage({
    appendTo: '#app', // 挂载到#app下面，默认是在body下面
    type,
    duration: 3000,
    ...options
  })
}
```

# 七、面包屑匹配

### 匹配规则

`面包屑是根据meta的路径统一获取的，因此适配面包屑需要遵循以下规则`

1. 所有路由需配置meta: { label: '面包屑名称' }

2. 如果有二级面包屑（如列表管理/详情）需要根据以下步骤进行配置

   1）列表页内容在`<page-wrapper route-name="列表页的路由名称"></page-wrapper>`内部写

   2）详情页路由需要配置成列表页的子级路由

   3）详情页路由名称name格式为 "列表页路由name::xxx"

   4）如果有三级页面，那么中间那个页面需要通过params传参，并且路由设置成'detail/:id格式'  参考mock-demo

### 匹配demo

#### 路由配置

```javascript
{
   path: "/mock-demo",
   name: "mockDemo",
   meta: { label: 'demo列表' }, // 一级面包屑名称
   component: () => import("@/views/mock/list.vue"),
   children: [
     {
        path: "detail/:id",
        name: "mockDemo::detail", // 上级页面的路由名称::detail
        meta: { label: '详情' }, // 二级面包屑名称
        component: () => import("@/views/mock/detail.vue"),
        children: [
           {
               path: "detail2",
               name: "mockDemo::detail::detail2",
               meta: { label: '三级页面' } // 三级面包屑名称
               component: () => import("@/views/mock/detail2.vue"),
           }
       ]
    },
    {
        path: "edit",
        name: "mockDemo::edit", // 上级页面的路由名称::detail
        meta: { label: '编辑页面' }, // 二级面包屑名称
        component: () => import("@/views/mock/edit.vue")
    }
  ]
}
```

#### 页面配置

```vue
<template>
  <page-wrapper :route-name="routeName">
     <!-- 列表页面的内容 -->
  </page-wrapper>
<template>
<script lang="ts" setup>
   const routeName = 'mockDemo'  // 当前页面的路由名称
   const toDetail = (record: any) => {
       $router.push({
           name: `${routeName}::detail`,
           // 如果跳转的详情页不是最后一层页面，需要通过params传参，否则使用query的话通过面包屑返回时参数会丢失
           // 如果跳转的详情页是最后一层页面，可以通过query传参
           params: {
            id: record.id
           }
       })
   }
</script>    
```



# 八、基于实际开发框架可能需要修改的部分

#### 全局class的名称定义  

- 通过**base-app-frame**作为全局class 将所有的公共样式放在这个class下面写来避免样式污染
- 名称根据实际项目去定义（建议以项目名称进行定义）

#### 微应用名称的定义 

- vite.config.ts 中 qiankun("**skybox-base**", { useDevMode: useDevMode})

#### 微应用的静态资源匹配路径

- vite.config.ts 中   base: __DEV ? "./" : "**/lynxiao/skybox-base**"

#### 微应用打包生成的目录名称

- vite.config.ts 中 outDir: "**skybox-base**"
- utils/helper.ts中require方法的封装   replace("public", "**skybox-base**")

#### 一些公共的业务代码封装的部分修改

- aixos的baseURL 以及响应参数统一处理逻辑（以实际项目接口返参格式为准）
- my-table中loadData中的 **page和size**的定义（以实际项目列表接口传参字段定义为准）
- my-table中对列表响应数据的处理（以实际项目列表接口返参格式为准）

​          
