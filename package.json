{"name": "base-app", "private": true, "version": "0.0.0", "type": "module", "description": "qiankun子应用", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.18", "@microsoft/fetch-event-source": "^2.0.1", "@vueuse/core": "^11.2.0", "axios": "^1.7.7", "echarts": "^5.6.0", "element-plus": "^2.8.0", "jsencrypt": "^3.3.2", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "monaco-editor": "^0.52.0", "no-vue3-cron": "^2.0.0", "pinia": "^2.2.2", "qs": "^6.13.0", "skynet-pandora-ui": "^0.5.92", "sortablejs": "^1.15.3", "turing-plugin": "^1.1.2", "vite-plugin-qiankun": "^1.0.15", "vue": "^3.4.0", "vue-i18n": "^10.0.4", "vue-json-viewer": "^3.0.4", "vue-router": "^4.4.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/json-bigint": "^1.0.4", "@types/node": "^18.14.6", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "eslint": "^8.35.0", "eslint-plugin-vue": "^9.9.0", "fast-glob": "^3.3.2", "sass": "^1.63.6", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.6", "vue-tsc": "^1.8.5"}}