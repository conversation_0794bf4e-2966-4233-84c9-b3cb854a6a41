<template>
  <div class="layout" :class="{ 'only-view': isQiankun }">
    <Header v-if="!isQiankun" style="height: 50px"></Header>
    <el-container class="layout-section" :class="{ 'layout-single-page': !isQiankun }">
      <Sidebar v-if="!isQiankun"></Sidebar>
      <!-- 如果是作为qiankun集成到主应用，仅展示这一部分内容 -->
      <el-main class="layout-main">
        <Breadcrumb v-if="!noBreadCrumb"></Breadcrumb>
        <div class="layout-view basic-app" :class="{ 'no-bread-crumb': noBreadCrumb }">
          <router-view></router-view>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import Header from "@/components/layout/Header.vue";
import Sidebar from "@/components/layout/Sidebar.vue";
import Breadcrumb from "@/components/layout/Breadcrumb.vue";
import { isQiankun } from "@/utils/constants";
import { computed } from "vue";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const { $router } = useCtx();

//是否是主页页面，非控制台不显示面包屑
const noBreadCrumb = computed(() => {
  sessionStorage.setItem("lynxiao-base", "lynixiao-base");
  const storeVuex: any = sessionStorage.getItem("storeVuex");
  const store: any = dataC.safeObject(storeVuex);
  //项目里配置不显示面包屑
  const hiddenBreadCrumb = $router.currentRoute.value.meta.hiddenBreadCrumb;
  //如果是home类型页面也不显示面包屑
  const homeList: Array<any> = store?.homeList || [];
  const path = $router.currentRoute.value.path;
  const comparePath = window.location.pathname + "#" + path;
  // 是否隐藏面包屑
  return Boolean(hiddenBreadCrumb || homeList.find((item) => item.home.includes(comparePath)));
});
</script>

<style lang="scss"></style>
