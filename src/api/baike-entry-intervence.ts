import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`
const entryBaseUrl = baseUrl + '/baike-word/'
const detailUrl = entryBaseUrl + 'detail'

/**
 * 获取百科列表
 */
export function getEntry(params: any): Promise<any> {
  const url = entryBaseUrl+'page'
  return axios.get(url, {params})
}
/**
 * 分页获取百科词条
 */
export function getEntryDetail(data: any): Promise<any> {
  const url = detailUrl+'/list'
  return axios.post(url, data)
}
/**
 * 修改子词条顺序V2
 */
export function updateDetailSort(data: any): Promise<any> {
  const url = entryBaseUrl+'update/sort'
  return axios.put(url, data)
}
/**
 * 修改主词条的状态V2
 */
export function updateStatus(id:any,data: any): Promise<any> {
  const url = entryBaseUrl+'update/'+id+`?status=${data.status}`
  return axios.put(url, data)
}
/**
 * 编辑词条
 */
export function updateDetail(data: any): Promise<any> {
  const url = detailUrl
  return axios.put(url, data)
}
/**
 * docId获取词条
 */
export function getDetailByDoc(params: any): Promise<any> {
  const url = entryBaseUrl+'doc'
  return axios.get(url, {params})
}
/**
 * 插入词条
 */
export function insertDoc(data: any): Promise<any> {
  const url = entryBaseUrl+'insert/doc'
  return axios.post(url, data)
}
