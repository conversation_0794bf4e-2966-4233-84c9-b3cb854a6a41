import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/func`

/**
 * 编辑函数
 * @param region 区域
 * @param data 函数数据
 */
export function updateMetaFunc(region: string, data: any): Promise<any> {
    const url = `${baseUrl}/${region}`
    return axios.put(url, data)
}

/**
 * 创建函数
 * @param region 区域
 * @param data 函数数据
 */
export function createMetaFunc(region: string, data: any): Promise<any> {
    const url = `${baseUrl}/${region}`
    return axios.post(url, data)
}

/**
 * 编辑函数版本
 * @param region 区域
 * @param data 版本数据
 */
export function updateMetaFuncVersion(region: string, data: any): Promise<any> {
    const url = `${baseUrl}/version/${region}`
    return axios.put(url, data)
}

/**
 * 创建函数版本
 * @param region 区域
 * @param data 版本数据
 */
export function createMetaFuncVersion(region: string, data: any): Promise<any> {
    const url = `${baseUrl}/version/${region}`
    return axios.post(url, data)
}

/**
 * 获取函数列表
 * @param region 区域
 * @param params 查询参数
 */
export function getMetaFuncList(region: string, params: any): Promise<any> {
    const url = `${baseUrl}/${region}/page`
    return axios.get(url, { params })
}

/**
 * 获取函数版本列表
 * @param region 区域
 * @param params 查询参数
 */
export function getMetaFuncVersionList(region: string, params: any): Promise<any> {
    const url = `${baseUrl}/version/${region}/page`
    return axios.get(url, { params })
}

/**
 * 删除函数
 * @param region 区域
 * @param id 函数ID
 */
export function deleteMetaFunc(region: string, id: string): Promise<any> {
    const url = `${baseUrl}/${region}/${id}`
    return axios.delete(url)
}

/**
 * 删除函数版本
 * @param region 区域
 * @param id 版本ID
 */
export function deleteMetaFuncVersion(region: string, id: string): Promise<any> {
    const url = `${baseUrl}/version/${region}/${id}`
    return axios.delete(url)
}

/**
 * 同步函数版本
 * @param region 区域
 * @param data 同步数据
 */
export function syncMetaFuncVersion(region: string, data: any): Promise<any> {
    const url = `${baseUrl}/version/${region}/sync`
    return axios.post(url, data)
}

/**
 * 复制函数版本
 * @param region 区域
 * @param id 版本ID
 */
export function copyMetaFuncVersion(region: string, id: string): Promise<any> {
    const url = `${baseUrl}/version/copy/${region}/${id}`
    return axios.get(url)
}

export function debugMetaFuncVersion(region:any,data: any): Promise<any> {
    const url = `${baseUrl}/version_call/${region}`
    return axios.post(url, data)
}
/**
 * 获取调试列表
 * @param region 区域
 * @param params 查询参数（分页等）
 */
export function getVersionCallPage(region: string, params?: any): Promise<any> {
    const url = `${baseUrl}/version_call/${region}/page`
    return axios.get(url, { params })
  }