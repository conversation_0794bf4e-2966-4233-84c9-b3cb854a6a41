import axios from './axios'
const baseUrl = '/flames-workflow/flames/workflow/api/v1'
const baseUrl2 = '/flames-provider/flames/api/v1'
import qs from "qs"
const flowUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/`
const areaBaseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/`;


// ==========任务流组件===============
/**
 * 分页查询
 */
export function getCompLists(params: any = {}): Promise<any> {
	const url = baseUrl + '/workflowComponents?' + qs.stringify(params)
	return axios.get(url)
}

/**
 * 不分页查询
 */
export function getCompListUnpage(): Promise<any> {
	const url = baseUrl + '/workflowComponents/unPage'
	return axios.get(url)
}

/**
 * 根据ID查询详情
 */
export function getDetailById(id: string): Promise<any> {
	const url = baseUrl + `/workflowComponents/${id}`
	return axios.get(url)
}

/**
 * 删除
 */
export function delComp(id: string): Promise<any> {
	const url = baseUrl + `/workflowComponents/${id}`
	return axios.delete(url)
}

/**
 * 复制
 */
export function copyComp(id: string): Promise<any> {
	const url = baseUrl + `/workflowComponents/${id}/copy`
	return axios.post(url)
}

/**
 * 新增组件
 */
export function addComp(data: any = {}): Promise<any> {
	const url = baseUrl + `/workflowComponents`
	return axios.post(url, data)
}

/**
 * 更新组件
 */
export function updateComp(data: any = {}): Promise<any> {
	const url = baseUrl + `/workflowComponents`
	return axios.put(url, data)
}

/** 任务流助手分类 */
export function assistantType(type: string = 'WORK_FLOW_ASSISTANT'): Promise<any> {
	const url = baseUrl2 + `/labels/unPage?kindCode.equals=${type}&sort=sortNo,asc`
	return axios.get(url)
}



// ==========任务流助手===============
// /**
//  * 分页查询助手
//  */
//  export function getAssistantLists(params: any = {}): Promise<any> {
// 	const url = baseUrl + '/workflowAssistants?' + qs.stringify(params)
// 	return axios.get(url)
// }

/**
 * 分页查询助手
 */
 export function getAssistantLists(params: any = {}): Promise<any> {
	const url = baseUrl2 + '/assistant?' + qs.stringify(params)
	return axios.get(url)
}

/**
 * 新增助手
 */
 export function addAssistant(params: any = {}): Promise<any> {
	const url = baseUrl + '/workflowAssistants'
	return axios.post(url, params)
}

/**
 * 更新助手
 */
 export function updateAssistant(params: any = {}): Promise<any> {
	const url = baseUrl + '/workflowAssistants'
	return axios.put(url, params)
}

/**
 * 更新助手状态
 */
 export function updateAssistantStatus(params: any = {}): Promise<any> {
	const url = baseUrl + '/workflowAssistants'
	return axios.patch(url, params)
}

/**
 * 删除助手
 */
 export function delAssistant(id: string): Promise<any> {
	const url = baseUrl + `/workflowAssistants/${id}`
	return axios.delete(url)
}

/**
 * 复制助手
 */
 export function copyAssistant(id: string): Promise<any> {
	const url = baseUrl + `/workflowAssistants/${id}/copy`
	return axios.post(url)
}

/**
 * 根据ID查询详情
 */
export function getAssistantById(id: string): Promise<any> {
	const url = baseUrl + `/workflowAssistants/${id}`
	return axios.get(url)
}

/** 同步应用中心 / 取消同步 */
export function syncOrCancel(data: any) {
	const url = baseUrl2 + `/assistant/sync`
	return axios.patch(url, data)
}

// ==========任务流版本管理===============
/**
 * 分页查询
 */
export function getVersionList(params: any = {}): Promise<any> {
	const url = baseUrl + `/workflowAssistantVersions?` + qs.stringify(params)
	return axios.get(url)
}

/**
 * 更新版本状态
 */
export function updateVersion(params: any = {}): Promise<any> {
	const url = baseUrl + `/workflowAssistantVersions`
	return axios.patch(url, params)
}

/**
 * 更新版本
 */
export function putVersion(params: any = {}): Promise<any> {
	const url = baseUrl + `/workflowAssistantVersions`
	return axios.put(url, params)
}

/**
 * 删除版本
 */
export function delVersion(id: string): Promise<any> {
	const url = baseUrl + `/workflowAssistantVersions/${id}`
	return axios.delete(url)
}

/**
 * 复制版本
 */
export function copyVersion(id: string): Promise<any> {
	const url = baseUrl + `/workflowAssistantVersions/${id}/copy`
	return axios.post(url)
}

/** 
 * 根据流程ID查询画布详情
 */
export function getFlowById(id: string): Promise<any> {
	const url = baseUrl + `/workflowProcesses/${id}`
	return axios.get(url)
}

/** 
 * 保存画布信息
 */
 export function updateFlow(params: any = {} ): Promise<any> {
	const url = baseUrl + `/workflowProcesses`
	return axios.patch(url, params)
}

/** 
 * 调用流程
 */
 export function callProcesses(params: any = {} ): Promise<any> {
	const url = baseUrl + `/workflowProcesses/call`
	return axios.post(url, params)
}

/** 
 * 获取版本的历史记录
 */
 export function getVersionHistory(params: any = {} ): Promise<any> {
	const url = baseUrl + `/workflowProcessHistories?` + qs.stringify(params)
	return axios.get(url)
}

/**
 * 根据用户输入的API地址获取动态的数据
 * @param url 
 * @returns 
 */
export function getAPIData(url: string): Promise<any> {
	const apiUrl = url.startsWith('/proxyApi') ? url.substring(9) : url
	return axios.get(apiUrl)
}

/** 创建调试会话 */
export function createSession(assistantId: number, version: string) {
  const url = baseUrl2 + '/chats'
  return axios.post(url, { assistantId, version, kindCode: 'WORK_FLOW_ASSISTANT', title: '新对话窗口', mode: 1, type: 2 })
}

export function getAreaEnum(): Promise<any> {
  const url = areaBaseUrl + 'region';
  return axios.get(url);
}
// 场景策略版本查询
export function getSceneVersion(params): Promise<any> {
  const url = flowUrl+ 'scene/version/search';
  return axios.get(url,{params});
}

// 归因策略版本查询
export function getProductAll(params: any): Promise<any> {
  const url = flowUrl+ 'product/all';
  return axios.get(url,{params});
}

// 获取已启用的产品方案
export function getOpenedProductList(): Promise<any> {
	const url = `${flowUrl}product/opened-list?sort=createdDate,desc`
	return axios.get(url)
}
// 获取已启用的产品方案版本
export function getOpenedProductVersionList(): Promise<any> {
	const url = `${flowUrl}product/version/opened-list?sort=createdDate,desc`
	return axios.get(url)
}

//获取流程入参
export function getProcessInputArgs(processId: String) {
  const url = flowUrl + `canvas/process/${processId}/get-input-args`;
  return axios.get(url);
}