import { cloneDeep } from 'lodash';
import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/workflow-component`

/* 组件 */
export function getCompList(data: any): Promise<any> {
  const params = {
    'code.contains': data.code || '',
    'name.contains': data.name || '',
    'sort': data.sort || 'createdDate,desc',
    page: data.page,
    size: data.size
  }
  return axios.get(baseUrl, {params})
}


export function saveCopm(data: any): Promise<any> {
  const url = baseUrl
  return axios.post(url, data)
}

// 编辑组件
export function editCopm(data: any): Promise<any> {
  const url = baseUrl
  return axios.put(url, data)
}

export function deleteComp (id: string) {
  const url = `${baseUrl}/${id}`
  return axios.delete(url)
}

export function getCompDetail (id: string) {
  const url = `${baseUrl}/${id}`
  return axios.get(url)
}

/* 组件版本 */
// export function getCompVersionList (data: any) {
//   // console.log('接口参数为：', data)
//   const {page, size} = data
//   return new Promise((resolve: any) => {
//     const res = compVersionJSON
//     const result = cloneDeep(res.data.content);
//     const totalElements = result.length
//     const content = result.slice((page - 1)*size, page * size)
//     resolve({
//       totalElements,
//       content
//     });
//   });
// }

export const getCompVersionList = async (data: any) => {
  const {id, page, size, keywords} = data
  const res: any = await getCompDetail(id)
  return new Promise((resolve: any) => {
    // 前端自己进行分页以及查询
    const versionList = cloneDeep(res.components || []);
    const result = versionList.filter((v: any) => !keywords || v.name.includes(keywords))
    const totalElements = result.length
    const content = result.slice((page - 1)*size, page * size)
    resolve({
      totalElements,
      content
    });
  })
}

export function saveCopmVerison(id:string, data: any): Promise<any> {
   const url = `${baseUrl}/${id}/version`
  return axios.post(url, data)
}


export function deleteCompVersion(id: string, versionId: string) {
  const url = `${baseUrl}/${id}/version/${versionId}`
  return axios.delete(url)
}

/**
 * 获取组件分类列表
 * @returns 
 */
export function getComponentCategoryList() {
  const url = `${baseUrl}/labels`
  return axios.get(url)
}