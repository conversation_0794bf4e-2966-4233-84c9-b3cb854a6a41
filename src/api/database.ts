import { cloneDeep } from 'lodash';
import databaseJSON from '@/mock/database'

// 获取索引库列表
export function getDatabaseList (data: any) {
  // console.log('接口参数为：', data)
  const {page, size} = data
  return new Promise((resolve: any) => {
    const res = databaseJSON
    const result = cloneDeep(res.data.content);
    const totalElements = result.length
    const content = result.slice((page - 1)*size, page * size)
    resolve({
      payload: {
        totalElements,
        content
      }
    });
  });
  }