import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`
const datasetBaseUrl = baseUrl + '/dataset'
const versionBaseUrl = baseUrl + '/dataset-version'

/**
 * 新增数据集
 */
export function insertDataset(data: any): Promise<any> {
    const url = datasetBaseUrl
    return axios.post(url, data)
}

/**
 * 删除数据集
 */
export function removeDataset(id: string): Promise<any> {
    const url = datasetBaseUrl + `/${id}`
    return axios.delete(url)
}

/**
 * 更新数据集
 */
export function modifyDataset(data: any): Promise<any> {
    const url = datasetBaseUrl + `/${data.id}`
    return axios.put(url, data)
}

/**
 * 分页查询数据集
 */
export function getDatasetListPage(search: string, page: number, size: number, sort: string): Promise<any> {
    const url = datasetBaseUrl + `/page?search=${search}&page=${page}&size=${size}&sort=${sort}`
    return axios.get(url)
}


/**
 * 查询数据集-根据datasetId
 */
export function getDataset(id: string): Promise<any> {
    const url = datasetBaseUrl + `/${id}`
    return axios.get(url)
}

/**
 * 获取全部数据集
 */
export function getMetaDatasetList(): Promise<any> {
    const url = datasetBaseUrl
    return axios.get(url)
}

/**
 * 导出数据集数据
 */
export function exportExcel(): any {
    const url = datasetBaseUrl + `/export`;
    return axios.get(url, { responseType: "blob" });
}

/**
 * 新增数据集版本
 */
export function insertDatasetVersion(data: any): Promise<any> {
    const url = versionBaseUrl
    return axios.post(url, data)
}

/**
 * 删除数据集版本
 */
export function removeDatasetVersion(id: string): Promise<any> {
    const url = versionBaseUrl + `/${id}`
    return axios.delete(url)
}

/**
 * 更新数据集版本
 */
export function modifyDatasetVersion(data: any): Promise<any> {
    const url = versionBaseUrl + `/${data.id}`
    return axios.put(url, data)
}

/**
 * 更新数据集版本
 */
export function modifyDatasetVersionInfo(data: any): Promise<any> {
    const url = versionBaseUrl + `/info/${data.id}`
    return axios.put(url, data)
}


/**
 * 分页查询数据集版本
 */
export function getDatasetVersionListPage(datasetId: string, page: number, size: number, sort: string): Promise<any> {
    const url = versionBaseUrl + `/by-dataset/${datasetId}?page=${page}&size=${size}&sort=${sort}`
    return axios.get(url)
}

/**
 * 查询全部数据集版本
 */
export function getDatasetVersionList(datasetId: string): Promise<any> {
    const url = versionBaseUrl + `/by-dataset/${datasetId}?sort=version,desc`
    return axios.get(url)
}

/**
 * 查询数据集版本信息-根据datasetVersionId
 */
export function getDatasetVersion(datasetVersionId: string): Promise<any> {
    const url = versionBaseUrl + `/${datasetVersionId}?id=${datasetVersionId}`
    return axios.get(url)
}

/**
 * 计算数据集版本
 */
export function computeDatasetVersion(id: string, data: any) {
    const url = versionBaseUrl + `/generate/${id}`
    return axios.put(url, data)
}

/**
 * 去重数据集版本
 */
export function dedupDatasetVersion(id: string, dedup: any) {
    const url = versionBaseUrl + `/dedup/${id}`
    return axios.put(url, dedup)
}


/**
 * 发布数据集版本
 */
export function publishDatasetVersion(id: string, data:any) {
    const url = versionBaseUrl + `/publish/${id}`
    return axios.put(url, data)
}

/**
 * 复制数据集版本
 */
export function copyDatasetVersion(id: string) {
    const url = versionBaseUrl + `/copy/${id}`
    return axios.post(url)
}

/**
 * 删除数据
 */
export function clearDatasetVersion(id: string) {
    const url = versionBaseUrl + `/clear/${id}`
    return axios.delete(url)
}

/**
 * 导出统计信息
 */
export function exportSites(data: any) {
    const url = versionBaseUrl + `/export_sites`
    return axios.post(url, data, { responseType: "blob" })
}
