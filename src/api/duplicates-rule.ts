import { cloneDeep } from 'lodash';
import duplicatesJSO<PERSON> from '@/mock/rule'

// 模版；列表
export function getDuplicatesRuleList (data: any) {
    // console.log('接口参数为：', data)
    const {page, size} = data
    return new Promise((resolve: any) => {
      const res = duplicatesJSON
      const result = cloneDeep(res.data.content);
      const totalElements = result.length
      const content = result.slice((page - 1)*size, page * size)
      resolve({
        totalElements,
        content
      });
    });
  }