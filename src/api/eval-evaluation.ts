import axios from "./axios";
const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/`;
import JSONBIG from "json-bigint";
/**
 * 获取百科列表
 */
export function getPlanPage(params: any): Promise<any> {
  const url = baseUrl + "plan";
  return axios.get(url, {
    params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果,简单的返回JSONBIG.parse(data)会导致类型错误
          return JSON.parse(JSON.stringify(JSONBIG.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return JSON.parse(data);
        }
      },
    ],
  });
}
/**
 * 创建分析计划
 */
export function insertPlan(data: any): Promise<any> {
  const url = baseUrl + "plan";
  return axios.post(url, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}
/**
 * 修改分析计划
 */
export function modifyPlan(id: any,data:any): Promise<any> {
  const url = baseUrl + "plan/" + id;
  return axios.put(url,data);
}
/**
 * 查询分析计划基本信息
 */
export function getPlanInfo(id: any): Promise<any> {
  const url = baseUrl + "plan/" + id;
  return axios.get(url, {
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果,简单的返回JSONBIG.parse(data)会导致类型错误
          return JSON.parse(JSON.stringify(JSONBIG.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return JSON.parse(data);
        }
      },
    ],
  });
}

/**
 * 删除分析计划基本信息
 */
export function deletePlanInfo(id: any): Promise<any> {
  const url = baseUrl + "plan/" + id;
  return axios.delete(url);
}
/**
 * 查看分析计划详情
 */
export function getPlanDetailList(id: any): Promise<any> {
  const url = baseUrl + `plan/${id}/records?page=1&size=1000`;
  return axios.get(url);
}
/**
 * 查看分析计划详情
 */
export function getPlanProcess(id: any): Promise<any> {
  const url = baseUrl + "plan/" + id+'/task-progress';
  return axios.get(url);
}
/**
 * 查看分析计划详情
 */
export function getPlaTaskStart(id: any): Promise<any> {
  const url = baseUrl + "plan/" + id+'/task-start';
  return axios.post(url);
}
/**
 * 查看分析计划结果
 */
export function getPlanResultList(id: any, status:any): Promise<any> {
  const url = baseUrl + `plan/${id}/task-result?status=${status}&page=1&size=1000`;
  return axios.get(url);
}
/**
 * 导出分析计划明细-问题列表
 */
export function downloadPlanDetail(id: any): Promise<any> {
  let url = baseUrl + `plan/${id}/records/export`;
  return axios.get(url, { responseType: "blob" });
}

/**
 * 导出分析计划结果
 */
export function downloadPlanResult(id: any,status: any): Promise<any> {
  let url = baseUrl + `plan/${id}/task-result/export?status=${status}`;
  return axios.get(url, { responseType: "blob" });
}

/**
 * 查看分析计划全链路信息
 */
export function getTraceinfo(id:any): Promise<any> {
  const url = baseUrl + "plan/record/" + id+'/trace';
  return axios.get(url);
}
export function getPlanGroupNameData(): Promise<any> {
	const apiUrl = baseUrl+'plan/group-name-list'
	return axios.get(apiUrl)
}
export function getStrategyGroupNameData(): Promise<any> {
	const apiUrl = baseUrl+'strategy/group-name-list'
	return axios.get(apiUrl)
}
/**
 * 查询归因策略列表
 */
export function getStrategyPage(params: any): Promise<any> {
  const url = baseUrl + "strategy";
  return axios.get(url, {
    params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果,简单的返回JSONBIG.parse(data)会导致类型错误
          return JSON.parse(JSON.stringify(JSONBIG.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return JSON.parse(data);
        }
      },
    ],
  });
}
/**
 * 查询分析计划基本信息
 */
export function getStrategyInfo(id: any): Promise<any> {
  const url = baseUrl + "strategy/" + id;
  return axios.get(url, {
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果,简单的返回JSONBIG.parse(data)会导致类型错误
          return JSON.parse(JSON.stringify(JSONBIG.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return JSON.parse(data);
        }
      },
    ],
  });
}

/**
 * 创建策略计划
 */
export function insertStrategy(data: any): Promise<any> {
  const url = baseUrl + "strategy";
  return axios.post(url, data);
}
/**
 * 修改策略计划
 */
export function modifyStrategy(id: any,data:any): Promise<any> {
  const url = baseUrl + "strategy/" + id;
  return axios.put(url,data);
}


/**
 * 获取版本列表
 */
export function getStrategyVersionList(id: any,category:any,params:any): Promise<any> {
  const url = baseUrl + "strategy/version/by-eval/" + id+'/'+category;
  return axios.get(url,{params});
}
/**
 * 修改策略计划版本
 */
export function modifyStrategyVersion(id: any,data:any): Promise<any> {
  const url = baseUrl + "strategy/version/" + id;
  return axios.put(url,data);
}
/**
 * 插入策略计划版本
 */
export function insertStrategyVersion(data:any): Promise<any> {
  const url = baseUrl + "strategy/version";
  return axios.post(url,data);
}
/**
 * 发布策略计划版本
 */
export function publishStrategyVersion(id:any): Promise<any> {
  const url = baseUrl + "strategy/version/publish/"+id;
  return axios.put(url);
}
/**
 * 复用策略计划版本
 */
export function copyStrategyVersion(id:any): Promise<any> {
  const url = baseUrl + "strategy/version/copy/"+id;
  return axios.put(url);
}
/**
 * 删除策略计划
 */
export function deleteStrategy(id:any): Promise<any> {
  const url = baseUrl + "strategy/"+id;
  return axios.delete(url);
}

/**
 * 删除策略计划
 */
export function deleteStrategyVersion(id:any): Promise<any> {
  const url = baseUrl + "strategy/version/"+id;
  return axios.delete(url);
}
/**
 * 导出数据集数据
 */
export function exportExcel(mode:any): any {
    const url = baseUrl + `/plan/download/template`;
    return axios.get(url, { params:{mode},responseType: "blob" });
}

// 场景策略版本查询
export function getSceneVersionRange(id:any,params?:any): Promise<any> {
  const url = baseUrl+ 'mark-category/scene-list/'+id;
  return axios.get(url,{params});
}

// 归因策略版本查询
export function getStrategyVersion(category:any,params: any): Promise<any> {
  const url = baseUrl+ 'strategy/version/search/'+category;
  return axios.get(url,{params});
}