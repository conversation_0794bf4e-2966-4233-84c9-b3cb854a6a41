import axios from "./axios";


const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/standard-module`;


//保存目录
export function save(data: any) {
    const url = baseUrl;
    return axios.post(url, data);
}

/**
 * 根据模块获取目录
 */
 export function find(moduleCode:string): Promise<any> {
    const url = baseUrl+'/'+moduleCode;
    return axios.get(url)
}

//删除目录
export function deleteCatelog(treeCode: String, moduleCode: string) {
    const url = baseUrl + '/' + treeCode + "/" + moduleCode;
    return axios.delete(url);
  }
