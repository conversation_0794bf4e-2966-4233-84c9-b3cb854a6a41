import axios from "./axios";
import { dataC } from "turing-plugin";
import JSONBIG from "json-bigint";

const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mark-standard`;

// 表格
export function getTablePage(params:any): Promise<any> {
    const url = baseUrl + '/findByPage';
    return axios.get(url,{params});
}

// 表格
export function getList(params:string): Promise<any> {
    const url = baseUrl + '/list';
    return axios.get(url,{params});
}

//保存目录
export function save(data: any) {
    const url = baseUrl;
    return axios.post(url, data);
}

// copy
export function copy(param: string): Promise<any> {
    const url = baseUrl+ '/copy/' + param;
    return axios.get(url);
}

// 删除
export function deleteRecord(param: string): Promise<any> {
    const url = baseUrl+ '/' + param;
    return axios.delete(url);
}

// 详情
export function findById(param: string): Promise<any> {
    const url = baseUrl+ '/' + param;
    return axios.get(url);
}


// 启用/停用
export function enableOrDisable(id: any, flag: boolean): Promise<any> {
    const url = baseUrl + "/" + id + "/" + flag
    return axios.put(url);
  }