import axios from "./axios";

const fieldsBaseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/field-config`;
const concernNodeBaseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/concern-node`;
const expProdBaseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/exp-prod`;

//========================字段配置 start===========================
/**
 * 创建或更新字段配置
 */
export function save(data: any) {
  const url = fieldsBaseUrl;
  return axios.post(url, data);
}

/**
 * 分页查询字段配置
 */
export function findByPage(params: any): Promise<any> {
  const url = fieldsBaseUrl + '/findByPage';
  return axios.get(url, { params });
}

/**
 * 查询所有字段配置
 */
export function list(category?: string): Promise<any> {
  const url = fieldsBaseUrl + '/list';
  const params: any = {};
  if (category) params.category = category;
  return axios.get(url, { params });
}

/**
 * 查询字段配置详情
 */
export function findById(id: string): Promise<any> {
  const url = fieldsBaseUrl + '/' + id;
  return axios.get(url);
}

/**
 * 删除字段配置
 */
export function deleteById(id: string): Promise<any> {
  const url = fieldsBaseUrl + '/' + id;
  return axios.delete(url);
}

/**
 * 根据字段名查询配置
 */
export function findByFieldName(fieldName: string): Promise<any> {
  const url = fieldsBaseUrl + '/field/' + fieldName;
  return axios.get(url);
}

/**
 * 查询所有字段分类
 */
export function findAllCategories(): Promise<any> {
  const url = fieldsBaseUrl + '/categories';
  return axios.get(url);
}

/**
 * 根据分类查询字段配置
 */
export function findByCategory(category: string): Promise<any> {
  const url = fieldsBaseUrl + '/category/' + category;
  return axios.get(url);
}

/**
 * 检查字段名是否存在
 */
export function checkFieldName(fieldName: string, excludeId?: string): Promise<any> {
  const url = fieldsBaseUrl + '/check-field-name';
  const params: any = { fieldName };
  if (excludeId) params.excludeId = excludeId;
  return axios.get(url, { params });
}

//========================字段配置end===========================

//========================关注节点配置 start===========================

/**
 * 创建或更新关注节点配置
 */
export function saveConcernNode(data: any) {
  const url = concernNodeBaseUrl;
  return axios.post(url, data);
}

/**
 * 分页查询关注节点配置
 */
export function findConcernNodeByPage(params: any): Promise<any> {
  const url = concernNodeBaseUrl + '/findByPage';
  return axios.get(url, { params });
}

/**
 * 查询所有关注节点配置
 */
export function listConcernNode(): Promise<any> {
  const url = concernNodeBaseUrl + '/list';
  return axios.get(url);
}

/**
 * 查询关注节点配置详情
 */
export function findConcernNodeById(id: string): Promise<any> {
  const url = concernNodeBaseUrl + '/' + id;
  return axios.get(url);
}

/**
 * 删除关注节点配置
 */
export function deleteConcernNodeById(id: string): Promise<any> {
  const url = concernNodeBaseUrl + '/' + id;
  return axios.delete(url);
}

//========================关注节点配置end===========================

//========================体验产品配置 start===========================

/**
 * 创建或更新体验产品配置
 */
export function saveExpProd(data: any) {
  const url = expProdBaseUrl;
  return axios.post(url, data);
}

/**
 * 分页查询体验产品配置
 */
export function findExpProdByPage(params: any): Promise<any> {
  const url = expProdBaseUrl + '/findByPage';
  return axios.get(url, { params });
}

/**
 * 查询所有体验产品配置
 */
export function listExpProd(): Promise<any> {
  const url = expProdBaseUrl + '/list';
  return axios.get(url);
}

/**
 * 查询体验产品配置详情
 */
export function findExpProdById(id: string): Promise<any> {
  const url = expProdBaseUrl + '/' + id;
  return axios.get(url);
}

/**
 * 删除体验产品配置
 */
export function deleteExpProdById(id: string): Promise<any> {
  const url = expProdBaseUrl + '/' + id;
  return axios.delete(url);
}

/**
 * 更新体验产品启用状态
 */
export function updateExpProdEnabled(id: string, enabled: boolean): Promise<any> {
  const url = expProdBaseUrl + '/' + id + '/enabled/' + enabled;
  return axios.put(url);
}

/**
 * 更新体验产品启用状态
 */
export function sortExpProd(data:any ): Promise<any> {
  const url = expProdBaseUrl + '/sort';
  return axios.post(url,data);
}

//========================体验产品配置end===========================
