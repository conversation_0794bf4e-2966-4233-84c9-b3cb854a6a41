import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/mission`
// 创建
export function createMission(data: any): Promise<any> {
  const url = `${baseUrl}/create`;
  return axios.post(url, data);
}
// 编辑
export function updateMission(id: string, data: any): Promise<any> {
  const url = `${baseUrl}/update/${id}`;
  return axios.post(url, data);
}

// 发布任务
export function publishMission(id: string): Promise<any> {
  const url = `${baseUrl}/publish/${id}`;
  return axios.get(url);
}

// 复制任务
export function copyMission(id: string): Promise<any> {
  const url = `${baseUrl}/copy/${id}`;
  return axios.get(url);
}

// 测评任务自动归因
export function ascribeMission(id: string): Promise<any> {
  const url = `${baseUrl}/ascribe/${id}`;
  return axios.get(url);
}

// 测评任务自动归因
export function ascribeProcess(id: string): Promise<any> {
  const url = `${baseUrl}/ascribe-process/${id}`;
  return axios.get(url);
}

// 分页查询query集
export function getMissionPage(params: any): Promise<any> {
  const url = `${baseUrl}/page`;
  return axios.get(url, { params });
}

// 启、禁用任务
export function enableMission(id: string, enable: boolean): Promise<any> {
  const url = `${baseUrl}/enable/${id}`;
  return axios.get(url, { params: { enable } });
}

// 删除query集
export function deleteQueryGroup(id: string): Promise<any> {
  const url = `${baseUrl}/delete/${id}`;
  return axios.delete(url);
}

export function queryLabelers(): Promise<any> {
  const url = `${baseUrl}/mark-user`;
  return axios.get(url);
}
// export function getMessionDetails(params:any): Promise<any> {
//   const url = `${baseUrl}/detail`;
//   return axios.get(url,{params});
// }
export function getPlanGroupNameData(): Promise<any> {
  const url = `${baseUrl}/label/list`;
  return axios.get(url);
}
export function deleteMission(id: string): Promise<any> {
  const url = `${baseUrl}/delete/${id}`;
  return axios.delete(url);
}

//获取我的任务
export function myMission(): Promise<any> {
  const url = `${baseUrl}/my-list`;
  return axios.get(url);
}

//获取我的任务详情
export function myMissionDetail(missionId: String): Promise<any> {
  const url = `${baseUrl}/my-detail?id=${missionId}`;
  return axios.get(url);
}

//标注记录查看
export function myMissionQuery(missionId: String, recordId: String, type: String): Promise<any> {
  const url = `${baseUrl}/my-detail/switch-query`;
  const params: any = { missionId, type };
  if (recordId) params.recordId = recordId;
  return axios.get(url, { params });
}
//归档
export function archiveMission(id: string): Promise<any> {
  const url = `${baseUrl}/archived/${id}`;
  return axios.get(url);
}

//场景策略过滤范围
export function findSceneList(id: string): Promise<any> {
  const url = `${baseUrl}/scene-list/${id}`;
  return axios.get(url);
}

