import axios from "./axios";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`;
const idxDbTemBaseUrl = baseUrl + "/idx-db";
const idxDbInstBaseUrl = baseUrl + "/idx-db-inst";
import SSEClient from "./sse-client";

/**
 * 新增特征库模板
 */
export function insertIdxDbTem(data: any): Promise<any> {
  const url = idxDbTemBaseUrl;
  return axios.post(url, data);
}

/**
 * 删除特征库模板
 */
export function removeIdxDbTem(id: string): Promise<any> {
  const url = idxDbTemBaseUrl + `/${id}`;
  return axios.delete(url);
}

/**
 * 更新特征库模板
 */
export function modifyIdxDbTem(data: any): Promise<any> {
  const url = idxDbTemBaseUrl + `/${data.id}`;
  return axios.put(url, data);
}

/**
 * 更新特征库模板基础信息
 */
export function modifyIdxDbTemBase(data: any): Promise<any> {
  const url = idxDbTemBaseUrl + `/base/${data.id}`;
  return axios.put(url, {
    name: data.name,
    description: data.description,
  });
}

/**
 * 分页查询特征库模板
 */
export function getIdxDbTemListPage(search: string, page: number, size: number, sort: string): Promise<any> {
  const url = idxDbTemBaseUrl + `/page`;
  return axios.get(url, { params: { search, page, size, sort } });
}

/**
 * 复制特征库模板
 */
export function copyIdxDbTem(id: string): Promise<any> {
  const url = idxDbTemBaseUrl + `/copy/${id}`;
  return axios.post(url);
}

/**
 * 启用特征库模板
 */
export function enabledIdxDbTem(id: string): Promise<any> {
  const url = idxDbTemBaseUrl + `/enabled/${id}`;
  return axios.put(url, {
    enabled: true,
  });
}

/**
 * 禁用特征库模板
 */
export function disabledIdxDbTem(id: string): Promise<any> {
  const url = idxDbTemBaseUrl + `/enabled/${id}`;
  return axios.put(url, {
    enabled: false,
  });
}

/**
 * 查询全部特征库模板
 */
export function getIdxDbTemList(): Promise<any> {
  const url = idxDbTemBaseUrl;
  return axios.get(url);
}

/**
 * 新增索引库实例
 */
export function insertIdxDbInst(data: any): Promise<any> {
  const url = idxDbInstBaseUrl;
  return axios.post(url, data);
}

/**
 * 删除索引库实例
 */
export function removeIdxDbInst(id: string): Promise<any> {
  const url = idxDbInstBaseUrl + `/${id}`;
  return axios.delete(url);
}

/**
 * 更新索引库实例
 */
export function modifyIdxDbInst(data: any): Promise<any> {
  const url = idxDbInstBaseUrl + `/${data.id}`;
  return axios.put(url, data);
}

/**
 * 更新索引库实例基础信息
 */
export function modifyIdxDbInstBase(data: any): Promise<any> {
  const url = idxDbInstBaseUrl + `/base/${data.id}`;
  return axios.put(url, {
    name: data.name,
    description: data.description,
  });
}

/**
 * 启用索引库实例
 */
export function enabledIdxDbInst(id: string): Promise<any> {
  const url = idxDbInstBaseUrl + `/enabled/${id}`;
  return axios.put(url, {
    enabled: true,
  });
}

/**
 * 禁用索引库实例
 */
export function disabledIdxDbInst(id: string): Promise<any> {
  const url = idxDbInstBaseUrl + `/enabled/${id}`;
  return axios.put(url, {
    enabled: false,
  });
}

/**
 * 分页查询索引库实例
 */
export function getIdxDbInstListPage(params: any): Promise<any> {
  const url = idxDbInstBaseUrl + `/page`;
  return axios.get(url, { params });
}

/**
 * 查询索引库实例-根据idxDbInstId
 */
export function getIdxDbInst(id: string): Promise<any> {
  const url = idxDbInstBaseUrl + `/${id}`;
  return axios.get(url);
}

/**
 * 发布索引库实例
 */
export function publishIdxDbInst(id: string, publish: any): Promise<any> {
  const url = idxDbInstBaseUrl + `/publish/${id}`;
  return axios.put(url, publish);
}

/**
 * 去重索引库实例
 */
export function dedupIdxDbInst(id: string, dedup: any) {
  const url = idxDbInstBaseUrl + `/dedup/${id}`;
  return axios.put(url, dedup);
}

/**
 * 删除索引库实例数据
 */
export function clearIdxDbInst(id: string): Promise<any> {
  const url = idxDbInstBaseUrl + `/clear/${id}`;
  return axios.delete(url);
}

/**
 * 导出索引库实例数据
 */
export function exportExcel(): any {
  const url = idxDbInstBaseUrl + `/export`;
  return axios.get(url, { responseType: "blob" });
}

/**
 * 导出统计信息
 */
export function exportSites(data: any) {
  const url = idxDbInstBaseUrl + `/export_sites`;
  return axios.post(url, data, { responseType: "blob" });
}

/**
 * 根据区域获取ES信息
 */
export function getRegionEsInfoList(region: string): Promise<any> {
  const url = baseUrl + `/es/list?region=${region}`;
  return axios.get(url);
}
