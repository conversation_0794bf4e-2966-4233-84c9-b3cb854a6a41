import axios from "./axios";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/`;
const domainBaseUrl = baseUrl + "domain-black";
const urlBaseUrl = baseUrl + "url-black";
// domain
/**
 * 获取列表
 */
export function getDomainBlack(data: any): Promise<any> {
  const url = domainBaseUrl + "/list" + `?page=${data.page}&size=${data.size}`;
  return axios.post(url, data);
}

/**
 * 新增
 */
export function addDomainBlack(data: any): Promise<any> {
  const url = domainBaseUrl;
  return axios.post(url, data);
}
/**
 * 编辑
 */
export function editDomainBlack(
  id: any,
  data: any,
  areacode: any
): Promise<any> {
  const url = domainBaseUrl + "/" + areacode + "/" + id;
  return axios.put(url, data);
}
/**
 * 启停
 */

export function updateDomainBlackStatus(id: any, data: any): Promise<any> {
  const url =
    domainBaseUrl +
    "/opt/" +
    data.regionCode +
    "/" +
    id +
    "?enabled=" +
    data.enabled;
  return axios.put(url);
}
/**
 * 日志
 */
export function getDomainLog(params: any, regionCode: any): Promise<any> {
  const url = domainBaseUrl + "/log/" + regionCode;
  return axios.get(url, { params });
}

// url
/**
 * 获取列表
 */
export function geturlBlackInfo(data: any): Promise<any> {
  const url = urlBaseUrl;
  return axios.post(url, data);
}
/**
 * 创建
 */
export function addUrlBlack(data: any): Promise<any> {
  const url = urlBaseUrl + "/create";
  return axios.post(url, data);
}
/**
 * 日志
 */
export function getUrlBlackLog(id: any, data: any): Promise<any> {
  const url = urlBaseUrl + "/log/" + id;
  return axios.get(url, data);
}
/**
 * 启停
 */
export function updateURLBlackStatus(id: any, data: any): Promise<any> {
  const url = urlBaseUrl + "/opt/" + id + "/" + data.status+ "/" + data.regionCode;
  return axios.put(url);
}
/**
 * 分页获取url黑名单
 */
export function geturlBlackPage(data: any): Promise<any> {
  const url = urlBaseUrl + "/page";
  return axios.post(url,data);
}

/**
 * 删除url黑名单
 */
export function deleteUrlBlackPage(data: any): Promise<any> {
  const url = urlBaseUrl+"/delete/"+data.id;
  return axios.delete(url);
}

/**
 * 删除domain黑名单
 */
export function deleteDoaminBlackPage(data: any): Promise<any> {
  const url = domainBaseUrl+'/'+ data.regionCode+ "/delete/"+data.id;
  return axios.delete(url);
}
/**
 * 批量刷新
 */
export function getUrlRefresh(): Promise<any> {
  const url = urlBaseUrl + "/batch/refresh" ;
  return axios.get(url);
}