import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/dedup`
const baseUrl1 = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/`

/**
 * 新增
 */
export function insertMetaDedup(data: any): Promise<any> {
    const url = baseUrl
    return axios.post(url, data)
}

/**
 * 删除
 */
export function removeMetaDedup(id: string): Promise<any> {
    const url = baseUrl + `/${id}`
    return axios.delete(url)
}

/**
 * 更新
 */
export function modifyMetaDedup(data: any): Promise<any> {
    const url = baseUrl + `/${data.id}`
    return axios.put(url, data)
}

/**
 * 分页查询
 */
export function getMetaDedupListPage(search: string, page: number, size: number, sort: string): Promise<any> {
    const url = baseUrl + `/page?search=${search}&page=${page}&size=${size}&sort=${sort}`
    return axios.get(url)
}

/**
 * 查询全部
 */
export function getMetaDedupList(region:string): Promise<any> {
    const url = baseUrl1+'func/'+region+'/enabled_version?type.equals=dedup'
    return axios.get(url)
}