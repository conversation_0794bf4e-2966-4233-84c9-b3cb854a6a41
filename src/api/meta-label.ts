import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/label`

/**
 * 更新
 */
export function modifyMetaLabel(data: any): Promise<any> {
    const url = baseUrl + `/${data.id}`
    return axios.put(url, data)
}

/**
 * 分页查询
 */
export function getMetaLabelListPage(search: string, page: number, size: number, sort: string): Promise<any> {
    const url = baseUrl + `?search=${search}&page=${page}&size=${size}&sort=${sort}`
    return axios.get(url)
}

/**
 * 查询等级字典列表项
 */
export function getMetaLabelList(): Promise<any> {
    const url = baseUrl + '/unPage'
    return axios.get(url)
}