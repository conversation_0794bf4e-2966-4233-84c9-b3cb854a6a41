import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/region`

/**
 * 新增
 */
export function insertMetaRegion(data: any): Promise<any> {
    const url = baseUrl
    return axios.post(url, data)
}

/**
 * 删除
 */
export function removeMetaRegion(id: string): Promise<any> {
    const url = baseUrl + `/${id}`
    return axios.delete(url)
}

/**
 * 更新
 */
export function modifyMetaRegion(data: any): Promise<any> {
    const url = baseUrl + `/${data.id}`
    return axios.put(url, data)
}

/**
 * 分页查询
 */
export function getMetaRegionListPage(search: string, page: number, size: number, sort: string): Promise<any> {
    const url = baseUrl + `/page?search=${search}&page=${page}&size=${size}&sort=${sort}`
    return axios.get(url)
}

/**
 * 查询全部
 */
export function getMetaRegionList(): Promise<any> {
    const url = baseUrl
    return axios.get(url)
}