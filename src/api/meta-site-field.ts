import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/site-fields`

/**
 * 更新
 */
export function modifyMetaSiteField(data: any): Promise<any> {
    const url = baseUrl + `/${data.id}`
    return axios.put(url, data)
}
/**
 * 新增
 */
export function addMetaSiteField(data: any): Promise<any> {
    const url = baseUrl
    return axios.post(url, data)
}
/**
 * 新增
 */
export function deleteMetaSiteField(id: any): Promise<any> {
    const url = baseUrl+'/'+id
    return axios.delete(url)
}
/**
 * 分页查询
 */
export function getMetaSiteFieldListPage(params:any): Promise<any> {
    const url = baseUrl 
    return axios.get(url,{params})
}

/**
 * 查询所有类别
 */
export function getMetaSiteFieldCategoryList(): Promise<any> {
    const url = baseUrl + `/category`
    return axios.get(url)
}

/**
 * 查询属性列表项
 */
export function getMetaSiteFieldList(): Promise<any> {
    const url = baseUrl + `?sort=idx,asc`
    return axios.get(url)
}

/**
 * 查询属性列表项
 */
export function getVectorDbFieldList(): Promise<any> {
    const url = baseUrl + `/vector-db-fields`
    return axios.get(url)
}

/**
 * 查询属性列表项
 */
export function getFilterFieldList(): Promise<any> {
    const url = baseUrl + `/feature-base-field`
    return axios.get(url)
}