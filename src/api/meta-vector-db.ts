import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/meta/vector-db`

/**
 * 新增
 */
export function insertMetaVectorDb(data: any): Promise<any> {
    const url = baseUrl
    return axios.post(url, data)
}

/**
 * 删除
 */
export function removeMetaVectorDb(id: string): Promise<any> {
    const url = baseUrl + `/${id}`
    return axios.delete(url)
}

/**
 * 更新
 */
export function modifyMetaVectorDb(data: any): Promise<any> {
    const url = baseUrl + `/${data.id}`
    return axios.put(url, data)
}

/**
 * 分页查询
 */
export function getMetaVectorDbListPage(search: string, page: number, size: number, sort: string): Promise<any> {
    const url = baseUrl + '/page' + `?search=${search}&page=${page}&size=${size}&sort=${sort}`
    return axios.get(url)
}

/**
 * 查询全部
 */
export function getMetaVectorDbList(): Promise<any> {
    const url = baseUrl
    return axios.get(url)
}