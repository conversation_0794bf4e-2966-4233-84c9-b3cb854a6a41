import { cloneDeep } from 'lodash';
import templateJ<PERSON><PERSON> from '@/mock/template'
import appJSO<PERSON> from '@/mock/app'
import cityJSON from '@/mock/city'
import dataPreviewJSON from '@/mock/data-preview'

// 应用列表
export function getAppList (data: any) {
  // console.log('接口参数为：', data)
  const {page, size} = data
  return new Promise((resolve: any) => {
    const res = appJSON
    const result = cloneDeep(res.data.content);
    const totalElements = result.length
    const content = result.slice((page - 1)*size, page * size)
    resolve({
      content,
      totalElements
    });
  });
}

export function getCityList () {
  return new Promise((resolve: any) => {
    const res = cityJSON
    const result = cloneDeep(res.data);
    resolve(result);
  });
}

// 模版；列表
export function getTemplateList (data: any) {
  // console.log('接口参数为：', data)
  const {page, size} = data
  return new Promise((resolve: any) => {
    const res = templateJSON
    const result = cloneDeep(res.data.content);
    const totalElements = result.length
    const content = result.slice((page - 1)*size, page * size)
    resolve({
      totalElements,
      content
    });
  });
}

// 获取数据预览数据
export function getContentData (data: any) {
  // console.log('接口参数为：', data)
  const {page, size} = data
  return new Promise((resolve: any) => {
    const res = dataPreviewJSON
    const result = cloneDeep(res.data.content);
    const totalElements = result.length
    const content = result.slice((page - 1)*size, page * size)
    resolve({
      totalElements,
      content
    });
  });
}