import axios from "./axios";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/dataset-space`;
const baseUrl1 = `/${import.meta.env.VITE_TLB_NAME}/api/v1/dataset`;

/**
 * 树
 */
export function getTree(params: any): Promise<any> {
  const url = baseUrl;
  return axios.get(url, {params});
}
export function createTree(data: any): Promise<any> {
  const url = baseUrl;
  return axios.post(url, data);
}

export function editTree(id: any, data: any): Promise<any> {
  const url = baseUrl+'/'+  id;
  return axios.put(url, data);
}
export function deleteTreeNode(id: any): Promise<any> {
  const url = baseUrl +'/'+ id;
  return axios.delete(url);
}

/**
 * 词典
 */
export function getDicPage( params: any): Promise<any> {
  const url = baseUrl1+'/page';
  return axios.get(url, { params });
}
export function addDic(data: any): Promise<any> {
  const url = baseUrl1;
  return axios.post(url, data);
}
export function deleteDic(id: any,): Promise<any> {
  const url = baseUrl1 +'/'+ id;
  return axios.delete(url);
}
export function editDic(id: any, data: any): Promise<any> {
  const url = baseUrl1 +'/'+ id;
  return axios.put(url, data);
}
export function exportDic(params: any): Promise<any> {
  const url = baseUrl1+'/'+ "export";
  return axios.get(url, { params, responseType: "blob" });
}
