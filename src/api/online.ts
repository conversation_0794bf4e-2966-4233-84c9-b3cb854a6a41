import axios from "./axios";

const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/online-plan`;
const portalUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`;
const productOnlineUrl = baseUrl + `/product`;
const idxOnlineUrl = baseUrl + `/idx`;

// 产品方案部分
// 获取上线计划列表
export function getProductOnlinePlanList(data: any): Promise<any> {
  const params = {
    enabled: data.enabled,
    productId: data.productId || "",
    search: data.name || "",
    sort: data.sort || "createdDate,desc",
    page: data.page,
    size: data.size,
    targetRegion: data.targetRegion,
    status: data.status,
  };
  return axios.get(productOnlineUrl, { params });
}

// 创建上线计划
export function createProductOnlinePlan(data: any): Promise<any> {
  const url = `${productOnlineUrl}`;
  return axios.post(url, data);
}

// 编辑上线计划
export function editProductOnlinePlan(data: any): Promise<any> {
  const url = `${productOnlineUrl}/${data.id}`;
  return axios.put(url, data);
}

// 编辑上线计划
export function editProductOnlinePlanInfo(data: any): Promise<any> {
  const url = `${productOnlineUrl}/info/${data.id}`;
  return axios.put(url, data);
}

// 删除上线计划
export function deleteProductOnlinePlan(id: any): Promise<any> {
  const url = `${productOnlineUrl}/${id}`;
  return axios.delete(url);
}

// 校验产品方案版本是否能上线
export function checkProductVersionValid(regionCode: string, productId: string, productVersionId: string): Promise<any> {
  const url = `${productOnlineUrl}/check`;
  return axios.get(url, { params: { regionCode, productId, productVersionId } });
}

// 同步
export function syncProductOnlinePlan(id: any): Promise<any> {
  const url = `${productOnlineUrl}/sync/${id}`;
  return axios.put(url);
}

// 启动
export function enabledProductOnlinePlan(id: any): Promise<any> {
  const url = `${productOnlineUrl}/enabled/${id}?enabled=true`;
  return axios.put(url);
}

// 禁用
export function disabledProductOnlinePlan(id: any): Promise<any> {
  const url = `${productOnlineUrl}/enabled/${id}?enabled=false`;
  return axios.put(url);
}

// 清除数据
export function clearProduct(id: any): Promise<any> {
  const url = `${productOnlineUrl}/clear/${id}`;
  return axios.delete(url);
}

//索引库部分
// 获取上线计划列表
export function getIdxDbOnlinePlanList(params: any): Promise<any> {
  return axios.get(idxOnlineUrl, { params });
}

/**
 * 查询上线计划-根据id
 */
export function getIdxDbOnlinePlan(id: string): Promise<any> {
  const url = idxOnlineUrl + `/${id}`;
  return axios.get(url);
}

// 创建上线计划
export function createIdxDbOnlinePlan(data: any): Promise<any> {
  const url = `${idxOnlineUrl}`;
  return axios.post(url, data);
}

// 编辑上线计划
export function editIdxDbOnlinePlan(data: any): Promise<any> {
  const url = `${idxOnlineUrl}/${data.id}`;
  return axios.put(url, data);
}

/**
 * 更新基础信息
 */
export function editIdxDbOnlinePlanBase(data: any): Promise<any> {
  const url = `${idxOnlineUrl}/base/${data.id}`;
  return axios.put(url, {
    name: data.name,
    description: data.description,
  });
}

// 删除上线计划
export function deleteIdxDbOnlinePlan(id: any): Promise<any> {
  const url = `${idxOnlineUrl}/${id}`;
  return axios.delete(url);
}

// 校验索引库实例是否能上线
export function checkIdxInstValid(regionCode: string, idxDbInstId: string): Promise<any> {
  const url = `${idxOnlineUrl}/check`;
  return axios.get(url, { params: { regionCode, idxDbInstId } });
}

// 同步
export function syncIdxDbOnlinePlan(id: any, data?: any): Promise<any> {
  const url = `${idxOnlineUrl}/sync/${id}`;
  return axios.put(url, data);
}

// 回放
export function replayIdxDbOnlinePlan(id: any, data?: any): Promise<any> {
  const url = `${idxOnlineUrl}/replay/${id}`;
  return axios.put(url, data);
}

// 删除索引库
export function clearIdxDb(id: any): Promise<any> {
  const url = `${idxOnlineUrl}/index-del/${id}`;
  return axios.delete(url);
}

// 清空索引库数据
export function clearIdxDbData(id: any): Promise<any> {
  const url = `${idxOnlineUrl}/index-clear/${id}`;
  return axios.delete(url);
}

// 启用
export function enabledIdxDb(id: any): Promise<any> {
  const url = `${idxOnlineUrl}/enabled/${id}?enabled=true`;
  return axios.put(url);
}

// 禁用
export function disabledIdxDb(id: any): Promise<any> {
  const url = `${idxOnlineUrl}/enabled/${id}?enabled=false`;
  return axios.put(url);
}

/**
 * 获取区域
 */
export function getMetaRegionList(): Promise<any> {
  const url = `${portalUrl}/meta/region`;
  return axios.get(url);
}

/**
 * 获取索引库实例
 */
export function getIdxDbInstList() {
  const url = `${portalUrl}/idx-db-inst/list?type=1&enabled=true`;
  return axios.get(url);
}

/**
 * 导出索引库
 */
export function exportIdxDbInstList() {
  const url = `${baseUrl}/idx/export`;
  return axios.get(url, { responseType: "blob" });
}
