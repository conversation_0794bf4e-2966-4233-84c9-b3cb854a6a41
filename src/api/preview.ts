import axios from "./axios";
import { dataC } from "turing-plugin";
import { keys } from "lodash";
import JSONBIG from "json-bigint";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/data-item`;

/**
 * 数据预览-分页查询-fromSite
 */
export function getPreviewListPageFromSite(siteId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site/?siteId=${siteId}&page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}
/**
 * 数据预览-总量查询-fromSite
 */
export function getCountFromSite(siteId: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site/count?siteId=${siteId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-数据导出-fromSite
 */
export function exportPreviewListFromSite(siteId: string, query: any, limit: number): any {
  let url = baseUrl + `/from-site/export?siteId=${siteId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.limit = limit;
  return axios.get(url, { params: params, responseType: "blob" });
}

/**
 * 数据预览-分页查询-fromRule
 */
export function getPreviewListPageFromRule(ruleId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site-version?ruleId=${ruleId}&page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-总量查询-fromRule
 */
export function getCountFromRule(ruleId: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site-version/count?ruleId=${ruleId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-数据导出-fromRule
 */
export function exportPreviewListFromRule(ruleId: string, query: any, limit: number): any {
  let url = baseUrl + `/from-site-version/export?ruleId=${ruleId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.limit = limit;
  return axios.get(url, { params: params, responseType: "blob" });
}

/**
 * 数据预览-分页查询-fromSiteVersion
 */
export function getPreviewListPageFromSiteVersion(siteVersionId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site-version?siteVersionId=${siteVersionId}&page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-总量查询-fromSiteVersion
 */
export function getCountFromSiteVersion(siteVersionId: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site-version/count?siteVersionId=${siteVersionId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-数据导出-fromSiteVersion
 */
export function exportPreviewListFromSiteVersion(siteVersionId: string, query: any, limit: number): Promise<any> {
  let url = baseUrl + `/from-site-version/export?siteVersionId=${siteVersionId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.limit = limit;
  return axios.get(url, { params: params, responseType: "blob" });
}

/**
 * 数据预览-分页查询-fromSiteVersionConflict
 */
export function getPreviewListPageFromSiteVersionConflict(siteVersionId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-site-version/conflict/${siteVersionId}?page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-分页查询-fromDatasetVersion
 */
export function getPreviewListPageFromDatasetVersion(datasetVersionId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-dataset?datasetVersionId=${datasetVersionId}&page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-总量查询-fromDatasetVersion
 */
export function getCountFromDatasetVersion(datasetVersionId: string, query: any): Promise<any> {
  let url = baseUrl + `/from-dataset/count?datasetVersionId=${datasetVersionId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-数据导出-fromDatasetVersion
 */
export function exportPreviewListFromDatasetVersion(datasetVersionId: string, query: any, limit: number): Promise<any> {
  let url = baseUrl + `/from-dataset/export?datasetVersionId=${datasetVersionId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.limit = limit;
  return axios.get(url, { params: params, responseType: "blob" });
}

/**
 * 数据预览-分页查询-fromDatasetVersionSite
 */
export function getPreviewListPageFromDatasetVersionSite(datasetVersionId: string, page: number, size: number, sort: string, query: any, site: string): Promise<any> {
  let url = baseUrl + `/from-dataset?datasetVersionId=${datasetVersionId}&page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.site = site;
  return axios.get(url, { params });
}

/**
 * 数据预览-总量查询-fromDatasetVersionSite
 */
export function getCountFromDatasetVersionSite(datasetVersionId: string, query: any, site: string): Promise<any> {
  let url = baseUrl + `/from-dataset/count?datasetVersionId=${datasetVersionId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.site = site;
  return axios.get(url, { params });
}

/**
 * 数据预览-数据导出-fromDatasetVersionSite
 */
export function exportPreviewListFromDatasetVersionSite(datasetVersionId: string, query: any, site: string, limit: number): Promise<any> {
  let url = baseUrl + `/from-dataset/export?datasetVersionId=${datasetVersionId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  params.site = site;
  params.limit = limit;
  return axios.get(url, { params: params, responseType: "blob" });
}

/**
 * 数据预览-获取正文
 */
export function getPreviewContent(id: string, previewFrom: string, fromId: string): Promise<any> {
  const url = baseUrl + `/${id}?previewFrom=${previewFrom}&fromId=${fromId}`;
  return axios.get(url);
}

/**
 * 数据预览-数据聚合统计-fromRegion
 */
export function getPreviewCapacityFromRegion(idxDbInstId: string, indexId: string, region: string): Promise<any> {
  const url = baseUrl + `/from-region/capacity`;
  const params = {
    idxDbInstId: idxDbInstId,
    indexId: indexId,
    region: region,
  };
  return axios.get(url, { params });
}

/**
 * 数据预览-分页查询-fromRegion
 */
export function getPreviewListPageFromRegion(idxDbInstId: string, page: number, size: number, sort: string, query: any, extraParams: any): Promise<any> {
  let url = baseUrl + `/from-region/page-site?&idxDbInstId=${idxDbInstId}&vector=false&page=${page}&size=${size}&sort=${sort}`;
  if (!dataC.isEmpty(extraParams.site)) {
    url = url + `&site=${extraParams.site}`;
  }
  url = url + `&indexId=${extraParams.indexId}&region=${extraParams.region}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      params[key] = query[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-总量查询-fromRegion
 */
export function getCountFromRegion(params: any): Promise<any> {
  let url = baseUrl + `/from-region/count`;
  keys(params).forEach((key) => {
    if (dataC.isEmpty(params[key])) {
      delete params[key];
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-获取正文-fromRegion
 */
export function getPreviewContentFromRegion(indexId: string, region: string, docId: string, hot: boolean): Promise<any> {
  const url = baseUrl + `/from-region/content`;
  const params = {
    indexId: indexId,
    region: region,
    docId: docId,
    hot: hot,
    vector: true,
  };
  return axios.get(url, {
    params: params,
    transformResponse: [
      function (data) {
        try {
          // 如果转换成功则返回转换的数据结果,简单的返回JSONBIG.parse(data)会导致类型错误
          return JSON.parse(JSON.stringify(JSONBIG.parse(data)));
        } catch (err) {
          // 如果转换失败，则包装为统一数据格式并返回
          return JSON.parse(data);
        }
      },
    ],
  });
}

/**
 * 数据预览-获取错误码-fromIndexPublishFail
 */
export function getErrCode(): Promise<any> {
  const url = baseUrl + `/from-region/error-code`;
  return axios.get(url);
}

/**
 * 数据预览-分页查询-fromIndexPublishFail
 */
export function getPreviewListPageFromIndexPublishFail(taskId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-region/failed?taskId=${taskId}&page=${page}&size=${size}&sort=${sort}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      if (query[key] instanceof Array) {
        params[key] = query[key].join(",");
      } else {
        params[key] = query[key];
      }
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-总量查询-fromIndexPublishFail
 */
export function getCountFromFail(siteId: string, query: any): Promise<any> {
  let url = baseUrl + `/from-region/failed/count?taskId=${siteId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      if (query[key] instanceof Array) {
        params[key] = query[key].join(",");
      } else {
        params[key] = query[key];
      }
    }
  });
  return axios.get(url, { params });
}

/**
 * 数据预览-数据导出-fromIndexPublishFail
 */
export function exportPreviewListFromIndexPublishFail(taskId: string, query: any): any {
  let url = baseUrl + `/from-region/failed/export?taskId=${taskId}`;
  const params: any = {};
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      if (query[key] instanceof Array) {
        params[key] = query[key].join(",");
      } else {
        params[key] = query[key];
      }
    }
  });
  return axios.get(url, { params: params, responseType: "blob" });
}
