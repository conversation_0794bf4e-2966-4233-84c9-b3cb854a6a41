import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/product`

/* 产品列表 */
export function getProductList(data: any): Promise<any> {
    const params = {
        'search': data.name || '',
        'sort': data.sort || 'createdDate,desc',
        page: data.page,
        size: data.size
    }
    const url = `${baseUrl}`
    return axios.get(url, {params})
}

// 获取已启用的产品方案
export function getOpenedProductList(): Promise<any> {
    const url = `${baseUrl}/opened-list?sort=createdDate,desc`
    return axios.get(url)
}

// 查询所有已经上线启用的产品方案
export function getOnlineEnabledProductList(): Promise<any> {
    const url = `${baseUrl}/online-enabled-list?sort=createdDate,desc`
    return axios.get(url)
}

// 获取已发布的版本
export function getOpenedProductVersionList(id: any, regionCode:any) {
    const url = `${baseUrl}/version/opened/${id}?sort=lastModifiedDate,desc&regionCode=${regionCode}`
    return axios.get(url)
}

// 创建产品
export function addProduct(data: any): Promise<any> {
    const url = `${baseUrl}`
    return axios.post(url, data)
}

// 编辑产品
export function editProduct(data: any): Promise<any> {
    const url = `${baseUrl}/${data.id}`
    return axios.put(url, data)
}

// 删除产品
export function deleteProduct(id: any): Promise<any> {
    const url = `${baseUrl}/${id}`
    return axios.delete(url)
}

// 查询产品详情
export function getProductDetail(id: any): Promise<any>  {
    const url = `${baseUrl}/${id}`
    return axios.get(url)
}

// 启用/禁用产品
export function enableOrDisableProduct(data: any): Promise<any> {
    const url = `${baseUrl}/${data.id}/open?opened=${data.opened}`
    return axios.put(url)
}

// 查询产品版本列表
export function getProductVersionList(params: any) {
    const url = `${baseUrl}/version/by-product/${params.flowId}`;
    return axios.get(url, { params });
}

// 创建产品版本
export function addProductVersion(data: any) {
    const url = `${baseUrl}/version`
    return axios.post(url, data)
}

// 编辑产品版本
export function editProductVersion(data: any) {
    const url = `${baseUrl}/version/${data.id}`
    return axios.put(url, data)
}

// 删除产品版本
export function deleteProductVersion(id: any) {
    const url = `${baseUrl}/version/${id}`
    return axios.delete(url)
}

// 复制产品版本
export function copyProductVersion(id: any) {
    const url = `${baseUrl}/version/copy/${id}`
    return axios.put(url)
}

// 发布产品版本
export function publishProductVersion(id: any) {
    const url = `${baseUrl}/version/publish/${id}`
    return axios.put(url)
}

// 生成模板
export function generateTemplate(id: any) {
    const url = `${baseUrl}/version/template/${id}`
    return axios.put(url)
}

//修改启用禁用状态
export function changeVersionOpened(id: any, opened: boolean) {
    const url = `${baseUrl}/version/${id}/open?opened=${opened}`
    return axios.put(url)
}
