import axios from './axios'

const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/scene`
const portalUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`

/* 策略列表 */
export function getSceneList(data: any): Promise<any> {
  const params = {
    'search': data.name || '',
    'sort': data.sort || 'createdDate,desc',
    page: data.page,
    size: data.size
  }
  const url = `${baseUrl}`
  return axios.get(url, {params})
}

// 创建策略
export function addScene(data: any): Promise<any> {
  const url = `${baseUrl}`
  return axios.post(url, data)
}

// 编辑策略
export function editScene(data: any): Promise<any> {
  const url = `${baseUrl}/${data.id}`
  return axios.put(url, data)
}

// 删除策略
export function deleteScene(id: any): Promise<any> {
  const url = `${baseUrl}/${id}`
  return axios.delete(url)
}

// 查询策略详情
export function getsceneDetail(id: any): Promise<any>  {
  const url = `${baseUrl}/${id}`
  return axios.get(url)
}

// 查询策略版本列表
export function getSceneVersionList(data: any) {
  const params = {
    'sort': data.sort || 'createdDate,desc',
    page: data.page,
    size: data.size
  }
  const url = `${baseUrl}/version/by-scene/${data.id}`
  return axios.get(url, {params})
}

// 创建场景版本
export function addSceneVersion(data: any) {
  const url = `${baseUrl}/version`
  return axios.post(url, data)
}

// 编辑场景版本
export function editSceneVersion(data: any) {
  const url = `${baseUrl}/version/${data.id}`
  return axios.put(url, data)
}

// 删除场景版本
export function deleteSceneVersion(id: any) {
  const url = `${baseUrl}/version/${id}`
  return axios.delete(url)
}

// 获取区域列表
export function getRegionList(type: number) {
  const url = `${portalUrl}/meta/region/by-type?type=${type}`
  return axios.get(url)
}

// 复制场景版本
export function copySceneVersion(id: any) {
  const url = `${baseUrl}/version/copy/${id}`
  return axios.put(url)
}

// 发布场景版本
export function publishSceneVersion(id: any) {
  const url = `${baseUrl}/version/publish/${id}`
  return axios.put(url)
}

// 生成模板
export function generateTemplate(id: any) {
  const url = `${baseUrl}/version/template/${id}`
  return axios.put(url)
}

// 切换产品可用
export function toggleProductAvailable(data: any) {
  const url = `${baseUrl}/${data.id}/open?opened=${data.opened}`
  return axios.put(url)
}

/**
 * 获取场景分组列表
 * @returns 
 */
export function getSceneGroupList() {
  const url = `${baseUrl}/group-name-list`
  return axios.get(url)
}

export function getSceneVersionListByName() {
  const url = baseUrl + `/version/search`;
  return axios.get(url);
}