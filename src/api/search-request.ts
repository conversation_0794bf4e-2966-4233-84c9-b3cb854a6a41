import axios from './axios'

const baseUrl = `/${import.meta.env.VITE_EVAL_NAME}/eval/api/v1/search-api-result`

// Types
interface PageParams {
  pageNum: number
  pageSize: number
  [key: string]: any
}

interface ExportParams {
  count: number
  [key: string]: any
}

interface SearchResult {
  id: string
  region: string
  [key: string]: any
}

interface TaskResult {
  id: string
  [key: string]: any
}

/**
 * 分页查询搜索结果
 * @param region 区域
 * @param params 查询参数
 */
export function getSearchResults(params: PageParams): Promise<any> {
  const url = `${baseUrl}/page`
  return axios.post(url, params)
}

/**
 * 导出搜索结果
 * @param region 区域
 * @param params 导出参数（包含count限制，不包含分页参数）
 */
export function exportSearchResults(data: ExportParams): Promise<any> {
  const url = `${baseUrl}/export-task`
  return axios.post(url, data)
}

/**
 * 下载搜索结果
 * @param id 结果ID
 */
export function downloadSearchResult(id: string): Promise<any> {
  const url = `${baseUrl}/${id}/download`
  return axios.get(url, { responseType: 'blob' })
}

/**
 * 查看全链路日志
 * @param id 结果ID
 */
export function getSearchTrace(id: string,region:string): Promise<any> {
  const url = `${baseUrl}/${region}/${id}/trace`
  return axios.get(url)
}

/**
 * 删除任务
 * @param params 删除参数
 */
export function deleteSearchTask(ids: string[]): Promise<any> {
  const url = `${baseUrl}/task/delete`
  return axios.delete(url, { data: ids})
}

/**
 * 查看任务列表
 * @param params 查询参数
 */
export function getSearchTasks(params: PageParams): Promise<any> {
  const url = `${baseUrl}/task/page`
  return axios.get(url, { params })
}

export function getSearchIntent(params?: PageParams): Promise<any> {
  const url = `${baseUrl}/intent`
  return axios.get(url, { params })
}
