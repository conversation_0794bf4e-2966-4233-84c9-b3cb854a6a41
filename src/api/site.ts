import axios from "./axios";
import { dataC, timeC } from "turing-plugin";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1`;
const siteBaseUrl = baseUrl + "/site";
const versionBaseUrl = baseUrl + "/site-version";
const ruleBaseUrl = baseUrl + "/rule";
import SSEClient from "./sse-client";

/**
 * 新增站点
 */
export function insertSite(data: any): Promise<any> {
  const url = siteBaseUrl;
  return axios.post(url, data);
}

/**
 * 删除站点
 */
export function removeSite(id: string): Promise<any> {
  const url = siteBaseUrl + `/${id}`;
  return axios.delete(url);
}

/**
 * 更新站点
 */
export function modifySite(data: any): Promise<any> {
  const url = siteBaseUrl + `/${data.id}`;
  return axios.put(url, data);
}

/**
 * 分页查询站点
 */
export function getSiteListPage(search: string, labels: string, scenarios: string, page: number, size: number, sort: string): Promise<any> {
  let url = siteBaseUrl + `?search=${search}`;
  if (!dataC.isEmpty(labels)) {
    url = url + `&labels=${labels}`;
  }
  if (!dataC.isEmpty(scenarios)) {
    url = url + `&scenarios=${scenarios}`;
  }
  url = url + `&page=${page}&size=${size}&sort=${sort}`;
  return axios.get(url);
}

/**
 * 查询所有标签列表
 */
export function getSiteTagList(): Promise<any> {
  const url = siteBaseUrl + `/tag`;
  return axios.get(url);
}

/**
 * 查询站点-根据siteId
 */
export function getSite(id: string): Promise<any> {
  const url = siteBaseUrl + `/${id}?id=${id}`;
  return axios.get(url);
}

/**
 * 查询站点-根据site
 */
export function getSiteByName(site: string): Promise<any> {
  const url = siteBaseUrl + `/find-by-site/${site}`;
  return axios.get(url);
}

/**
 * 导出站点数据
 */
export function exportExcel(): any {
  const url = siteBaseUrl + `/export`;
  return axios.get(url, { responseType: "blob" });
}

/**
 * 新增站点版本
 */
export function insertSiteVersion(data: any): Promise<any> {
  const url = versionBaseUrl;
  return axios.post(url, data);
}

/**
 * 删除站点版本
 */
export function removeSiteVersion(id: string): Promise<any> {
  const url = versionBaseUrl + `/${id}`;
  return axios.delete(url);
}

/**
 * 更新站点版本
 */
export function modifySiteVersion(data: any): Promise<any> {
  const url = versionBaseUrl + `/${data.id}`;
  return axios.put(url, data);
}

/**
 * 分页查询站点版本
 */
export function getSiteVersionListPage(siteId: string, page: number, size: number, sort: string): Promise<any> {
  const url = versionBaseUrl + `/by-site/${siteId}?siteId=${siteId}&page=${page}&size=${size}&sort=${sort}`;
  return axios.get(url);
}

/**
 * 查询站点版本-根据siteId
 */
export function getSiteVersionList(siteId: string): Promise<any> {
  const url = versionBaseUrl + `/${siteId}`;
  return axios.get(url);
}

/**
 * 查询站点版本-根据datasetId
 */
export function getSiteVersionListByDatasetId(datasetId: string): Promise<any> {
  const url = versionBaseUrl + `/by-dataset/${datasetId}`;
  return axios.get(url);
}

/**
 * 查询站点版本-根据datasetVersionId
 */
export function getSiteVersionListByDatasetVersionId(datasetVersionId: string): Promise<any> {
  const url = versionBaseUrl + `/by-dataset-version/${datasetVersionId}`;
  return axios.get(url);
}

/**
 * 计算站点版本
 */
export function computeSiteVersion(id: string) {
  const url = versionBaseUrl + `/compute/${id}`;
  return axios.put(url);
}

/**
 * 去重站点版本
 */
export function dedupSiteVersion(id: string, dedup: any) {
  const url = versionBaseUrl + `/dedup/${id}`;
  return axios.put(url, dedup);
}

/**
 * 发布站点版本
 */
export function publishSiteVersion(id: string) {
  const url = versionBaseUrl + `/publish/${id}`;
  return axios.put(url);
}

/**
 * 复制站点版本
 */
export function copySiteVersion(id: string) {
  const url = versionBaseUrl + `/copy/${id}`;
  return axios.post(url);
}

/**
 * 删除数据
 */
export function clearSiteVersion(id: string) {
  const url = versionBaseUrl + `/clear/${id}`;
  return axios.delete(url);
}

/**
 * 新增站点规则
 */
export function insertRule(data: any): Promise<any> {
  const url = ruleBaseUrl;
  return axios.post(url, data);
}

/**
 * 删除站点规则
 */
export function removeRule(id: string): Promise<any> {
  const url = ruleBaseUrl + `/${id}`;
  return axios.delete(url);
}

/**
 * 更新站点规则
 */
export function modifyRule(data: any): Promise<any> {
  const url = ruleBaseUrl + `/${data.id}`;
  return axios.put(url, data);
}

/**
 * 查询站点规则-根据siteVersionId
 */
export function getRuleList(siteVersionId: string, page: number, size: number, sort: string): Promise<any> {
  const url = ruleBaseUrl + `/by-site-version/${siteVersionId}?page=${page}&size=${size}&sort=${sort}`;
  return axios.get(url);
}

/**
 * 查询站点规则-根据ruleId
 */
export function getRule(ruleId: string): Promise<any> {
  const url = ruleBaseUrl + `/${ruleId}`;
  return axios.get(url);
}

/**
 * 复制站点规则
 */
export function copyRule(id: string): Promise<any> {
  const url = ruleBaseUrl + `/copy/${id}`;
  return axios.post(url);
}

/**
 * 调试站点规则
 */
export function testRule(data: any): Promise<any> {
  const url = ruleBaseUrl + `/test`;
  return axios.post(url, data);
}

/**
 * 查询规则调试记录
 */
export function getRuleTestTraceList(ruleId: string, page: number, size: number, sort: string): Promise<any> {
  const url = ruleBaseUrl + `/test/trace/${ruleId}?page=${page}&size=${size}&sort=${sort}`;
  return axios.get(url);
}

/**
 * 删除规则调试记录
 */
export function deleteRuleTestTraceList(ruleId: string, ids: Array<string>): Promise<any> {
  const url = ruleBaseUrl + `/test/trace`;
  return axios.delete(url, { data: { ruleId: ruleId, ids: ids } });
}

/**
 * 清空规则调试记录
 */
export function clearRuleTestTraceList(ruleId: string): Promise<any> {
  const url = ruleBaseUrl + `/test/trace/${ruleId}`;
  return axios.delete(url);
}

/**
 * 调试站点规则结果反馈
 */
export function testRuleFeedback(ruleId: string, pass: boolean): Promise<any> {
  const url = ruleBaseUrl + `/test-feedback`;
  return axios.post(url, {
    ruleId: ruleId,
    pass: pass,
  });
}

/**
 * 获取mongoDSL
 */
export function mongo(expr: any): Promise<any> {
  const url = ruleBaseUrl + `/mongo`;
  return axios.post(url, expr);
}
