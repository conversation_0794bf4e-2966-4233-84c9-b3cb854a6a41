import { fetchEventSource } from '@microsoft/fetch-event-source';

const baseURL = '/lynxiao/proxyApi';

type SSEOpenHandler = (data: any) => void;
type SSEMessageHandler = (data: any) => void;
type SSEErrorHandler = (error: Event) => void;

class SSEClient {
  private url: string;
  private data: any;
  private openHandler: SSEOpenHandler | null = null;
  private messageHandler: SSEMessageHandler | null = null;
  private errorHandler: SSEErrorHandler | null = null;
  private reconnectInterval: number = 3000; // 默认重连时间为3秒
  private controller = new AbortController(); // 用来终止连接

  constructor(url: string, data: any) {
    this.url = url;
    this.data = data;
  }

  // 设置连接成功处理函数
  onOpen(handler: SSEOpenHandler): SSEClient {
    this.openHandler = handler;
    return this;
  }

  // 设置消息处理函数
  onMessage(handler: SSEMessageHandler): SSEClient {
    this.messageHandler = handler;
    return this;
  }

  // 设置错误处理函数
  onError(handler: SSEErrorHandler): SSEClient {
    this.errorHandler = handler;
    return this;
  }

  // 使用 fetchEventSource 连接到 SSE
  connect() {
    const client = this;
    fetchEventSource(baseURL + client.url, {
      method: 'POST',  // 请求方法
      signal: client.controller.signal,
      headers: {
        'Authorization': 'Basic dGFndXNlcjp0YWdwYXNzd29yZA==',
        'Accept': 'text/event-stream',
        'Content-Type': 'application/json;charset=utf-8',
      },
      openWhenHidden: true, // 离开页面导致的连接断开可以通过设置 openWhenHidden: true来避免
      body: JSON.stringify(client.data),
      async onopen(response) {
        // 打开连接后的回调
        console.log('SSE 连接成功', );
        if (client.openHandler && response) {
          client.openHandler(response);
        }
      },
      onmessage(event) {
        // 接收到消息时的回调
        if (client.messageHandler && event.data) {
          try {
            const data = JSON.parse(event.data);
            client.messageHandler(data);
          } catch (error) {
            console.error('消息解析失败', error);
          }
        }
      },
      onerror(error) {
        // 出现错误时的回调
        console.error('SSE 连接错误', error);
        if (client.errorHandler) {
          client.errorHandler(error);
        }
      },
      onclose() {
        console.log('SSE 连接关闭');
      },
    });
  }

  // 断开连接 
  disconnect() {
    console.log('断开连接');
    this.controller.abort();
  }
}

export default SSEClient;