import axios from "./axios";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/api/v1/task`;

/**
 * 在线任务查询
 */
export function getRunningTaskListPage(search: string, page: number, size: number, sort: string): Promise<any> {
  const url = baseUrl + `/running-task?search=${search}&page=${page}&size=${size}&sort=${sort}`;
  return axios.get(url);
}

/**
 * 获取任务信息
 */
export function getTaskProgressListByTask(taskIdList: Array<string>) {
  const url = baseUrl + `/progress-task`;
  return axios.post(url, {
    taskIds: taskIdList,
  });
}

/**
 * 任务取消
 */
export function cancelTask(taskId: string) {
  const url = baseUrl + `/cancel/${taskId}`;
  return axios.delete(url);
}

/**
 * 任务历史查询
 */
export function getTaskHistoryListPage(refId: string, dataTaskType: number, page: number, size: number, sort: string): Promise<any> {
  let url = baseUrl + `/task-history?refId=${refId}`;
  if (dataTaskType != null) {
    url = `${url}&dataTaskType=${dataTaskType}`;
  }
  url = `${url}&page=${page}&size=${size}&sort=${sort}`;
  return axios.get(url);
}

/**
 * 查询所有任务类型
 */
export function getTaskTypeList(): Promise<any> {
  const url = baseUrl + `/getTaskTypeList`;
  return axios.get(url);
}

/**
 * 查询所有任务类型
 */
export function getDataTaskTypeList(): Promise<any> {
  const url = baseUrl + `/getDataTaskTypeList`;
  return axios.get(url);
}

/**
 * 强制完成任务
 */
export function forceCompleteTask(taskId: String): Promise<any> {
  const url = baseUrl + `/complete/${taskId}`;
  return axios.put(url);
}

