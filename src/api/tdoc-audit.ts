import axios from "./axios";
import { dataC } from "turing-plugin";
import { assign, keys } from "lodash";
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/stream/api/v1/data-audit`;

/* 产品列表 */
export function getTdocAuditListPage(tab: Number, data: any): Promise<any> {
  const obj: any = {
    tab: tab,
    sort: data.sort || "createdDate,desc",
    page: data.page,
    size: data.size,
  };
  const params = assign({}, data, obj);
  const url = `${baseUrl}/`;
  return axios.get(url, { params });
}

// 查询条件-领域
export function getCondDomainList(): Promise<any> {
  const url = `${baseUrl}/cond/tags`;
  return axios.get(url);
}

// 查询条件-数据集
export function getCondDatasetList(): Promise<any> {
  const url = `${baseUrl}/cond/dataset`;
  return axios.get(url);
}

// 查询条件-索引库
export function getCondIdxDbList(): Promise<any> {
  const url = `${baseUrl}/cond/idx-db`;
  return axios.get(url);
}

// 查询条件-站点版本
export function getCondSiteVersionList(datasetVersionId: String, idxDbInstId: String, site: string): Promise<any> {
  let url = `${baseUrl}/cond/site-version?site=${site}`;
  if (!dataC.isEmpty(datasetVersionId)) {
    url = `${url}&datasetVersionId=${datasetVersionId}`;
  } else if (!dataC.isEmpty(idxDbInstId)) {
    url = `${url}&idxDbInstId=${idxDbInstId}`;
  }
  return axios.get(url);
}

/**
 * 审核通过/不通过/彻底删除/入库操作 列表页
 */
export function auditListOperate(operate: String, auditListOperateDto: any) {
  const url = `${baseUrl}/operate/list-${operate}`;
  if (auditListOperateDto.selectedAll) {
    auditListOperateDto.auditIds = ""; //防止get请求字符串长度过长
  } else {
    auditListOperateDto.auditIds = auditListOperateDto.auditIds.join(",");
  }
  return axios.get(url, { params: auditListOperateDto });
}

/**
 * 审核通过/不通过/彻底删除/入库操作 详情页
 */
export function auditOperate(operate: String, auditOperateDto: any) {
  const url = `${baseUrl}/operate/${operate}`;
  keys(auditOperateDto).forEach((key) => {
    if (dataC.isEmpty(auditOperateDto[key])) {
      delete auditOperateDto[key];
    }
  });
  if (auditOperateDto.selectedAll) {
    auditOperateDto.ids = ""; //防止get请求字符串长度过长
  } else {
    auditOperateDto.ids = auditOperateDto.ids.join(",");
  }
  return axios.get(url, { params: auditOperateDto });
}

/**
 * 数据预览-分页查询-formTdocAudit
 */
export function getPreviewListPageFromTdocAudit(auditId: string, page: number, size: number, sort: string, query: any): Promise<any> {
  let url = baseUrl + `/from-audit/?auditId=${auditId}&page=${page}&size=${size}&sort=${sort}`;
  keys(query).forEach((key) => {
    if (key != "page" && key != "size" && key != "sort" && key != "total" && !dataC.isEmpty(query[key])) {
      url = url + `&${key}=${query[key]}`;
    }
  });
  return axios.get(url);
}

/**
 * 数据预览-总量查询-formTdocAudit
 */
export function getCountFromTdocAudit(auditId: string, query: any): Promise<any> {
  let url = baseUrl + `/from-audit/count?auditId=${auditId}`;
  keys(query).forEach((key) => {
    if (!dataC.isEmpty(query[key])) {
      url = url + `&${key}=${query[key]}`;
    }
  });
  return axios.get(url);
}

/**
 * 数据预览-获取正文
 */
export function getPreviewContent(id: string, fromId: string): Promise<any> {
  const url = baseUrl + `/${id}?fromId=${fromId}`;
  return axios.get(url);
}

/**
 * 增量数据-数据统计
 */
export function getCount(): Promise<any> {
  const url = baseUrl + `/count`;
  return axios.get(url);
}
