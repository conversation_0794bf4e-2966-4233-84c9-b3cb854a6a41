import axios from './axios'

const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/template`

/* 模板列表 */
export function getTemplateList(data: any): Promise<any> {
  const params = {
    'search': data.name || '',
    'sort': data.sort || 'createdDate,desc',
    page: data.page || 1,
    size: data.size
  }
  const url = `${baseUrl}`
  return axios.get(url, {params})
}

// 创建模板
export function addTemplate(data: any): Promise<any> {
  const url = `${baseUrl}`
  return axios.post(url, data)
}

// 编辑模板
export function editTemplate(data: any): Promise<any> {
  const url = `${baseUrl}/${data.id}`
  return axios.put(url, data)
}


// 删除模板
export function deleteTemplate(id: any): Promise<any> {
  const url = `${baseUrl}/${id}`
  return axios.delete(url)
}

// 另存为模板
export function saveAsTemplate(data: any): Promise<any> {
  const url = `${baseUrl}/save-as`
  return axios.put(url, data)
}

/**
 * 根据类型查询模板
 * @param data 
 * @returns 
 */
export function getTemplateListByType(type: number): Promise<any> {
  const url = `${baseUrl}/by-type/${type}`;
  return axios.get(url)
}

/**
 * 获取模板分组列表
 * @returns 
 */
export function getTemplateGroupList(type: number) {
  const url = `${baseUrl}/group-name-list/${type}`
  return axios.get(url)
}