
import axios from "./axios";
import { dataC, timeC } from "turing-plugin";
const baseUrl = `/${import.meta.env.VITE_SUPPORT_NAME}/api/v1`;
const siteBaseUrl = baseUrl + "/statistics/";
const portalUrl = baseUrl + "/portal/";
export function getBusinessTraffic(params:any): Promise<any> {
  const url = siteBaseUrl + `app/overview`;
  return axios.get(url,{params});
}

export function getEnvironmentalTraffic(params:any): Promise<any> {
  const url = siteBaseUrl + `evn/overview`;
  return axios.get(url,{params});
}

export function getBusinessApplication(): Promise<any> {
  const url = portalUrl + `enable-app`;
  return axios.get(url);
}
export function getProductPlan(): Promise<any> {
  const url = portalUrl + `online-enabled-product-list`;
  return axios.get(url);
}
export function getReport(type:any,params:any): Promise<any> {
  const url = siteBaseUrl + `query/${type}`;
  return axios.get(url,{params}); 
}
export function exportReport(type:any,params:any): Promise<any> {
  const url = siteBaseUrl + `export/${type}`;
  return axios.get(url,{params,responseType:'blob'}); 
}
export function exportDynamicsReport(type:any,params:any): Promise<any> {
  const url = siteBaseUrl + `export/dynamics/${type}`;
  return axios.get(url,{params,responseType:'blob'}); 
}
export function getPoint(params:any): Promise<any> {
  const url = siteBaseUrl + `burial/overview`;
  return axios.get(url,{params}); 
}
export function getPointReportCard(): Promise<any> {
  const url = siteBaseUrl + `query/burialType`;
  return axios.get(url); 
}
export function getDynamicsBurialType(): Promise<any> {
  const url = siteBaseUrl + `query/dynamics-burial-type`;
  return axios.get(url); 
}
export function getDynamicsColumn(type:any,params:any): Promise<any> {
  const url = siteBaseUrl + `query/dynamics/${type}`;
  return axios.get(url,{params}); 
}