import axios from './axios'
const baseUrl = `/${import.meta.env.VITE_TLB_NAME}/flow/api/v1/product/traffic`

// 产品分流列表
export function getTrafficList(data: any): Promise<any> {
    const params = {
        envType: data.envType,
        productId: data.productId,
        sort: data.sort || 'createdDate,desc',
        page: data.page || 1,
        size: data.size,
        status: data.status,
    }
    return axios.get(baseUrl, {params})
}

// 创建产品分流
export function addTraffic(data: any): Promise<any> {
    const url = `${baseUrl}`
    return axios.post(url, data)
}

// 编辑产品分流
export function editTraffic(data: any): Promise<any> {
    const url = `${baseUrl}/${data.id}`
    return axios.put(url, data)
}

// 删除产品分流
export function deleteTraffic(id: any): Promise<any> {
    const url = `${baseUrl}/${id}`
    return axios.delete(url)
}

// 启用/停用产品分流
export function enableOrDisableTraffic(data: any): Promise<any> {
    const url = `${baseUrl}/status/${data.id}?enabled=${data.enabled}&envType=${data.envType}`
    return axios.put(url)
}

// 发布产品分流
export function publishTraffic(id: any): Promise<any> {
    const url = `${baseUrl}/publish/${id}`
    return axios.put(url)
}

// 复制产品分流
export function copyTraffic(id: any): Promise<any> {
    const url = `${baseUrl}/copy/${id}`
    return axios.put(url)
}

// 归档
export function handleDocument(id: any): Promise<any> {
    const url = `${baseUrl}/archive/${id}`
    return axios.put(url)
}