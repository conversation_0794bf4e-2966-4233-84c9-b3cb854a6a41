<template>
  <div >
    <el-select ref="buttonRef" v-model="selectedValues" multiple placeholder="请选择" @remove-tag="handleRemoveTag"
      popper-class="transfer-select" @visible-change="handleClick">
      <el-option v-for="item in selectedList" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>
    <el-popover ref="popoverRef" placement="bottom" trigger="click" width="630" virtual-triggering :teleported="false"
      :virtual-ref="buttonRef" :visible="popVisible" popper-class="transfer-popper">
      <div class="transfer-container">
        <div class="transfer-close" @click="handleClose">×</div>
        <el-transfer v-model="transferValue" filterable :data="transferData" ref="transferRef" :titles="['可选', '已选']"
          @change="handleTransferChange"  />
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, unref } from 'vue'
import { ClickOutside as vClickOutside } from 'element-plus'
const props = defineProps({
  // 所有可选数据
  data: {
    type: Array,
    default: () => []
  },
  // 已选数据
  modelValue: {
    type: Array,
    default: () => []
  },
  transferData: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['update:modelValue'])
const buttonRef = ref()
const popoverRef = ref()
const popVisible = ref(false)
const selectedValues = ref<any[]>([])
const transferValue = ref<any[]>([])
const transferRef = ref(null)
const selectedList = computed(() => {
  return props.transferData.map(item => ({
    value: item.key,
    label: item.label
  })).filter(item => selectedValues.value.includes(item.value))
})

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  selectedValues.value = val
  transferValue.value = val
}, { immediate: true })

// 处理穿梭框变化
const handleTransferChange = (value: any[]) => {
  selectedValues.value = value
  popVisible.value = false
  emits('update:modelValue', value)
}

// 处理移除标签
const handleRemoveTag = (tag: any) => {
  const index = transferValue.value.indexOf(tag)
  if (index > -1) {
    transferValue.value.splice(index, 1)
  }
}

// 处理点击事件
const handleClick = (e: boolean) => {
  console.log(e, 'focus');
  popVisible.value = true
}
const onClickOutside = () => {
  popVisible.value = false
}
// 处理关闭事件
const handleClose = () => {
  popVisible.value = false
  console.log('close');

  // unref(popoverRef).popperRef?.delayHide?.()
}
</script>

<style lang="scss" scoped>
.el-transfer {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-top: 5px;
  ::v-deep{
    .el-transfer-panel{
    width: 203px;
  }
  }
}

.transfer-container {
  position: relative;
  padding-top: 12px;

  .transfer-close {
    position: absolute;
    right: 3px;
    top: -4px;
    width: 19px;
    height: 19px;
    cursor: pointer;
  }
}
</style>
<style>
.transfer-select {
  display: none;
}
</style>