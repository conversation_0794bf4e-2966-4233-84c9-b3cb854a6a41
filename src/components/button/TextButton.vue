<template>
  <span
    class="m-text-button can-click mg-right-24"
    :class="{
      'is-disabled': disabled,
      'button-success': type === 'success',
      'button-info': type === 'info',
      'button-warning': type === 'warning',
      'button-danger': type === 'danger',
    }"
    @click="handleClick"
  >
    <slot />
  </span>
</template>
<script lang="ts" setup>
interface Props {
  disabled?: boolean;
  type?: string;
}
const props = defineProps<Props>();
const emit = defineEmits(['click']);
const handleClick = () => {
  if (!props.disabled) {
    emit('click');
  }
};
</script>

<style lang="scss">
.can-click {
  color: $primary-color;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    color: var(--el-color-primary-light-3);
  }

  &.is-disabled {
    color: var(--el-color-primary-light-5);
    border-color: var(--el-color-primary-light-8);
    cursor: not-allowed;
  }

  &.button-success {
    color: $success-color;
  }

  &.button-info {
    color: $info-color;
  }

  &.button-warning {
    color: $warning-color;
  }

  &.button-danger {
    color: $danger-color;
  }
}

</style>
