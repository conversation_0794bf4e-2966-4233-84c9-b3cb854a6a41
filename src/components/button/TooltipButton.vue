<template>
  <el-button 
    v-if="clickable"
    ref="buttonRef"
    v-bind="$attrs"
    :type="styleType"
    :icon="styleIcon"
    @click="handleClick">
    <slot></slot>
  </el-button>
  <el-tooltip  placement="top" effect="dark" v-else>
    <template #content>
      {{ tips }}
    </template>
    <el-button 
      ref="buttonRef"
       v-bind="$attrs"
      :type="styleType"
      :icon="styleIcon"
      @click="handleClick">
      <slot></slot>
    </el-button>
  </el-tooltip>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import i18n from "@/utils/i18n";
import { THROTTLE_TIME } from '@/utils/constants'
import useCtx from "@/hooks/useCtx";
const { $app } = useCtx();
const props = defineProps({
  tips: { type: String, default: i18n.global.t("placeholder.pleaseChooseData") }, // 提示
  handle: { type: String },                    // 操作名称
  type: { type: String, default: 'default' },  // 按钮类型
  icon: { type: String },
  clickable: { type: Boolean, default: true }, // 是否可点击
  time: { type: Number, default: THROTTLE_TIME }
})
const emits = defineEmits(['click'])
// 像新增、导入、导出、删除等按钮样式固定直接传以下自定义类型即可，也可以直接传按钮type
const styleType: any = computed(() => {
  const type: string = props.type;
  if (type === "import") {
    return "default";
  } else if (type === "export") {
    return "default";
  } else if (type === "delete") {
    return "danger";
  } else {
    return type;
  }
});

const styleIcon: any = computed(() => {
  // const type: string = props.type;
  // if (type === "import") {
  //   return "Upload";
  // } else if (type === "export") {
  //   return "Download";
  // } else if (type === "delete") {
  //   return "Delete";
  // } else {
  //   return props.icon;
  // }
  return props.icon;
});

let oldTime = 0
const buttonRef = ref<any>(null);
const handleClick = () => {
  // 点击完成后让按钮失去焦点，否则按钮背景颜色不能恢复
  const target: any = buttonRef.value.ref;
  target.blur();
  if (props.clickable) {
    // 一定时间内只能执行一次操作
    const newTime = (new Date()).getTime()
    if (newTime - oldTime > props.time + 100) {
      emits('click')
    } 
    oldTime = (new Date()).getTime()
  } else {
    const tips = props.handle ? i18n.global.t('placeholder.pleaseChooseDataTip', {handle: props.handle}) : props.tips
    $app.$message.warning(tips + '！')
  }
};
</script>

<style lang="scss" scoped>

</style>