<template>
  <el-button
    ref="buttonRef"
    :type="styleType"
    :icon="styleIcon"
    :plain="stylePlain"
    v-if="!getDisabledTips"
    :disabled="isDisabled"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot></slot>
  </el-button>
  <!-- 禁用的按钮需要给出禁用提示 -->
  <el-tooltip placement="top" effect="dark" v-else :content="getDisabledTips">
    <el-button
      ref="buttonRef"
      :type="styleType"
      :icon="styleIcon"
      :plain="stylePlain"
      :disabled="isDisabled"
      v-bind="$attrs"
      @click="handleClick"
    >
      <slot></slot>
    </el-button>
  </el-tooltip>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { THROTTLE_TIME } from "@/utils/constants";
import { textC, dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const props = defineProps({
  type: { type: String, default: "default" }, // 按钮类型
  icon: { type: String },
  plain: { type: Boolean, default: false },
  time: { type: Number, default: THROTTLE_TIME },
  operationAuth: { type: String, default: "" },
  disabled: { type: Boolean, default: false },
  disabledTips: { type: String, default: "" },
  stopPropagation: { type: Boolean, default: false },
});
const { proxy, $app, $auth } = useCtx();
const emits = defineEmits(["click"]);
const isDisabled = computed(() => {
  if (
    !dataC.isEmpty(props.operationAuth) &&
    !$auth.testAuth(props.operationAuth)
  ) {
    return true;
  }
  return props.disabled;
});
const getDisabledTips = computed(() => {
  if (
    !dataC.isEmpty(props.operationAuth) &&
    !$auth.testAuth(props.operationAuth)
  ) {
    return "权限不足";
  }
  return props.disabled && props.disabledTips;
});
// 像新增、导入、导出、删除等按钮样式固定直接传以下自定义类型即可，也可以直接传按钮type
const styleType: any = computed(() => {
  const type: string = props.type;
  if (type === "add") {
    return "primary";
  } else if (type === "import") {
    return "primary";
  } else if (type === "export") {
    return "primary";
  } else if (type === "delete") {
    return "danger";
  } else {
    return type;
  }
});

const stylePlain: any = computed(() => {
  const type: string = props.type;
  if (type === "add") {
    return false;
  } else if (type === "import") {
    return true;
  } else if (type === "export") {
    return true;
  } else if (type === "delete") {
    return true;
  } else {
    return props.plain;
  }
});

const styleIcon = computed(() => {
  const type: string = props.type;
  if (type === "add") {
    return "CirclePlus";
  } else if (type === "import") {
    return "Upload";
  } else if (type === "export") {
    return "Download";
  } else if (type === "delete") {
    return "Delete";
  } else {
    return props.icon;
  }
});
const buttonRef = ref<any>(null);
let oldTime = 0;
const handleClick = (event) => {
  if (props.stopPropagation) {
    event.stopPropagation();
  }
  // 一定时间内只能执行一次操作
  const newTime = new Date().getTime();
  if (newTime - oldTime > props.time + 100) {
    emits("click");
  }
  oldTime = new Date().getTime();
  // 点击完成后让按钮失去焦点，否则按钮背景颜色不能恢复
  const target: any = buttonRef.value.ref;
  target.blur();
};
</script>

<style lang="scss" scoped></style>
