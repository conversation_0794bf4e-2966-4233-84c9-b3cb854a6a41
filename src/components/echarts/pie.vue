<template>
  <div ref="pieChart" :style="{ width: width, height: height }"></div>
</template>

<script lang="ts" setup>
import echarts from "@/components/echarts/index.ts";
import customizedJSON from "./customized.json";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { map, cloneDeep, debounce } from "lodash";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const props = defineProps({
  width: {
    type: String,
    default: "100%",
  },
  name: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "100%",
  },
  data: {
    type: Array,
    required: true,
    defualt: () => [],
  },
  title: {
    type: String,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
  legendShow: {
    type: Boolean,
    default: true,
  },
  color: {
    type: Array,
  },
});
const pieChart = ref(null);
let chartInstance: any = null;
/* events */
const events = reactive({});
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(pieChart.value);
  updateChart();
};
const updateChart = () => {
  // 计算总和
  var total = (props.data || []).reduce((sum, cur) => {
    sum += cur.value;
    return sum;
  }, 0);
  const option = {
    title: props.title,
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ({d}%)",
    },
    legend: {
      show:props.legendShow,
      orient: "vertical",
      left: "right",
      type: "scroll",
      // 定义富文本样式
      textStyle: {
        rich: {
          bold: {
            // 自定义样式名
            fontWeight: "bold", // 加粗
            fontSize: 14, // 保持与全局一致
          },
        },
      },
      formatter: function (name) {
        // 在 legend 中显示百分比
        var target;
        for (var i = 0; i < props.data.length; i++) {
          if (props.data[i].name === name) {
            target = props.data[i].value;
            return `{bold|${name}}  ${((target / total) * 100).toFixed(2)}%`;
          }
        }
        return "";
      },
    },
    series: [
      {
        name: props.name,
        type: "pie",
        radius: ["40%", "90%"], // 内半径和外半径
        data: props.data,
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        itemStyle: {
          normal: {
            // 使用回调函数为每个扇区指定颜色
            color: function (params) {
              var colorList = props.color ? props.color : customizedJSON.color;
              return colorList[params.dataIndex % colorList.length]; // 确保不会超出颜色列表的范围
            },
          },
        },
        ...props.series,
      },
    ],
    ...props.option,
  };
  chartInstance.setOption(option);
};
const debounceResize = debounce(() => {
  chartInstance.resize();
});
onMounted(() => {
  initChart();
  window.addEventListener("resize", debounceResize);
});
onUnmounted(() => {
  chartInstance?.dispose(); // 组件卸载时销毁[1](@ref)
  window.removeEventListener("resize", debounceResize);
});
const resize =()=>{
  if (chartInstance) {
    chartInstance.resize(); // 调用 ECharts 实例的 resize 方法
  }
}
// watch监听
watch(
  () => props.data,
  (nu) => {
    if (nu) {
      updateChart();
    }
  }
);
defineExpose({
  resize
})
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
}
</style>
