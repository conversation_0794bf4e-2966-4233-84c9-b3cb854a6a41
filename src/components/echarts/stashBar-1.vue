<template>
  <div ref="stashChart" :style="{ width: width, height: height }"></div>
</template>

<script lang="ts" setup>
import echarts from "@/components/echarts/index.ts";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { map, cloneDeep, debounce } from "lodash";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const props = defineProps({
  transfer: {
    type: Boolean,
    default: false,
  },
  width: {
    type: String,
    default: "100%",
  },
  name: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "100%",
  },
  data: {
    type: Array,
    required: true,
  },
  xKey: {
    type: String,
    default: "name",
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
  labelShow: {
    type: Boolean,
    default: false,
  },
});
const stashChart = ref(null);
let chartInstance: any = null;
/* events */
const events = reactive({});
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(stashChart.value);
  updateChart();
};
const updateChart = () => {
  const minNum = 10;
  let option;
  const data = props.data || [];
  // 数据处理
  const categories = [
    ...new Set(data.flatMap((d) => d.num.map((n) => n.name))),
  ];
  const dates = data.map((d) => d.date);

  // 生成 series 数据
  const series = [...categories.map((category) => ({
    name: category,
    type: "bar",
    barMaxWidth:'35',
    stack: "总量",
    yAxisIndex: 0,
    data: data.map((d) => {
      const item = d.num.find((n) => n.name === category);
      return item ? item.value : 0;
    }),
  })),...categories.map((category) => {
    return {
    name: category+'有结果率',
    type: "line",
    smooth: true,
    yAxisIndex: 1,
    data: data.map((d) => {
      const item = d.num.find((n) => n.name === category);
      return item ? item.hasResultPercentage : 0;
    }),
  }
  })];
  option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      enterable: true, // 允许鼠标交互[4](@ref)
      extraCssText: "max-height: 200px; overflow-y: auto;", // 垂直滚动[4](@ref)
    },
    dataZoom: [
      {
        type: "inside",
        disabled: data.length < minNum,
      },
      {
        show: data.length >= minNum,
        height: 20, 
      },
    ],
    grid: {
      left: "0%",
      right: "1%",
      bottom: "45",
      
      containLabel: true,
    },
    legend: {
      data: [...categories,...categories.map(item=>item+'有结果率')],
      type: "scroll",
    },
    xAxis: {
      type: "category",
      data: dates,
    },
    yAxis: [
      {
        type: "value",
        name: "",
        yAxisIndex: 0
      },
      {
        type: "value",
        axisLabel: {
          formatter: "{value} %",
        },
         yAxisIndex: 1
      },
    ],
    series: series,
  };

  if (data.length == 0) {
    option.graphic = {
      type: "text", // 指定类型为文本
      left: "center", // 水平居中
      top: "center", // 垂直居中
      style: {
        text: "暂无数据", // 显示的文本内容
        font: "16px Arial", // 字体样式
        fill: "#999", // 字体颜色
      },
    }; // 显示占位符
    chartInstance.setOption(option, true);
  } else {
    option.graphic = null; // 不显示占位符
    chartInstance.setOption(option, true);
  }
};
const debounceResize = debounce(() => {
  chartInstance.resize();
});
onMounted(() => {
  initChart();
  window.addEventListener("resize", debounceResize);
});
onUnmounted(() => {
  chartInstance?.dispose(); // 组件卸载时销毁[1](@ref)
  window.removeEventListener("resize", debounceResize);
});
// watch监听
watch(
  () => props.data,
  (nu) => {
    if(nu){
      updateChart();
    }
  }
);
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
}
</style>
