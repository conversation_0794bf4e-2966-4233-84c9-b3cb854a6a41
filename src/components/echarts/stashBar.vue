<template>
  <div ref="stashChart" :style="{ width: width, height: height }"></div>
</template>

<script lang="ts" setup>
import echarts from "@/components/echarts/index.ts";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { map, cloneDeep, debounce } from "lodash";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import * as util from "@/utils/common";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const props = defineProps({
  transfer: {
    type: Boolean,
    default: false,
  },
  width: {
    type: String,
    default: "100%",
  },
  name: {
    type: String,
    default: "",
  },
  height: {
    type: String,
    default: "100%",
  },
  data: {
    type: Array,
    required: true,
  },
  xKey: {
    type: String,
    default: "name",
  },
  yKeys: {
    type: Array,
    required: true,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
  labelShow: {
    type: Boolean,
    default: false,
  },
});
const stashChart = ref(null);
let chartInstance: any = null;
/* events */
const events = reactive({});
const initChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(stashChart.value);
  updateChart();
};
const updateChart = () => {
  const minNum = 10;
  let option;
  const data = props.data || [];
  if (props.transfer) {
    option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
        formatter: function (params) {
          // 获取当前数据项
          const dataItem = params[0].data;

          // 基础模板
          let tooltipHtml = `<div style="padding:5px">`;
          tooltipHtml += `<strong>${dataItem.date}</strong><br/>`;

          // 动态生成指标显示
          props.yKeys.forEach((keyConfig) => {
            const value = dataItem[keyConfig.value];
            const percent = keyConfig.prencentKey
              ? dataItem[keyConfig.prencentKey]
              : null;
            const marker = params.find(item => keyConfig.label == item.seriesName).marker
            // 格式化数值
            const formattedValue = value.toLocaleString(); // 添加千分位
            const formattedPercent = percent ? `(${percent.toFixed(2)}%)` : "";

            tooltipHtml += `${marker}${keyConfig.label}：${util.formatNumber(formattedValue)} ${formattedPercent}<br/>`;
          });

          tooltipHtml += `</div>`;
          return tooltipHtml;
        },
      },
      dataZoom: [
        // {
        //   type: "inside",
        //   disabled: data.length < minNum,
        // },
        {
          show: data.length >= minNum,
          height: 20, 
        },
      ],
      legend: {},
      grid: {
        left: "3%",
        right: "4%",
        bottom: "50",
        containLabel: true,
      },
      // 互换 X 轴和 Y 轴的配置
      xAxis: {
        type: "category", // 原 yAxis 的配置
        data: (props.data || []).map((item) => item[props.xKey]), // 原 yAxis 的数据
      },
      yAxis: {
        type: "value", // 原 xAxis 的配置
      },
      series: props.yKeys.map((key, index) => ({
        name: key.label,
        type: "bar",
        barMaxWidth:'35',
        stack: "total",
        label: {
          show: false,
          formatter: (params) => {
            if (key.prencentKey) {
              // 数据字段可能需要根据实际数据结构调整
              return params.value
                ? `${params.value}(${params.data[key.prencentKey]}%)`
                : "";
            } else {
              return params.value ? `${util.formatNumber(params.value)}` : "";
            }
          },
        },
        emphasis: {
          focus: "series",
        },
        // 数据映射需适配垂直柱状图
        data: props.data.map((item) => ({
          ...item,
          value: item[key.value], // value 对应 yAxis
        })),
      })),
    };
  } else {
    option = {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          // Use axis to trigger tooltip
          type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
        },
        formatter: function (params) {
        let res = params[0].name + "<br>";
        params.forEach((item) => {
          res += `${item.marker} ${item.seriesName}: ${util.formatNumber(item.value)}<br>`;
        });
        return res;
      },
      },

      legend: {},
      grid: {
        left: "2%",
        right: "5%",
        bottom: "0%",
        containLabel: true,
      },
      xAxis: {
        type: "value",
      },
      yAxis: {
        type: "category",
        data: data.map((item) => item[props.xKey]),
        axisLabel: {
          rotate: 20, // 文字倾斜的角度，根据需要调整
          margin: 20,
        },
      },
      series: props.yKeys.map((key, index) => ({
        name: key.label,
        type: "bar",
        barMaxWidth:'35',
        stack: "total",
        barWidth: "40px",
        label: {
          show: true,
          formatter: (params) => {
            if (key.prencentKey) {
              return params.value
                ? `${util.formatNumber(params.value)}(${params.data[key.prencentKey]}%)`
                : "";
            } else {
              return params.value ? `${util.formatNumber(params.value)}` : "";
            }
          },
        },
        emphasis: {
          focus: "series",
        },
        data: data.map((item) => ({ ...item, value: item[key.value] })),
      })),
    };
  }

  if (data.length == 0) {
    option.graphic = {
      type: "text", // 指定类型为文本
      left: "center", // 水平居中
      top: "center", // 垂直居中
      style: {
        text: "暂无数据", // 显示的文本内容
        font: "16px Arial", // 字体样式
        fill: "#999", // 字体颜色
      },
    }; // 显示占位符
    chartInstance.setOption(option, true);
  } else {
    option.graphic = null; // 不显示占位符
    chartInstance.setOption(option, true);
  }
};
const debounceResize = debounce(() => {
  chartInstance.resize();
});
onMounted(() => {
  initChart();
  window.addEventListener("resize", debounceResize);
});
onUnmounted(() => {
  chartInstance?.dispose(); // 组件卸载时销毁[1](@ref)
  window.removeEventListener("resize", debounceResize);
});
// watch监听
watch(
  () => props.data,
  (nu) => {
    if(nu){
      updateChart();
    }
  }
);
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
}
</style>
