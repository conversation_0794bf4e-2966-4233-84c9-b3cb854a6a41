<template>
  <el-form
    class="my-form"
    ref="ruleFormRef"
    :model="form"
    :rules="rules"
    v-bind="{
      'label-width': '100px',
      ...$attrs
    }"
    @submit.native.prevent
    style="padding-right: 20px"
  >
    <template v-for="elem of formArr">
      <h3 class="common-part-title" v-if="elem.separate" style="padding-bottom: 20px;padding-top: 10px;">
        {{ elem.separate.title }}
      </h3>
      <el-form-item 
        :prop="elem.key" 
        v-if="!(elem.hidden && elem.hidden(form))"
        v-bind="elem.attrs">
        <template #label v-if="elem.label">
          <div class="flex">
            {{elem.label}}
            <my-tooltip :showAfter="500" :content="elem.tooltip" v-if="elem.tooltip" direction="right">
            </my-tooltip>
          </div> 
        </template>
        <!-- 文本框 -->
        <el-input
          v-if="elem.type === 'input'"
          v-model="form[elem.key]"
          v-bind="{
            'placeholder': $t('placeholder.pleaseInput') + elem.label,
            'clearable': true,
            'maxlength': name_maxlength,
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
        />
        <!-- 文本域 -->
        <el-input
          v-if="elem.type === 'textarea'"
          type="textarea"
          v-model="form[elem.key]"
          v-bind="{
            'placeholder': $t('placeholder.pleaseInput') + elem.label,
            'clearable': true,
            'show-word-limit': elem.attrs && elem.attrs.maxlength ? true : false,
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
        />
        <!-- 数字输入框 -->
        <el-input-number
          v-if="elem.type === 'inputNumber'"
          v-model="form[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
          />
        <!-- 单选按钮 -->
        <el-radio-group
          v-if="elem.type === 'radio'"
          v-model="form[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
        > 
          <template v-for="item of elem.options" :key="item.value">
            <el-radio
              :value="item.value"
              :disabled="item.disabled"
              v-if="!item.hidden">
              <div class="flex">
                {{ item.label }}
                <my-tooltip :showAfter="500" :content="item.tooltip" v-if="item.tooltip" direction="right">
                </my-tooltip>
              </div>
            </el-radio>
          </template>
        </el-radio-group>
        <!-- 多选按钮 -->
        <el-checkbox-group 
          v-if="elem.type === 'checkbox'"
          v-model="form[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
          >
          <el-checkbox 
            v-for="item of elem.options"
            :key="item.value"
            :value="item.value"
            :disabled="item.disabled">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
        <!-- 开关 -->
        <el-switch 
          v-if="elem.type === 'switch'"
          v-model="form[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events">
        </el-switch>
        <!-- 下拉选择框 -->
        <my-select 
          v-if="elem.type === 'select'"
          v-model="form[elem.key]"
          :options="elem.options"
          :style="{width: elem.width || '100%'}"
          v-bind="{
            'placeholder': $t('placeholder.pleaseSelect') + elem.label,
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
        />
        <!-- 级联选择 -->
        <el-cascader 
          v-else-if="elem.type === 'cascader'" 
          v-model="form[elem.key]"
          :options="elem.options"
          :style="{width: elem.width || '100%'}"
          v-bind="{
            'placeholder': $t('placeholder.pleaseSelect') + elem.label,
            'clearable': true,
            'filterable': true,
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"/>
        <!-- 日期选择框 -->
        <el-date-picker
          v-else-if="elem.type === 'datePicker'"
          v-model="form[elem.key]"
          type="date"
          :style="{width: elem.width || '100%'}"
          v-bind="{
            'placeholder': $t('time.chooseDate'),
            'clearable': true,
            'format': dateFormat,
            'value-format': dateFormat,
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
        >
        </el-date-picker>
        <!-- 日期(时间)选择范围 -->
        <el-date-picker
          v-else-if="['daterange', 'datetimerange'].includes(elem.type)"
          v-model="elem.modelValue"
          :type="elem.type"
          v-bind="{
            'range-separator': $t('time.to'),
            'start-placeholder': elem.type === 'daterange' ? $t('time.startDate') : $t('time.startTime'),
            'end-placeholder': elem.type === 'daterange' ? $t('time.endDate') : $t('time.endTime'),
            'format': elem.type === 'daterange' ? dateFormat : datetimeFormat,
            'value-format': elem.type === 'daterange' ? dateFormat : datetimeFormat,
            'popper-class': 'custom-content-view',
            'disabled': (elem.disabled && elem.disabled(form)),
            ...elem.attrs
          }"
          v-on="elem.events"
          :style="{width: elem.width || '100%'}">
        </el-date-picker>
        <!-- 文件上传 -->
        <my-upload
          v-else-if="elem.type === 'file'"
          v-model="form[elem.key]"
          v-bind="elem.attrs"
          v-on="elem.events"
          >
          <my-button type="primary">上传文件</my-button>
        </my-upload>
        <!-- 插槽 -->
        <template v-else-if="elem.type === 'slot'">
          <slot :name="elem.slotName"></slot>
        </template>
      </el-form-item>
    </template>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import type { FormInstance } from "element-plus";
import { NAME_MAX_LENGTH } from "@/utils/constants";

const props = defineProps({
  rules: { type: Object },
  ruleForm: { type: Object },
  formItems: { type: Object, default: () => {} },
});
const emits = defineEmits(["submit"]);

const name_maxlength = ref<number>(NAME_MAX_LENGTH);
const dateFormat = ref<string>('YYYY-MM-DD')
const datetimeFormat =  ref<string>('YYYY-MM-DD hh:mm:ss')

const ruleFormRef = ref<FormInstance>();
// 将对象格式的表单项转成数组，方便遍历
const formArr: any = computed(() => {
  let arr = [];
  for (let key in props.formItems) {
    arr.push({
      key,
      ...props.formItems[key],
    });
  }
  return arr;
});
const form = ref<any>({});
watch(
  () => props.ruleForm,
  (nu) => {
    form.value = nu;
  }
);
const submitForm = async () => {
  const formEl: FormInstance | undefined = ruleFormRef.value;
  if (!formEl) return;
  let result = null
  await formEl.validate((valid) => {
    if (valid) {
      emits("submit", { ...form.value });
      result = { ...form.value }
    } else {
      result = null
    }
  });
  return result
};
const resetForm = () => {
  const formEl: FormInstance | undefined = ruleFormRef.value;
  if (!formEl) return;
  formEl.resetFields();
};
defineExpose({
  submitForm,
  resetForm,
});
</script>

<style lang="scss" scoped>
.my-form {
  .no-label {
    :deep(.el-form-item__content) {
      margin-left: 0 !important;
    }
  }
}
</style>
