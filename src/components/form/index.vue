<template>
  <el-form
    class="my-form"
    ref="ruleFormRef"
    :model="ruleForm"
    :rules="rules"
    v-bind="{
      'label-width': '100px',
      ...$attrs
    }"
    @submit.native.prevent
    style="padding-right: 20px"
  >
    <template v-for="elem of formArr">
      <div style="display: flex" v-if="!(elem.hiddenSeparate && elem.hiddenSeparate(ruleForm))">
        <h3 class="common-part-title" v-if="elem.separate" style="margin-bottom: 16px; margin-top: 10px;">
          {{ elem.separate.title }}
        </h3>
        <el-switch v-if="elem.separate?.enabled != undefined" v-model="elem.separate.enabled" style="margin-top: 5px; margin-left: 10px"> </el-switch>
      </div>
      <el-form-item 
        :prop="elem.key" 
        :style="{width: elem.width || '100%'}"
        v-if="!(elem.hidden && elem.hidden(ruleForm))"
        v-bind="elem.attrs">
        <template #label v-if="elem.label">
          <div class="flex">
            {{elem.label}}
            <my-tooltip :showAfter="500" :content="elem.tooltip" v-if="elem.tooltip" direction="right">
            </my-tooltip>
          </div> 
        </template>
        <!-- 文本框 -->
        <el-input
          v-if="elem.type === 'input'"
          v-model="ruleForm[elem.key]"
          v-bind="{
            'placeholder': $t('placeholder.pleaseInput') + elem.label,
            'clearable': true,
            'maxlength': name_maxlength,
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"
        >
          <template #append v-if="elem.append">
            <div v-html="elem.append" @click="elem.customRender.click(ruleForm[elem.key])"></div>
          </template>
        </el-input>
        <!-- 文本域 -->
        <el-input
          v-if="elem.type === 'textarea'"
          type="textarea"
          v-model="ruleForm[elem.key]"
          v-bind="{
            'placeholder': $t('placeholder.pleaseInput') + elem.label,
            'clearable': true,
            'show-word-limit': elem.attrs && elem.attrs.maxlength ? true : false,
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"
        />
        <!-- 数字输入框 -->
        <el-input-number
          v-if="elem.type === 'inputNumber'"
          v-model="ruleForm[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"
          />
        <!-- 单选按钮 -->
        <el-radio-group
          v-if="elem.type === 'radio'"
          v-model="ruleForm[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"
        > 
          <template v-for="item of elem.options" :key="item.value">
            <el-radio
              :value="item.value"
              :disabled="item.disabled"
              v-if="!item.hidden">
              <div class="flex">
                {{ item.label }}
                <my-tooltip :showAfter="500" :content="item.tooltip" v-if="item.tooltip" direction="right">
                </my-tooltip>
              </div>
            </el-radio>
          </template>
        </el-radio-group>
        <!-- 多选按钮 -->
        <el-checkbox-group 
          v-if="elem.type === 'checkbox'"
          v-model="ruleForm[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"
          >
          <el-checkbox 
            v-for="item of elem.options"
            :key="item.value"
            :value="item.value"
            :disabled="item.disabled">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
        <!-- 开关 -->
        <el-switch 
          v-if="elem.type === 'switch'"
          v-model="ruleForm[elem.key]"
          v-bind="{
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events">
        </el-switch>
        <!-- 下拉选择框 -->
        <my-select 
          v-if="elem.type === 'select'"
          v-model="ruleForm[elem.key]"
          :options="elem.options"
          :style="{width: elem.width || '100%'}"
          v-bind="{
            'placeholder': $t('placeholder.pleaseSelect') + elem.label,
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs,
          }"
          v-on="elem.events"
        />
        <!-- 级联选择 -->
        <el-cascader 
          v-else-if="elem.type === 'cascader'" 
          v-model="ruleForm[elem.key]"
          :options="elem.options"
          :style="{width: elem.width || '100%'}"
          v-bind="{
            'placeholder': $t('placeholder.pleaseSelect') + elem.label,
            'clearable': true,
            'filterable': true,
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"/>
        <!-- 日期选择框 -->
        <el-date-picker
          v-else-if="elem.type === 'datePicker'"
          v-model="ruleForm[elem.key]"
          type="date"
          :style="{width: elem.width || '100%'}"
          v-bind="{
            'placeholder': $t('time.chooseDate'),
            'clearable': true,
            'format': dateFormat,
            'value-format': dateFormat,
            'disabled': (elem.disabled && elem.disabled(ruleForm)),
            ...elem.attrs
          }"
          v-on="elem.events"
        >
        </el-date-picker>
        <!-- 日期时间 -->
        <el-date-picker
          v-else-if="elem.type === 'datetime'"
          v-model="ruleForm[elem.key]"
          :disabled-date="elem.disabledDate"
          type="datetime"
          placeholder="选择时间日期"
        />
        
        <!-- 日期(时间)选择范围 -->
        <el-date-picker
        v-else-if="['daterange', 'datetimerange'].includes(elem.type)"
            v-model="ruleForm[elem.key]"
            :type="elem.type"
            v-bind="{
              'range-separator': '至',
              'start-placeholder': elem.type === 'daterange' ? '开始日期' : '开始时间',
              'end-placeholder': elem.type === 'daterange' ? '结束日期' : '结束时间',
              'format': elem.type === 'daterange' ? dateFormat : datetimeFormat,
              'value-format': elem.type === 'daterange' ? dateFormat : datetimeFormat,
              'popper-class': 'custom-content-view',
              'disabled': (elem.disabled && elem.disabled(ruleForm)),
              ...elem.attrs
            }"
          v-on="elem.events"
          :style="{width: elem.width || '100%'}">
        </el-date-picker>
        <!-- 文件上传 -->
        <my-upload
          v-else-if="elem.type === 'file'"
          v-model="ruleForm[elem.key]"
          v-bind="elem.attrs"
          v-on="elem.events"
          >
          <my-button type="primary">上传文件</my-button>
        </my-upload>
        <!-- 插槽 -->
        <template v-else-if="elem.type === 'slot'">
          <slot :name="elem.slotName"></slot>
        </template>
      </el-form-item>
    </template>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import type { FormInstance } from "element-plus";
import { NAME_MAX_LENGTH } from "@/utils/constants";

const props = defineProps({
  rules: { type: Object },
  ruleForm: { type: Object as any}, // 由于ruleForm是个引用数据类型，因此子组件中修改ruleForm中的属性是支持的，不会报错
  formItems: { type: Object, default: () => {} },
  index: { type: Number, default: 0 },
});
const remoteMethed=(val)=>{
  return function(val){

  }
}
const name_maxlength = ref<number>(NAME_MAX_LENGTH);
const dateFormat = ref<string>('YYYY-MM-DD')
const datetimeFormat =  ref<string>('YYYY-MM-DD HH:mm:ss')

const ruleFormRef = ref<FormInstance>();
// 将对象格式的表单项转成数组，方便遍历
const formArr: any = computed(() => {
  let arr = [];
  for (let key in props.formItems) {
    arr.push({
      key,
      ...props.formItems[key],
    });
  }
  return arr;
});
const submitForm = (callback: Function) => {
  const formEl: FormInstance | undefined = ruleFormRef.value;
  if (!formEl) return;
  formEl.validate((valid: any) => {
    callback(valid)
  }).catch((error) => {
  console.error('捕获到异常:', error);
});
};
const getValidateResult = () => new Promise((resolve, reject) => {
  const formEl: FormInstance | undefined = ruleFormRef.value;
  if (!formEl) return;
  formEl.validate((valid, fields) => {
    valid ? resolve({ valid, fields }) : reject({ valid, fields });
  });
})
const resetForm = () => {
  const formEl: FormInstance | undefined = ruleFormRef.value;
  if (!formEl) return;
  formEl.resetFields();
};
defineExpose({
  submitForm,
  resetForm,
  ruleFormRef,
  getValidateResult
});
</script>

<style lang="scss">
.my-form {
  .no-label {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }

  .required-label {
    .el-form-item__label::before {
      color: var(--el-color-danger);
      content: "*";
      margin-right: 4px;
    }
  }
}
</style>
