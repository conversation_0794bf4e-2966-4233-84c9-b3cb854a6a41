/*
* @Description: 将所有的自定义组件进行全局注册
* @Author: mxliu5
* @Date: 2024-10-16
*/

import { createApp } from "vue";

import myButton from "@/components/button/index.vue";
import TooltipButton from "@/components/button/TooltipButton.vue";
import mySelect from "@/components/select/index.vue";
import myDialog from "@/components/dialog/index.vue";
import myDrawer from "@/components/drawer/index.vue";
import myForm from "@/components/form/index.vue";
import myQuery from "@/components/query/index.vue";
import myOperation from "@/components/operation/index.vue";
import myPagination from "@/components/pagination/index.vue";
import myTable from "@/components/table/index.vue";
import TablePage from "@/components/table-page/index.vue";
import myEmpty from "@/components/empty/index.vue";
import StatusTag from "@/components/status/status-tag.vue";
import StatusDot from "@/components/status/status-dot.vue";
import myUpload from "@/components/upload/Upload.vue";
import myTooltip from "@/components/tooltip/index.vue";
import svgIcon from "@/components/svg-icon/index.vue";
import PageWrapper from "@/components/layout/PageWrapper.vue";
import LayoutPart from "@/components/layout/LayoutPart.vue";

const components: any = {
  myButton,
  TooltipButton,
  mySelect,
  myDialog,
  myDrawer,
  myForm,
  myQuery,
  myOperation,
  myPagination,
  myTable,
  TablePage,
  myEmpty,
  StatusTag,
  StatusDot,
  myUpload,
  myTooltip,
  svgIcon,
  PageWrapper,
  LayoutPart
};
export function initComponent(app: ReturnType<typeof createApp>) {
  for (let componentName in components) {
    const component = components[componentName]
    app.component(componentName, component);
  }
}
