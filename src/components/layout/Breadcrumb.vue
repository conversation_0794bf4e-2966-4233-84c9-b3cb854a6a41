<template>
  <div class="layout-tab">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="(item, index) of breadcrumbList"
        :key="index"
        :to="{
          name: item.name,
          query: item.query,
          params: item.params,
        }"
        replace
      >
        {{ item.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const { $app } = useCtx();

const breadcrumbList = ref<Array<any>>([]);
const getBreadcrumbList = () => {
  const matched = $app.$route.matched;
  let metaLabel = $app.$route.query?.metaLabel;
  if (!(metaLabel instanceof Array)) {
    metaLabel = [metaLabel];
  }
  console.log('matched', matched)
  breadcrumbList.value = matched
    .filter((v: any) => v.name) // 过滤掉不能跳转即name为空的路由
    .map((x: any, index: number) => {
      return {
        title: index == 0 ? x.meta?.label : !dataC.isEmpty(metaLabel) ? metaLabel[index - 1] : x.meta?.label,
        name: x.name,
        query: $app.$route.query,
        params: $app.$route.params,
      };
    });
  // console.log('breadcrumbList', breadcrumbList.value)
};
watch(
  () => $app.$route,
  () => {
    getBreadcrumbList();
  }
);
onMounted(() => {
  getBreadcrumbList();
});
</script>

<style lang="scss">
.xinghuo-search .layout-tab {
  height: var(--breadcrumb-height);
  @include flex();
  padding: 0 16px;
  background: #fff;
  flex-shrink: 0;
  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      font-weight: normal;
      color: $text-color-third;
    }
    &:last-child {
      .el-breadcrumb__inner {
        font-weight: 550;
        color: $text-color;
      }
    }
  }
}
</style>
