<template>
  <my-dialog 
    class="fullscreen-page"
    v-model="dialogVisible"
    fullscreen
    :with-header="false"
    :with-footer="false">
    <div class="fullscreen-page__header">
      <div class="flexBetween" style="width: 100%">
        <div class="header-left">
        <span class="header-back" @click="close">
          <el-icon><Back /></el-icon>
        </span>
        <p class="header-title">{{title}}</p>
      </div>
      <slot name="header"></slot>
      </div>
    </div>
    <div class="fullscreen-page__container">
      <slot></slot>
    </div>
  </my-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
defineProps({
  title: { type: String }
})
const emits = defineEmits(['close'])
const dialogVisible = ref<boolean>(true)
const close = () => {
  emits('close')
};
</script>

<style lang="scss" scoped>
.fullscreen-page {
  .fullscreen-page__header {
    height: 52px;
    line-height: 52px;
    background: rgba(255, 255, 255, .7);
    border-bottom: 1px solid #ddd;
    padding: 0 16px;
    @include flexBetween();
    .header-left {
      @include flex();
      .header-back {
        @include model(32px, 32px);
        @include flexCenter();
        background: #f0f4fe;
        border-radius: 5px;
        margin-right: 12px;
        cursor: pointer;
      }
      .header-title {
        font-size: 16px;
        color: #000;
      }
    }
  }
  .fullscreen-page__container {
    @include area-padding();
    @include calc-height(52px);
    overflow-y: auto;
  }
}
</style>

<style lang="scss">
.xinghuo-search .fullscreen-page.el-dialog{
  background: url('@/assets/images/system-bg.png') no-repeat;
  background-size: cover;
  padding: 0;
  .el-dialog__body {
    padding: 0;
  }
}
</style>