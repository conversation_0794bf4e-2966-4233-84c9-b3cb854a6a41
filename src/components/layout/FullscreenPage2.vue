<template>
  <div class="fullscreen-page">
    <div class="fullscreen-page__header">
      <div class="header-left">
        <span class="header-back" @click="close">
          <el-icon><Back /></el-icon>
        </span>
        <p class="header-title">{{title}}</p>
      </div>
        <slot name="header"></slot>
    </div>
    <div class="fullscreen-page__container">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import useCtx from '@/hooks/useCtx';
const { $router } = useCtx();
defineProps({
  title: { type: String }
})
const close = () => {
  $router.back()
};
</script>

<style lang="scss" scoped>
.fullscreen-page {
  position: fixed;
  top: 0;
  left: 0;
  @include model(100%, 100%);
  .fullscreen-page__header {
    height: 52px;
    line-height: 52px;
    background: linear-gradient(94deg,#c4dfff 2.91%,#e4e7ff 51.96%,#e6f2ff 97%);
    border-bottom: 1px solid #ddd;
    padding: 0 16px;
    @include flexBetween();
    .header-left {
      @include flex();
      .header-back {
        @include model(32px, 32px);
        @include flexCenter();
        background: #f0f4fe;
        border-radius: 5px;
        margin-right: 12px;
        cursor: pointer;
      }
      .header-title {
        font-size: 16px;
        color: #000;
      }
    }
  }
  .fullscreen-page__container {
    @include area-padding();
    @include calc-height(52px);
    overflow-y: auto;
  }
}
</style>