<template>
  <div class="layout-header">
    <div class="tool-bar-left">
      <svg-icon class="logo" name="logo" width="28px"></svg-icon>
      <h2 class="logo-text">星火搜索</h2>
    </div>
    <div class="tool-bar-right flex">
      <span class="img-head"></span>
      <div class="label-item" style="margin-right: 10px;"><label>账号名称：</label>{{ userName_CN }}</div>
      <el-dropdown @command="handleCommand">
        <div class="label-item logout-wrap">
          <label>账号：</label>
          {{ userName }}
          <svg-icon name="icon-drop" style="margin-left: 10px"/>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="logout">退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import useStore from '@/store'
import { storeToRefs } from "pinia";
const { authority } = useStore()
const { loadAndSetUserInfo } = authority
const { userName, userName_CN } = storeToRefs(authority); // 这里一定要使用storeToRefs，才能实时获取用户名称

const _created = () => {
  loadAndSetUserInfo()
}
_created()

// 退出登录
const handleCommand = (val: string) => {
  if (val === 'logout') {
    //
  }
}
</script>

<style lang="scss" scoped>
// 头部组件样式
.layout-header {
  height: $header-height;
  padding: 0 $padding-primary;
  @include flexBetween();
  >div {
    @include flex();
  }
  .tool-bar-left {
    .logo {
      margin-right: 8px;
    }
  }
  .tool-bar-right {
    .img-head {
      @include model(40px, 40px);
      margin-right: 16px;
      @include border-radius(50%);
      background: url("@/assets/images/icon-user.png") no-repeat;
      background-size: cover;
    }
    .label-item {
      color: #183D5C;
      @include flex();
      >label {
        color: #71899d;
      }
    }
    .logout-wrap {
      cursor: pointer;
    }
  }
}
</style>
