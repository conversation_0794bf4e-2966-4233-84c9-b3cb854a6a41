<template>
  <div class="layout-part">  
    <div class="part-title flexBetween">
        <slot name="title" v-if="slots.title"></slot>
        <span v-else>{{ title }}</span>
        <slot name="right" v-if="slots.right"></slot>
    </div>
    <div class="part-content" v-if="slots.default" :style="{padding}">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSlots } from 'vue';
const slots = useSlots()
defineProps({
  title: { type: String, default: '' },
  padding: { type: String, default: '12px'}
})
</script>

<style lang="scss" scoped>
.layout-part {
  &+.layout-part {
    margin-top: 12px;
  }
}
.part-title {
  position: relative;
  padding-left: 12px;
  font-size: 16px;
  font-weight: 550;
  @include flexBetween();
  &::before {
    content: "";
    position: absolute;
    background-color: $primary-color;
    width: 3px;
    height: 16px;
    left: 0px;
    border-radius: 0 5px 5px 0;
  }
}
</style>