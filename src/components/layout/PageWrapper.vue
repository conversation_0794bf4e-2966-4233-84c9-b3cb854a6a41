<!-- 为了通过matched获取面包屑轨迹，因此详情页面需要嵌在列表页的子路由中。这样方便追加面包屑 -->
<template>
  <!-- 一级页面 -->
  <slot v-if="!hidePage"></slot> 
  <!-- 嵌在一级页面中的详情等页面 -->
  <router-view></router-view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import useCtx from "@/hooks/useCtx";
const { $app } = useCtx();

const props = defineProps({
  routeName: { type: String }
})

// 当进入详情页后隐藏上一级页面
const hidePage = computed(() => {
  console.log($app.$route.name,props.routeName);
  
  return $app.$route.name.includes(props.routeName)
})
</script>

<style lang="scss" scoped></style>
  