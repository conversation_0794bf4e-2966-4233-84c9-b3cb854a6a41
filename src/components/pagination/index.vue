<!-- 全局分页组件 -->
<template>
    <el-pagination
      class="custom-pagination"
      background
      :current-page="pageNum"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :layout="pageLayout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </template>
  
  <script lang="ts" setup>
  defineProps({
    pageLayout: {
      type: String,
      default() {
        return 'total, prev, pager, next, sizes, jumper';
      },
    },
    pageNum: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizes: {
      type: Array as any,
      default: () => [10, 20, 30, 40],
    },
    total: {
      type: Number,
      default: 0,
    },
  });
  const emits = defineEmits(["size_change", "current-change"]);
  
  const handleSizeChange = function (val: any) {
    emits("size_change", val);
  };
  const handleCurrentChange = function (val: any) {
    emits("current-change", val);
  };
  </script>
  
  <style lang="scss">
  </style>
  