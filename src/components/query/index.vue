<!-- 
 公共查询栏：
 1.支持自定义每个查询项的宽度
 2.查询/重置按钮 当查询项过多折叠时，按钮组居右对齐。否则按钮组紧跟在最后一个查询项后面
-->
<template>
  <div class="t-query">
    <el-form style="width: 100%" @submit.native.prevent inline>
      <el-form-item
        class="query-item"
        v-for="(item, key) in actualQueryItems"
        :key="key"
        :label="item.label"
        :label-width="item.labelWidth"
        v-show="!item.hidden && getIndex(key) < limitCount && item.checked"
      >
        <!-- 文本框 -->
        <el-input
          v-if="item.type === 'input'"
          v-model="item.modelValue"
          :style="{ width: item.width || '240px' }"
          v-bind="{
            placeholder: '请输入' + item.label,
            clearable: true,
            ...item.attrs,
          }"
          @keydown.enter="searchImmediatelyFun"
        >
          <template #append v-if="item.showAppend">
            <el-button :icon="Search" />
          </template>
        </el-input>
        <!-- 数字输入框 -->
        <el-input-number
          v-else-if="item.type === 'inputNumber'"
          :style="{ width: item.width }"
          v-model="item.modelValue"
          v-bind="item.attrs"
          v-on="item.events"
          @change="searchImmediatelyFun"
        />
        <el-select
          v-else-if="item.type === 'select'"
          v-model="item.modelValue"
          :style="{ width: item.width || '150px' }"
          v-bind="{
            placeholder: '请选择' + item.label,
            clearable: true,
            filterable: true,
            'collapse-tags': true,
            'collapse-tags-tooltip': true,
            'max-collapse-tags': 1,
            ...item.attrs,
          }"
          @change="searchImmediatelyFun"
          v-on="item.events"
        >
          <el-option v-for="sub in item.options" :key="sub.value" :label="sub.label" :value="sub.value"> </el-option>
        </el-select>
        <!-- 级联选择 -->
        <el-cascader
          v-else-if="item.type === 'cascader'"
          v-model="item.modelValue"
          :options="item.options"
          v-bind="{
            clearable: true,
            filterable: true,
            placeholder: '请选择' + item.label,
            ...item.attrs,
          }"
          @change="searchImmediatelyFun"
          v-on="item.events"
          :style="{ width: item.width || '160px' }"
        />
        <!-- radioButton -->
        <el-radio-group v-else-if="item.type === 'radioButton'" v-model="item.modelValue" v-bind="item.attrs" v-on="item.events" @change="searchImmediatelyFun">
          <el-radio-button v-for="x of item.options" :key="x.value" :value="x.value">
            {{ x.label }}
          </el-radio-button>
        </el-radio-group>
        <!-- 日期选择框 -->
        <el-date-picker
          v-else-if="item.type === 'datePicker'"
          v-model="item.modelValue"
          type="date"
          v-bind="{
            placeholder: '选择日期',
            format: dateFormat,
            'value-format': dateFormat,
            'popper-class': 'custom-content-view',
            ...item.attrs,
          }"
          @change="searchImmediatelyFun"
          v-on="item.events"
          :style="{ width: item.width }"
        >
        </el-date-picker>
        <!-- 日期(时间)选择范围 -->
        <el-date-picker
          v-else-if="['daterange', 'datetimerange'].includes(item.type)"
          v-model="item.modelValue"
          :type="item.type"
          v-bind="{
            'range-separator': '至',
            'start-placeholder': item.type === 'daterange' ? '开始日期' : '开始时间',
            'end-placeholder': item.type === 'daterange' ? '结束日期' : '结束时间',
            format: item.type === 'daterange' ? dateFormat : datetimeFormat,
            'value-format': item.type === 'daterange' ? dateFormat : datetimeFormat,
            'popper-class': 'custom-content-view',
            ...item.attrs,
          }"
          @change="searchImmediatelyFun"
          v-on="item.events"
          :style="{ width: item.width }"
        >
        </el-date-picker>
        <!-- 快速选择日期范围 -->
        <div class="date-quick flex" v-else-if="item.type === 'dateQuick'">
          <el-radio-group v-model="item.quick.modelValue" v-bind="item.quick.attrs" @change="(val: any) => quickDateChange(val, item)" style="flex-shrink: 0">
            <el-radio-button v-for="x of item.quick.options" :key="x.value" :value="x.value" plain>
              {{ x.label }}
            </el-radio-button>
          </el-radio-group>
          <!-- 日期选择框 -->
          <el-date-picker
            v-model="item.modelValue"
            :type="item.quick.withTime ? 'datetimerange' : 'daterange'"
            :disabledDate="(date: any) => (item.attrs && item.attrs.disabledDate && item.attrs.disabledDate(date)) || quickDisabledDate(date, item)"
            v-bind="{
              'range-separator': '至',
              'start-placeholder': !item.quick.withTime ? '开始日期' : '开始时间',
              'end-placeholder': !item.quick.withTime ? '结束日期' : '结束时间',
              format: !item.quick.withTime ? dateFormat : datetimeFormat,
              'value-format': !item.quick.withTime ? dateFormat : datetimeFormat,
              'popper-class': 'custom-content-view',
              ...item.attrs,
            }"
            :style="{ width: item.width }"
            style="margin-left: 10px"
          >
          </el-date-picker>
        </div>
        <!-- 插槽 -->
        <template v-else-if="item.type === 'slot'">
          <slot :name="item.slotName"></slot>
        </template>
      </el-form-item>
      <!-- 刷新按钮 -->
      <el-form-item v-if="refreshBtn.show" style="margin-right: 10px">
        <el-button class="refresh-btn" :icon="Refresh" @click="reset" />
      </el-form-item>
      <!-- 设置按钮 -->
      <el-form-item v-if="settingBtn.show" style="margin-right: 0px">
        <el-popover placement="right" :width="150" trigger="click">
          <template #reference>
            <el-button class="refresh-btn" :icon="Setting"> </el-button>
          </template>
          <div v-for="(item, key) in actualQueryItems">
            <el-checkbox v-show="!item.hidden" v-model="item.checked" :value="true" @change="queryItemsCheckedChange(key, item.checked)">{{
              item.label || item.attrs?.placeholder || ""
            }}</el-checkbox>
          </div>
        </el-popover>
      </el-form-item>
      <el-form-item
        v-if="searchBtn.show || showMore"
        id="t-query-btn-wrap"
        :style="{
          float: showMore || searchBtn.right ? 'right' : 'none',
          marginLeft: refreshBtn.show ? '20px' : '0px',
        }"
      >
        <template v-if="searchBtn.show">
          <my-button type="primary" @click="search">{{ searchBtn.searchName || "查询" }}</my-button>
          <my-button type="primary" plain @click="reset">{{ searchBtn.resetName || "清除" }}</my-button>
        </template>
        <el-link type="primary" :underline="false" v-if="showMore" @click="toggle" style="margin-left: 12px">
          {{ isExpand ? "收起" : "展开" }}
          <el-icon style="position: relative; top: 2px">
            <ArrowUp v-if="isExpand" />
            <ArrowDown v-else />
          </el-icon>
        </el-link>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts" name="TQuery">
import { ref, watch, computed, onMounted, onUnmounted, nextTick } from "vue";
import { keys, cloneDeep, debounce } from "lodash";
import { Refresh, Search, Setting } from "@element-plus/icons-vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const { $app } = useCtx();
const props = defineProps({
  queryItems: { type: Object, default: () => {} },
  searchBtn: {
    type: Object,
    default: () => {
      return {
        show: false, // 是否展示搜索按钮
        right: false, // 搜索按钮是否居右对齐
      };
    },
  },
  refreshBtn: {
    type: Object,
    default: () => {
      return {
        show: false,
      };
    },
  },
  settingBtn: {
    type: Object,
    default: () => {
      return {
        show: false,
      };
    },
  },
  supportFold: {
    type: Boolean,
    default: false,
  },
  name: { type: String, default: "default" },
});
const emits = defineEmits(["toggle", "search", "reset"]);

const dateFormat = ref<string>("YYYY-MM-DD");
const datetimeFormat = ref<string>("YYYY-MM-DD HH:mm:ss");

//查询项的key
const queryKey = computed(() => `${$app.$route.path}:query:${props.name}`);
//查询项的实际展示
const actualQueryItems = ref([]);
//动态查询项
// 备份一份，方便重置
const queryItemsCopy = ref({});
watch(
  () => props.queryItems,
  (newQueryItems) => {
    queryItemsCopy.value = cloneDeep(newQueryItems);
    const oldActualQueryItems = cloneDeep(actualQueryItems.value);
    const clonedItems = cloneDeep(newQueryItems);
    const storageKey = queryKey.value;
    const storedData = dataC.safeObject(localStorage.getItem(storageKey)) || {};
    const updatedStorage = { ...storedData };

    Object.entries(clonedItems).forEach(([key, item]) => {
      if (!(key in updatedStorage)) {
        updatedStorage[key] = { checked: true };
      }

      item.checked = updatedStorage[key].checked;
      if (item.type !== "slot") {
        item.modelValue = dataC.isEmpty(item.modelValue) ? oldActualQueryItems[key]?.modelValue : item.modelValue;
        item.defaultValue = item.defaultValue ?? item.modelValue;
      }
    });

    localStorage.setItem(storageKey, JSON.stringify(updatedStorage));

    actualQueryItems.value = clonedItems;
  },
  { immediate: true, deep: true }
);
const queryItemsCheckedChange = (key: String, checked: Boolean) => {
  //获取缓存内容
  const storage = dataC.safeObject(localStorage.getItem(queryKey.value)) || {};
  //记录查询项选中情况
  storage[key] = { checked };
  localStorage.setItem(queryKey.value, JSON.stringify(storage));
};

// 监听屏幕分辨率，计算查询项的总宽度，超出屏幕的隐藏
const limit = ref<number>(1000);
const resizeScreem = () => {
  limit.value = 1000; // 每次在计算itemW之前都要将limit设大一点，确保能够通过dom获取到当前item的宽度。如果limit以上一次计算的结果为准，很有可能导致部分item通过v-show隐藏了 因此对应的宽度也算不出来。这样宽度计算的就是有问题的
  const El: any = document.querySelector(".t-query");
  if (!El) return false;
  const queryW = El.offsetWidth; // 查询框的总宽度
  const btnW = 192; // 按钮的宽度
  const items = document.querySelectorAll(".t-query .query-item"); // 获取每一个查询项
  // items通过dom获取到之后，将limit重置为0 以计算实际可展示的limit
  let itemW = 0;
  nextTick(() => {
    limit.value = 0;
    items.forEach((item: any) => {
      itemW += item.offsetWidth;
      if (itemW < queryW - btnW) {
        // 容纳查询项的总宽度需要去掉按钮占用的宽度
        limit.value++;
      }
    });
  });
};

// 展开收起
const isExpand = ref<Boolean>(false);
const toggle = () => {
  isExpand.value = !isExpand.value;
  emits("toggle");
};

const getIndex = (key: string) => {
  const keys = Object.keys(props.queryItems);
  const index = keys.indexOf(key);
  return index;
};
// 总查询条件的个数
const queryCount = computed(() => {
  const keys = Object.keys(props.queryItems);
  return keys.length;
});
// 可展示的查询条件个数[当父组件传参不支持折叠展示所有查询，或者当前状态为展开时展示所有查询项，limitCount不限制，否则根据屏幕宽度进行计算]
const limitCount = computed(() => {
  return !props.supportFold || isExpand.value ? 1000 : limit.value;
});
// 是否展示“展开”
const showMore = computed(() => {
  return props.supportFold && (isExpand.value || queryCount.value > limitCount.value);
});

// 查询条件赋值
const getQuery = (obj: any) => {
  let query: any = {};
  for (let key in obj) {
    query[key] = obj[key].modelValue;
  }
  return query;
};
const toSearch = (arr: any) => {
  const query = getQuery(arr); // 获取查询条件的值
  emits("search", query); // 查询
};
const search = () => {
  toSearch(actualQueryItems.value);
};
const reset = () => {
  emits("reset", queryItemsCopy.value); // 清空查询条件
  const queryItems = actualQueryItems.value;

  Object.keys(queryItems).forEach((key) => {
    const item = queryItems[key];
    if (item) {
      item.modelValue = item.defaultValue;
    }
  });

  toSearch(queryItems);
};

// 是否立即搜索
const searchImmediatelyFun = () => {
  if (!props.searchBtn.show) {
    search();
  }
};

const quickDateChange = (val: any, item: any) => {
  item.modelValue = timeC.createRangeByDay(val, item.quick.withTime);
};

const quickDisabledDate = (date: any, item: any) => {
  try {
    // 以最大的天数计算禁用范围，如最大天数为7  那仅7天内的数据可选
    const options = item.quick.options;
    const maxDay = options[options.length - 1].value;
    const dateRange: Array<any> = timeC.createRangeByDay(maxDay);
    let startDate = new Date(dateRange[0]);
    const endDate = new Date(dateRange[1]);
    startDate.setDate(startDate.getDate() - 1);
    return date < startDate || date > endDate;
  } catch {
    return false;
  }
};

const debounceFun = debounce(resizeScreem, 200);

/* 生命周期 */
onMounted(() => {
  if (props.supportFold) {
    resizeScreem();
    window.addEventListener("resize", debounceFun);
  }
});
onUnmounted(() => {
  window.removeEventListener("resize", debounceFun);
});
defineExpose({
  search,
  reset,
  resizeScreem,
  searchImmediatelyFun,
});
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.t-query {
  // @include flexBetween();
  display: flex;
  justify-content: center;
  align-items: center;
  .right {
    text-align: right;
  }
  .expand-btn {
    margin-left: 12px;
  }
  .query-item {
    margin-right: 0 !important;
    padding-right: 12px;
    :deep(.el-radio-group) {
      height: 32px;
    }
  }
  #t-query-btn-wrap {
    margin-right: 0 !important;
  }
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>

<style lang="scss">
.t-query {
  .el-radio-group {
    .el-radio-button.is-active {
      .el-radio-button__inner {
        background-color: #fff;
        color: var(--el-color-primary);
      }
    }
  }
  .refresh-btn {
    padding: 8px;
  }
}
</style>
