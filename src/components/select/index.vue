<!-- 支持分页的逻辑待添加 -->
<template>
    <component
      ref="selectRef"
      :is="useVirtual ? 'el-select-v2' : 'el-select'"
      class="custom-select"
      v-model="selectValue"
      :options="useVirtual ? options : null"
      v-bind="{
        'clearable': true,
        'filterable': true,
        'teleported': false,
        ...$attrs
      }"
      remote-show-suffix
      @change="onChange"
      @blur="onBlur"
      @remove-tag="removeTag"
    >
      <template v-if="!useVirtual">
        <el-option
          v-for="item of options"
          :key="item[defaultProps.value]"
          :value="item[defaultProps.value]"
          :label="item[defaultProps.label]"
          :disabled="item.disabled"
        />
      </template>
    </component>
  </template>
  
  <script setup lang="ts">
  import { ref, watch } from 'vue'
  import { dataC } from 'turing-plugin'
  import { valueLabel } from '@/utils/interface'
  const emits = defineEmits(['update:modelValue', 'change', 'remove-tag'])
  const props = defineProps({
    modelValue: { default: '' },   // 父组件v-model绑定的值
    options: { type: Array as any, deafult () {return []} },
    valueInLabel: { type: Boolean, default: false }, // change时是否将绑定值的其他属性带上
    useVirtual: { type: Boolean, default: false }, // 是否开启虚拟列表
    // option默认的属性值；默认为value/label
    defaultProps: {
      type: Object as any,
      default: () => {
        return {
          value: 'value',
          label: 'label'
        }
      }
    }
  })
  
  const selectValue = ref<any>(props.modelValue || undefined)
  const selectRef = ref<any>(null)
  const onChange = (val: any) => {
    // 清空选项时触发remote-method重新请求数据
    if (!val && selectRef.value.remote) {
      selectRef.value.remoteMethod && selectRef.value.remoteMethod()
    }
    if (props.valueInLabel) {
      const arr = props.options as Array<valueLabel>
      let item = null
      const valueKey = props.defaultProps.value
      if (Array.isArray(val)) {
        item = arr.filter((v: any) => val.includes(v[valueKey]))
      } else {
        item = arr.find((v: any) => val === v[valueKey])
      }
      emits('change', item) // 选中对象而不是value  这样方便有些地方需要传value以外的参数
    } else {
      emits('change', val)
    }
  }
  
  const onBlur = () => {
    // blur时触发remote-method重新请求数据
    selectRef.value.remoteMethod && selectRef.value.remoteMethod()
  }
  
  const removeTag = (val: any) => {
    emits('remove-tag', val)
  }
  
  watch(
    () => props.modelValue,
    (nu) => {
      selectValue.value = dataC.isEmpty(nu) ? undefined : nu
    }
  )
  watch(
    () => selectValue.value,
    (nu) => {
      emits('update:modelValue', nu)
    },
    { deep: true }
  )
  </script>
  
  <style lang="scss" scoped>
  .custom_select__pagination {
    padding: 2px 10px;
  }
  </style>