<template>
  <div class="status-wrapper" :class="size" v-if="name">
    <span 
      class="status-dot"
      :class="type"
      :style="{color: textColor}"
      ref="statusDotRef">
      <span class="dot" :style="{background: dotColor}"></span>
      {{name}}
      <el-tooltip :content="reason" v-if="reason">
        <el-icon class="icon-info"><Warning /></el-icon>
      </el-tooltip>
    </span>
  </div>
  <div v-else>{{ NO_TEXT }}</div>
</template>

<script setup lang="ts">
import { NO_TEXT } from '@/utils/constants'
defineProps({
  type: {
    type: String,
    defalut: 'primary'
  },
  name: {
    type: String,
    default: ''
  },
  reason: {
    type: String,
    defalut: ''
  },
  size: {
    type: String,
    default: 'default'
  },
  dotColor: {
    type: String
  },
  textColor: {
    type: String
  }
})
</script>

<style lang="scss" scoped>
// 状态标签
.status-dot {
  // display: flex;这条属性会让表格的固定列存在丢失下边框的问题
  align-items: center;
  font-weight: normal;
  .dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 6px;
  }
  &.success {
    color: $success-color;
    .dot {
      background: $success-color;
    }
  }
  &.warning {
    color: $warning-color;
    .dot {
      background: $warning-color;
    }
  }
  &.primary {
    color: $primary-color;
    .dot {
      background: $primary-color;
    }
  }
  &.info {
    color: $info-color;
    .dot {
      background: $info-color;
    }
  }
  &.danger {
    color: $danger-color;
    .dot {
      background: $danger-color;
    }
  }
  &.exception {
    color: $danger-color;
    .dot {
      background: $danger-color;
    }
  }
}

.status-wrapper {
  display: inline-block;
  &.default {
    .dot {
      width: 6px;
      height: 6px;
      margin-right: 6px;
    }
  }
  &.large {
    .dot {
      width: 8px;
      height: 8px;
      margin-right: 8px;
    }
  }
  &.small {
    .dot {
      width: 4px;
      height: 4px;
    }
  }
}
.icon-info {
  margin-left: 5px;
  color: $text-color-secondary;
  cursor: pointer;
}
</style>