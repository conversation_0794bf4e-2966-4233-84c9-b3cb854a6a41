<template>
  <div class="status-wrapper" :class="{'show-cursor': showCursor, border}" @click="handleClick" v-if="name">
    <span 
      class="status-tag"
      :class="type">
      {{name}}
    </span>
    <el-tooltip :content="reason" v-if="reason">
      <el-icon class="icon-info"><Warning /></el-icon>
    </el-tooltip>
  </div>
  <div v-else>{{ NO_TEXT }}</div>
</template>

<script setup lang="ts">
import { NO_TEXT } from '@/utils/constants'
defineProps({
  type: {
    type: String,
    defalut: 'primary'
  },
  name: {
    type: String,
    default: ''
  },
  reason: {
    type: String,
    defalut: ''
  },
  showCursor: {
    type: Boolean,
    default: false
  },
  border: {
    type: Boolean,
    default: true
  }
})
const emits = defineEmits(['click'])
const handleClick = () => {
  emits('click')
}
</script>

<style lang="scss" scoped>
// 状态标签
.status-tag {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 500;
  height: 20px;
  line-height: 16px;
  background: #eceef3;
  color: $info-color;
  border: 1px solid $info-color;
  &.success {
    background: $success-bg;
    color: $success-color;
    border-color: var(--el-color-success-light-5);
  }
  &.warning {
    background: $warning-bg;
    color: $warning-color;
    border-color: var(--el-color-warning-light-5);
  }
  &.primary {
    background: $primary-bg;
    color: $primary-color;
    border-color: var(--el-color-primary-light-5);
  }
  &.info {
    background: $info-bg;
    color: $info-color;
    border-color: var(--el-color-info-light-5);
  }
  &.danger {
    background: $danger-bg;
    color: $danger-color;
    border-color: var(--el-color-danger-light-5);
  }
}
.status-wrapper {
  display: inline-block;
  .status-tag {
    border-width: 0;
  }
  &.border {
    .status-tag {
      border-width: 1px;
    }
  }
}
.icon-info {
  margin-left: 2px;
  color: $text-color-secondary;
  position: relative;
  top: 2px;
  cursor: pointer;
}
</style>