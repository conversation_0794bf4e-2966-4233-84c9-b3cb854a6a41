<!-- 
  管理端列表页公共组件：
  所有列表交互引入该组件，方便后期统一维护 
  包括：筛选栏、操作栏、列表栏、分页栏
  支持查询项较少、搜索栏和操作栏在一排展示；如果较多则分两排展示
-->
<template>
  <div class="table-page-wrapper height-adaptive" ref="table-page-wrapper">
    <!-- 头部 -->
    <div class="header-wrapper" ref="custom-header" v-if="hasSlot('header')">
      <slot name="header"></slot>
    </div>
    <!-- 查询+操作都写在这里 -->
    <div class="query-wrapper" ref="custom-query">
      <slot name="query"></slot>
    </div>
    <!-- 表格 -->
    <div class="table-wrapper" style="height: 100%">
      <el-table
        ref="myTableRef"
        class="custom-table"
        :data="table.tableData"
        :height="height || table.tableHeight"
        v-bind="{
          border: true,
          'show-overflow-tooltip': {
            placement: 'left',
          },
          ...$attrs,
        }"
        @selection-change="events.handleSelectionChange"
        @sort-change="events.handleSortChange"
        :row-key="rowKey"
        :row-class-name="rowClassName"
      >
        <!-- 多选框 -->
        <el-table-column type="selection" width="55" fixed="left" v-if="withSelection" :selectable="(row: any) => selectable(row)" />
        <!-- 序号 -->
        <el-table-column type="index" width="80" label="序号" fixed="left" v-if="withOrder">
          <template #header>
            <div class="flex">
              <span>序号&nbsp;</span>
              <el-popover placement="right" :width="150" trigger="click">
                <template #reference>
                  <el-button link :icon="Operation"> </el-button>
                </template>
                <div v-for="item in actualColumns">
                  <el-checkbox
                    v-if="item.prop != 'operation'"
                    v-model="item.checked"
                    :value="true"
                    @change="events.handleColumnChange(item.prop, item.checked)"
                    >{{ item.label }}</el-checkbox
                  >
                </div>
              </el-popover>
            </div>
          </template>
          <template #default="{ $index }">
            <span v-if="page">{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
            <span v-else>{{ $index + 1 }}</span>
          </template>
        </el-table-column>

        <template v-for="(item, index) of actualColumns">
          <el-table-column
            :key="index"
            resizable
            v-bind="{
              sortable: withSort == false || item.sortable == false || item.prop === 'operation' ? false : 'custom',
              ...item,
            }"
            v-if="item.checked"
          >
            <!-- 默认项 -->
            <template #default="scope" v-if="!item.custom">
              {{ getText(scope.row[item.prop]) }}
              <el-tag size="small" style="margin-left: 6px" v-if="item.withTag && item.tagProps && item.tagProps.show(scope.row)">
                {{ item.tagProps.label }}
              </el-tag>
            </template>
            <!-- 支持复制的项 -->
            <template #default="scope" v-if="!item.custom && item.withCopy">
              <div class="flex">
                <div class="btn-title">
                  <span>{{ getText(scope.row[item.prop]) }}</span>
                </div>
                <el-icon class="icon-copy" @click="copyText(scope.row[item.prop])">
                  <CopyDocument />
                </el-icon>
              </div>
            </template>
            <!-- 可点击的项 -->
            <template #default="scope" v-if="item.custom === 'link' && item.customRender">
              <text-button
                v-if="!isEmpty(scope.row[item.prop])"
                type="primary"
                @click="item.customRender.click(scope.row)"
                :disabled="item.customRender.disabled && item.customRender.disabled(scope.row)"
                :style="{ fontWeight: item.blod ? 550 : 400 }"
              >
                {{ scope.row[item.prop] }}
              </text-button>
              <span v-else>{{ NO_TEXT }}</span>
            </template>
            <!-- 带编辑按钮的项 -->
            <template #default="scope" v-if="item.custom === 'editButton' && item.customRender">
              <div class="flex">
                <div class="btn-title">
                  <span>{{ scope.row[item.prop] }}</span>
                </div>
                <el-button
                  link
                  type="primary"
                  @click="item.customRender.btnClick(scope.row)"
                  :disabled="item.customRender.btnDisabled && item.customRender.btnDisabled(scope.row)"
                >
                  <el-icon>
                    <Edit />
                  </el-icon>
                </el-button>
              </div>
            </template>
            <!-- 可点击且带编辑按钮的项 -->
            <template #default="scope" v-if="item.custom === 'editLink' && item.customRender">
              <div class="flex">
                <text-button
                  type="primary"
                  class="btn-title"
                  @click="item.customRender.linkClick(scope.row)"
                  :disabled="item.customRender.linkDisabled && item.customRender.linkDisabled(scope.row)"
                  :style="{ fontWeight: item.blod ? 550 : 400 }"
                >
                  <span>
                    {{ scope.row[item.prop] }}
                  </span>
                </text-button>
                <el-button
                  link
                  type="primary"
                  @click="item.customRender.btnClick(scope.row)"
                  :disabled="item.customRender.btnDisabled && item.customRender.btnDisabled(scope.row)"
                >
                  <el-icon>
                    <Edit />
                  </el-icon>
                </el-button>
              </div>
            </template>
            <!-- 状态项 -->
            <template #default="scope" v-if="['status', 'tagStatus'].includes(item.custom) && item.customRender">
              <template v-if="!isEmpty(scope.row[item.prop])" v-for="(obj, key) in item.customRender.options" :key="key">
                <component
                  :is="item.custom === 'status' ? 'status-dot' : 'status-tag'"
                  :type="obj.type"
                  :name="obj.name || obj.label"
                  :reason="item.customRender.reason ? item.customRender.reason(scope.row) : null"
                  v-if="
                    (!Array.isArray(item.customRender.options) && scope.row[item.prop] == key) ||
                    (Array.isArray(item.customRender.options) && scope.row[item.prop] == obj.value)
                  "
                />
              </template>
              <span v-else>{{ NO_TEXT }}</span>
            </template>
            <!-- 鼠标移入支持编辑文本 -->
            <template #default="scope" v-if="item.custom === 'editInput' && item.customRender">
              <div class="edit-input-text" v-if="!scope.row.isEditing">
                {{ getText(scope.row[item.prop]) }}
                <el-icon class="icon-copy" @click="copyText(scope.row[item.prop])" v-if="item.withCopy">
                  <CopyDocument />
                </el-icon>
                <el-icon class="icon" @click="events.editInputFocus(item, scope)">
                  <EditPen />
                </el-icon>
              </div>
              <el-input
                id="custom-edit-input"
                v-else
                v-model="scope.row[item.prop]"
                :placeholder="'请输入' + item.label"
                clearable
                v-bind="item.customRender.attrs"
                @blur="(e: any) => events.editInputBlur(e, item, scope)"
              >
              </el-input>
            </template>
            <!-- 开关 -->
            <template #default="scope" v-if="item.custom === 'switch' && item.customRender">
              <el-tooltip
                placement="top"
                effect="dark"
                v-if="item.customRender.disabled && item.customRender.disabled(scope.row) && item.customRender.disabledTips"
              >
                <template #content>{{ item.customRender.disabledTips }} </template>
                <el-switch
                  v-model="scope.row[item.prop]"
                  :disabled="true"
                  :before-change="item.customRender.beforeChange.bind({}, scope.row)"
                  v-bind="{
                    'active-value': 1,
                    'inactive-value': 0,
                    ...item.customRender.attrs,
                  }"
                >
                </el-switch>
              </el-tooltip>
              <el-switch
                v-else
                v-model="scope.row[item.prop]"
                :disabled="!testAuth() || (item.customRender.disabled && item.customRender.disabled(scope.row))"
                :before-change="item.customRender.beforeChange.bind({}, scope.row)"
                v-bind="{
                  'active-value': 1,
                  'inactive-value': 0,
                  ...item.customRender.attrs,
                }"
              >
              </el-switch>
            </template>
            <!-- 其他自定义组件 按需添加 -->
            <!-- 插槽 -->
            <template #default="scope" v-if="item.slotName">
              <slot :name="item.slotName" :row="scope.row"></slot>
            </template>
            <!-- 操作项 -->
            <template #default="scope" v-if="item.prop === 'operation' && !item?.slotName">
              <div class="flex">
                <!-- 在外面的操作按钮 -->
                <template v-for="(operation, i) of noCollapsedOperations">
                  <!-- 禁用的按钮需要给出禁用提示 -->
                  <my-button
                    link
                    :type="operation.btnType || 'primary'"
                    :key="'operation' + i"
                    v-if="isExit(scope.row, operation) && (!(operation.hidden && operation.hidden(scope.row)) || !operation.hidden)"
                    :disabledTips="getDisabledTips(scope.row, operation)"
                    :disabled="isDisabled(scope.row, operation)"
                    @click="events.handleOperation(scope.row, operation, scope.$index)"
                  >
                    {{ operation.label }}
                  </my-button>
                </template>
                <!-- 在更多里面的操作按钮 -->
                <el-dropdown v-if="collapsedOperations.length > 0" trigger="click">
                  <my-button link type="primary" style="margin-left: 12px">
                    更多<el-icon style="margin-left: 5px"><arrow-down /></el-icon>
                  </my-button>
                  <template #dropdown>
                    <template v-for="(operation, i) in collapsedOperations">
                      <el-dropdown-item
                        :key="'operation2' + i"
                        v-if="isExit(scope.row, operation)"
                        @click="events.handleOperation(scope.row, operation, scope.$index)"
                        :disabled="isDisabled(scope.row, operation)"
                      >
                        <my-button
                          link
                          :type="operation.btnType || 'primary'"
                          :disabled="isDisabled(scope.row, operation)"
                          :disabledTips="getDisabledTips(scope.row, operation)"
                        >
                          {{ operation.label }}
                        </my-button>
                      </el-dropdown-item>
                    </template>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </template>
        <template #empty>
          <my-empty :size="120" />
        </template>
      </el-table>
    </div>
    <!-- 分页 -->
    <div class="pagination-wrapper" ref="custom-pagination" v-if="withPagination">
      <my-pagination v-bind="page" @current-change="events.currentChange" @size-change="events.sizeChange" :pageLayout="pageLayout" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted, nextTick, watch, ref, computed } from "vue";
import { assign, cloneDeep, debounce } from "lodash";
import { textC, dataC } from "turing-plugin";
import { copyText, getText } from "@/utils/helpers";
import { NO_TEXT } from "@/utils/constants";
import useCtx from "@/hooks/useCtx";
import TextButton from "@/components/button/TextButton.vue";
import { CopyDocument, ArrowDownBold, Operation } from "@element-plus/icons-vue";
import { useSlots } from "vue";
import Sortable from "sortablejs";
import useStore from "@/store";
import { watchEffect } from "vue";
const { common } = useStore();
const slots = useSlots();
const { isEmpty } = dataC;
const { proxy, $app, $auth } = useCtx();
const testAuth = () => {
  if (!dataC.isEmpty(props.operationAuth) && !$auth.testAuth(props.operationAuth)) {
    return false;
  }
  return true;
};
const props = defineProps({
  withSelectable: { type: Boolean },
  height: { type: Number, default: 0 },
  name: { type: String, default: "default" },
  dragRow: { type: Boolean },
  rowKey: { type: String, default: "id" },
  // 查询栏相关
  query: {
    default() {
      return {};
    },
  },
  // 分页相关
  withPagination: { type: Boolean, default: true },
  withStorePagination: { type: Boolean, default: true },
  // 分页的布局
  pageLayout: {
    type: String,
    default() {
      return "total, prev, pager, next, sizes, jumper";
    },
  },
  defaultPageSizes: {
    type: Array,
    default() {
      return [20, 50, 100, 200];
    },
  },
  //排序相关
  withSort: { type: Boolean, default: true },
  defaultSort: {
    type: Object,
    default(val: any) {
      return val.withSort ? { prop: "createdDate", order: "desc" } : {};
    },
  },
  // 列表相关
  columns: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  operations: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  operationAuth: { type: String, default: "" },
  withSelection: { type: Boolean, default: false },
  withOrder: { type: Boolean, default: true },
  // 多选框是否禁用【根据行数据的某个字段去区分，如状态为失败的数据禁选】
  selectable: {
    type: Function,
    default() {
      return true;
    },
  },
  // 列表查询接口
  loadDataApi: { type: Function, default: (e: any) => e },
  loadImmediately: { type: Boolean, default: true }, // 是否立即查询，有些查询需要父组件自行去操作，可以传参false;
  // 处理请求参数
  transformQuery: { type: Function, default: (e: any) => e },
  // 加载完数据之后，对数据的转换逻辑
  transformListData: { type: Function, default: (e: any) => e },
});
const emits = defineEmits(["operation", "selection-change", "reload", "row-change"]);
const tableKey = computed(() => `${$app.$route.path}:table-page:${props.name}`);
/* 查询项变化 */
watch(
  () => props.query,
  () => {
    page.pageNum = 1;
    loadData();
  },
  { deep: true }
);
/* 查询项变化 */
watch(
  () => tableKey.value,
  () => {
    if (!props.withStorePagination || !props.loadImmediately) {
      return;
    }
    nextTick(() => {
      if (common.pageList.has(tableKey.value)) {
        page = { ...page, ...common.pageList.get(tableKey.value).page };
        sort = { ...sort, ...common.pageList.get(tableKey.value).sort };
        loadData();
      } else {
        page.pageNum = 1;
        common.storePage(tableKey.value, { page, sort }, props.withStorePagination);
      }
    });
  },
  { deep: true, immediate: true }
);
// 判断插槽是否存在
function hasSlot(name: string) {
  return !!slots[name];
}
/* 分页 */
let page = reactive({
  pageNum: 1,
  pageSize: props.defaultPageSizes[0] as number,
  pageSizes: props.defaultPageSizes as Array<number>,
  total: 0,
});

/* 排序 */
let sort = reactive({
  prop: !dataC.isEmpty(props.defaultSort) ? props.defaultSort.prop : "",
  order: !dataC.isEmpty(props.defaultSort) ? props.defaultSort.order : "",
});
/* 列的实际展示 */
const actualColumns = ref([]);
/* 表格 */
const table = reactive({
  tableData: [],
  tableHeight: 0,
});

/* 事件 */
const events = reactive({
  handleColumnChange: (key: String, checked: Boolean) => {
    //获取缓存内容
    const storage = dataC.safeObject(localStorage.getItem(tableKey.value)) || {};
    //记录查询项选中情况
    storage[key] = { checked };
    localStorage.setItem(tableKey.value, JSON.stringify(storage));
  },
  editInputBlur: (e: any, item: any, scope: any) => {
    const val = e.target.value;
    scope.row.isEditing = false;
    item.customRender.blur(val, scope.row);
  },
  editInputFocus: (item: any, scope: any) => {
    scope.row.isEditing = true;
    nextTick(() => {
      const El = document.getElementById("custom-edit-input");
      const textLength = scope.row[item.prop].length;
      textC.setSelectionRange(El, textLength);
    });
  },
  handleSelectionChange: (arr: any) => {
    emits("selection-change", arr);
  },
  handleSortChange: (data: any) => {
    sort.prop = data.prop;
    sort.order = data.order;
    loadData();
    common.storePage(tableKey.value, { page, sort }, props.withStorePagination);
  },
  handleOperation: (record: any, operation: any, index: number) => {
    emits(
      "operation",
      assign(
        {},
        { record },
        {
          type: operation.type,
          index,
        }
      )
    );
  },
  currentChange: (val: number) => {
    page.pageNum = val;
    loadData();
    common.storePage(tableKey.value, { page, sort }, props.withStorePagination);
  },
  sizeChange: (val: number) => {
    page.pageSize = val;
    loadData();
    common.storePage(tableKey.value, { page, sort }, props.withStorePagination);
  },
});
//这个之后要修改的，toDo
const rowClassName = ({ row }, rowIndex) => {
  const res = [];
  res.push(props.dragRow ? "draggable-row" : "no-drag");
  if (props.name == "idx-db-inst-table" || props.name == "idxDB") {
    if (dataC.isEmpty(row.clusterId)) {
      res.push("old-row");
    }
  }
  return res.join(" ");
};
// 操作项“更多”
const collapsedOperations = computed(() => {
  return props.operations.filter((x) => x.collapsed);
});
const noCollapsedOperations = computed(() => {
  return props.operations.filter((x) => !x.collapsed);
});

/* 操作列禁用和隐藏逻辑 */
const isDisabled = (record: any, operation: any) => {
  if (!testAuth() && operation.auth !== "all") return true;
  return typeof operation.disabled === "function" ? !!operation.disabled(record) : !!operation.disabled;
};
const isExit = (record: any, operation: any) => {
  return typeof operation.exist === "function" ? !!operation.exist(record) : true;
};
const getDisabledTips = (record: any, operation: any) => {
  if (!testAuth() && operation.auth !== "all") return "权限不足";
  return typeof operation.disabledTips === "function" ? operation.disabledTips(record) : operation.disabledTips;
};

/* 列表接口调用（根据分页和query加载列表数据） */
const loadData = () => {
  const data = {
    page: page.pageNum,
    size: page.pageSize,
    sort: !dataC.isEmpty(sort.order) ? `${sort.prop.replace("Render", "")},${sort.order.replace("ending", "")}` : "",
  };
  const pageData = props.withPagination ? data : {};
  const params = props.transformQuery(assign({}, pageData, props.query));
  return props
    .loadDataApi(params)
    .then((res: any) => {
      if (props.withPagination) {
        table.tableData = props.transformListData(res.content, res.totalElements);
        page.total = res.totalElements;
      } else {
        table.tableData = props.transformListData(res.data);
      }
      nextTick(() => {
        mediaHeight();
      });
    })
    .catch(() => {
      nextTick(() => {
        mediaHeight();
      });
    });
};

// 页面加载完成，是否立即调用查询接口，有些查询需要父组件自行去操作，可以传参false
if (props.loadImmediately && !common.pageList.has(tableKey.value)) {
  loadData();
}

// 计算表格高度
const mediaHeight = () => {
  const contextHeight = proxy.$refs["table-page-wrapper"]?.offsetHeight || 0;
  const queryHeight = proxy.$refs["custom-query"]?.offsetHeight || 0;
  const headerHeight = proxy.$refs["custom-header"]?.offsetHeight || 0;
  const paginationHeight = proxy.$refs["custom-pagination"]?.offsetHeight || 0;
  table.tableHeight = contextHeight - (queryHeight + paginationHeight + headerHeight + 13);
};

//获取当前分页参数
const getPage = () => {
  return page;
};
//获取当前排序参数
const getSort = () => {
  return sort;
};
//获取表格数据
const getTableData = () => {
  return table.tableData;
};
//设置表格数据
const setTableData = (list: array<any>) => {
  table.tableData = props.transformListData(list);
};
//设置数据总量
const setTotal = (total: number) => {
  page.total = total;
};
const initDropTable = () => {
  Sortable.create(document.querySelector(".el-table__body-wrapper tbody") as any, {
    animation: 500,
    sort: true,
    filter: ".no-drag",
    //拖拽结束后触发
    onEnd: ({ newIndex, oldIndex }: any) => {
      const movedItem = table.tableData.splice(oldIndex, 1)[0];
      table.tableData.splice(newIndex, 0, movedItem);
      emits("row-change", table.tableData);
    },
  });
};

/* 生命周期 */
const debounceFun = debounce(mediaHeight, 200);
onMounted(() => {
  const debounceFun = debounce(mediaHeight, 200);
  window.addEventListener("resize", debounceFun);
}),
  onUnmounted(() => {
    window.removeEventListener("resize", debounceFun);
  });

/* 抛出所有的原生方法 */
const myTableRef = ref<any>(null);
const defaultExposes = [
  "clearSelection",
  "getSelectionRows",
  "toggleRowSelection",
  "toggleAllSelection",
  "toggleRowExpansion",
  "setCurrentRow",
  "clearSort",
  "clearFilter",
  "doLayout",
  "sort",
  "scrollTo",
  "setScrollTop",
  "setScrollLeft",
];
const setExposes = () => {
  const exposes: any = {};
  defaultExposes.forEach((key: string) => {
    exposes[key] = (...args: any) => {
      myTableRef.value[key](...args);
    };
  });
  return exposes;
};

defineExpose({
  ...setExposes(),
  loadData,
  mediaHeight,
  getPage,
  getSort,
  getTableData,
  setTableData,
  setTotal,
});
// 动态列
watch(
  () => props.columns,
  (val: any[]) => {
    //获取查询项的克隆对象
    const obj = cloneDeep(val);
    //获取缓存内容
    const storage = dataC.safeObject(localStorage.getItem(tableKey.value)) || {};
    //如果有新加的列，则设置为选中
    obj.forEach((item) => {
      if (!(item.prop in storage)) {
        storage[item.prop] = { checked: true };
      }
    });
    localStorage.setItem(tableKey.value, JSON.stringify(storage));
    //根据缓存内容设置选中情况
    obj.forEach((item) => {
      item.checked = storage[item.prop].checked;
    });
    actualColumns.value = obj;
  },
  { immediate: true, deep: true }
);
// 动态拖拉行
watch(
  () => props.dragRow,
  (val: Boolean) => {
    if (val) {
      nextTick(() => {
        initDropTable();
      });
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.table-page-wrapper {
  height: 100%;
  > div {
    @include area-padding;
    background: #fff;
  }
  .header-wrapper {
    padding-bottom: 0px;
  }
  .query-wrapper {
    padding-bottom: 0px;
  }
  .table-wrapper {
    padding-top: 0;
  }
  .pagination-wrapper {
    position: relative;
    @include flex();
    justify-content: flex-end;
    padding-top: 0;
  }
}
.icon-copy {
  color: $primary-color;
  margin-left: 5px;
  cursor: pointer;
}
.edit-input-text {
  cursor: pointer;
  > .icon {
    display: none;
    margin-left: 4px;
    color: $primary-color;
  }
  &:hover {
    > .icon {
      display: inline-block;
    }
  }
}
::v-deep .draggable-row {
  cursor: pointer;
}
.flex {
  width: 100%;
  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .btn-title.el-button + .el-button {
    margin-left: 0;
  }
}
::v-deep .old-row {
  --el-table-tr-bg-color: var(--el-color-info-light-9);
}
</style>
