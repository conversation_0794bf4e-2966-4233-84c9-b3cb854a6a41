<template>
  <el-table-column
    v-if="item.custom !== 'columns'"
    :key="index"
    resizable
    v-bind="{
      sortable: withSort == false || item.sortable == false || item.prop === 'operation' ? false : 'custom',
      ...item,
    }"
  >
    <!-- header插槽 -->
    <template #header>
      <slot :name="item.labelSlotName"></slot>
    </template>
    <!-- 默认项 -->
    <template #default="scope" v-if="!item.custom">
      {{ item.type=='number'?util.formatNumber(scope.row[item.prop]):getText(scope.row[item.prop]) }}
      <!-- 支持复制的项 -->
      <el-icon class="icon-copy" v-if="item.withCopy" @click="copyText(scope.row[item.prop])">
        <CopyDocument />
      </el-icon>
      <el-tag size="small" style="margin-left: 6px" v-if="item.withTag && item.tagProps && item.tagProps.show(scope.row)">
        {{ item.tagProps.label }}
      </el-tag>
    </template>
    <!-- 可点击的项 -->
    <template #default="scope" v-if="item.custom === 'link' && item.customRender">
      <!-- 支持复制的项 -->
      <el-icon class="icon-copy" v-if="item.withCopy" @click="copyText(scope.row[item.prop])">
        <CopyDocument />
      </el-icon>
      <text-button
        v-if="!isEmpty(scope.row[item.prop])"
        type="primary"
        @click="item.customRender.click(scope.row)"
        :disabled="item.customRender.disabled && item.customRender.disabled(scope.row)"
        :style="{ fontWeight: item.blod ? 550 : 400 }"
      >
        {{ scope.row[item.prop] }}
      </text-button>
      <span v-else>{{ NO_TEXT }}</span>
    </template>
    <!-- 状态项 -->
    <template #default="scope" v-if="['status', 'tagStatus'].includes(item.custom) && item.customRender">
      <template v-if="!isEmpty(scope.row[item.prop])" v-for="(obj, key) in item.customRender.options" :key="key">
        <component
          :is="item.custom === 'status' ? 'status-dot' : 'status-tag'"
          :type="obj.type"
          :name="obj.name || obj.label"
          :reason="item.customRender.reason ? item.customRender.reason(scope.row) : null"
          v-if="
            (!Array.isArray(item.customRender.options) && scope.row[item.prop] == key) ||
            (Array.isArray(item.customRender.options) && scope.row[item.prop] == obj.value)
          "
        />
      </template>
      <span v-else>{{ NO_TEXT }}</span>
    </template>
    <!-- 鼠标移入支持编辑文本 -->
    <template #default="scope" v-if="item.custom === 'editInput' && item.customRender">
      <div class="edit-input-text" v-if="!scope.row.isEditing">
        {{ getText(scope.row[item.prop]) }}
        <el-icon class="icon" @click="events.editInputFocus(item, scope)">
          <EditPen />
        </el-icon>
      </div>
      <el-input
        id="custom-edit-input"
        v-else
        v-model="scope.row[item.prop]"
        :placeholder="'请输入' + item.label"
        clearable
        v-bind="item.customRender.attrs"
        @blur="(e: any) => events.editInputBlur(e, item, scope)"
      >
      </el-input>
    </template>
    <!-- 开关 -->
    <template #default="scope" v-if="item.custom === 'switch' && item.customRender">
      <el-tooltip placement="top" effect="dark" v-if="item.customRender.disabled && item.customRender.disabled(scope.row) && item.customRender.disabledTips">
        <template #content>{{ item.customRender.disabledTips }} </template>
        <el-switch
          v-model="scope.row[item.prop]"
          :disabled="item.customRender.disabled && item.customRender.disabled(scope.row)"
          :before-change="item.customRender.beforeChange.bind({}, scope.row)"
          v-bind="{
            'active-value': 1,
            'inactive-value': 0,
            ...item.customRender.attrs,
          }"
        >
        </el-switch>
      </el-tooltip>
      <el-switch
        v-else
        v-model="scope.row[item.prop]"
        :disabled="item.customRender.disabled && item.customRender.disabled(scope.row)"
        :before-change="item.customRender.beforeChange.bind({}, scope.row)"
        v-bind="{
          'active-value': 1,
          'inactive-value': 0,
          ...item.customRender.attrs,
        }"
      >
      </el-switch>
    </template>
    <!-- 其他自定义组件 按需添加 -->
    <!-- 插槽 -->
    <template #default="scope" v-if="item.slotName">
      <slot :name="item.slotName" :row="scope.row"></slot>
    </template>
    <!-- 操作项 -->
    <template #default="scope" v-if="item.prop === 'operation' && !item?.slotName">
      <div class="flex">
        <!-- 在外面的操作按钮 -->
        <template v-for="(operation, i) of noCollapsedOperations">
          <!-- 禁用的按钮需要给出禁用提示 -->
          <el-tooltip placement="top" effect="dark" v-if="isDisabled(scope.row, operation)">
            <template #content>{{ getDisabledTips(scope.row, operation) }} </template>
            <my-button
              link
              :type="operation.btnType || 'primary'"
              :key="'operation' + i"
              v-if="isExit(scope.row, operation)"
              :disabled="isDisabled(scope.row, operation)"
              @click="events.handleOperation(scope.row, operation, scope.$index)"
            >
              {{ operation.label }}
            </my-button>
          </el-tooltip>
          <my-button
            v-else
            link
            :type="operation.btnType || 'primary'"
            :key="'operation' + i"
            v-if="isExit(scope.row, operation)"
            :disabled="isDisabled(scope.row, operation)"
            @click="events.handleOperation(scope.row, operation, scope.$index)"
          >
            {{ operation.label }}
          </my-button>
        </template>
        <!-- 在更多里面的操作按钮 -->
        <el-dropdown v-if="collapsedOperations.length > 0">
          <my-button link type="primary" style="margin-left: 12px">
            更多<el-icon style="margin-left: 5px"><arrow-down /></el-icon>
          </my-button>
          <template #dropdown>
            <template v-for="(operation, i) in collapsedOperations">
              <el-dropdown-item :key="'operation2' + i" v-if="isExit(scope.row, operation)">
                <el-tooltip placement="top" effect="dark" v-if="isDisabled(scope.row, operation) && operation.disabledTips">
                  <template #content>{{ getDisabledTips(scope.row, operation) }} </template>
                  <my-button
                    link
                    :type="operation.btnType || 'primary'"
                    :disabled="isDisabled(scope.row, operation)"
                    @click="events.handleOperation(scope.row, operation, scope.$index)"
                  >
                    {{ operation.label }}
                  </my-button>
                </el-tooltip>
                <my-button
                  v-else
                  link
                  :type="operation.btnType || 'primary'"
                  :disabled="isDisabled(scope.row, operation)"
                  @click="events.handleOperation(scope.row, operation, scope.$index)"
                >
                  {{ operation.label }}
                </my-button>
              </el-dropdown-item>
            </template>
          </template>
        </el-dropdown>
      </div>
    </template>
  </el-table-column>
  <el-table-column v-else :label="item.label">
    <template #default="scope">
      <CustomColumn v-for="child in item.columns" :item="child" :operations="operations" :withSort="withSort" :defaultSort="defaultSort" :operationAuth="operationAuth" :events="events">
        <template v-slot:[child.slotName]="scope">
          <slot :name="child.slotName" :row="scope.row"></slot>
        </template>
      </CustomColumn>
    </template>
  </el-table-column>
</template>
<script setup lang="ts">
import { reactive, onMounted, onUnmounted, nextTick, watch, ref, computed } from "vue";
import { assign, cloneDeep, debounce } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import { NO_TEXT } from "@/utils/constants";
import { textC, dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import TextButton from "@/components/button/TextButton.vue";
import { CopyDocument } from "@element-plus/icons-vue";
import CustomColumn from "@/components/table/CustomColumn.vue";
import { useSlots } from "vue";
import * as util from "@/utils/common";
const slots = useSlots();
const { isEmpty } = dataC;
const { proxy, $auth } = useCtx();
const props = defineProps({
  item: {
    type: Object,
    default() {
      return { columns: [] };
    },
  },
  //排序相关
  withSort: { type: Boolean, default: true },
  defaultSort: {
    type: Object,
    default(val: any) {
      return val.withSort ? { prop: "createdDate", order: "desc" } : {};
    },
  },
  operations: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  events: {
    type: Object,
    default() {
      return {};
    },
  },
  operationAuth: { type: String, default: "" },
});
// 判断插槽是否存在
function hasSlot(name: string) {
  return !!slots[name];
}
let arr = [
  { label: "url被去重", prop: "url" },
  { label: "url被站点规则筛除", prop: "url" },
  { label: "url未爬取", prop: "url" },
  { label: "召回", prop: "url" },
  { label: "粗排", prop: "url" },
  { label: "精排", prop: "url" },
  { label: "重排", prop: "url" },
  { label: "后处理", prop: "url" },
];
/* 排序 */
const sort = reactive({
  prop: !dataC.isEmpty(props.defaultSort) ? props.defaultSort.prop : "",
  order: !dataC.isEmpty(props.defaultSort) ? props.defaultSort.order : "",
});
const emits = defineEmits(["operation", "selection-change"]);
// 操作项“更多”
const collapsedOperations = computed(() => {
  return props.operations.filter((x) => x.collapsed);
});
const noCollapsedOperations = computed(() => {
  return props.operations.filter((x) => !x.collapsed);
});

/* 操作列禁用和隐藏逻辑 */
const isDisabled = (record: any, operation: any) => {
  if (!dataC.isEmpty(props.operationAuth) && !$auth.testAuth(props.operationAuth)) {
    return true;
  }
  return typeof operation.disabled === "function" ? !!operation.disabled(record) : !!operation.disabled;
};
const isExit = (record: any, operation: any) => {
  return typeof operation.exist === "function" ? !!operation.exist(record) : true;
};
const getDisabledTips = (record: any, operation: any) => {
  if (!dataC.isEmpty(props.operationAuth) && !$auth.testAuth(props.operationAuth)) {
    return "权限不足";
  }
  return typeof operation.disabledTips === "function" ? operation.disabledTips(record) : operation.disabledTips;
};

/* 抛出所有的原生方法 */
const myTableRef = ref<any>(null);
defineExpose({});
</script>
