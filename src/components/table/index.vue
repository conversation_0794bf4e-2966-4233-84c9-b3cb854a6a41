<!-- 单纯的表格组件 -->
<template>
  <div class="table-page-wrapper height-adaptive" ref="table-page-wrapper">
    <!-- 头部 -->
    <div class="header-wrapper" ref="custom-header" v-if="hasSlot('header')">
      <slot name="header"></slot>
    </div>
    <!-- 查询+操作都写在这里 -->
    <div class="query-wrapper" ref="custom-query">
      <slot name="query"></slot>
    </div>
    <!-- 表格 -->
    <div class="table-wrapper" style="height: 100%">
      <el-table
        ref="myTableRef"
        class="custom-table"
        :data="table.tableData"
        :height="table.tableHeight"
        v-bind="{
          border: true,
          'show-overflow-tooltip': {
            placement: 'left',
          },
          ...$attrs,
        }"
        @selection-change="events.handleSelectionChange"
        @sort-change="events.handleSortChange"
        :row-key="rowKey"
        :row-class-name="rowClassName"
      >
        <!-- 多选框 -->
        <el-table-column type="selection" width="55" fixed="left" v-if="withSelection" :selectable="(row: any) => selectable(row)" />
        <!-- 序号 -->
        <el-table-column type="index" width="70" label="序号" fixed="left" v-if="withOrder">
          <template #default="{ $index }">
            <span v-if="page">{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
            <span v-else>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <CustomColumn :item="column"  v-for="column in columns" :operations="operations" :withSort="withSort" :defaultSort="defaultSort" :operationAuth="operationAuth" :events="events" >
          <template v-slot:[column.labelSlotName]>
            <slot :name="column.labelSlotName"></slot>
          </template>
          <template v-slot:[column.slotName]="scope">
            <slot :name="column.slotName" :row="scope.row"></slot>
          </template>
        </CustomColumn>
        <template #empty>
          <my-empty :size="120" />
        </template>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" ref="custom-pagination" v-if="withPagination">
      <my-pagination v-bind="page" @current-change="events.currentChange" @size-change="events.sizeChange" :pageLayout="pageLayout" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted, nextTick, watch, ref, computed } from "vue";
import { assign, cloneDeep, debounce } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import CustomColumn from "./CustomColumn.vue";
import { NO_TEXT } from "@/utils/constants";
import { textC, dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import TextButton from "@/components/button/TextButton.vue";
import { CopyDocument } from "@element-plus/icons-vue";
import { useSlots } from "vue";
const slots = useSlots();
const { isEmpty } = dataC;
const { proxy, $auth } = useCtx();
const props = defineProps({
  dragRow: { type: Boolean },
  rowKey: { type: String, default: "id" },
  // 查询栏相关
  query: {
    default() {
      return {};
    },
  },
  // 分页相关
  withPagination: { type: Boolean, default: true },
  // 分页的布局
  pageLayout: {
    type: String,
    default() {
      return "total, prev, pager, next, sizes, jumper";
    },
  },
  defaultPageSizes: {
    type: Array,
    default() {
      return [20, 30, 40, 50];
    },
  },
  //排序相关
  withSort: { type: Boolean, default: true },
  defaultSort: {
    type: Object,
    default(val: any) {
      return val.withSort ? { prop: "createdDate", order: "desc" } : {};
    },
  },
  // 列表相关
  columns: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  operationAuth: { type: String, default: "" },
  operations: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  withSelection: { type: Boolean, default: false },
  withOrder: { type: Boolean, default: true },
  // 多选框是否禁用【根据行数据的某个字段去区分，如状态为失败的数据禁选】
  selectable: {
    type: Function,
    default() {
      return true;
    },
  },
  // 列表查询接口
  tableData: {
    type: Array,
    default() {
      return [];
    },
  },
  loadImmediately: { type: Boolean, default: false }, // 是否立即查询，有些查询需要父组件自行去操作，可以传参false;
  // 处理请求参数
  transformQuery: { type: Function, default: (e: any) => e },
  // 加载完数据之后，对数据的转换逻辑
  transformListData: { type: Function, default: (e: any) => e },
});
/* 分页 */
const page = reactive({
  pageNum: 1,
  pageSize: props.defaultPageSizes[0] as number,
  pageSizes: props.defaultPageSizes as Array<number>,
  total: 0,
});

// 判断插槽是否存在
function hasSlot(name: string) {
  return !!slots[name];
}
const rowClassName = ({ row }, rowIndex) => {
  let className = props.dragRow ? "draggable-row" : "no-drag";
  // if(row.errorMsg){
  //   className +=' warning-row'
  // }
  return className;
};
/* 排序 */
const sort = reactive({
  prop: !dataC.isEmpty(props.defaultSort) ? props.defaultSort.prop : "",
  order: !dataC.isEmpty(props.defaultSort) ? props.defaultSort.order : "",
});
const emits = defineEmits(["operation", "selection-change"]);
/* 表格 */
const table = reactive({
  columns: props.columns,
  tableData: [],
  tableHeight: 0,
});
// 操作项“更多”
const collapsedOperations = computed(() => {
  return props.operations.filter((x) => x.collapsed);
});
const noCollapsedOperations = computed(() => {
  return props.operations.filter((x) => !x.collapsed);
});

/* 操作列禁用和隐藏逻辑 */
const isDisabled = (record: any, operation: any) => {
  if (!dataC.isEmpty(props.operationAuth) && !$auth.testAuth(props.operationAuth)) {
    return true;
  }
  return typeof operation.disabled === "function" ? !!operation.disabled(record) : !!operation.disabled;
};
const isExit = (record: any, operation: any) => {
  return typeof operation.exist === "function" ? !!operation.exist(record) : true;
};
const getDisabledTips = (record: any, operation: any) => {
  if (!dataC.isEmpty(props.operationAuth) && !$auth.testAuth(props.operationAuth)) {
    return "权限不足";
  }
  return typeof operation.disabledTips === "function" ? operation.disabledTips(record) : operation.disabledTips;
};
/* 列表接口调用（根据分页和query加载列表数据） */
const loadData = () => {
  let result: any[] = [];
  const totalElements = props.tableData.length;
  if (props.withPagination) {
    // 前端自己进行分页以及查询
    result = cloneDeep(props.transformListData(props.tableData, totalElements) || [])
      .sort((a: any, b: any) => {
        let sortNumber = a[sort.prop] > b[sort.prop] ? 1 : -1;
        return sort.order === "ascending" ? sortNumber : -sortNumber;
      })
      .slice((page.pageNum - 1) * page.pageSize, page.pageNum * page.pageSize);
  } else {
    result = props.transformListData(props.tableData).sort((a: any, b: any) => {
        let sortNumber = a[sort.prop] > b[sort.prop] ? 1 : -1;
        return sort.order === "ascending" ? sortNumber : -sortNumber;
      });
  }
  table.tableData = result;
  page.total = totalElements;

  mediaHeight();
};

// 计算表格高度
const mediaHeight = () => {
  const contextHeight = proxy.$refs["table-page-wrapper"]?.offsetHeight || 0;
  const queryHeight = proxy.$refs["custom-query"]?.offsetHeight || 0;
  const headerHeight = proxy.$refs["custom-header"]?.offsetHeight || 0;
  const paginationHeight = proxy.$refs["custom-pagination"]?.offsetHeight || 0;
  table.tableHeight = contextHeight - (queryHeight + paginationHeight + headerHeight + 13);
};
if (props.loadImmediately) {
  loadData();
}
/* 生命周期 */
const debounceFun = debounce(mediaHeight, 200);
onMounted(() => {
  const debounceFun = debounce(mediaHeight, 200);
  window.addEventListener("resize", debounceFun);
}),
  onUnmounted(() => {
    window.removeEventListener("resize", debounceFun);
  });
// 页面加载完成，是否立即调用查询接口，有些查询需要父组件自行去操作，可以传参false
/* 事件 */
const events = reactive({
  editInputBlur: (e: any, item: any, scope: any) => {
    const val = e.target.value;
    scope.row.isEditing = false;
    item.customRender.blur(val, scope.row);
  },
  editInputFocus: (item: any, scope: any) => {
    scope.row.isEditing = true;
    nextTick(() => {
      const El = document.getElementById("custom-edit-input");
      const textLength = scope.row[item.prop].length;
      textC.setSelectionRange(El, textLength);
    });
  },
  handleSelectionChange: (arr: any) => {
    emits("selection-change", arr);
  },
  handleSortChange: (data: any) => {
    sort.prop = data.prop;
    sort.order = data.order;
    loadData();
  },

  handleOperation: (record: any, operation: any, index: number) => {
    emits(
      "operation",
      assign(
        {},
        { record },
        {
          type: operation.type,
          index,
        }
      )
    );
  },
  currentChange: (val: number) => {
    page.pageNum = val;
    loadData();
  },
  sizeChange: (val: number) => {
    page.pageSize = val;
    loadData();
  },
});
/* 抛出所有的原生方法 */
const myTableRef = ref<any>(null);
const defaultExposes = [
  "clearSelection",
  "getSelectionRows",
  "toggleRowSelection",
  "toggleAllSelection",
  "toggleRowExpansion",
  "setCurrentRow",
  "clearSort",
  "clearFilter",
  "doLayout",
  "sort",
  "scrollTo",
  "setScrollTop",
  "setScrollLeft",
];
const setExposes = () => {
  const exposes: any = {};
  defaultExposes.forEach((key: string) => {
    exposes[key] = (...args: any) => {
      myTableRef.value[key](...args);
    };
  });
  return exposes;
};
defineExpose({
  loadData,
  mediaHeight,
  ...setExposes(),
});
// 动态列
watch(
  () => props.columns,
  (val: any[]) => {
    table.columns = val;
  },
  { immediate: true, deep: true }
);
// 动态列
watch(
  () => props.tableData,
  (val: any[]) => {
    loadData()
  },
  { deep: true,immediate:true }
);

</script>

<style lang="scss" scoped>
.table-page-wrapper {
  height: 100%;
  > div {
    @include area-padding;
    background: #fff;
  }
  .header-wrapper {
    padding-bottom: 0px;
  }
  .query-wrapper {
    padding-bottom: 0px;
  }
  .table-wrapper {
    padding-top: 0;
  }
  .pagination-wrapper {
    position: relative;
    @include flex();
    justify-content: flex-end;
    padding-top: 0;
  }
}
::v-deep{
  .icon-copy {
  color: $primary-color;
  margin-left: 5px;
  cursor: pointer;
}
.edit-input-text {
  cursor: pointer;
  > .icon {
    display: none;
    margin-left: 4px;
    color: $primary-color;
  }
  &:hover {
    > .icon {
      display: inline-block;
    }
  }
}
}
::v-deep .draggable-row {
  cursor: pointer;
}
::v-deep .warning-row {
  background-color: var(--el-color-warning-light-9) !important; /* 警告颜色 */
  color: #000; /* 文字颜色 */
}
</style>
