<template>
    <el-tooltip
       :effect="effect"
       :placement="placement"
       v-bind="$attrs">
       <template #content>
         <div class="tooltip-content" :style="{maxWidth}">
           <slot name="content" v-if="slots.content"></slot>
           <p v-else v-html="content"></p>
         </div>
       </template>
       <div class="tooltip-cursor">
         <slot v-if="slots.default"></slot>
         <el-icon
           v-else 
           style="position: relative; top: 1.5px;color: #81878C;"
           :style="{
             marginLeft: direction === 'right' ? '3px' : '0px',
             marginRight: direction === 'left' ? '3px' : '0px',
           }">
           <Warning />
         </el-icon>
       </div>
     </el-tooltip>
 </template>
 
 <script setup lang="ts">
 import { useSlots } from 'vue';
 defineProps({
   effect: {type: String, default: 'dark'},
   placement: {type: String as any, default: 'top'},
   content: {type: String},
   direction: {type: String, default: 'left'},
   maxWidth: { type: String, default: '400px' }
 })
 const slots = useSlots()
 </script>
 
 <style lang="scss" scoped>
 .tooltip-cursor {
   cursor: pointer;
   display: inline-block;
 }
 </style>