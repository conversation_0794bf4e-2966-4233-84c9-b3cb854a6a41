<template>
  <el-upload
    ref="uploadRef"
    class="custom-upload"
    :class="{'is-drag-style': dragStyle}"
    :drag="drag"
    :accept="accept"
    :multiple="multiple"
    :http-request="httpRequest"
    :on-remove="onRemove"
  >
    <!-- 自定义上传交互 -->
    <slot v-if="!dragStyle"></slot>
    <template #tip v-if="!dragStyle">
      <el-link 
        class="down-temp" 
        type="primary" 
        :underline="false" 
        @click="downloadTemplate" 
        v-if="showDownload" 
        style="margin-left: 32px;"
        :style="{left: downLeft}">
        <svg-icon name="icon-word" width="16px" style="margin-right: 8px;"/>
        {{ downText }}
      </el-link>
      <div class="el-upload__tip">
        <slot name="tips" v-if="slots.tips"></slot>
        <p v-else>{{ uploadTip || `仅支持上传 ${accept} 文件，且不超过${maxSize}` }}</p>
      </div>
    </template>
    <!-- 拖拽上传界面 -->
    <template v-if="dragStyle">
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">拖拽文件到这 或者 <em>点击上传</em></div>
    </template>
    <template #tip v-if="dragStyle">
      <div class="flexBetween" style="align-items: flex-start;">
        <div class="el-upload__tip">
          <slot name="tips" v-if="slots.tips"></slot>
          <p v-else>{{ uploadTip || `仅支持上传 ${accept} 文件，且不超过${maxSize}` }}</p>
        </div>
        <el-link type="primary" :underline="false" @click="downloadTemplate" v-if="showDownload" style="margin-top: 10px;width: 120px">
          {{ downText }}
        </el-link>
      </div>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import { ref, nextTick, useSlots } from 'vue'
import { fileC } from 'turing-plugin'
import useCtx from "@/hooks/useCtx";
const slots = useSlots()
const { $app } = useCtx();
const emits = defineEmits(['change', 'update:modelValue', 'download'])
const props = defineProps({
  modelValue: {},
  drag: { type: Boolean, default: false }, // 是否支持拖拽
  dragStyle: { type: Boolean, default: false }, // 是否为拖拽风格的样式
  multiple: { type: Boolean, default: false },
  accept: { type: String },
  maxSize: { type: String },
  uploadTip: { type: String },
  showDownload: { type: Boolean, default: false },
  downLeft: { type: String, default: '80px' },
  downText: { type: String, default: '模版下载' }
})


const getLimitSize = () => {
  const maxSize: any = props.maxSize;
  const unit = maxSize.substr(maxSize.length - 1, 1);
  const max = Number(maxSize.substr(0, maxSize.length - 1));
  //将单位转换为bit
  let limitSize = 0;
  if (["G", "g"].indexOf(unit) > -1) {
    limitSize = max * 1024 * 1024 * 1024;
  } else if (["M", "m"].indexOf(unit) > -1) {
    limitSize = max * 1024 * 1024;
  } else if (["K", "k"].indexOf(unit) > -1) {
    limitSize = max * 1024;
  }
  return limitSize;
};
// 自定义实现文件上传
const fileList = ref<any>([])
const uploadRef = ref<any>(null)
const httpRequest = (option: any) => {
  const file: any = option.file;
  // 文件类型限制
  if (props.accept) {
    const acceptArr = props.accept.split(","); //支持的文件类型
    const fileSuffix = fileC.getFileSuffix(file.name); //当前文件后缀
    if (acceptArr.indexOf(`.${fileSuffix}`) === -1) {
      clearFiles();
      $app.$message.error("文件类型仅支持" + props.accept);
      return false
    }
  }
  // 文件大小限制
  if (props.maxSize && file.size > getLimitSize()) {
    clearFiles();
    $app.$message.error("文件最大支持" + props.maxSize);
    return false
  }
  nextTick(() => {
    //不支持多选，只取最后一次添加的文件
    if (!props.multiple) {
      const last =
        document.querySelectorAll(".el-upload-list__item").length - 1;
      const items = document.querySelectorAll(".el-upload-list__item");
      items.forEach((item, index) => {
        index !== last && item.remove();
      });
      handleChange(file)
    } else {
      fileList.value.push(file);
      handleChange(fileList.value);
    }
  });
};
// 文件删除
const onRemove = (file: any, fileList: Array<any>) => {
  console.log(file)
  if (!props.multiple) {
    handleChange(null)
  } else {
    handleChange(fileList);
  }
};

const clearFiles = () => {
  uploadRef.value && uploadRef.value.clearFiles();
  onRemove(null, [])
};

const handleChange = (val: any) => {
  emits("update:modelValue", val);
  emits("change", val);
}

const downloadTemplate = () => {
  emits("download")
}

defineExpose({clearFiles})
</script>

<style lang="scss">
</style>

