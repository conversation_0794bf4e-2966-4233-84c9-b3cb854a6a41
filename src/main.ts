import { createApp } from 'vue'
import ElementPlus from "element-plus";
import { initComponent } from '@/components'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import locale from "element-plus/es/locale/lang/zh-cn";
import i18n from '@/utils/i18n'
import App from './App.vue'
import routes from './router'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import { createRouter, createWebHashHistory, RouterHistory } from 'vue-router'
import { renderWithQiankun, qiankunWindow } from 'vite-plugin-qiankun/dist/helper'
/** 全局引入skynet-pandora-ui库 */
import SkynetPandoraUI from 'skynet-pandora-ui'
import 'skynet-pandora-ui/lib/style.css'
/* svg-icon */
import 'virtual:svg-icons-register';
/*公共样式文件引入 */
import '@/styles/index.scss'
import initDirective from '@/utils/directives.js';
let instance: any = null
let router = null
let history: RouterHistory
const pinia = createPinia()
declare global {
  interface Window {
    __POWERED_BY_QIANKUN__: any
    __INJECTED_PUBLIC_PATH_BY_QIANKUN__: any
  }
}
function render(props: any = {}) {
  const { container } = props
  instance = createApp(App)
  // 将props挂载到全局上，用于主子应用的交互
  instance.config.globalProperties.$microProps = props
  history = createWebHashHistory(
    qiankunWindow.__POWERED_BY_QIANKUN__ ? '/' : '/'
  )
  // 注册ElementPlus
  instance.use(ElementPlus, {
    locale
  });
  // 注册所有的element-icon
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    instance.component(key, component)
  }
  // 注册自定义组件
  initComponent(instance) 
  initDirective(instance)
  // 注册SkynetPandoraUI公共组件
  instance.use(SkynetPandoraUI)
  // 注册路由
  router = createRouter({
    history,
    routes
  })
  instance.use(router)
  instance.use(pinia)
  instance.use(i18n)
  instance.mount(container ? container.querySelector("#appBaseGlobalId") : "#appBaseGlobalId");
  if (qiankunWindow.__POWERED_BY_QIANKUN__) {
    // console.log('我正在作为子应用运行。。。')
  }
}

renderWithQiankun({
  mount(props: any) {
    render(props);
  },
  bootstrap() {
  },
  update() {
  },
  unmount(props: any) {
    console.log('unmount', props)
    instance.unmount();
    instance._container.innerHTML = "";
    instance = null;
    history.destroy() // 不卸载router会导致其他应用路由失败
    router = null
  }
});

if (!qiankunWindow.__POWERED_BY_QIANKUN__) {
  // console.log('非qiankun运行')
  render({})
}