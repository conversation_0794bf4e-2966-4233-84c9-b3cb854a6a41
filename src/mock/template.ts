export default {
  "code": "000000",
  "desc": "success",
  "data": {
    "content": [
      {
        "id": 1,
        "name": "模板名称1",
        "appId": "APPIDITHKDLSL111",
        "type": 1,
        "status": 1,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容111",
        "city": "suzhou",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 2,
        "name": "模板名称2",
        "appId": "APPIDITHKDLSL222",
        "type": 2,
        "status": 2,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容222",
        "city": "fuyang",
        "age": 25,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 3,
        "name": "模板名称3",
        "appId": "APPIDITHKDLSL333",
        "type": 2,
        "status": 3,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容333",
        "rejectReason": "不同意",
        "city": "fuyang",
        "age": 30,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 4,
        "name": "模板名称4",
        "appId": "APPIDITHKDLSL444",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容444",
        "city": "anhui",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 5,
        "name": "模板名称5",
        "appId": "APPIDITHKDLSL555",
        "type": 1,
        "status": 1,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容555",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 6,
        "name": "模板名称6",
        "appId": "APPIDITHKDLSL666",
        "type": 2,
        "status": 2,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容666",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 7,
        "name": "模板名称7",
        "appId": "APPIDITHKDLSL777",
        "type": 2,
        "status": 5,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容777",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 8,
        "name": "模板名称8",
        "appId": "APPIDITHKDLSL888",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容888",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 9,
        "name": "模板名称9",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 3,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容999",
        "rejectReason": "不符合要求，不通过",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 10,
        "name": "模板名称10",
        "appId": "APPIDITHKDLSL999",
        "type": 2,
        "status": 2,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容101010",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 11,
        "name": "模板名称11",
        "appId": "APPIDITHKDLSL999",
        "type": 2,
        "status": 3,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容111111",
        "rejectReason": "不通过",
        "city": "anhui",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 12,
        "name": "模板名称12",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容121212",
        "city": "fuyang",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 8,
        "name": "模板名称8",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容888",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 9,
        "name": "模板名称9",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 6,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容999",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 10,
        "name": "模板名称10",
        "appId": "APPIDITHKDLSL999",
        "type": 2,
        "status": 2,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容101010",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 11,
        "name": "模板名称11",
        "appId": "APPIDITHKDLSL999",
        "type": 2,
        "status": 3,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容111111",
        "rejectReason": "不符合要求，不通过",
        "city": "fuyang",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 12,
        "name": "模板名称12",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容121212",
        "city": "anhui",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 8,
        "name": "模板名称8",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容888",
        "city": "tongning",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 9,
        "name": "模板名称9",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 3,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容999",
        "rejectReason": "不符合要求，不通过",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 10,
        "name": "模板名称10",
        "appId": "APPIDITHKDLSL999",
        "type": 2,
        "status": 2,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容101010",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      },
      {
        "id": 11,
        "name": "模板名称11",
        "appId": "APPIDITHKDLSL999",
        "type": 2,
        "status": 3,
        "enable": 0,
        "num": 10,
        "date": "2016-08-10 35:38:20",
        "customText": "这是一段自定义插槽内容111111",
        "rejectReason": "不符合要求，不通过",
        "city": "hefei"
      },
      {
        "id": 12,
        "name": "模板名称12",
        "appId": "APPIDITHKDLSL999",
        "type": 1,
        "status": 4,
        "enable": 1,
        "num": 5,
        "date": "2016-05-03 35:38:20",
        "customText": "这是一段自定义插槽内容121212",
        "city": "hefei",
        "age": 18,
        "isEnd": false,
        "school": "北京大学",
        "hobby": ["唱歌", "跳舞"]
      }
    ],
    "totalElements": 12
  }
}