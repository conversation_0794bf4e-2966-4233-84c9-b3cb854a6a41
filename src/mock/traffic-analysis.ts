export default {
  header: {
    code: 0,
    message: "ok",
  },
  payload: {
    data: {
      // =============================检索请求流量分析======================
      totalRequest: {
        count: 428469, // 检索请求总量
        avg: 112423, // 日均
      },
      successRequest: {
        num: [
          { value: 122222, name: "失败数量" },
          { value: 2222222, name: "成功数量" },
        ],
        percentage: 99.99, // 成功率
      },

      hasResult: {
        count: 23333, //有结果
        total: 33333, // 总数
        percentage: 88.88, // 有结果率
        trend: {
          value: 12, // 环比
          name: "环比",
        },
      },
      app: [
        {
          name: "医疗检索(appId)",
          value: 2333, // 总数
          percentage: 67.9,
        },
        {
          name: "医疗检索(appId)1",
          value: 2333, // 总数
          percentage: 67.9,
        },
      ],

      // =======================检索请求趋势分析=============================

      overview: {
        grandTotal: 4133342, // 累计检索总量
        conditionGrandTotal: 3452,
        trend: {
          name: "环比",
          value: 12,
        },
      },
      trend: {
        requestCount: [
          {
            date: "2025-02-24", // 当查询一年的流量时，精确到月
            requestCount: 112321, // 请求总数
            hasResultCount: 110221, // 有结果数
            hasResultpercentage: 92.31, // 有结果率
            noResultCount: 1123, // 无结果数
            trafficPercentage: 89.21, // 流量占比
          },
          {
            date: "2025-02-25", // 当查询一年的流量时，精确到月
            requestCount: 112321,
            hasResultCount: 110221,
            hasResultPercentage: 92.31,
            noResultCount: 1123,
          },
          {
            date: "2025-02-26", // 当查询一年的流量时，精确到月
            requestCount: 112321, // 请求总数
            hasResultCount: 110221, // 有结果数
            hasResultpercentage: 92.31, // 有结果率
            noResultCount: 1123, // 无结果数
            trafficPercentage: 89.21, // 流量占比
          },
          {
            date: "2025-02-27", // 当查询一年的流量时，精确到月
            requestCount: 112321,
            hasResultCount: 110221,
            hasResultPercentage: 92.31,
            noResultCount: 1123,
          },
          {
            date: "2025-02-28", // 当查询一年的流量时，精确到月
            requestCount: 112321,
            hasResultCount: 110221,
            hasResultPercentage: 92.31,
            noResultCount: 1123,
          },

          {
            date: "2025-02-24", // 当查询一年的流量时，精确到月
            requestCount: 112321, // 请求总数
            hasResultCount: 110221, // 有结果数
            hasResultpercentage: 92.31, // 有结果率
            noResultCount: 1123, // 无结果数
            trafficPercentage: 89.21, // 流量占比
          },
          {
            date: "2025-02-25", // 当查询一年的流量时，精确到月
            requestCount: 112321,
            hasResultCount: 110221,
            hasResultPercentage: 92.31,
            noResultCount: 1123,
          },
          {
            date: "2025-02-26", // 当查询一年的流量时，精确到月
            requestCount: 112321, // 请求总数
            hasResultCount: 110221, // 有结果数
            hasResultpercentage: 92.31, // 有结果率
            noResultCount: 1123, // 无结果数
            trafficPercentage: 89.21, // 流量占比
          },
          {
            date: "2025-02-27", // 当查询一年的流量时，精确到月
            requestCount: 112321,
            hasResultCount: 110221,
            hasResultPercentage: 92.31,
            noResultCount: 1123,
          },
          {
            date: "2025-02-28", // 当查询一年的流量时，精确到月
            requestCount: 112321,
            hasResultCount: 110221,
            hasResultPercentage: 92.31,
            noResultCount: 1123,
          },
        ],
      },

      // =====================产品方案流量分析=======================================
      productTrend: [
        {
          //"date": null, // 当查询一年的流量时，精确到月
          value: 112321, // 请求总数
          hasResultCount: 110221, // 有结果数
          hasResultpercentage: 92.31, // 有结果率
          noResultCount: 1123, // 无结果数
          trafficPercentage: 89.21, // 流量占比
          name: "产品方案名称1",
          code: "产品方案编码1",
          son: [
            {
              requestCount: 112321, // 请求总数
              hasResultCount: 112300, // 有结果数
              hasResultpercentage: 1, // 有结果率
              noResultCount: 21, // 无结果数
              name: "产品方案版本名称1",
              code: "产品方案版本编码1",
              version: "V1",
            },
            {
              requestCount: 112321, // 请求总数
              hasResultCount: 70, // 有结果数
              hasResultpercentage: 2, // 有结果率
              noResultCount: 30, // 无结果数
              name: "产品方案版本名称2",
              code: "产品方案版本编码2",
              version: "V1",
            },
            {
              requestCount: 112321, // 请求总数
              hasResultCount: 60, // 有结果数
              hasResultpercentage: 3, // 有结果率
              noResultCount: 40, // 无结果数
              name: "产品方案版本名称3",
              code: "产品方案版本编码3",
              version: "V1",
            },
          ],
        },
        {
          //"date": null, // 当查询一年的流量时，精确到月
          value: 112321, // 请求总数
          hasResultCount: 110221, // 有结果数
          hasResultpercentage: 92.31, // 有结果率
          noResultCount: 1123, // 无结果数
          trafficPercentage: 89.21, // 流量占比
          name: "产品方案名称2",
          code: "产品方案编码2",
          son: [
            {
              requestCount: 112321, // 请求总数
              hasResultCount: 110221, // 有结果数
              hasResultpercentage: 92.31, // 有结果率
              noResultCount: 1123, // 无结果数
              trafficPercentage: 89.21, // 流量占比
              name: "产品方案版本名称2",
              code: "产品方案版本编码",
              version: "V1",
            },
          ],
        },
      ],
    },
  },
};
