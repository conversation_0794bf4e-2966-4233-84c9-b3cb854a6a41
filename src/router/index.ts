import { RouteRecordRaw } from "vue-router";
import { dataC } from "turing-plugin";

function getFirstConsoleMenu(sideList: any[] = [], homeList: any[] = []): string {
  const homeListStr = homeList.map((item) => item.home);
  function getFirstMenu(list: any[]) {
    for (const item of list) {
      if (item.items?.length) {
        const result: string = getFirstMenu(item.items);
        if (result) return result;
      } else if (item.console && !homeListStr.includes(item.console)) {
        return item.console.replace(/^\/lynxiao\/base\/#/, "");
      }
    }
    return false;
  }
  return getFirstMenu(sideList);
}

function getFirstHomeMenu(homeList: any[] = []) {
  return homeList?.[0]?.home?.replace(/^\/lynxiao\/base\/#/, "");
}

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: () => {
      const storeVuex: any = sessionStorage.getItem("storeVuex");
      const store: any = dataC.safeObject(storeVuex);
      return getFirstConsoleMenu(store?.consoleList) || getFirstHomeMenu(store?.homeList) || "/site";
    },
  },
  {
    path: "/meta-label",
    name: "meta-label",
    meta: { label: "等级字典配置" },
    component: () => import("@/views/meta-config/meta-label/MetaLabelIndex.vue"),
  },
  {
    path: "/meta-site-field",
    name: "meta-site-field",
    meta: { label: "站点属性配置" },
    component: () => import("@/views/meta-config/meta-site-field/MetaSiteFieldIndex.vue"),
  },
  {
    path: "/meta-region",
    name: "meta-region",
    meta: { label: "区域环境管理" },
    component: () => import("@/views/meta-config/meta-region/MetaRegionIndex.vue"),
  },
  {
    path: "/idx-db-tem",
    name: "idx-db-tem",
    meta: { label: "特征库模板" },
    component: () => import("@/views/meta-config/Idx-db-tem/IdxDbTemIndex.vue"),
  },
  {
    path: "/meta-vector-db",
    name: "meta-vector-db",
    meta: { label: "向量库模板" },
    component: () => import("@/views/meta-config/meta-vector-db/MetaVectorDbIndex.vue"),
  },
  {
    path: "/remove-duplicates",
    name: "removeDuplicates",
    meta: { label: "去重规则管理" },
    component: () => import("@/views/meta-config/remove-duplicates/list.vue"),
    children: [
      {
        path: "detail",
        name: "removeDuplicates::detail",
        meta: { label: "详情" },
        component: () => import("@/views/meta-config/remove-duplicates/detail.vue"),
      },
    ],
  },
  {
    path: "/meta-word",
    name: "meta-word",
    meta: { label: "字典管理" },
    component: () => import("@/views/meta-config/meta-dictionary/word/MetaWordIndex.vue"),
  },
  {
    path: "/baike-entry-intervence",
    name: "baike-entry-intervence",
    meta: { label: "百科词条干预" },
    component: () => import("@/views/intervence-manage/baike-entry-intervence/indexNew.vue"),
    children: [
      {
        path: "detail",
        name: "baike-entry-intervence::detail",
        meta: { label: "详情" },
        component: () => import("@/views/intervence-manage/baike-entry-intervence/detail/index.vue"),
      },
    ],
  },
  {
    path: "/manual-intervention",
    name: "manual-intervention",
    meta: { label: "人工干预数据" },
    component: () => import("@/views/intervence-manage/manual-intervence/ManualInterventionIndex.vue"),
    children: [
      {
        path: "domain-black-log",
        name: "manual-intervention::domain-black-log",
        meta: { label: "日志" },
        component: () => import("@/views/intervence-manage/manual-intervence/domainBlack/DomainBlackLog.vue"),
      },
      {
        path: "url-black-log",
        name: "manual-intervention::url-black-log",
        meta: { label: "日志" },
        component: () => import("@/views/intervence-manage/manual-intervence/urlBlack/URLBlackLog.vue"),
      },
      {
        path: "url-black-add",
        name: "manual-intervention::url-black-add",
        meta: { label: "新增" },
        component: () => import("@/views/intervence-manage/manual-intervence/urlBlack/URLBlackAdd.vue"),
      },
    ],
  },
  {
    path: "/meta-dict",
    name: "meta-dict",
    meta: { label: "词典管理" },
    component: () => import("@/views/meta-config/meta-dictionary/dic/MetaDictionaryIndex.vue"),
  },
  {
    path: "/site",
    name: "site",
    meta: { label: "站点管理" },
    component: () => import("@/views/data-manage/site/config/SiteIndex.vue"),
    children: [
      {
        path: "version",
        name: "site::version",
        meta: { label: "版本管理" },
        component: () => import("@/views/data-manage/site/version/VersionIndex.vue"),
        children: [
          {
            path: "rule",
            name: "site::version::rule",
            meta: { label: "规则管理" },
            component: () => import("@/views/data-manage/site/rule/RuleIndex.vue"),
            children: [
              {
                path: "rule-test",
                name: "site::version::rule::test",
                meta: { label: "规则调试" },
                component: () => import("@/views/data-manage/site/rule/RuleTest.vue"),
              },
            ],
          },
        ],
      },
    ],
  },
  {
    path: "/dataset",
    name: "dataset",
    meta: { label: "爬虫数据集" },
    component: () => import("@/views/data-manage/dataset/config/DatasetIndex.vue"),
    children: [
      {
        path: "version",
        name: "dataset::version",
        meta: { label: "版本管理" },
        component: () => import("@/views/data-manage/dataset/version/VersionIndex.vue"),
        children: [
          {
            path: "preview",
            name: "dataset::version::preview",
            meta: { label: "数据预览" },
            component: () => import("@/views/data-manage/dataset/preview/PreviewIndex.vue"),
          },
        ],
      },
    ],
  },
  // 索引库管理
  {
    path: "/idx-db",
    name: "idx-db",
    component: () => import("@/views/data-manage/idx-db-inst/config/IdxDbInstIndex.vue"),
    meta: { label: "索引库管理" },
    children: [
      {
        path: "statistic",
        name: "idx-db::statistic",
        meta: { label: "数据集" },
        component: () => import("@/views/data-manage/idx-db-inst/statistic/StatisticIndex.vue"),
        children: [
          {
            path: "preview",
            name: "idx-db::statistic::preview",
            meta: { label: "数据预览" },
            component: () => import("@/views/data-manage/idx-db-inst/preview/PreviewIndex.vue"),
          },
        ],
      },
    ],
  },
  //冲突数据页面
  {
    path: "/conflict-preview",
    name: "conflict-preview",
    meta: { label: "冲突数据" },
    component: () => import("@/views/common/preview/ConflictPreview.vue"),
  },
  //失败数据页面
  {
    path: "/fail-preview",
    name: "fail-preview",
    meta: { label: "失败数据" },
    component: () => import("@/views/common/preview/FailPreview.vue"),
  },
  //上线计划失败数据页面
  {
    path: "/online-fail-preview",
    name: "online-fail-preview",
    meta: { label: "失败数据" },
    component: () => import("@/views/common/preview/FailPreview.vue"),
  },
  //任务页面
  {
    path: "/task",
    name: "task",
    meta: { label: "任务管理" },
    component: () => import("@/views/background-manage/task/TaskIndex.vue"),
  },
  //任务执行历史
  {
    path: "/task-history",
    name: "task-history",
    meta: { label: "任务历史" },
    component: () => import("@/views/common/task/TaskHistory.vue"),
  },
  // 调度组件管理
  {
    path: "/component",
    name: "component",
    meta: { label: "调度组件" },
    component: () => import("@/views/meta-config/comp/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "component::version",
        meta: { label: "组件版本" },
        component: () => import("@/views/meta-config/comp/version/list.vue"),
        children: [
          {
            path: "add",
            name: "component::version::add",
            meta: { label: "创建组件版本" },
            component: () => import("@/views/meta-config/comp/version/add.vue"),
          },
        ],
      },
    ],
  },
  // 场景策略管理
  {
    path: "/scene",
    name: "scene",
    meta: { label: "场景策略" },
    component: () => import("@/views/flow-manage/scene/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "scene::version",
        meta: { label: "场景策略版本" },
        component: () => import("@/views/flow-manage/scene/version/list.vue"),
      },
    ],
  },
  // 编排模板管理
  {
    path: "/template",
    name: "template",
    meta: { label: "编排模版" },
    component: () => import("@/views/flow-manage/template/list.vue"),
  },
  // 产品方案管理
  {
    path: "/product",
    name: "product",
    meta: { label: "产品方案" },
    component: () => import("@/views/flow-manage/product/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "product::version",
        meta: { label: "产品方案版本" },
        component: () => import("@/views/flow-manage/product/version/list.vue"),
      },
    ],
  },
  // 业务应用管理
  {
    path: "/app",
    name: "app",
    meta: { label: "业务应用" },
    component: () => import("@/views/business-manage/app/list.vue"),
    children: [
      {
        path: "version/:id",
        name: "app::version",
        meta: { label: "业务关联产品" },
        component: () => import("@/views/business-manage/app/version/list.vue"),
      },
    ],
  },
  // 上线计划管理
  {
    path: "/online",
    name: "online",
    meta: { label: "上线计划" },
    component: () => import("@/views/business-manage/online/index.vue"),
    children: [
      {
        path: "statistic",
        name: "online::statistic",
        meta: { label: "统计信息" },
        component: () => import("@/views/business-manage/online/idxDb/statistic/StatisticIndex.vue"),
        children: [
          {
            path: "preview",
            name: "online::statistic::preview",
            meta: { label: "数据预览" },
            component: () => import("@/views/business-manage/online/idxDb/preview/PreviewIndex.vue"),
          },
        ],
      },
      {
        path: "asset-preview",
        name: "online::asset-preview",
        meta: { label: "数据预览" },
        component: () => import("@/views/business-manage/online/asset/preview/PreviewIndex.vue"),
      },
    ],
  },
  // 产品分流管理
  {
    path: "/traffic",
    name: "traffic",
    meta: { label: "上线分流" },
    component: () => import("@/views/business-manage/traffic/list.vue"),
  },
  // 产品分流管理
  {
    path: "/traffic-verify",
    name: "traffic-verify",
    meta: { label: "验证分流" },
    component: () => import("@/views/business-manage/traffic/list.vue"),
  },
  //分析计划
  {
    path: "/analysis-plan",
    name: "analysis-plan",
    meta: { label: "分析计划" },
    component: () => import("@/views/evaluation-manage/analysis-plan/AnalysisPlanIndex.vue"),
    children: [
      {
        path: "details",
        name: "analysis-plan::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-manage/analysis-plan/details/index.vue"),
        children: [
          {
            path: "position",
            name: "analysis-plan::details::position",
            meta: { label: "全链路定位" },
            component: () => import("@/views/evaluation-manage/analysis-plan/details/position.vue"),
          },
        ],
      },
    ],
  },
  //归因策略
  {
    path: "/attribution-strategy",
    name: "attribution-strategy",
    meta: { label: "归因策略" },
    component: () => import("@/views/evaluation-manage/attribution-strategy/Index.vue"),
    children: [
      {
        path: "details",
        name: "attribution-strategy::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-manage/attribution-strategy/details/index.vue"),
      },
    ],
  },
  {
    path: "/evaluation-reslut",
    name: "evaluation-reslut",
    meta: { label: "结果统计" },
    component: () => import("@/views/evaluation-manage/result/index.vue"),
    children: [
      {
        path: "details",
        name: "evaluation-reslut::details",
        meta: { label: "结果分析明细" },
        component: () => import("@/views/evaluation-manage/result/detail.vue"),
      },
    ],
  },
  {
    path: "/standard-dim-group",
    name: "standard-dim-group",
    meta: { label: "维度标准" },
    component: () => import("@/views/standard/standardGroupIndex.vue"),
    children: [
      {
        path: "details",
        name: "standard-dim-group::details",
        meta: { label: "维度列表" },
        component: () => import("@/views/standard/group-list.vue"),
      },
    ],
  },
  {
    path: "/mark-standard",
    name: "mark-standard",
    meta: { label: "测评标准" },
    component: () => import("@/views/standard/markStandardIndex.vue"),
    children: [
      {
        path: "details",
        name: "mark-standard::details",
        meta: { label: "标准明细" },
        component: () => import("@/views/standard/standard-list.vue"),
      },
    ],
  },
  // 标注页面
  {
    path: "/mark-index",
    name: "mark-index",
    meta: { label: "标注", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/MarkIndex.vue"),
  },
  // 标注组件级联选中测试页面
  {
    path: "/annotation-cascade-test",
    name: "annotation-cascade-test",
    meta: { label: "标注组件级联选中测试" },
    component: () => import("@/views/evaluation-manage/mark/AnnotationCascadeTest.vue"),
  },
  // 标注组件功能演示页面
  {
    path: "/annotation-demo",
    name: "annotation-demo",
    meta: { label: "标注组件功能演示" },
    component: () => import("@/views/evaluation-manage/mark/AnnotationDemo.vue"),
  },
  // 基于树组件的标注组件测试页面
  {
    path: "/annotation-tree-test",
    name: "annotation-tree-test",
    meta: { label: "树组件标注测试" },
    component: () => import("@/views/evaluation-manage/mark/AnnotationTreeTest.vue"),
  },
  // 标注组件左右排列布局测试页面
  {
    path: "/annotation-tree-layout-test",
    name: "annotation-tree-layout-test",
    meta: { label: "树组件左右排列测试" },
    component: () => import("@/views/evaluation-manage/mark/AnnotationTreeLayoutTest.vue"),
  },
  // 标注组件级联选中功能测试页面
  {
    path: "/annotation-cascade-tree-test",
    name: "annotation-cascade-tree-test",
    meta: { label: "树组件级联选中测试" },
    component: () => import("@/views/evaluation-manage/mark/AnnotationCascadeTreeTest.vue"),
  },
  // 标注组件宽度自适应测试页面
  {
    path: "/annotation-width-adaptive-test",
    name: "annotation-width-adaptive-test",
    meta: { label: "树组件宽度自适应测试" },
    component: () => import("@/views/evaluation-manage/mark/AnnotationWidthAdaptiveTest.vue"),
  },
  // DocAnnotation集成AnnotationTreeComponent测试页面
  {
    path: "/doc-annotation-tree-test",
    name: "doc-annotation-tree-test",
    meta: { label: "DocAnnotation集成测试" },
    component: () => import("@/views/evaluation-manage/mark/DocAnnotationTreeTest.vue"),
  },
  // DocAnnotation标注区域左右排列测试页面
  {
    path: "/doc-annotation-layout-test",
    name: "doc-annotation-layout-test",
    meta: { label: "DocAnnotation左右排列测试" },
    component: () => import("@/views/evaluation-manage/mark/DocAnnotationLayoutTest.vue"),
  },
  // DocAnnotation标注区域滚动条测试页面
  {
    path: "/doc-annotation-scroll-test",
    name: "doc-annotation-scroll-test",
    meta: { label: "DocAnnotation滚动条测试" },
    component: () => import("@/views/evaluation-manage/mark/DocAnnotationScrollTest.vue"),
  },
  // 测评字段配置
  {
    path: "/field-setting",
    name: "field-setting",
    meta: { label: "测评字段配置"},
    component: () => import("@/views/evaluation-setting/fields-setting/FieldsSetting.vue"),
  },
  // 策略字段配置 - （关注节点配置）
  {
    path: "/concern-node-setting",
    name: "concern-node-setting",
    meta: { label: "策略字段配置"},
    component: () => import("@/views/evaluation-setting/concern-node-setting/ConcernNodeSetting.vue"),
  },
  // 体验产品配置
  {
    path: "/exp-prod-setting",
    name: "exp-prod-setting",
    meta: { label: "体验产品配置"},
    component: () => import("@/views/evaluation-setting/exp-prod-setting/ExpProdSetting.vue"),
  },
  {
    path: "/mark-index/position",
    name: "mark-index::position",
    meta: { label: "全链路定位", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/MarkPosition.vue"),
  },
  //体验页面
  {
    path: "/experience-index",
    name: "experience-index",
    meta: { label: "体验", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/ExperienceIndex.vue"),
  },
  {
    path: "/experience-index/position",
    name: "experience-index::position",
    meta: { label: "全链路定位", hiddenBreadCrumb: true },
    component: () => import("@/views/evaluation-manage/mark/ExperiencePosition.vue"),
  },
  // 搜索请求
  {
    path: "/search-request",
    name: "search-request",
    meta: { label: "搜索请求" },
    component: () => import("@/views/evaluation-manage/search-request/index.vue"),
    children: [
      {
        path: "position",
        name: "search-request::position",
        meta: { label: "全链路定位" },
        component: () => import("@/views/evaluation-manage/search-request/position.vue"),
      },
    ],
  },
  // 运营可视化
  {
    path: "/business-traffic-analysis",
    name: "business-traffic-analysis",
    meta: { label: "业务应用流量分析" },
    component: () => import("@/views/operational-visibility/business-traffic/index.vue"),
  },
  {
    path: "/environmental-traffic-analysis",
    name: "environmental-traffic-analysis",
    meta: { label: "环境流量分析" },
    component: () => import("@/views/operational-visibility/environmental-traffic/index.vue"),
  },
  {
    path: "/point-traffic-anlaysis",
    name: "point-traffic-anlaysis",
    meta: { label: "埋点流量分析" },
    component: () => import("@/views/operational-visibility/point-traffic-anlaysis/index.vue"),
  },
  {
    path: "/report-anlaysis",
    name: "report-anlaysis",
    meta: { label: "报表分析" },
    component: () => import("@/views/operational-visibility/report-anlaysis/index.vue"),
    children: [
      {
        path: "details",
        name: "report-anlaysis::details",
        meta: { label: "业务应用流量汇总表" },
        component: () => import("@/views/operational-visibility/report-anlaysis/table.vue"),
      },
    ],
  },
  //增量审核
  {
    path: "/tdoc-audit",
    name: "tdoc-audit",
    meta: { label: "增量审核" },
    component: () => import("@/views/data-manage/tdoc-audit/TdocAuditIndex.vue"),
    children: [
      {
        path: "detail",
        name: "tdoc-audit::detail",
        meta: { label: "审核明细" },
        component: () => import("@/views/data-manage/tdoc-audit/TdocAuditDetail.vue"),
      },
    ],
  },
  {
    path: "/offline-dataset",
    name: "offline-dataset",
    meta: { label: "离线数据集" },
    component: () => import("@/views/data-manage/offline-dataset/index.vue"),
    children: [
      {
        path: "version",
        name: "offline-dataset::version",
        meta: { label: "版本管理" },
        component: () => import("@/views/data-manage/offline-dataset/version/VersionIndex.vue"),
        children: [
          {
            path: "preview",
            name: "offline-dataset::version::preview",
            meta: { label: "数据预览" },
            component: () => import("@/views/data-manage/offline-dataset/preview/PreviewIndex.vue"),
          },
        ],
      },
    ],
  },
  // 测评任务
  {
    path: "/evaluation-task",
    name: "evaluation-task",
    meta: { label: "测评任务" },
    component: () => import("@/views/evaluation-task-manage/evaluation-task/index.vue"),

    children: [
      {
        path: "details",
        name: "evaluation-task::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-task-manage/evaluation-task/details/index.vue"),
        children: [
          {
            path: "tag-details",
            name: "evaluation-task::details::tag-details",
            meta: { label: "query标注明细" },
            component: () => import("@/views/evaluation-task-manage/evaluation-task/details/tag-details.vue"),
          },
        ],
      },
    ],
  },
  // query集
  {
    path: "/query-set",
    name: "query-set",
    meta: { label: "query集" },
    component: () => import("@/views/evaluation-task-manage/query-set/index.vue"),

    children: [
      {
        path: "details",
        name: "query-set::details",
        meta: { label: "计划明细" },
        component: () => import("@/views/evaluation-task-manage/query-set/details/index.vue"),
        children: [
          {
            path: "position",
            name: "query-set::details::position",
            meta: { label: "全链路定位" },
            component: () => import("@/views/evaluation-task-manage/query-set/details/position.vue"),
          },
        ],
      },
    ],
  },
  {
    path: "/cloudfunc",
    name: "cloudfunc",
    meta: { label: "云函数管理" },
    component: () => import("@/views/cloudfunc/list/index.vue"),
    children: [
      {
        path: "version",
        name: "cloudfunc::version",
        meta: { label: "版本管理" },
        component: () => import("@/views/cloudfunc/version/index.vue"),
      },
    ],
  },
  {
    path: "/my-mission",
    name: "my-mission",
    meta: { label: "我的任务" },
    component: () => import("@/views/evaluation-manage/mission/MyMission.vue"),
  },
  /* 模板demo页面 */
  {
    path: "/mock-demo",
    name: "mockDemo",
    meta: { label: "demo列表" },
    component: () => import("@/views/mock/list.vue"),
    children: [
      {
        path: "detail/:id",
        name: "mockDemo::detail",
        meta: { label: "详情" },
        component: () => import("@/views/mock/detail.vue"),
        children: [
          {
            path: "detail2",
            name: "mockDemo::detail::detail2",
            meta: { label: "详情页的详情页" },
            component: () => import("@/views/mock/detail2.vue"),
          },
        ],
      },
    ],
  },
  {
    path: "/mock-demo2",
    name: "mockDemo2",
    meta: { label: "demo2列表" },
    component: () => import("@/views/mock/demo2/list.vue"),
    children: [
      {
        path: "detail",
        name: "mockDemo2::detail",
        meta: { label: "详情" },
        component: () => import("@/views/mock/demo2/detail.vue"),
      },
    ],
  },
];

export default routes;
