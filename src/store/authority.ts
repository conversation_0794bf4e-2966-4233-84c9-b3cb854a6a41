/* 用户信息管理器 */
import { defineStore } from "pinia";
import { ref } from "vue";
import { getUserInfo, getAuthorityList } from '@/api/authority'

const useAuthorityStore = defineStore(
  "authority",
  () => {
    const userInfo = ref<any>({});
    const userName = ref<string>('')
    const userName_CN = ref<string>('')
    const orgCode  = ref<string>('')
    // 获取用户信息，并全局存储
    function loadAndSetUserInfo() {
      return new Promise(resolve => {
        getUserInfo().then((res: any) => {
          userInfo.value = res.data || {}
          userName.value = userInfo.value.userName
          userName_CN.value = userInfo.value.userName_CN
          orgCode.value = userInfo.value.orgCode
          resolve(userInfo.value)
        })
      })
    }
    // 获取菜单信息，并全局存储
    const menuList = ref<Array<any>>([])
    function loadAndSetAuthorityList () {
      return new Promise(resolve => {
        getAuthorityList().then((res: any) => {
          menuList.value = res.data || []
          // console.log('菜单信息', menuList.value)
          resolve(menuList.value)
        })
      })
    }
    return { 
      userInfo, 
      userName, 
      userName_CN, 
      orgCode, 
      menuList,
      loadAndSetUserInfo,
      loadAndSetAuthorityList
    };
  }
);
export default useAuthorityStore