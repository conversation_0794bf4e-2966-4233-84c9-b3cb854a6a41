import { defineStore } from "pinia";
import { ref } from "vue";
import * as commonApi from "@/api/common";
import * as evalEvaluationApi from "@/api/eval-evaluation";
import * as util from "@/utils/common";
const useWordStore = defineStore("common", () => {
  const pageList = ref<any>(new Map());
  const columnList = ref<any>(new Map());
  const areaEnum =ref([])
  const groupEnum =ref([])
  const biaozhuEnum =ref([])
  const sceneVersionList =ref([])
  // 存储页码
  function storePage(key: any, val: any, flag: boolean) {
    if (flag) {
      pageList.value.set(key, { page: val.page, sort: val.sort });
    }
  }
  function storeColumn(key: any, val: any, flag: boolean) {
    if (flag) {
      columnList.value.set(key, val);
    }
  }
  function getAreaList() {
    if(areaEnum.value&&areaEnum.value.length){
      return areaEnum.value
    }
    return new Promise(resolve => {
      commonApi.getAreaEnum().then((res: any) => {
        areaEnum.value = res.content || []
        resolve(areaEnum.value)
      })
    })
  }

  async function getSceneVersionList (id:any)  {
    return new Promise(resolve => {
      evalEvaluationApi.getSceneVersionRange(id).then((res: any) => {
        sceneVersionList.value = res.data.map((item: any) => ({
         ...item,
         label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
         value: item.processId,
       }));
       resolve(sceneVersionList.value)
      }).catch((err:any)=>{
        resolve([])
      })
    })
  };
  // 获取已启用的产品方案版本
  function getOpenedProductVersionList() {
    return new Promise(resolve => {
      commonApi.getOpenedProductVersionList().then((res: any) => {
        resolve(res.data)
      })
    })
  }
  // 获取已启用的产品方案
  function getOpenedProductList() {
    return new Promise(resolve => {
      commonApi.getOpenedProductList().then((res: any) => {
        resolve(res.data)
      })
    })
  }
  return { pageList, storePage, areaEnum, getAreaList,groupEnum ,columnList,storeColumn,getSceneVersionList,getOpenedProductVersionList,getOpenedProductList};
});
export default useWordStore;
