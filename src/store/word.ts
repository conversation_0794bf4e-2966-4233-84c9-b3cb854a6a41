import { defineStore } from "pinia";
import { ref } from "vue";
import * as metaWordApi from "@/api/meta-word";

const useWordStore = defineStore(
  "word",
  () => {
    const area = ref<string>('');
    const areaEnum =ref([])
    // 菜单折叠
    function areaChange(val: string) {
      area.value = val
    }
    function getAreaList() {
      return new Promise(resolve => {
        metaWordApi.getAreaEnum().then((res: any) => {
          areaEnum.value = res.content || []
          area.value = areaEnum.value.find((item:any)=>item.envType == 1)?.code||areaEnum.value?.[0]?.code
          resolve(areaEnum.value)
        })
      })
    }
    return { area, areaChange ,getAreaList};
  }
);
export default useWordStore