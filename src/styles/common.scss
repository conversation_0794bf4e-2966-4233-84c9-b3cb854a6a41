@import "./common/layout.scss";
@import "./common/custom-ui.scss";
// 删除弹框的样式表
.app-base-custom-del-message-box {
  .el-message-box__container {
    display: flex;

    .el-message-box-icon--warning {
      position: relative;
      transform: none;
      flex-shrink: 0;
      margin-left: 16px;
      margin-top: 3px;
      color: #ff9900;
    }

    .el-message-box__message {
      padding-left: 16px;

      >p {
        min-height: 72px;

        h1 {
          font-size: 16px;
          margin-bottom: 8px;
        }
      }
    }
  }
  .el-message-box-icon--warning {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACQ0lEQVRYR91X3W3bQAz+aEnXAhLQdANngiYT1NkgG8SdoOlzIVWx0ec4E9TZIBskmaD2BOkIDiABrWyZxZ0iVXH0c7oYEFA+6YE/Hz/ySIrQs1DP8WEMYD31RmBwnoATRPcmyWgD4PDtcGPZZww+BeioOhgvCHRjp5trCn//0gHUCoBDHKwH7iWIxjoOCx3mmbONLyjEqsmuEUDy3T1CSrcgHHQKniszVrD4RHyNF3X2tQCSiTsG0Q+jwLtGzJ9EEM+rfFUCUJlv6edegudOBnxcxcQLAFnNvQdj2utQM1bONjrc7YkXAJKpOwforDV75iURnUs9Zp6B6EOrDfhK+LGyyeUZAPnU1pb90O5IRsW9CKKR1E0m3h0IH3XsnHRzWH6izwD8mXohAd90HJkCYODijR+FlQwkE3ehR6U5AwAvhB8fVwOYesVobWXBsATSr/CjgvniQ852Bm5bA/8bMkY9IM0JOHH86O7pO/NoAGAlgui9asIuzO0NQInK/wOAEZUMNQeYoOqpK5VNmA2UDs9QN9quHvNSBHFxT7xqEDnbjboR1gN7rjsJGwdRt1HMX0QQz56YOwfRpQ4pjaM46wPNZQS+Fn6sGOhg07yMVEPJdWx58p5715YRM6u7j4iGbboAHp00GrauY5VRnwdJnkmvJ1kBImNCvvHWctSU4BEDHhkdpcXOUT3hhgB91qhzSYWvnDQOX3WWlwPKJ5pY9piYT2tvBuYlE92IdDPf249JXdbq16wk+XrtxpJazf1K7wD+Aqy2GTBSvajyAAAAAElFTkSuQmCC) no-repeat;
    background-size: 100% 100%;

    svg {
      display: none;
    }
  }
}
.el-message .el-message__badge {
  display: none;
}
.common-part-title {
  position: relative;
  padding-left: 12px;
  font-size: 16px;
  font-weight: 550;
  @include flexBetween();
  &::before {
    content: "";
    position: absolute;
    background-color: $primary-color;
    width: 3px;
    height: 16px;
    left: 0px;
    border-radius: 0 5px 5px 0;
    
  }
}

// 将所有公共样式写在xinghuo-search，避免作为子应用时的样式污染
.xinghuo-search {
  min-width: 1000px;
  //自定义的一些样式表
  .required {
    padding: 5px;
    color: $danger-color;
    font-size: 14px;
  }
  .tip-text {
    @include tip-text();
  }
  .link-text {
    color: $primary-color;
  }
  .over-hide {
    @include no-wrap();
  }
  .flex {
    @include flex()
  }
  .flexBetween {
    @include flexBetween()
  }
  .flexBetweenStart {
    @include flexBetween();
    align-items: flex-start;
  }
  .flexBetweenEnd {
    @include flexBetween();
    align-items: flex-end;
  }
  // 给父级设置该样式，其中一个子集高度已知，另外一个子集设置高度100%，即可实现高度自适应
  .height-adaptive {
    @include height-adaptive()
  }
  // 公共的分栏标题
  .common-part-title {
    position: relative;
    padding-left: 12px;
    font-size: 16px;
    font-weight: 550;
    @include flexBetween();
    &::before {
      content: "";
      position: absolute;
      background-color: $primary-color;
      width: 3px;
      height: 16px;
      left: 0px;
      border-radius: 0 5px 5px 0;
      
    }
  }

  .item-row + .item-row{
    margin-top: 15px;
  }

  .dialog-footer {
    float: right;
  }
  
}

.el-button.el-button--primary.is-disabled.is-link {
  color: var(--el-color-info-light-5);
}

.m-text-button.can-click.mg-right-24.is-disabled {
  color: var(--el-color-info-light-5);
}

.collapse {
  border: none;
  .el-collapse-item__header {
    border: none;
    height: 23px;
    margin-bottom: 12px;
    span{
      font-size: 16px;
      font-weight: bold;
    }
  }
  .el-collapse-item__content{
    padding-bottom: 0;
  }
  .el-collapse-item__wrap {
    border: none;
  }
}
@mixin tringle() {
  display: inline-block;
  border: 8px solid transparent;
  position: relative;
}
.trangle_red {
  @include tringle();
  border-bottom: 8px solid red;
}
.trangle_green {
  @include tringle();
  border-top: 8px solid green;
  top: 8px;
}