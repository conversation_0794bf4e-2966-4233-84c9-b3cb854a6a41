// 统一加个前缀，防止污染其他组件
.xinghuo-search {
  // layout
  .el-header,
  .el-main {
    padding: 0;
  }
  // 表格
  .el-table {
    &:not(.el-table--small, .el-table--large) {
      .el-table__cell {
        padding: 10px 0;
      }
    }
    .el-table__header {
      .el-table__cell {
        font-weight: 550;
      }
    }
    .el-table__body tr:hover > td {
      background: #fafafa;
    }
    .el-popper {
      >span:first-child {
        display: inline-block;
        max-width: 500px;
        max-height: 320px !important;
        overflow-y: auto;
      }
    }
    .el-table__inner-wrapper::before {
      height: 0;
    }
    .el-button:focus-visible {
      outline: none;
    }
  }
  // 下拉选项
  .el-dropdown{
    >div {
      outline: none !important;
    }
  }
  .el-tabs {
    .el-tabs__header {
      margin-bottom: 0;
      .el-tabs__nav-wrap::after {
        height: 1px;
      }
      .custom-tabs-label {
        .el-icon, >span {
          vertical-align: middle;
        }
        >span {
          margin-left: 5px;
        }
      }
    }
  }
  // 消息提示框[需要将提示框放到#app元素上，默认body上，如果作为qiankun子应用使用，不能进行样式隔离]
  .el-message {
    background: #fff;
    min-width: 300px;
    .el-message__icon {
      font-size: 18px;
    }
    .el-message__content {
      color: $text-color;
    }
  }
  .el-message-box {
    padding-bottom: 0;
    .el-message-box__btns {
      padding: 24px 16px;
      justify-content: center;
    }
    .el-message-box__container {
      position: relative;
      >.el-icon {
        top: 10px;
      }
      .custom-message {
        >h3 {
          font-size: 16px;
          margin-bottom: 10px;
        }
      }
    }
  }
  // checkbox
  .vertical-checkbox {
    .el-checkbox {
      display: block;
      width: 100%;
      margin: 0;
    }
  }
}

