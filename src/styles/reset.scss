// css初始化
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;
  box-sizing: border-box;
}

html {
  font-size: 14px;
}
html, body, #app, #appBaseGlobalId {
  width: 100%;
  height: 100%;
  color: $text-color;
}
h1, h2, h3, h4, h5, h6 {
  color: $title-color;
}

::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  background-clip: padding-box;
  border-radius: 8px;
}

::-webkit-scrollbar-track-piece {
  background-color: #e6ebf5;
  border:0;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #3D6FFF;
}
