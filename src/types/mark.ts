export interface FeedbackDetail {
  name: string;
  code: string;
}
export interface Feedback {
  name: string;
  code: string;
  require: boolean;
  optType: string;
  content: FeedbackDetail[];
}

export interface Option {
  name: string;
  code: string;
  require: boolean;
  optType: string;
  feedbacks?: Feedback[];
}

export interface Dimension {
  name: string;
  code: string;
  require: boolean;
  optType: string;
  options: Option[];
}

export type MarkDimsInfo = Dimension[]; 