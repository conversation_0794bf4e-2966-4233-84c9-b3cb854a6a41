import { dataC, timeC } from "turing-plugin";
import { keys, assign, cloneDeep } from "lodash";

/**
 * 版本名称渲染的方法,例如count=3，num=1则返回001

 * @param num 版本号
 * @param count 版本号需要占据的位数
 * @param pre 前缀
 * @param post 后缀
 * @returns 
 */
export function padNumberToDigits(num: number, count: number = 3, pre: string = "", post: string = "") {
  if (num == null || num == undefined) {
    return "";
  }
  let str = num.toString();
  let diff = count - str.length;
  for (let i = 0; i < diff; i++) {
    str = "0" + str;
  }
  return `${pre}${str}${post}`;
}

/**
 * 渲染精品等级对象 {L01: 4}转换为{资讯: S}
 * @param obj 精品等级对象 形如  {L01: 4}
 * @param metaLabelList 渲染的数据源,渲染Key和value,其中每个Item例如{"code": "L01","name": "资讯","values": [{"key": "S","value": 4},{"key": "A","value": 3},{"key": "B","value": 2},{"key": "C","value": 1}]}
 * @returns
 */
export function displayLevels(obj: any, metaLabelList: Array<any>) {
  if (dataC.isEmpty(obj)) return obj;
  try {
    if (dataC.isEmpty(metaLabelList)) {
      return obj;
    }
    const result: { [key: string]: any } = {};
    for (const key in obj) {
      const label = dataC.getItemByValue(metaLabelList, key, "code");
      const displayKey = label.name;
      const displayValue = dataC.getItemByValue(label.values, obj[key], "value").key;
      result[displayKey] = displayValue;
    }
    const list: Array<any> = [];
    keys(result).forEach((key) => {
      list.push(`${key} : ${result[key]}`);
    });
    return list.join(",");
  } catch (error) {
    console.log(error);
    return obj;
  }
}

/**
 * 渲染质量等级
 * @param quality
 */
export function displayQuality(quality: any) {
  if (dataC.isEmpty(quality)) return quality;
  const list = [
    { key: "S", value: 4 },
    { key: "A", value: 3 },
    { key: "B", value: 2 },
    { key: "C", value: 1 },
  ];
  try {
    if (quality.type == "script") {
      return "规则计算";
    } else if (quality.type == "config") {
      return dataC.getItemByValue(list, quality.value, "value")["key"];
    }
  } catch (error) {
    console.log(error);
    return quality;
  }
}

/**
 * 渲染质量等级
 * @param quality
 */
export function displayQualityDataPreview(quality: any) {
  if (dataC.isEmpty(quality)) return quality;
  const list = [
    { key: "S", value: 4 },
    { key: "A", value: 3 },
    { key: "B", value: 2 },
    { key: "C", value: 1 },
  ];
  return dataC.getItemByValue(list, quality, "value")["key"];
}

/**
 * 渲染相似度
 * @param similarity
 */
export function displaySimilarity(similarity: any) {
  if (dataC.isEmpty(similarity)) return similarity;
  try {
    if (similarity.type == "script") {
      return "规则计算";
    } else if (similarity.type == "config") {
      return similarity.value;
    }
  } catch (error) {
    console.log(error);
    return similarity;
  }
}

/**
 * 渲染Url
 * @param url
 */
export function displayUrl(protocol: string, domain: string, path: string) {
  return `${protocol}://${domain}${path}`;
}

/**
 * 渲染入数据集
 * @param datasetConditions
 */
export function displayDataset(datasetConditions: Array<any>) {
  try {
    const datasetNameList = [];
    for (let i = 0; i < datasetConditions.length; i++) {
      for (let j = 0; j < datasetConditions[i].datasetList.length; j++) {
        datasetNameList.push(datasetConditions[i].datasetList[j].name);
      }
    }
    return datasetNameList.join(",");
  } catch (error) {
    console.log(error);
    return datasetConditions;
  }
}
export function downloadFile(blob: Blob, filename: string) {
  const blobUrl = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = blobUrl;
  a.download = filename || "download";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(blobUrl);
}

export const findNodeById: any = (tree: any[], nodeId: string | number) => {
  for (const node of tree) {
    if (node.id === nodeId) {
      return node; // 找到匹配的节点，返回它
    }
    if (node.children && node.children.length > 0) {
      // 如果节点有子节点，递归查找子节点
      const foundInChildren = findNodeById(node.children, nodeId);
      if (foundInChildren) {
        return foundInChildren; // 如果在子节点中找到匹配的节点，返回它
      }
    }
  }
  return null; // 如果没有找到匹配的节点，返回null
};

export const formatNumber: any = (number: any) => {
  if (typeof number === "number") {
    return number.toLocaleString();
  } else {
    return number;
  }
};
export const getCharactersLimit = (str: String, limit: Number, isMore = true) => {
  const obj = {
    short_summary: "",
    showMore: false,
    summary: str,
    isMore: false,
  };
  /*
   * 截取指定字节长度的字符串
   * 注：半角长度为1，全角长度为2
   * str:字符串
   * len:截取长度
   * return: 截取后的字符串及是否截取的标记（扩展用）code=1 字符串截断   code=0  字符串未截断
   */
  function cutStrByte(str: String, len: Number) {
    //校验参数
    if (!str || !len) {
      return { cutStr: "", code: 0 };
    }
    let code = "1", // 默认返回code值，已截断
      strLen = str.length, // 原字符串长度
      cutStr;
    //如果字符串长度小于截取长度的一半,则返回全部字符串
    if (strLen <= len / 2) {
      cutStr = str;
      code = "0";
    } else {
      //遍历字符串
      let strByteCount = 0;
      for (let i = 0; i < strLen; i++) {
        //中文字符字节加2  否则加1
        strByteCount += getByteLen(str.charAt(i));
        //i从0开始 截断时大于len 只截断到第i个
        if (strByteCount > len) {
          cutStr = str.substring(0, i);
          break;
        } else if (strByteCount == len) {
          cutStr = str.substring(0, i + 1);
          break;
        }
      }
    }
    //cutstr为空，没有截断字符串
    if (!cutStr) {
      cutStr = str;
      code = "0";
    }
    return { cutStr: cutStr, code: code };
  }
  /**
   * 获取字节长度，全角字符两个单位长度，半角字符1个单位长度
   */
  function getByteLen(val) {
    let len = 0;
    if (!val) {
      return len;
    }
    for (let i = 0; i < val.length; i++) {
      if (!val[i]) {
        continue;
      }
      // 全角
      if (val[i].match(/[^\x00-\xff]/gi) != null) {
        len += 2;
      } else {
        len += 1;
      }
    }
    return len;
  }
  if (cutStrByte(str, limit).code == "1") {
    obj.short_summary = cutStrByte(str, limit).cutStr + "...";
    obj.showMore = true;
    obj.isMore = isMore;
  }
  return obj;
};

export const getTextLength = (str: any) => {
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    // 判断字符是否在汉字的Unicode编码范围内
    if (char >= 0x4e00 && char <= 0x9fa5) {
      length += 2; // 中文字符加2
    } else {
      length += 1; // 其他字符（包括英文）加1
    }
  }
  return length;
};

export const getEsInfoRender = (esInfo: any) => {
  try {
    const totalInBillions = esInfo.quotaTotal / 100000000;
    const usedPercentage = (esInfo.quotaUsed / esInfo.quotaTotal) * 100;
    return `容量${totalInBillions.toFixed(0)}亿，已用${usedPercentage.toFixed(2)}%`;
  } catch (e) {
    return `已用:${esInfo.quotaUsed}/总量:${esInfo.quotaTotal})`;
  }
};
export function formatNumberToChinese(num:number=0) {
  const units = ["", "万", "亿", "万亿"];
  let unitIndex = 0;

  while (num >= 10000 && unitIndex < units.length - 1) {
      num /= 10000;
      unitIndex++;
  }

  // 保留两位小数
  return `${parseFloat(num.toFixed(2))}${units[unitIndex]}`;
}

export const  flattenArr=(arr:any[],key='Children')=> {
  let result = [];
  for (const item of arr) {
    if (item.issues && Array.isArray(arr[key])) {
      // 添加当前层级的 issues 元素
      result.push(...arr[key]);
      // 递归处理子元素的 issues
      const nested = flattenArr(arr[key]) as Array<any>;
      result.push(...nested);
    }
  }
  return result;
}
export function alphaIncrement(start = 'A') {
  let code = start.charCodeAt(0);
  return () => String.fromCharCode(++code);
}

export function generateTableColumns(data: any,key='extendFieldMap') {
  const arr = new Set();
  data.forEach((item: any) => {
    Object.keys(item[key] || {}).forEach((key: any) => {
      arr.add(key);
    });
  });
  
  return [...arr].map((key) => ({
    prop: key,
    label: key,
    minWidth: getTextLength(key) * 10 + 40,
  }));
}
