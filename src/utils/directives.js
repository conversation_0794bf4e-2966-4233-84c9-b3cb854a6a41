import { validateCode, codeErrMsg, validateName, validateNameReg } from '@/utils/validate';
export const noSpecialCharactersDirective = {
  mounted(el) {
    el.addEventListener('input', function(event) {
      if (!validateName(event.target.value)) {
        event.target.value = event.target.value.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_-]/g, '');
      }
    });
  }
};

export default function initDirective(app) {
  app.directive('no-special-characters', noSpecialCharactersDirective);
}
