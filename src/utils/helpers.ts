
/*
* @Description: 一些公共的方法
* @Author: mxliu5
* @Date: 2024-10-16
*/
import { textC } from 'turing-plugin'
import { successMessage } from '@/utils/app-tip'
import i18n from '@/utils/i18n'
import { NO_TEXT } from '@/utils/constants'
const $t: any = i18n.global.t

// 自定义一个require
export const require = (imgPath: string): string => {
  try {
    const handlePath = imgPath.replace("@", "..");
    let imgUrl = new URL(handlePath, import.meta.url).href;
    if (import.meta.env.MODE === "production") {
      imgUrl = imgUrl.replace("public", "lynxiao/skybox-base");
    }
    return imgUrl;
  } catch (error) {
    console.warn(error);
    return ''
  }
};

// 自定义一个qs
export const queryString = (obj: any): string => {
  let queryString = ''
  for (let [key, value] of Object.entries(obj)) { // [key, value]集合
    queryString += `${key}=${value}&`
  }
  queryString = queryString.replace(/&$/, '') // 去除最后一个&
  return queryString
}

// 复制
export function copyText (beCopyText: string, successMsg ? : string) {
  textC.copyText(beCopyText, () => {
    successMessage(successMsg || $t('tip.copySuccess'))
  })
}


// 文本展示
export const getText = (text: any) => {
  if (text === 0) {
    return text;
  } else {
    return text || NO_TEXT;
  }
};
