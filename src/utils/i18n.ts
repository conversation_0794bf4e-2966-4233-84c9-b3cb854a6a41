/*
* @Description: 国际化语言，可以定义一些公共的文本，1来可以方便替换公共文本 2来后期可以直接集成国际化语言 
* @Author: mxliu5
* @Date: 2024-10-16
*/

import { createI18n  } from "vue-i18n";
import isArray from "lodash/isArray";
import { valueLabel } from '@/utils/interface'
import zhCN from "@/lang/zh_CN.json"
import langConfig from "@/lang/langConfig"

// 将语言数据加入到messages中
let messages: any  = {};
if (isArray(langConfig)) {
  langConfig.forEach((v: valueLabel) => {
    let lang = null;
    switch (v.value) {
      case "zh_CN":
        lang = zhCN;
        break;
    }
    messages[v.value] = lang;
  });
}

// 初始化语言数据
const i18n: any = createI18n ({
  legacy: false, // 组合api模式，需要设置为false
  globalInjection: true, // 全局生效
  locale: 'zh_CN',
  messages: messages
})

export default i18n
