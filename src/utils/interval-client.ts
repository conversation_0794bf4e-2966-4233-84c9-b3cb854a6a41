class IntervalClient {
    private timeout: number;
    private immediate: boolean;
    private intervalId: any;
    private handler: any;
    private handlerData: any;
    private retryTime: any;
  
    constructor(timeout: number, immediate: boolean, retryTime: number = 1) {
      this.timeout = timeout;
      this.immediate = immediate;
      this.retryTime = retryTime;
    }
  
    // 设置消息处理函数
    onHandler(handler: any, handlerData: any): IntervalClient {
      this.handler = handler;
      this.handlerData = handlerData;
      return this;
    }
  
    // 打开连接
    connect() {
      const client = this;
      if (client.immediate) {
        client.handler(client.handlerData);
      }
      this.intervalId = setInterval(() => {
        client.handler(client.handlerData);
      }, client.timeout);
    }
  
    // 断开连接
    disconnect() {
      clearInterval(this.intervalId);
    }
  
    getRetryTime() {
      return this.retryTime;
    }
  
    plusRetryTime() {
      this.retryTime++;
    }
  }
  
  export default IntervalClient;
  