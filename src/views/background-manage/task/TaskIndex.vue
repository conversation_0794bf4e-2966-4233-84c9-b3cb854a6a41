<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="task">
      <task-table ref="taskTableRef"></task-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import taskTable from "./TaskTable.vue";
import useCtx from "@/hooks/useCtx";

const { $app, proxy, $router } = useCtx();
const routeName = "task";

const events = reactive({});
</script>
<style lang="scss">
.task {
}
</style>
