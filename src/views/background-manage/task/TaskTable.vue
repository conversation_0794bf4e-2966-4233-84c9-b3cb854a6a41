<template>
  <div class="task-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
        </div>
      </template>
      <template #process="scope">
        <task-process
          :taskId="scope.row.taskId"
          :taskStatus="scope.row.taskStatus"
          :taskList="scope.row.taskDetailList"
          @cancel-task="loadList"
          :disabled="!testAuth()"
        ></task-process>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted } from "vue";
import { assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import taskProcess from "@/views/common/task/TaskProcess.vue";
import * as taskApi from "@/api/task";
import IntervalClient from "@/utils/interval-client";

const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/task/edit");
};
const { api } = useStore();
const modelValue = reactive({
  taskTypeList: [],
  dataTaskTypeList: [],
});
// 任务类型
const taskTypeComputed = computed(() => {
  const res = {};
  modelValue.taskTypeList.forEach((item) => {
    res[item.value] = {
      type: "info",
      name: item.label,
    };
  });
  return res;
});
// 数据任务类型
const dataTaskTypeComputed = computed(() => {
  const res = {};
  modelValue.dataTaskTypeList.forEach((item) => {
    res[item.value] = {
      type: "info",
      name: item.label,
    };
  });
  return res;
});
//列配置
const columns = ref([
  { prop: "id", label: "ID", width: 220, withCopy: true },
  { prop: "taskId", label: "任务ID", width: 220, withCopy: true },
  { prop: "name", label: "名称", width: 250 },
  {
    prop: "dataType",
    label: "数据类型",
    width: 120,
    custom: "status",
    customRender: {
      options: dataTaskTypeComputed,
    },
  },
  {
    prop: "taskType",
    label: "任务类型",
    width: 150,
    custom: "status",
    customRender: {
      options: taskTypeComputed,
    },
  },
  {
    prop: "process",
    label: "任务进度",
    slotName: "process",
    width: 300,
    showOverflowTooltip: false,
    sortable: false,
  },
  { prop: "description", label: "描述", minWidth: 100 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
]);
//查询面板
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
});
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    taskApi.getRunningTaskListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      //增加任务监控
      getIntervalClinet(result.content);
      //返回数据
      resolve(result);
    });
  });
};
//定时任务监控
const intervalClinet = ref(null);
const getIntervalClinet = (tableData: Array<any>) => {
  //如果已有定时任务对象，则停止并创建新的
  intervalClinet.value?.disconnect();
  //如果列表为空 则不创建新的
  if (dataC.isEmpty(tableData)) return;
  // 获取定时任务对象并启动,以持续刷新任务信息
  intervalClinet.value = new IntervalClient(3000, true);
  intervalClinet.value.onHandler(getTaskProgressList, tableData).connect();
};
//获取任务进度信息
const getTaskProgressList = (tableData: Array<any>) => {
  const taskIdList = tableData.map((x: any) => {
    return x.taskId;
  });
  const progressList = [];
  taskApi.getTaskProgressListByTask(taskIdList).then((result: any) => {
    progressList.push(
      ...result.content.map((x) => {
        if (!dataC.isEmpty(x.taskDetailList)) x.taskId = x.taskDetailList[0].taskId;
        return x;
      })
    );
    const list = tableData.map((x: any) => {
      const task = dataC.getItemByValue(progressList, x.taskId, "taskId");
      if (!dataC.isEmpty(task.taskId)) {
        x.taskStatus = task.status;
        x.taskType = task.type;
        x.taskDetailList = task.taskDetailList;
      }
      return x;
    });
    proxy.$refs["myTableRef"].setTableData(list);
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [];
const handleOperation = (data: any) => {
  const { type, record } = data;
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => {
  taskApi.getTaskTypeList().then((result) => {
    modelValue.taskTypeList = result.data;
  });
  taskApi.getDataTaskTypeList().then((result) => {
    modelValue.dataTaskTypeList = result.data;
  });
});
//销毁
onUnmounted(() => {
  intervalClinet.value?.disconnect();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.task-table {
  height: 100%;
}
</style>
