<template>
  <my-drawer class="component-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
      <template #appIdSlot>
        <div v-if="!isUpdate">
          <el-radio-group v-model="type" @change="switchType">
            <el-radio :label="-1">系统生成</el-radio>
            <el-radio :label="-2">人工定义</el-radio>
          </el-radio-group>
          <el-input v-if="type == -2" v-model="ruleForm.appId" style="width: 200px; margin-left: 8px;" placeholder="请输入AppId" />
        </div>
        <span v-else>{{ ruleForm.appId }}</span>
      </template>
    </my-form>
  </my-drawer>
</template>
  
  <script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useValidate from "@/hooks/validate";
import { createApp, editApp, getAppPartnerList, getTypes } from "@/api/app";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  return (formType.value === "add" ? t("btn.new") : t("btn.edit")) + "业务应用";
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 业务名称列表
const appList = ref([]);
const getAppList = () => {
  getAppPartnerList().then((res) => {
    appList.value = res.data?.map((item: any) => ({
      label: item,
      value: item,
    }));
  });
};
getAppList();

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
  getAppList();
  getTypeList();
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editApp : createApp;
      const params = cloneDeep(ruleForm.value);
      if (type.value == -1) {
        params.appId = null;
      }
      func(params).then((res) => {
        $app.$message.success(
          isUpdate.value ? "业务应用编辑成功" : "业务应用新建成功"
        );
        handleClose();
        emits("reload");
      });
    }
  });
};

/* 校验 */
const { validateNameRule, validateCodeRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const type = ref<any>(-1);
const defaultForm = {
  id: undefined,
  appId: "",
  name: "",
  type: "",
  description: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const validateAppId = (rule : any, value: any, callback: any, msg: string,) => {
      if (value === ''&&type.value==-2) {
        return callback(new Error(msg))
      } else {
        callback()
      }
    }
const rules = reactive<FormRules>({
  // appId: [{ required: true, message: "请选择AppId", trigger: "blur" }],
  appId: [{trigger: "blur", validator: (rule: any, value: any, callback: any) => validateAppId(rule, value, callback, "请输入appId") }],
  name: [{ required: true, message: "请输入业务名称", trigger: "blur" }],
  type: [{ required: true, message: "请输入类型", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  appId: {
    label: "AppId",
    type: "slot",
    slotName: "appIdSlot",
    options: [
      { value: -1, label: "系统生成" },
      { value: -2, label: "人工定义" },
    ],
    attrs: {
      disabled: computed(() => isUpdate.value),
    },
  },
  name: {
    label: "业务名称",
    type: "select",
    options: computed(() => appList.value),
    attrs: {
      allowCreate: true,
    }
  },
  type: {
    label: "类型",
    type: "select",
    options: [],
    attrs: {
      allowCreate: true,
    }
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});

// 切换AppId
const switchType = () => {
  formRef.value.ruleFormRef.clearValidate("appId");
};

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  });
};
//获取类型列表
const getTypeList = async () => {
  const labels = await getTypes();
  formItems.value.type.options = labels.data.map((item) => {
    return {
      label: item,
      value: item
    }
  });
}
//初始化
onMounted(async () => {
  getTypeList();
});
defineExpose({ openDialog });
</script>
  
  <style lang="scss">
</style>
  