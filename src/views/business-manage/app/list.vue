<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="component-list">
      <table-page
        ref="myTableRef"
        :query="query"
        :columns="columns"
        :operations="operations"
        :loadDataApi="getAppList"
        operationAuth="/base/#/app/edit"
        :transformListData="transformListData"
        @operation="handleOperation"
      >
        <!-- 查询 + 操作插槽内容 -->
        <template #query>
          <div class="flexBetween">
            <my-query
              :refresh-btn="{ show: true }"
              :queryItems="queryItems"
              @search="events.searchQuery"
              @reset="events.reset"
            />
            <my-operation
              style="
                margin-bottom: 12px;
                display: flex;
                justify-content: flex-end;
              "
            >
              <template #buttonGroup>
                <my-button
                  type="add"
                  @click="events.add"
                  operationAuth="/base/#/app/edit"
                  >新建业务应用</my-button
                >
              </template>
            </my-operation>
          </div>
        </template>
        <template #convertappSecret="{ row }">
          <div>
            <span>
              {{ events.maskAppSecret(row.appSecret) }}
              <el-link
                class="icon-copy"
                type="primary"
                :underline="false"
                @click="copyText(row.appSecret)"
                :icon="CopyDocument"
              />
            </span>
          </div>
        </template>
      </table-page>
    </div>
    <AddDialog ref="addRef" @reload="loadList" />
    <RecordDialog
      ref="productRef"
      :title="drawerConfig.title"
      :columns="drawerConfig.columns"
      :query="drawerConfig.query"
      :loadDataApi="drawerConfig.api"
    />
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import AddDialog from "./add.vue";
import RecordDialog from "./recordList.vue";
import { copyText } from "@/utils/helpers";
import { CopyDocument } from "@element-plus/icons-vue";
import {
  getAppList,
  deleteApp,
  enableOrDisable,
  getAppProductList,
  getTypes,
  editEmail,
} from "@/api/app.ts";
import { toggleProductAvailable } from "@/api/scene.ts";

const { t } = useI18n();
const { $app, proxy, $router } = useCtx();
const routeName = "app";

/* 查询 */
const query = ref<any>({});
const queryItems = ref<any>({
  search: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: {
      placeholder: "Appid",
    },
  },
  type: {
    label: "类型",
    type: "select",
    options: [],
  },
});

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "name",
    label: "业务名称",
    custom: "link",
    width: 200,
    customRender: {
      click: (record: any) => {
        events.toVersion(record);
      },
    },
  },
  { prop: "appId", label: "AppId", width: 120, withCopy: true },
  {
    prop: "appSecret",
    label: "AppSecret",
    width: 140,
    slotName: "convertappSecret",
  },
  { prop: "description", label: "描述", minWidth: 120 },
  {
    prop: "emails",
    label: "邮件列表",
    minWidth: 230,
    custom: "editInput",
    withCopy: true,
    customRender: {
      blur: (val: any, record: any) => {
        editEmail(record.id, val).then((res) => {
          loadList();
          $app.$message.success("邮件编辑成功");
        });
      },
    },
  },
  { prop: "type", label: "类型", width: 120 },
  {
    prop: "enabled",
    label: "是否启用",
    width: 112,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app
          .$confirm({
            title: `确定${record.enabled ? "停用" : "启用"}业务应用“${
              record.name
            }”吗？`,
          })
          .then(() => {
            enableOrDisable(record.id, !record.enabled).then((res) => {
              loadList();
              $app.$message.success(
                record.enabled ? "业务应用停用成功" : "业务应用启用成功"
              );
            });
          });
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDate", label: "更新时间", width: 170 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDate", label: "创建时间", width: 170 },
  { prop: "operation", label: "操作", width: 190, fixed: "right" },
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  // { type: "products", label: '关联产品列表'},
  { type: "delete", label: t("btn.delete"), btnType: "danger" },
  { type: "record", label: "操作记录" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    proxy.$refs.addRef?.openDialog("edit", record);
  } else {
    typeof events[type](record) == "function" && events[type](record);
  }
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}`;
    x.enabledRender = x.enabled ? "启用" : "停用";
    x.createdDate = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDate = timeC.format(
      x.lastModifiedDate,
      "YYYY-MM-DD hh:mm:ss"
    );
    return x;
  });
};

// 产品列表 && 操作记录
const drawerConfig = reactive({
  title: "",
  query: {},
  api: "",
  columns: [],
});

/* events */
const events = reactive({
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "业务应用", n: record.name }),
      })
      .then(() => {
        deleteApp(record.id).then(() => {
          loadList();
          $app.$message.success("业务应用删除成功");
        });
      });
  },
  reset: (obj: any) => {},
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  toVersion: (record: any) => {
    $router.push({
      name: `${routeName}::version`,
      params: { id: record.id },
      query: { metaLabel: record.name },
    });
  },
  products: (record: any) => {
    drawerConfig.api = getAppProductList;
    drawerConfig.columns = [
      { prop: "productName", label: "产品名称" },
      { prop: "productCode", label: "产品编码" },
    ];
    drawerConfig.title = "关联产品列表";
    drawerConfig.query = { id: record.id };
    proxy.$refs.productRef?.openDialog(record.id);
  },
  record: (record: any) => {
    $app.$message.success(`待开发`);
    // drawerConfig.api = getProductList
    // drawerConfig.columns = [
    //   { prop: "createdDate", label: "操作时间", width: 180 },
    //   { prop: "type", label: "类型" },
    //   { prop: 'description', label: '描述', showOverflowTooltip: false, width: 150 },
    //   { prop: "createdBy", label: "操作人", width: 150 },
    // ]
    // drawerConfig.title = '操作记录'
    // proxy.$refs.productRef?.openDialog(record.id)
  },
  maskAppSecret: (appSecret: any) => {
    // 中间6位字符
    if (!appSecret) {
      return "";
    }
    const start = appSecret.substring(0, 3); // 显示前3位
    const end = appSecret.substring(appSecret.length - 3); // 显示后3位
    const masked = start + "******" + end; // 中间6位用星号替换
    return masked;
  },
});

/* 列表刷新 */
const loadList = () => {
  getTypeList();
  proxy.$refs.myTableRef.loadData();
};
//获取类型列表
const getTypeList = async () => {
  const labels = await getTypes();
  queryItems.value.type.options = labels.data.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
};
//初始化
onMounted(async () => {
  getTypeList();
});
</script>

<style lang="scss" scoped></style>
