<template>
  <my-drawer
      v-if="dialogVisible"
      class="component-add"
      v-model="dialogVisible"
      :title="title"
      :showConfirm="false"
      @close="handleClose">
    <table-page
      ref="myTableRef"
      :query="query"
      :columns="columns"
      :loadDataApi="loadDataApi"
      :transformListData="transformListData"
    />
  </my-drawer>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { timeC } from 'turing-plugin'

const props = defineProps<{
  title: string
  columns: any[]
  loadDataApi: any,
  query: any
}>()

// 弹窗相关
const dialogVisible = ref<boolean>(false)
const handleClose = () => {
  dialogVisible.value = false
}

const openDialog = (params: any) => {
  dialogVisible.value = true
}

// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDate = timeC.format(x.createdDate, 'YYYY-MM-DD hh:mm:ss')
    return x;
  });
};

defineExpose({ openDialog });
</script>

<style lang="scss">
</style>
