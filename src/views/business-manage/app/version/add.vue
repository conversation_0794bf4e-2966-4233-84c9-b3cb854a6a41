<template>
  <my-drawer
    class="component-add"
    v-model="dialogVisible"
    title="关联产品"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
    >
      <template #requestLimit>
        <el-radio-group v-model="type" @change="switchType">
          <el-radio :label="-1">无限量</el-radio>
          <el-radio :label="-2">自定义</el-radio>
        </el-radio-group>
        <el-input-number
          v-if="type == -2"
          :min="0"
          :max="2000000000"
          v-model="ruleForm.requestLimit"
          style="width: 200px; margin-left: 8px"
          placeholder="请输入调用量限制"
        />
      </template>
      <template #qps>
        <el-input-number
          :min="0"
          v-model="ruleForm.qpsLimit"
          style="width: 200px"
          placeholder="请输入QPS使用限制"
        />
        <el-select
          v-model="ruleForm.qpsUnit"
          style="width: 100px; margin-left: 8px"
        >
          <el-option
            v-for="item in unitMap"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useValidate from "@/hooks/validate";
import { getOpenedProductList } from "@/api/product.ts";
import { getMetaRegionList } from "@/api/meta-region.ts";
import { createProduct, editProduct } from "@/api/app.ts";
import { getTemplateList } from "@/api/template.ts";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { unitMap } from "./config.ts";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload"]);
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 获取产品列表
const productList = ref([]);
const getProductListApi = async () => {
  getOpenedProductList().then((res) => {
    if (res?.data?.length) {
      productList.value = res.data.map((x: any) => ({
        label: x.name,
        value: x.id,
        code: x.code,
      }));
    }
  });
};
getProductListApi();

//获取区域列表
const metaRegionList = ref([]);
const getMetaRegionListApi = async () => {
  getMetaRegionList().then((res) => {
    if (res?.content?.length) {
      metaRegionList.value = res.content?.map((x: any) => ({
        label: x.name,
        code: x.code,
        value: x.code,
      }));
    }
  });
};

getMetaRegionListApi();

// 切换调用量限制
const type = ref(-1);
const switchType = (type: number) => {
  ruleForm.value.requestLimit = type == -1 ? -1 : "";
};

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      // 若为空白模板，去除templateId字段
      const params = cloneDeep(ruleForm.value);
      params.authDeadline = new Date(params.authDeadline).getTime();
      if (isUpdate.value) {
        editProduct(params, params.id).then((res) => {
          $app.$message.success("关联产品编辑成功");
          dialogVisible.value = false;
          emits("reload");
        });
      } else {
        createProduct(params).then((res) => {
          $app.$message.success("关联产品新建成功");
          dialogVisible.value = false;
          emits("reload");
        });
      }
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  productId: "",
  productCode: "",
  metaRegionCode: "",
  applicationId: "",
  concurrentQuota: "",
  qpsLimit: 60,
  qpsUnit: 3,
  requestLimit: -1,
  authDeadline: "",
  appId: "",
  id: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      trigger: "blur",
      validator: (rule: any, value: any, callback: any) =>
        validateNameRule(rule, value, callback, "请输入策略名称"),
    },
  ],
  description: [{ required: true, message: "请输入策略描述", trigger: "blur" }],
  templateId: [
    { required: true, message: "请选择创建方式", trigger: "change" },
  ],
});
// 表单项
const formItems = ref<any>({
  productId: {
    label: "产品名称",
    type: "select",
    options: computed(() => productList.value),
    events: {
      change: () => {
        nextTick(() => {
          ruleForm.value.productCode = productList.value?.find(
            (item) => item.value == ruleForm.value.productId
          )?.code;
        });
      },
    },
    disabled: () => isUpdate.value,
  },
  productCode: {
    label: "产品编码",
    type: "input",
    disabled: () => true,
  },
  metaRegionCode: {
    label: "环境配置",
    type: "select",
    options: computed(() => metaRegionList.value),
    events: {},
    disabled: () => isUpdate.value,
  },
  concurrentQuota: {
    label: "并发配额",
    type: "inputNumber",
    attrs: {
      maxlength: 5,
      min: 0,
      max: 2000000000,
    },
  },
  qpsLimit: {
    label: "QPS使用限制",
    type: "slot",
    slotName: "qps",
  },
  requestLimit: {
    label: "调用量限制",
    type: "slot",
    slotName: "requestLimit",
  },
  authDeadline: {
    label: "授权截止时间",
    type: "datetime",
    attrs: { maxlength: 30 },
    disabledDate: (time: any) => {
      return time.getTime() < Date.now();
    },
  },
});

// 获取编排模板列表
const templateList = ref([]);
const getTemplateListApi = async () => {
  getTemplateList({ size: 100 }).then((res) => {
    if (res?.content?.length) {
      templateList.value = res?.content?.map((x: any) => ({
        label: x.name,
        value: x.id,
      }));
    }
    templateList.value.unshift({
      label: "空白模板",
      value: "0",
    });
  });
};
getTemplateListApi();

const openDialog = async (type1: string, row: any) => {
  // 1.打开弹窗
  formType.value = type1;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type1 === "edit") {
      if (row.requestLimit !== -1) {
        type.value = -2;
      }
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign({}, defaultForm, { applicationId: row });
    }
  });
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
