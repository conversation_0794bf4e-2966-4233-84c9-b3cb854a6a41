<template>
  <my-drawer class="asset-online-add" v-model="dialogVisible" :width="700" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :labelWidth="110" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" />
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from "vue";
import { assign } from "lodash";
import type { FormRules } from "element-plus";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as dataAssetApi from "@/api/data-asset";
import * as util from "@/utils/common";

const props = defineProps({
  metaRegionList: { type: Array, default: () => [] },
  idxBucketList: { type: Array, default: () => [] },
});

const emits = defineEmits(["reload"]);
const { $app } = useCtx();

const dialogVisible = ref(false);
const formRef = ref();
const formType = ref("add");

const dialogTitle = computed(() => `${formType.value === "add" ? "新建" : "编辑"}上线计划`);
const isUpdate = computed(() => formType.value === "edit");

const ruleForm = ref({
  id: undefined,
  name: "",
  description: "",
  bucketCode: "",
  targetRegion: "",
  clusterId: "",
});

const rules: FormRules = {
  name: [{ required: true, message: "请输入上线计划名称", trigger: "blur" }],
  bucketCode: [{ required: true, message: "请选择索引库", trigger: "change" }],
  targetRegion: [{ required: true, message: "请选择上线环境", trigger: "change" }],
  clusterId: [{ required: true, message: "集群不能为空", trigger: "change" }],
};

const formItems = computed(() => ({
  name: { label: "上线计划名称", type: "input" },
  description: { label: "上线计划描述", type: "textarea", attrs: { maxlength: 255 } },
  targetRegion: {
    label: "上线环境",
    type: "radio",
    options: props.metaRegionList,
    events: { change: (val: string) => regionChange(val) },
  },
  bucketCode: {
    label: "索引库",
    type: "select",
    options: props.idxBucketList,
    attrs: { clearable: false },
    disabled: () => dataC.isEmpty(ruleForm.value.targetRegion),
  },
  clusterId: {
    label: "集群",
    type: "select",
    options: clusterOptions.value,
    attrs: { filterable: false, clearable: false },
    disabled: () => dataC.isEmpty(ruleForm.value.targetRegion),
  },
}));

const clusterOptions = ref<any[]>([]);

const regionChange = async (region: string, clear = true) => {
  const result = await dataAssetApi.getRegionEsInfoList(region);
  clusterOptions.value = result.data
    .filter((item) => item.type === "full")
    .map((item) => ({
      label: `${item.name}(${util.getEsInfoRender(item)})`,
      value: item.id,
    }));
  if (clear) ruleForm.value.clusterId = "";
};

const handleClose = () => {
  formRef.value?.resetForm();
  dialogVisible.value = false;
};

const handleConfirm = () => {
  formRef.value?.submitForm((valid: boolean) => {
    if (!valid) return;

    dataAssetApi.saveBucketOnline(ruleForm.value).then(() => {
      $app.$message.success(`${isUpdate.value ? "编辑" : "新建"}成功`);
      dialogVisible.value = false;
      emits("reload");
    });
  });
};

const openDialog = async (type: string, row?: any) => {
  formType.value = type;
  dialogVisible.value = true;
  await nextTick();
  ruleForm.value = assign({}, ruleForm.value, row);
  formRef.value?.resetForm();
  if (isUpdate.value) regionChange(ruleForm.value.targetRegion, false);
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
