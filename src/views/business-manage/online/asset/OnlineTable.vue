<template>
  <div class="asset-idx-list">
    <OnlineEdit ref="onlineEditRef" :metaRegionList="metaRegionList" :idxBucketList="idxBucketList" @reload="loadList" />
    <TimeTask ref="timeTaskRef" @reload="loadList" />
    <FailDetail ref="failDetailRef" />
    <HistoryDetail ref="historyDetailRef" @fail-detail="events.failDetail" :operationAuth="operationAuth" />
    <table-page
      ref="myTableRef"
      name="idxDB"
      :query="query"
      :columns="columns"
      :operations="operations"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      @operation="handleOperation"
      operationAuth="/base/#/online/edit"
    >
      <template #query>
        <div class="flexBetween">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation style="margin-bottom: 12px; display: flex; justify-content: flex-end">
            <template #buttonGroup>
              <my-button type="export" @click="events.export" operationAuth="/base/#/online/edit">导出</my-button>
              <my-button type="add" @click="events.add" operationAuth="/base/#/online/edit">新建上线计划</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #task="scope">
        <task-process :taskInfo="scope.row.taskInfo" @reload="loadList" @fail-detail="events.failDetail" operationAuth="/base/#/online/edit" />
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { dataC, timeC } from "turing-plugin";
import OnlineEdit from "./OnlineEdit.vue";
import TimeTask from "./task/TimeTask.vue";
import TaskProcess from "./task/TaskProcess.vue";
import FailDetail from "./task/FailDetail.vue";
import HistoryDetail from "./task/HistoryDetail.vue";
import useCtx from "@/hooks/useCtx";
import * as dataAssetApi from "@/api/data-asset";
import * as util from "@/utils/common";
import IntervalClient from "@/utils/interval-client";

const { $app, proxy, $router, $auth } = useCtx();

// Refs
const metaRegionList = ref<any[]>([]);
const idxBucketList = ref<any[]>([]);
const myTableRef = ref();
const timeTaskRef = ref();
const failDetailRef = ref();
const historyDetailRef = ref();
const intervalClinet = ref<IntervalClient | null>(null);
const query = ref<any>({});

// Methods
const loadList = () => myTableRef.value?.loadData();

const loadListData = async (data: any) => {
  if (!metaRegionList.value.length) {
    const res = await dataAssetApi.getMetaRegionList();
    metaRegionList.value = res.content.filter((item) => item.envType === 2).map((item) => ({ label: item.name, value: item.code }));
  }

  const result = await dataAssetApi.getBucketOnlineListPage(data);
  setupTaskMonitoring(result.content);
  return result;
};

const setupTaskMonitoring = (tableData: any[]) => {
  intervalClinet.value?.disconnect();
  if (!tableData?.length) return;

  intervalClinet.value = new IntervalClient(5000, true);
  intervalClinet.value.onHandler(updateTaskProgress, tableData).connect();
};

const updateTaskProgress = async (tableData: any[]) => {
  const bucketCodeList = tableData.map((x) => x.code);
  if (!bucketCodeList?.length) return;

  const result = await dataAssetApi.getTaskProcessList(bucketCodeList);
  if (!result.data?.length) return;

  const updatedData = tableData.map((x) => {
    const task = result.data.find((t) => t.bucketCode === x.code);
    if (task?.bucketCode) {
      x.taskInfo = { ...task.dto, bucketName: x.name };
    }
    return x;
  });

  myTableRef.value?.setTableData(updatedData);

  if (!result.data.some((item) => item.dto.lastExecution?.status === 1)) {
    if (intervalClinet.value?.getRetryTime() < 3) {
      console.log(`没有执行中的任务,轮询将会在${3 - intervalClinet.value?.getRetryTime()}次后停止`);
      intervalClinet.value?.plusRetryTime();
    } else {
      console.log("没有执行中的任务,轮询已停止");
      intervalClinet.value?.disconnect();
    }
  }
};

const transformListData = (data: any[]) => {
  return data.map((x) => ({
    ...x,
    targetRegionRender: metaRegionList.value.find((r) => r.value === x.targetRegion)?.label || "",
    lastModifiedDateRender: timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss"),
    createdDateRender: timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss"),
  }));
};

const handleOperation = ({ type, record }: any) => events[type]?.(record);

// Query Items
const queryItems = reactive({
  search: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: { placeholder: "名称" },
  },
  enabled: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: [
      { value: true, label: "已启用" },
      { value: false, label: "已禁用" },
    ],
    attrs: { placeholder: "启用状态" },
  },
  targetRegion: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: metaRegionList,
    attrs: { placeholder: "上线环境" },
  },
  status: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: [
      { value: 1, label: "未归档" },
      { value: 3, label: "已归档" },
    ],
    attrs: { placeholder: "归档状态" },
  },
});

// Table Config
const columns = ref([
  {
    prop: "name",
    label: "名称",
    width: 180,
    custom: "editLink",
    blod: true,
    customRender: {
      linkClick: (record: any) => events.preview(record),
      linkDisabled: (record: any) => !record.idInRegion,
      btnClick: (record: any) => events.openDescriptionEditWindow(record),
      btnDisabled: () => true,
    },
  },
  { prop: "idxBucketName", label: "索引库桶名称", width: 200, sortable: false },
  { prop: "bucketCode", label: "索引库桶编码", width: 260, withCopy: true, sortable: false },
  {
    prop: "status",
    label: "状态",
    width: 100,
    sortable: false,
    custom: "status",
    customRender: { options: { 3: { type: "warning", name: "已归档" } } },
  },
  {
    prop: "enabled",
    label: "启用状态",
    width: 110,
    custom: "switch",
    customRender: {
      attrs: { activeValue: true, inactiveValue: false, size: "small" },
      beforeChange: (record: any) => handleStatusChange(record),
    },
  },
  { prop: "task", label: "任务进度", slotName: "task", width: 300, showOverflowTooltip: false, sortable: false },
  { prop: "targetRegionRender", label: "上线环境", width: 130 },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    custom: "editButton",
    customRender: {
      btnClick: (record: any) => events.openDescriptionEditWindow(record),
      btnDisabled: (record: any) => true,
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "operation", label: "操作", width: 170, fixed: "right" },
]);

const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.status === 3,
    disabledTips: (record: any) => (record.status === 3 ? "已归档，不可编辑" : "不可编辑"),
  },
  {
    type: "sync",
    label: "同步",
    disabled: (record: any) => record.enabled || record.status === 3,
    disabledTips: (record: any) => {
      if (record.enabled) return "已启用，不可同步";
      if (record.status === 3) return "已归档，不可同步";
      return "不可同步";
    },
  },
  {
    type: "delete",
    label: "归档上线计划",
    btnType: "danger",
    collapsed: true,
    disabled: (record: any) => record.enabled || record.status === 3,
    disabledTips: (record: any) => {
      if (record.enabled) return "已启用，不可归档";
      if (record.status === 3) return "已归档，不可再归档";
      return "不可归档";
    },
  },
  {
    type: "idxDel",
    label: "删除索引库",
    btnType: "danger",
    collapsed: true,
    disabled: (record: any) => record.enabled || record.status === 3,
    disabledTips: (record: any) => {
      if (record.enabled) return "已启用，不可删除";
      if (record.status === 3) return "已归档，不可删除";
      return "不可删除";
    },
  },
  {
    type: "schedule",
    label: "定时执行",
    btnType: "primary",
    collapsed: true,
    disabled: (record: any) => !record.idInRegion,
    disabledTips: (record: any) => {
      if (!record.idInRegion) return "未同步，不可操作";
      return "不可操作";
    },
  },
  {
    type: "history",
    label: "历史任务",
    btnType: "primary",
    collapsed: true,
    disabled: (record: any) => !record.idInRegion,
    disabledTips: (record: any) => {
      if (!record.idInRegion) return "未同步，不可操作";
      return "不可操作";
    },
  },
];

const handleStatusChange = (record: any) => {
  return new Promise((resolve, reject) => {
    const action = record.enabled ? "禁用" : "启用";
    const api = record.enabled ? dataAssetApi.disabledBucketOnline : dataAssetApi.enabledBucketOnline;

    $app
      .$confirm({ title: `确定${action} ${record.name}?` })
      .then(() => {
        api(record.id).then(() => {
          loadList();
          resolve(true);
          $app.$message.success(`${action} ${record.name} 成功`);
        });
      })
      .catch(reject);
  });
};

// Events
const events = reactive({
  search: (obj: any) => Object.assign(query.value, obj),
  reset: () => {},
  preview: (record: any) =>
    $router.push({
      name: `online::asset-preview`,
      query: {
        id: record.id,
        kind: "IDX",
        bucket: record.bucketCode,
        index: record.idInRegion,
        region: record.targetRegion,
        metaLabel: [record.name],
      },
    }),
  add: () => proxy.$refs.onlineEditRef?.openDialog("add"),
  edit: (record: any) => proxy.$refs.onlineEditRef?.openDialog("edit", record),
  sync: (record: any) => {
    $app
      .$confirm({ title: `您确认要同步“${record.name}”数据吗？` })
      .then(() => dataAssetApi.syncBucketOnline(record.id))
      .then(() => {
        $app.$message.success("已添加同步任务！");
        loadList();
      });
  },
  delete: (record: any) => {
    $app
      .$confirm({ title: `归档后索引库将无法使用，是否确认将“${record.name}”归档吗？` })
      .then(() => dataAssetApi.deleteBucketOnline(record.id))
      .then(() => {
        $app.$message.success("归档成功!");
        loadList();
      });
  },
  idxDel: (record: any) => {
    $app
      .$confirm({ title: `您确认要清除“${record.name}”的已上线数据吗？` })
      .then(() => dataAssetApi.deleteBucketOnlineIdx(record.id))
      .then(() => {
        $app.$message.success("清除数据成功!");
        loadList();
      });
  },
  export: () => dataAssetApi.exportBucketOnline().then((result) => util.downloadFile(result, `索引库.xlsx`)),
  schedule: (record: any) => timeTaskRef.value?.openWindow(record),
  history: (record: any) => historyDetailRef.value?.openWindow(false, record),
  failDetail: (taskInfo: any, executionInfo: any) => failDetailRef.value?.openWindow(taskInfo, executionInfo),
  openDescriptionEditWindow: (record: any) => {},
});

// Lifecycle
onMounted(async () => {
  const res = await dataAssetApi.getBucketList({ kindCodes: "IDX", enabled: true });
  idxBucketList.value = res.data.map((item) => ({
    label: `${item.name}(${item.enName})`,
    value: item.code,
  }));
});

onUnmounted(() => intervalClinet.value?.disconnect());

defineExpose({ loadList });
</script>

<style lang="scss">
.asset-idx-list {
  height: 100%;
}
</style>
