<template>
  <div class="data-cell-table">
    <PreviewDetail ref="previewDetailRef" v-if="showPreviewDetail" @close="showPreviewDetail = false" :operationAuth="operationAuth" />
    <MyTable
      ref="myTableRef"
      :name="`cell-table-${auditStatus}`"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformQuery="transformQuery"
      :transformListData="transformListData"
      :withSort="true"
      :withSelect="true"
      :operations="operations"
      @operation="handleOperation"
      :operationAuth="operationAuth"
      :loadImmediately="false"
    >
      <template #query>
        <my-query
          ref="myQueryRef"
          :name="`cell-query-${auditStatus}`"
          :queryItems="queryItems"
          :refreshBtn="{ show: true }"
          @search="events.search"
          @reset="events.reset"
        />
      </template>
      <template #pagination>
        <div class="pagination-info">
          <span style="white-space: nowrap">筛选后数据总量: {{ util.formatNumber(modelValue.dataItemFilterCount) }} </span>
        </div>
      </template>
      <template #_id="scope">
        <div class="flex">
          <el-button link type="primary" @click="events.previewData(scope.row)" class="btn-title">
            {{ scope.row._id }}
          </el-button>
          <el-icon class="icon-copy" @click="copyText(scope.row._id)">
            <CopyDocument />
          </el-icon>
        </div>
      </template>
    </MyTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, cloneDeep, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import MyTable from "@/views/common/preview/MyTable.vue";
import PreviewDetail from "./PreviewDetail.vue";
import useCtx from "@/hooks/useCtx";
import * as dataAssetApi from "@/api/data-asset";
import * as util from "@/utils/common";

const { $app, proxy, $router, $auth } = useCtx();

const emit = defineEmits(["update-count"]);

const props = defineProps({
  operationAuth: { type: String },
  auditStatus: { type: Number },
  schema: { type: Object },
});

const myTableRef = ref(null);
const previewDetailRef = ref(null);
const myQueryRef = ref(null);
const showPreviewDetail = ref(false);
const rowExample = ref({});
const query = ref({});

const modelValue = reactive({
  dataItemCount: "计算中",
  dataItemFilterCount: "计算中",
});

const queryItems = ref({
  ids: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    attrs: { placeholder: "请输入_id进行搜索" },
  },
  domains: {
    type: "domain",
    label: "",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    attrs: { placeholder: "请输入domain进行搜索" },
  },
  urls: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    attrs: { placeholder: "请输入url进行搜索" },
  },
});

const selectAll = computed(() => myTableRef.value?.getSelectAll() || false);

const columns = computed(() => {
  const list = [
    { prop: "_id", label: "_id", width: 220, slotName: "_id", fixed: "left" },
    { prop: "_uts", label: "更新时间", width: 170 },
  ];

  keys(rowExample.value).forEach((key) => {
    if (key === "_ref") {
      list.push({ prop: "_ref", label: "_ref", width: 220, withCopy: "left" });
    } else if (key !== "selected" && key !== "_id" && !key.endsWith("Render")) {
      const isTime = key.endsWith("_ts");
      const obj = dataC.getItemByValue(props.schema?.fields || [], key, "value");
      if (!key.startsWith("_") || obj.name) {
        list.push({
          prop: isTime ? `${key}Render` : key,
          label: obj.name || key,
          minWidth: 180,
        });
      }
    }
  });

  if (!dataC.isEmpty(props.schema.operations)) {
    list.push({ prop: "operation", label: "操作", width: 110, fixed: "right" });
  }

  return list;
});

const operations = computed(() =>
  props.schema?.operations?.map((item) => ({
    type: item.value,
    label: item.name,
    collapsed: !["correct", "delete"].includes(item.value),
    btnType: ["delete"].includes(item.value) ? "danger" : "primary",
  }))
);

// 方法
const loadListData = async (data: any) => {
  modelValue.dataItemFilterCount = "计算中";
  const result = await dataAssetApi.getCellListPage(data);
  getDataItemFilterCount(data);
  return result.data;
};

const transformQuery = (data: any) => ({
  auditStatus: props.auditStatus,
  bucketCode: $router.currentRoute.value.query.bucket,
  regionCode: $router.currentRoute.value.query.region,
  ...data,
});

const transformListData = (data: any) => {
  rowExample.value = data[0] || {};
  return data.map((x: any) => {
    keys(x).forEach((key) => {
      if (key.endsWith("_ts")) {
        x[`${key}Render`] = timeC.format(x[key], "YYYY-MM-DD hh:mm:ss");
      }
    });
    return x;
  });
};

const getDataItemFilterCount = (data: any) => {
  dataAssetApi.getCellCount(data).then((result) => {
    modelValue.dataItemFilterCount = result.data;
    if (modelValue.dataItemCount === "计算中") {
      modelValue.dataItemCount = result.data;
      emit("update-count", props.auditStatus, result.data);
    }
  });
};

const loadList = () => {
  myTableRef.value?.loadData();
};

const handleOperation = (data: any) => {
  const { type, record } = data;
  const operationMap = {
    delete: events.delete,
  };

  if (operationMap[type]) {
    operationMap[type](record);
  } else {
  }
};

// 事件
const events = {
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {
    Object.keys(obj).forEach((key) => {
      queryItems.value[key].modelValue = obj[key].defaultValue;
    });
    modelValue.dataItemCount = "计算中";
    emit("update-count", props.auditStatus, "计算中");
  },
  previewData: (record: any) => {
    showPreviewDetail.value = true;
    nextTick(() => {
      previewDetailRef.value?.refreshList(myTableRef.value?.getTableData(), myTableRef.value?.getPage(), {
        auditStatus: props.auditStatus,
        kindCode: $router.currentRoute.value.query.kind,
        bucketCode: $router.currentRoute.value.query.bucket,
        regionCode: $router.currentRoute.value.query.region,
        indexId: $router.currentRoute.value.query.index,
        schema: props.schema,
        dataItemFilterCount: modelValue.dataItemFilterCount,
      });
      previewDetailRef.value?.refreshContent(record);
    });
  },
  delete: async (record: any) => {
    try {
      await $app.$confirm({
        title: `确认要删除【${record.title || record.fileName || record._id}】吗?`,
      });
      await dataAssetApi.deleteCell(false, { bucketCode: $router.currentRoute.value.query.bucket }, [record._id]);
      loadList();
      $app.$message.success(`删除成功`);
    } catch {
      // 用户取消或操作失败
    }
  },
};

// 初始化
onMounted(() => {});

const getSelectTotal = () => (selectAll.value ? modelValue.dataItemFilterCount : myTableRef.value?.getSelectTotal() || 0);

// 公共函数：获取选中的ID列表
const getSelectedIds = () => {
  return selectAll.value
    ? []
    : myTableRef.value
        .getTableData()
        .filter((item) => item.selected)
        .map((item) => item._id);
};

// 公共函数：执行批量操作
const executeBatchOperation = async (operation) => {
  try {
    const { confirmMessage, apiMethod, params, successMessage } = operation;
    await $app.$confirm({ title: confirmMessage });
    await apiMethod(getOperateBatchParams(), ...(params || []));
    loadList();
    $app.$message.success(successMessage);
  } catch (error) {}
};

const approveBatch = () =>
  executeBatchOperation({
    confirmMessage: "确认要审核通过吗?",
    apiMethod: dataAssetApi.auditCell,
    params: [1],
    successMessage: "审核通过成功",
  });

const rejectBatch = () =>
  executeBatchOperation({
    confirmMessage: "确认要审核不通过吗?",
    apiMethod: dataAssetApi.auditCell,
    params: [0],
    successMessage: "审核不通过成功",
  });

const deleteBatch = () =>
  executeBatchOperation({
    confirmMessage: "确认要批量删除吗?",
    apiMethod: dataAssetApi.deleteCell,
    successMessage: "批量删除成功",
  });

const download = async () => {
  try {
    await $app.$confirm({ title: `确认要导出吗?` });
    const result = await dataAssetApi.exportCell(getOperateBatchParams());
    util.downloadFile(result, `数据单元数据.xlsx`);
  } catch {
    // 用户取消或操作失败
  }
};

const getOperateBatchParams = () => {
  const queryParams = {
    ...query.value,
    ...{ ids: !dataC.isEmpty(query.value.ids) ? query.value.ids.split(",") : [] }, //把查询条件里的ids从字符串修改为数组
    ...(!selectAll.value && getSelectedIds().length > 0 && { ids: getSelectedIds() }), //如果不是选择全部，则将批量选择的id传过去
    ...{ domains: !dataC.isEmpty(query.value.domains) ? query.value.domains.split(",") : [] }, //把查询条件里的domains从字符串修改为数组
    ...{ urls: !dataC.isEmpty(query.value.urls) ? query.value.urls.split(",") : [] }, //把查询条件里的ids从字符串修改为数组
    auditStatus: props.auditStatus,
    bucketCode: $router.currentRoute.value.query.bucket,
    regionCode: $router.currentRoute.value.query.region,
  };
  return queryParams;
};

defineExpose({ loadList, getSelectTotal, approve: approveBatch, reject: rejectBatch, delete: deleteBatch, download });
</script>

<style lang="scss" scoped>
.data-cell-table {
  height: 100%;

  ::v-deep {
    .query-wrapper,
    .table-wrapper {
      padding: 0 !important;
    }
  }

  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .icon-copy {
    color: $primary-color;
    margin-left: 5px;
    cursor: pointer;
  }

  .pagination-info {
    font-size: 16px;
    font-weight: 700;
    margin-right: 15px;

    span + span {
      margin-left: 20px;
    }
  }
}
</style>
