<template>
  <page-wrapper :route-name="`${routerName}::preview-table::`">
    <div class="preview-table">
      <div class="left-wrap">
        <div class="info">
          <span class="bold">名称：</span><span>{{ onlineInfo.name }}</span>
        </div>
        <div class="info">
          <span class="bold">索引库桶名称：</span><span>{{ onlineInfo.idxBucketName }}</span>
        </div>
        <div class="info">
          <span class="bold">索引库桶编码：</span><span>{{ onlineInfo.bucketCode }}</span>
        </div>
        <div class="info">
          <span class="bold">状态：</span><span>{{ onlineInfo.status === 3 ? "已归档" : "-" }}</span>
        </div>
        <div class="info">
          <span class="bold">启用状态：</span><span>{{ onlineInfo.enabled ? "启用" : "禁用" }}</span>
        </div>
        <div class="info">
          <span class="bold">上线环境：</span><span>{{ metaRegionList?.find((r) => r.code === onlineInfo.targetRegion)?.name || "" }}</span>
        </div>
        <div class="info">
          <span class="bold">描述：</span><span>{{ onlineInfo.description }}</span>
        </div>
        <div class="info">
          <span class="bold">更新人：</span><span>{{ onlineInfo.lastModifiedBy }}</span>
        </div>
        <div class="info">
          <span class="bold">更新时间：</span><span>{{ timeC.format(onlineInfo.lastModifiedDate) }}</span>
        </div>
        <div class="info">
          <span class="bold">创建人：</span><span>{{ onlineInfo.createdBy }}</span>
        </div>
        <div class="info">
          <span class="bold">创建时间：</span><span>{{ timeC.format(onlineInfo.createdDate) }}</span>
        </div>
      </div>
      <el-divider direction="vertical" style="height: 100%; margin: 0" />
      <div class="content-wrap">
        <div class="center-wrap">
          <div class="custom-operation">
            <div class="flex button-group">
              <div v-if="selectTotal > 0" class="total-box">
                {{ $t("title.selected") }}
                <span>{{ selectTotal }}</span>
                {{ $t("title.item") }}
              </div>
              <my-button
                v-if="showApproveButton"
                type="primary"
                :icon="icons.Finished"
                @click="events.approve"
                :operationAuth="operationAuth"
                :disabled="selectTotal <= 0"
                >审核通过
              </my-button>
              <my-button
                v-if="showRejectButton"
                type="warning"
                :icon="icons.Remove"
                @click="events.reject"
                :operationAuth="operationAuth"
                :disabled="selectTotal <= 0"
                >审核不通过
              </my-button>
              <my-button
                v-if="showDeleteButton"
                type="danger"
                :icon="icons.Delete"
                @click="events.delete"
                :operationAuth="operationAuth"
                :disabled="selectTotal <= 0"
                >批量删除
              </my-button>
              <my-button type="export" @click="events.download">导出</my-button>
            </div>
          </div>
          <div class="table-wrap">
            <el-tabs v-model="modelValue.activeAuditStatus" @tab-change="events.activeAuditStatusChange">
              <el-tab-pane v-for="(item, index) in auditStatusList" :key="item.name" :label="item.label" :name="item.name">
                <template #label>
                  {{ item.label }}
                  ({{ modelValue.cellCount[item.name] }})
                  <el-tag v-if="item.name === AUDIT_PRE" type="primary" size="small" style="margin-left: 3px">先审</el-tag>
                  <el-tag v-if="item.name === AUDIT_POST" type="warning" size="small" style="margin-left: 3px">后审</el-tag>
                </template>
                <div class="audit-pane">
                  <CellTable
                    :ref="`tableRef-${item.name}`"
                    :auditStatus="item.name"
                    :schema="modelValue.schema"
                    @update-count="events.updateCount"
                    :operationAuth="operationAuth"
                  />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import { dataC, timeC } from "turing-plugin";
import CellTable from "./CellTable.vue";
import * as icons from "@element-plus/icons-vue";
import * as dataAssetApi from "@/api/data-asset";
import { copyText } from "@/utils/helpers";
import useCtx from "@/hooks/useCtx";

const { proxy, $router } = useCtx();

const routerName = $router.currentRoute.value.name;
const operationAuth = `/base/#/online/edit`;

// Refs
const onlineInfo = ref({});
const metaRegionList = ref([]);

const AUDIT_PRE = -2;
const AUDIT_POST = -1;
const AUDIT_APPROVE = 1;
const AUDIT_REJECT = 0;

// 数据模型
const modelValue = reactive({
  activeAuditStatus: AUDIT_PRE,
  schema: {} as Record<string, any>,
  cellCount: { "-2": 0, "-1": 0, "1": 0, "0": 0 },
});

// 审核标签页
const auditStatusList = computed(() => {
  const list = [];
  if (bucketData.value.auditType === 2) {
    list.push({ label: "待审核", name: AUDIT_PRE }); //先审核
  } else if (bucketData.value.auditType === 1) {
    list.push({ label: "待审核", name: AUDIT_POST }); //后审核
  }
  list.push({ label: "审核通过", name: AUDIT_APPROVE });
  list.push({ label: "审核未通过", name: AUDIT_REJECT });
  return list;
});

//选中的桶数据
const bucketData = ref({});

const showApproveButton = computed(
  () => modelValue.activeAuditStatus === AUDIT_POST || modelValue.activeAuditStatus === AUDIT_PRE || modelValue.activeAuditStatus === AUDIT_REJECT
);
const showRejectButton = computed(
  () => modelValue.activeAuditStatus === AUDIT_POST || modelValue.activeAuditStatus === AUDIT_PRE || modelValue.activeAuditStatus === AUDIT_APPROVE
);
const showDeleteButton = computed(() => false);
const forceUpdate = ref(0); // 用于强制触发计算属性更新
const selectTotal = computed(() => {
  forceUpdate.value; // 依赖 forceUpdate，但实际不影响逻辑
  return proxy.$refs[`tableRef-${modelValue.activeAuditStatus}`]?.[0]?.getSelectTotal() || 0;
});

// 事件对象
const events = {
  activeAuditStatusChange: (auditStatus: number) => {
    proxy.$refs[`tableRef-${auditStatus}`]?.[0]?.loadList();
    window.dispatchEvent(new Event("resize"));
  },
  updateCount: (auditStatus: number, newVal: number) => {
    modelValue.cellCount[auditStatus] = newVal;
  },
  approve: async () => {
    //审核通过操作
    await proxy.$refs[`tableRef-${modelValue.activeAuditStatus}`]?.[0]?.approve();
    //更新当前tab以及审核通过tab
    await Promise.all([updateCellCount(modelValue.activeAuditStatus), updateCellCount(AUDIT_APPROVE)]);
  },
  reject: async () => {
    //审核不通过操作
    await proxy.$refs[`tableRef-${modelValue.activeAuditStatus}`]?.[0]?.reject();
    //更新当前tab以及审核不通过tab
    await Promise.all([updateCellCount(modelValue.activeAuditStatus), updateCellCount(AUDIT_REJECT)]);
  },
  delete: async () => {
    //删除操作
    await proxy.$refs[`tableRef-${modelValue.activeAuditStatus}`]?.[0]?.delete();
    //更新当前tab数量
    await updateCellCount(modelValue.activeAuditStatus);
  },
  download: () => {
    proxy.$refs[`tableRef-${modelValue.activeAuditStatus}`]?.[0]?.download();
  },
};

//更新tab数据单元cunt
const updateCellCount = async (auditStatus) => {
  modelValue.cellCount[auditStatus] = "计算中";
  const data = {
    bucketCode: $router.currentRoute.value.query.bucket,
    regionCode: $router.currentRoute.value.query.region,
  };
  modelValue.cellCount[auditStatus] = (await dataAssetApi.getCellCount({ auditStatus, ...data })).data;
};

// 初始化
onMounted(async () => {
  onlineInfo.value = (await dataAssetApi.getBucketOnlineDetail($router.currentRoute.value.query.id)).data;
  modelValue.schema = await dataAssetApi.getCellSchema($router.currentRoute.value.query.bucket);
  metaRegionList.value = (await dataAssetApi.getMetaRegionList()).content.filter((item) => item.envType === 2);
  bucketData.value = (await dataAssetApi.getBucketList({ bucketCodes: $router.currentRoute.value.query.bucket })).data[0];
  await nextTick();
  //设置第一个tab激活
  modelValue.activeAuditStatus = auditStatusList.value[0].name;
  events.activeAuditStatusChange(modelValue.activeAuditStatus);
  //读取三个审核tab的数量
  auditStatusList.value.forEach((item) => {
    updateCellCount(item.name);
  });
  forceUpdate.value++;
});
</script>

<style lang="scss" scoped>
.preview-table {
  display: flex;
  height: 100%;

  .left-wrap {
    overflow: auto;
    width: 300px;
    flex-shrink: 0;
    padding: 10px;

    .info {
      margin: 0 10px 10px 10px;
      .bold {
        font-weight: bold;
      }
    }
  }

  .content-wrap {
    height: 100%;
    width: calc(100% - 301px);
    padding: 0 10px;

    .center-wrap {
      position: relative;
      height: calc(100% - 40px);
      width: 100%;
      display: flex;

      .custom-operation {
        position: absolute;
        top: 4px;
        right: 0;
        height: auto;
        z-index: 1;

        .button-group {
          margin-bottom: 12px;
        }
      }

      .total-box {
        font-size: 16px;
        color: $text-color-secondary;
        margin-right: 12px;
        white-space: nowrap;

        > span {
          color: $primary-color;
          margin: 0 6px;
        }
      }

      .table-wrap {
        height: 100%;
        width: calc(100% - 300px);
        flex-grow: 1;

        .audit-pane {
          height: calc(100vh - 145px);
          padding-top: 10px;
        }
      }
    }
  }
}
</style>
