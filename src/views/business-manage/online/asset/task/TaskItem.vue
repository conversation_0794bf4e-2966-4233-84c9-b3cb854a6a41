<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="12">
        <status-dot :type="dotType" :name="dotName"></status-dot>
      </el-col>
      <el-col :span="12">
        <span>操作人:{{ executionInfo.lastModifiedBy }}</span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <span>
          源桶: {{ executionInfo.sourceBucketsNames || executionInfo.sourceBuckets }}
          <el-icon class="icon-copy" @click="copyText(executionInfo.sourceBuckets)"><CopyDocument /> </el-icon>
        </span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <span>
          任务Id: {{ executionInfo.taskId }}<el-icon class="icon-copy" @click="copyText(executionInfo.taskId)"><CopyDocument /> </el-icon>
        </span>
      </el-col>
      <el-col :span="12">
        <span>
          执行记录Id: {{ executionInfo.id }}<el-icon class="icon-copy" @click="copyText(executionInfo.id)"><CopyDocument /> </el-icon>
        </span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <span>
          流程Id: {{ executionInfo.astrolinkId }}<el-icon class="icon-copy" @click="copyText(executionInfo.astrolinkId)"><CopyDocument /> </el-icon>
        </span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <span>处理速度: {{ processSpeed.speed }}</span>
      </el-col>
      <el-col :span="12">
        <span>预估剩余时间: {{ processSpeed.estimatedTime }}</span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <span>开始时间: {{ timeC.format(executionInfo.startTime, "YYYY-MM-DD hh:mm:ss") }}</span>
      </el-col>
      <el-col :span="12">
        <span>结束时间: {{ timeC.format(executionInfo.endTime, "YYYY-MM-DD hh:mm:ss") }}</span>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <span> 处理数量: {{ executionInfo.done + executionInfo.fail }} </span>
      </el-col>
      <el-col :span="12">
        <span> 数据总量: {{ executionInfo.total }} </span>
      </el-col>
      <el-col :span="12">
        <span> 失败数量: {{ executionInfo.fail }} </span>
        <template v-if="executionInfo.fail > 0">
          <my-button v-if="!isRunning" link type="primary" @click="failedRetry" :operationAuth="operationAuth" style="margin-left: 12px">重试 </my-button>
          <my-button link type="primary" @click="failedDetail">详情</my-button>
        </template>
      </el-col>
    </el-row>
    <div style="display: flex">
      <el-progress :text-inside="true" :stroke-width="16" :percentage="percentage" status="primary" class="task-process" />
      <my-button v-if="isRunning" link type="primary" @click="cancel" class="cancel-btn" :operationAuth="operationAuth"> 取消 </my-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { dataC, timeC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import * as dataAssetApi from "@/api/data-asset";
import useCtx from "@/hooks/useCtx";

const props = defineProps({
  taskInfo: { type: Object, default: {} },
  executionInfo: { type: Object, default: {} },
  operationAuth: { type: String, default: "" },
});

const emit = defineEmits(["reload", "fail-detail"]);

const { $app, proxy } = useCtx();

const TASK_STATUS = {
  "0": { type: "info", name: "已创建" },
  "1": { type: "primary", name: "运行中" },
  "2": { type: "success", name: "已完成" },
  "-1": { type: "warning", name: "已取消" },
  "-2": { type: "info", name: "已重置" },
  "-3": { type: "danger", name: "失败" },
  "20": { type: "success", name: "强制完成" },
};

const isRunning = computed(() => props.executionInfo.status === 1);
const dotType = computed(() => TASK_STATUS[props.executionInfo.status]?.type || "info");
const dotName = computed(() => TASK_STATUS[props.executionInfo.status]?.name || "未知状态");
const percentage = computed(() => {
  //处理数量等于成功数量+失败数量
  const done = Number(props.executionInfo.done + props.executionInfo.fail || 0);
  //已结束状态下, 已处理和总量都为0 或者已处理大于等于总量
  if (props.executionInfo.status == 2 && ((done == 0 && props.executionInfo.total == 0) || done >= props.executionInfo.total)) {
    return 100;
  }
  //正常计算流程
  const total = Number(!props.executionInfo.total || props.executionInfo.total === 0 ? 100_000_000 : props.executionInfo.total);
  const result = (done / total) * 100;
  return Math.min(100, Math.floor(result * 100) / 100);
});
const processSpeed = computed(() => {
  //处理数量等于成功数量+失败数量
  const done = Number(props.executionInfo.done + props.executionInfo.fail || 0);
  // 当前时间
  const now = props.executionInfo.endTime ? new Date(props.executionInfo.endTime) : new Date();
  const start = new Date(props.executionInfo.startTime);
  // 计算已用时间（秒）
  const elapsedSeconds = (now - start) / 1000;
  // 计算处理速度,如果时间几乎为0，直接使用done作为速度
  const speedSecond = elapsedSeconds > 0 ? done / elapsedSeconds : done;
  const speedMinute = speedSecond * 60;
  // 格式化速度
  let speedStr;
  if (speedMinute >= 10000) {
    speedStr = `${(speedMinute / 10000).toFixed(0)}万/分钟`;
  } else {
    speedStr = `${Math.round(speedMinute)}/分钟`;
  }
  // 计算剩余时间（如果total为null或undefined，返回null）
  let estimatedTime = null;
  let estimatedTimeStr = "未知";
  if (props.executionInfo.status !== 1) {
    estimatedTimeStr = "0秒";
  } else if (props.executionInfo.total && props.executionInfo.total >= done && speedSecond > 0) {
    const estimatedItems = props.executionInfo.total - done;
    estimatedTime = estimatedItems / speedSecond;
    if (estimatedTime < 60) {
      estimatedTimeStr = `${Math.round(estimatedTime)}秒`;
    } else {
      estimatedTimeStr = `${Math.round(estimatedTime / 60)}分钟`;
    }
  }
  return {
    speed: speedStr,
    estimatedTime: estimatedTimeStr,
  };
});

const cancel = async () => {
  try {
    await $app.$confirm({ title: `确定取消任务吗?` });
    await dataAssetApi.cancelTask(props.taskInfo.bucketCode);
    emit("reload");
    $app.$message.success("取消任务成功");
  } catch (error) {
    if (error) {
      $app.$message.error(error);
    }
  }
};

const failedRetry = async () => {
  try {
    await $app.$confirm({ title: `确定失败重试吗?` });
    await dataAssetApi.failedRetryTask(props.taskInfo.bucketCode, props.executionInfo.id);
    emit("reload");
    $app.$message.success("已重试");
  } catch (error) {
    if (error) {
      $app.$message.error(error);
    }
  }
};

const failedDetail = () => {
  emit("fail-detail", props.taskInfo, props.executionInfo);
};
</script>

<style lang="scss" scoped>
.task-process {
  width: calc(100% - 45px);
}
.cancel-btn {
  margin-left: 5px;
  width: 40px;
}
.icon-copy {
  color: $primary-color;
  margin-left: 5px;
  cursor: pointer;
}
</style>
