<template>
  <my-drawer class="time-task" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <div>
      <el-radio-group v-model="enabledTask" @change="handleRadioChange">
        <el-radio-button label="开启定时任务" :value="true" />
        <el-radio-button label="关闭定时任务" :value="false" />
      </el-radio-group>
    </div>
    <div v-show="enabledTask">
      <div style="margin-top: 10px">
        <span class="cron-tip">
          cron预览 : <span class="el-tag el-tag--primary el-tag--light">{{ cronTrim }}</span>
        </span>
      </div>
      <template v-if="cronValue !== ''">
        <noVue3Cron ref="cronRef" :cron-value="cronValue" i18n="cn"> </noVue3Cron>
      </template>
    </div>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as dataAssetApi from "@/api/data-asset";
import { noVue3Cron } from "no-vue3-cron";
import "no-vue3-cron/lib/noVue3Cron.css";

const { $app, proxy, $router } = useCtx();
const { api } = useStore();

// 事件声明
const emit = defineEmits(["reload"]);

// Dialog相关
const dialogTitle = computed(() => `定时设置`);
const dialogVisible = ref(false);

const cronRef = ref(null);
const enabledTask = ref(false);
const cronValue = ref("");
const cronTrim = computed(() => {
  const val = cronRef.value?.getValue() || "0 0 0 * * ? *";
  return val.split(" ").splice(0, 6).join(" ");
});

const bucketCode = ref("");

// 方法：打开窗口
const openWindow = async (row: any) => {
  //渲染表单(后台存的时六位的cron，但是这个组件时七位的cron)
  dialogVisible.value = true;
  bucketCode.value = row.code;
  const res = await dataAssetApi.getBucketSchedule(bucketCode.value);
  cronValue.value = res.cron ? `${res.cron} *` : "0 0 0 * * ? *";
  enabledTask.value = !dataC.isEmpty(res.cron) ? true : false;
  //组件渲染成功后设置分钟选中(已通过css样式设置秒tab和年tab不可见)
  await nextTick();
  document.getElementById("tab-1")?.click();
};
// 方法：关闭窗口
const handleClose = () => {
  dialogVisible.value = false;
  bucketCode.value = "";
  cronValue.value = "";
  enabledTask.value = false;
};
// 方法：提交表单
const handleConfirm = () => {
  const check = checkCronFrequency(cronTrim.value);
  if (check) {
    $app.$message.warning(check);
    return;
  } else {
    dataAssetApi
      .saveBucketSchedule(bucketCode.value, enabledTask.value ? cronTrim.value : "")
      .then((res) => {
        $app.$message.success("修改成功");
        emit("reload");
        handleClose();
      })
      .catch((e) => {
        $app.$message.warning("修改失败");
      });
  }
};
const handleRadioChange = async (newVal) => {
  if (newVal) {
    //组件渲染成功后设置分钟选中(已通过css样式设置秒tab和年tab不可见)
    await nextTick();
    document.getElementById("tab-1")?.click();
  }
};
function checkCronFrequency(cronExpression) {
  const parts = cronExpression.split(" ");
  // 检查秒级频率（第一位）
  if (parts[0] === "*") {
    return "秒级定时任务执行频率过高，可能影响系统性能";
  }
  // 检查分级频率（第二位）
  if (parts[1] === "*") {
    return "分级定时任务执行频率过高，可能影响系统性能";
  }
  // 其他检查...
  return null; // 无过高频率问题
}
//初始化
onMounted(async () => {});
// 接口暴露
defineExpose({ openWindow });
</script>

<style lang="scss" scoped>
.time-task {
  .cron-tip {
    color: red;
    margin-left: 10px;
  }
  .no-vue3-cron-div {
    margin-top: 10px;
    position: relative;
  }
}
:deep(#tab-0) {
  display: none;
}
:deep(#tab-5) {
  display: none;
}
:deep(.language) {
  display: none;
}
:deep(.bottom) {
  display: none !important;
}
:deep(.el-button--small) {
  display: none;
}
</style>
