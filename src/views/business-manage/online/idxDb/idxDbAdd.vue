<template>
  <my-drawer class="pro-plan-add" v-model="dialogVisible" :width="700" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :labelWidth="120" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import { createIdxDbOnlinePlan, editIdxDbOnlinePlan, getMetaRegionList, getIdxDbInstList, checkIdxInstValid } from "@/api/online.ts";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import * as idxDbApi from "@/api/idx-db";
import * as util from "@/utils/common";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  return (formType.value === "add" ? t("btn.new") : t("btn.edit")) + "上线计划";
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});
const isRealtime = computed(() => {
  return dataC.getItemByValue(formItems.value.idxDbInstId.options, ruleForm.value.idxDbInstId)["realtime"] == true;
});

//区域修改
const regionChange = (region: string, clear: boolean = true) => {
  idxDbApi.getRegionEsInfoList(region).then((result) => {
    formItems.value.clusterId.options = result.data
      .filter((item) => {
        return item.type == "full";
      })
      .map((item) => ({
        label: `${item.name}(${util.getEsInfoRender(item)})`,
        value: item.id,
      }));
    formItems.value.hotClusterId.options = result.data
      .filter((item) => {
        return item.type == "hot";
      })
      .map((item) => ({
        label: `${item.name}(${util.getEsInfoRender(item)})`,
        value: item.id,
      }));
  });
  if (clear) {
    ruleForm.value.clusterId = "";
    ruleForm.value.hotClusterId = "";
  }
};

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editIdxDbOnlinePlan : createIdxDbOnlinePlan;
      func(ruleForm.value).then((res) => {
        $app.$message.success(isUpdate.value ? "上线计划编辑成功" : "上线计划新建成功");
        dialogVisible.value = false;
        emits("reload");
      });
    }
  });
};

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "",
  description: "",
  idxDbInstId: "",
  targetRegion: "",
  clusterId: "",
  hotClusterId: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
let oldForm = {}; //edit时保存传进来的form
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入上线计划名称", trigger: "blur" }],
  idxDbInstId: [
    { required: true, message: "请选择索引库", trigger: "change" },
    {
      validator: async (rule, value, callback) => {
        //如果是旧的值，不做校验
        if (isUpdate.value && value == oldForm.idxDbInstId) {
          callback();
          return;
        }
        //如果希望改成其他的值，进行校验
        const res = await checkIdxInstValid(ruleForm.value.targetRegion, value);
        if (!res.data.pass) {
          return callback(new Error(res.data.message));
        }
        callback();
      },
      trigger: "change",
    },
  ],
  targetRegion: [{ required: true, message: "请选择上线环境", trigger: "change" }],
  clusterId: [{ required: true, message: "集群不能为空", trigger: "change" }],
  hotClusterId: [{ required: true, message: "集群不能为空", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "上线计划名称",
    type: "input",
  },
  description: {
    label: "上线计划描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  targetRegion: {
    label: "上线环境",
    type: "radio",
    options: [],
    events: {
      change: (val: any) => {
        regionChange(val);
      },
    },
  },
  idxDbInstId: {
    label: "索引库",
    type: "select",
    options: [],
    attrs: { clearable: false },
    disabled: (record: any) => {
      return dataC.isEmpty(record.targetRegion);
    },
  },
  clusterId: {
    label: computed(() => {
      console.log(formItems.value.realtime);
      return isRealtime.value ? "存量集群" : "集群";
    }),
    type: "select",
    options: [],
    attrs: {
      filterable: false,
      clearable: false,
    },
    disabled: (record: any) => {
      return dataC.isEmpty(record.targetRegion);
    },
  },
  hotClusterId: {
    label: "实时集群",
    type: "select",
    options: [],
    attrs: {
      filterable: false,
      clearable: false,
    },
    hidden: () => {
      return !isRealtime.value;
    },
  },
});

const openDialog = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      oldForm = cloneDeep(row);
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
      regionChange(ruleForm.value.targetRegion, false);
    } else {
      ruleForm.value = assign({}, defaultForm, row);
    }
  });
};
onMounted(async () => {
  // 索引库相关
  getIdxDbInstList().then((res) => {
    formItems.value.idxDbInstId.options = res.data?.map((item) => ({
      label: item.name,
      value: item.id,
      realtime: item.realtime,
    }));
  });
  //区域相关
  getMetaRegionList().then((result) => {
    formItems.value.targetRegion.options = result.content
      .filter((item) => {
        return item.envType == 2;
      })
      .map((item) => ({
        label: item.name,
        value: item.code,
      }));
  });
});
defineExpose({ openDialog });
</script>

<style lang="scss"></style>
