<template>
  <page-wrapper route-name="online::statistic::">
    <div class="online-statistic">
      <el-tabs v-model="activeName" @tab-change="events.tabChange">
        <el-tab-pane label="索引库信息" name="online-info">
          <div class="online-info-pane">
            <idx-db-tem-edit ref="idxDbTemEditRef"></idx-db-tem-edit>
            <el-card class="info-card" :style="{ height: activeCollapse == 1 ? '220px' : '60px' }">
              <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
                <el-collapse-item :name="1">
                  <template #title>
                    <span style="font-size: 16px; font-weight: 700">基本信息</span>
                  </template>
                  <el-descriptions column="4">
                    <el-descriptions-item label="名称 : " label-class-name="bold">{{ modelValue.idxDbOnlinePlanData.name }}</el-descriptions-item>
                    <el-descriptions-item label="索引库实例 : " label-class-name="bold">{{
                      `${modelValue.idxDbInstData.name}(${modelValue.idxDbInstData.enName})`
                    }}</el-descriptions-item>
                    <el-descriptions-item label="编码 : " label-class-name="bold">{{ modelValue.idxDbInstData.code }}</el-descriptions-item>
                    <el-descriptions-item label="特征库配置 : " label-class-name="bold">
                      <el-button link type="primary" @click="events.readIdxConfig()" class="bold"> 配置详情 </el-button>
                    </el-descriptions-item>
                    <el-descriptions-item label="上线环境 : " label-class-name="bold">{{
                      dataC.getItemByValue(modelValue.metaRegionList, modelValue.idxDbOnlinePlanData.targetRegion)["label"]
                    }}</el-descriptions-item>
                    <el-descriptions-item label="支持实时 : " label-class-name="bold">{{ isRealtime ? "实时" : "非实时" }}</el-descriptions-item>
                    <el-descriptions-item :span="isRealtime ? 1 : 2" :label="isRealtime ? '存量集群 :' : '集群 :'" label-class-name="bold">
                      {{ dataC.getItemByValue(modelValue.regionEsInfoList, modelValue.idxDbInstData.clusterId)["label"] }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="isRealtime" label="实时集群 :" label-class-name="bold">
                      {{ dataC.getItemByValue(modelValue.regionEsInfoList, modelValue.idxDbInstData.hotClusterId)["label"] }}
                    </el-descriptions-item>
                    <el-descriptions-item span="3" label="描述 : " label-class-name="bold">{{
                      modelValue.idxDbOnlinePlanData.description
                    }}</el-descriptions-item>
                  </el-descriptions>
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card class="table-card" :style="{ height: activeCollapse == 1 ? 'calc(100vh - 400px)' : 'calc(100vh - 240px)' }">
              <template v-if="!isOfflineDataset">
                <div class="el-descriptions">
                  <div class="el-descriptions__header" :style="{ 'margin-bottom': isRealtime ? '0' : '10px' }">
                    <div class="el-descriptions__title">
                      <span>数据详情</span>&nbsp;
                      <el-button link type="primary" @click="events.refreshSite">
                        <el-icon size="18">
                          <Refresh />
                        </el-icon>
                      </el-button>
                      <template v-if="!isRealtime">
                        <span style="display: inline-block; min-width: 200px; margin-left: 15px"
                          >Mongo : {{ util.formatNumber(modelValue.dbCapacity.full.mongo) }}</span
                        >
                        <span style="display: inline-block; min-width: 200px">ES : {{ util.formatNumber(modelValue.dbCapacity.full.es) }}</span>
                      </template>
                    </div>
                    <div v-if="!isRealtime" class="el-descriptions__title" style="display: flex; align-items: end">
                      <span>数据集数据量 : {{ util.formatNumber(count) }}</span>
                      <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
                    </div>
                  </div>
                  <div v-if="isRealtime" class="el-descriptions__header" style="align-items: end">
                    <div class="el-descriptions__title">
                      <div>
                        <span>实时数据</span>
                        <span style="display: inline-block; min-width: 200px; margin-left: 15px"
                          >Mongo : {{ util.formatNumber(modelValue.dbCapacity.hot.mongo) }}</span
                        >
                        <span style="display: inline-block; min-width: 200px">ES : {{ util.formatNumber(modelValue.dbCapacity.hot.es) }}</span>
                      </div>
                      <div>
                        <span>存量数据</span>
                        <span style="display: inline-block; min-width: 200px; margin-left: 15px"
                          >Mongo : {{ util.formatNumber(modelValue.dbCapacity.full.mongo) }}</span
                        >
                        <span style="display: inline-block; min-width: 200px">ES : {{ util.formatNumber(modelValue.dbCapacity.full.es) }}</span>
                      </div>
                    </div>
                    <div class="el-descriptions__title" style="display: flex; align-items: end">
                      <span>数据集数据量 : {{ util.formatNumber(count) }}</span>
                      <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
                    </div>
                  </div>
                </div>
                <template v-if="!dataC.isEmpty(modelValue.idxDbInstData.id)">
                  <site-table ref="siteTableRef" :datasetVersionId="modelValue.idxDbInstData.datasetVersionId"></site-table>
                </template>
              </template>
              <template v-if="isOfflineDataset">
                <el-descriptions title="数据详情" column="2">
                  <el-descriptions-item label="Mongo : " label-class-name="bold">{{
                    util.formatNumber(modelValue.dbCapacity.full.mongo)
                  }}</el-descriptions-item>
                  <el-descriptions-item label="ES : " label-class-name="bold">{{ util.formatNumber(modelValue.dbCapacity.full.es) }}</el-descriptions-item>
                </el-descriptions>
              </template>
            </el-card>
          </div>
        </el-tab-pane>
        <el-tab-pane label="数据预览" name="online-preview">
          <div class="online-preview-pane">
            <preview-index></preview-index>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import siteTable from "@/views/data-manage/idx-db-inst/statistic/SiteTable.vue";
import idxDbTemEdit from "@/views/meta-config/Idx-db-tem/IdxDbTemEdit.vue";
import previewIndex from "../preview/PreviewIndex.vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as idxDbApi from "@/api/idx-db";
import * as onlineApi from "@/api/online";
import * as previewApi from "@/api/preview";
import * as util from "@/utils/common";

const { $router, proxy } = useCtx();
const { api } = useStore();
const siteTableRef = ref();

const idxDbInstId = $router.currentRoute.value.query.idxDbInstId;
const idxDbOnlinePlanId = $router.currentRoute.value.query.idxDbOnlinePlanId;

let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
const count = computed(() => {
  return siteTableRef.value?.getCount() ?? "-";
});
const activeName = ref("online-info");

const activeCollapse = ref([1]);

const modelValue = reactive({
  idxDbInstData: {},
  idxDbOnlinePlanData: {},
  dbCapacity: {
    full: {
      mongo: 0,
      es: 0,
    },
    hot: {
      mongo: 0,
      es: 0,
    },
  },
  regionEsInfoList: [],
  metaRegionList: [],
});
const isRealtime = computed(() => {
  return modelValue.idxDbInstData.realtime == true;
});
const isOfflineDataset = computed(() => {
  return modelValue.idxDbInstData.type == 1;
});
//初始化统计数据:来源于数据库
const getCapacity = () => {
  previewApi
    .getPreviewCapacityFromRegion(idxDbInstId, modelValue.idxDbOnlinePlanData.idInRegion, modelValue.idxDbOnlinePlanData.targetRegion)
    .then((result) => {
      modelValue.dbCapacity = result.content.dbCapacity;
    });
};
//获取环境列表
const getMetaRegionList = () => {
  api.getMetaRegionList().then((result) => {
    modelValue.metaRegionList = result.map((item) => ({
      label: item.name,
      value: item.code,
    }));
  });
};
//获取集群列表
const getRegionEsInfoList = () => {
  idxDbApi.getRegionEsInfoList(modelValue.idxDbOnlinePlanData.targetRegion).then((result) => {
    modelValue.regionEsInfoList = result.data.map((item) => ({
      label: `${item.name}(${util.getEsInfoRender(item)})`,
      value: item.id,
    }));
  });
};
//初始化
onMounted(async () => {
  modelValue.idxDbInstData = await idxDbApi.getIdxDbInst(idxDbInstId);
  modelValue.idxDbOnlinePlanData = (await onlineApi.getIdxDbOnlinePlan(idxDbOnlinePlanId)).data;
  getCapacity();
  getRegionEsInfoList();
  getMetaRegionList();
});
//事件列表
const events = reactive({
  tabChange: (tabPaneName: string) => {
    if (tabPaneName == "online-info") {
      // events.refreshSite();
    } else if (tabPaneName == "online-preview") {
      window.dispatchEvent(new Event("resize"));
    }
  },
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  refreshSite: () => {
    getCapacity();
    proxy.$refs["siteTableRef"].loadList();
  },
  exportExcel: () => {
    const columns = proxy.$refs["siteTableRef"].getColumns();
    const tableData = proxy.$refs["siteTableRef"].getTableData();
    const titles = columns.map((col) => col.label);
    const contents = tableData.map((item) => columns.map((col) => item[col.prop]));
    const parameters = [];
    if (!isRealtime.value) {
      parameters.push(
        ...[
          { name: "Mongo", value: modelValue.dbCapacity.full.mongo },
          { name: "ES", value: modelValue.dbCapacity.full.es },
          { name: "数据集数据量", value: count.value },
        ]
      );
    } else {
      parameters.push(
        ...[
          { name: "实时Mongo", value: modelValue.dbCapacity.hot.mongo },
          { name: "实时ES", value: modelValue.dbCapacity.hot.es },
          { name: "全量Mongo", value: modelValue.dbCapacity.full.mongo },
          { name: "全量ES", value: modelValue.dbCapacity.full.es },
          { name: "数据集数据量", value: count.value },
        ]
      );
    }
    idxDbApi.exportSites({ titles, contents, parameters }).then((res) => util.downloadFile(res, "统计信息.xlsx"));
  },
  readIdxConfig: () => {
    proxy.$refs["idxDbTemEditRef"].openWindow("readConfig", { idxConfig: dataC.safeObject(modelValue.idxDbInstData.idxDbConfig) });
  },
});
</script>
<style lang="scss">
.online-statistic {
  .el-tabs__header {
    padding-left: 16px;
    height: 40px;
  }

  .online-info-pane {
    padding: 12px 16px;

    .bold {
      font-weight: bold;
    }

    .info-card {
      .collapse {
        border: none;
        .el-collapse-item__header {
          border: none;
          height: 23px;
          margin-bottom: 12px;
        }

        .el-collapse-item__wrap {
          border: none;
        }
      }

      .el-card__body {
        height: 100%;
        .el-collapse {
          height: 100%;
          .el-collapse-item {
            height: 100%;
            .el-collapse-item__wrap {
              height: 100%;
              .el-collapse-item__content {
                height: 100%;
                .el-descriptions {
                  height: 100%;
                  .el-descriptions__body {
                    height: 100%;
                    overflow-y: auto;
                  }
                }
              }
            }
          }
        }
      }
    }

    .table-card {
      margin-top: 10px;

      .el-card__body {
        height: 100%;
      }
    }
  }

  .online-preview-pane {
    padding: 12px 16px;
  }
}
</style>
