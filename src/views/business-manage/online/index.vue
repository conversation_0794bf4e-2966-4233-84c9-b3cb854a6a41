<template>
  <page-wrapper route-name="online::">
    <div class="online">
      <el-tabs v-model="activeName" @tab-change="events.tabChange">
        <el-tab-pane label="产品方案" name="pro-plan">
          <div class="online-pane">
            <ProPlanList ref="proPlanTableRef"></ProPlanList>
          </div>
        </el-tab-pane>
        <el-tab-pane label="索引库" name="idx-db">
          <div class="online-pane">
            <IdxDbList ref="idxDbTableRef"></IdxDbList>
          </div>
        </el-tab-pane>
        <el-tab-pane label="索引库(桶)" name="asset-online">
          <div class="online-pane">
            <OnlineTable ref="assetOnlineTableRef"></OnlineTable>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import ProPlanList from "./proPlan/proPlanList.vue";
import IdxDbList from "./idxDb/idxDbList.vue";
import OnlineTable from "./asset/OnlineTable.vue";
import useCtx from "@/hooks/useCtx";

const { $router, proxy } = useCtx();
const activeName = ref("pro-plan");

const events = reactive({
  tabChange: (tabPaneName: string) => {
    if (tabPaneName == "pro-plan") {
      proxy.$refs.proPlanTableRef.loadList();
    } else if (tabPaneName == "idx-db") {
      proxy.$refs.idxDbTableRef.loadList();
    } else if (tabPaneName == "asset-online") {
      proxy.$refs.assetOnlineTableRef.loadList();
    }
  },
});
</script>
<style lang="scss">
.online {
  .el-tabs__header {
    padding-left: 16px;
    height: 40px;
  }

  .online-pane {
    padding: 16px;
    padding-bottom: 0;
    height: calc(100vh - 145px);

    .query-wrapper {
      padding: 0 !important;
    }

    .table-wrapper {
      padding: 0 !important;
    }
  }
}
</style>
