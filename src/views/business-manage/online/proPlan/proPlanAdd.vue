<template>
  <my-drawer class="pro-plan-add" v-model="dialogVisible" :width="700" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :labelWidth="120" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import { createProductOnlinePlan, editProductOnlinePlan, getMetaRegionList, checkProductVersionValid } from "@/api/online.ts";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import { getOpenedProductList, getProductVersionList } from "@/api/product.ts";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  return (formType.value === "add" ? t("btn.new") : t("btn.edit")) + "上线计划";
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 产品方案相关
const getProductListApi = () => {
  getOpenedProductList().then((res) => {
    formItems.value.productId.options = res?.data?.map((x: any) => ({
      label: x.name,
      value: x.id,
      code: x.code,
    }));
  });
};
getProductListApi();
const getProductVersions = (id: string, notClear?: boolean) => {
  !notClear && (ruleForm.value.productVersionId = "");
  id &&
    ruleForm.value.targetRegion &&
    getProductVersionList({ flowId: id, size: 1000 }).then((res) => {
      formItems.value.productVersionId.options = res?.content?.map((x: any) => ({
        label: `${x.name}(V${util.padNumberToDigits(x.version, 3)})`,
        value: x.id,
      }));
    });
};

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editProductOnlinePlan : createProductOnlinePlan;
      func(ruleForm.value).then((res) => {
        $app.$message.success(isUpdate.value ? "上线计划编辑成功" : "上线计划新建成功");
        dialogVisible.value = false;
        emits("reload");
      });
    }
  });
};

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "",
  description: "",
  targetRegion: "",
  productId: "",
  productVersionId: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
let oldForm = {};//edit时保存传进来的form
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入上线计划名称", trigger: "blur" }],
  targetRegion: [{ required: true, message: "请选择上线环境", trigger: "change" }],
  productId: [{ required: true, message: "请选择产品方案名称", trigger: "change" }],
  productVersionId: [
    { required: true, message: "请选择产品方案版本", trigger: "change" },
    {
      validator: async (rule, value, callback) => {
        //如果是旧的值，不做校验
        if (isUpdate.value && value == oldForm.productVersionId) {
          callback();
          return;
        }
        //如果希望改成其他的值，进行校验
        const res = await checkProductVersionValid(ruleForm.value.targetRegion, ruleForm.value.productId, value);
        if (!res.data.pass) {
          return callback(new Error(res.data.message));
        }
        callback();
      },
      trigger: "change",
    },
  ],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "上线计划名称",
    type: "input",
  },
  description: {
    label: "上线计划描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  targetRegion: {
    label: "上线环境",
    type: "radio",
    options: [],
    events: {
      change: (val: any) => {
        getProductVersions(ruleForm.value.productId);
      },
    },
  },
  productId: {
    label: "产品方案",
    type: "select",
    options: [],
    attrs: { clearable: false },
    events: {
      change: (val: any) => {
        getProductVersions(val);
      },
    },
    disabled: (record: any) => {
      return dataC.isEmpty(record.targetRegion)
    }
  },
  productVersionId: {
    label: "产品方案版本",
    type: "select",
    options: [],
    attrs: { clearable: false },
    disabled: (record: any) => {
      return dataC.isEmpty(record.productId)
    }
  },
});

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      oldForm = cloneDeep(row);
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
      getProductVersions(ruleForm.value.productId, true);
    } else {
      ruleForm.value = assign({}, defaultForm, row);
    }
  });
};
onMounted(async () => {
  getMetaRegionList().then((result) => {
    formItems.value.targetRegion.options = result.content
      .filter((item) => {
        return item.envType == 2;
      })
      .map((item) => ({
        label: item.name,
        value: item.code,
      }));
  });
});
defineExpose({ openDialog });
</script>

<style lang="scss"></style>
