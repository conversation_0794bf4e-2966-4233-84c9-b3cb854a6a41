<template>
  <div class="pro-plan-list">
    <AddDialog ref="addRef" @reload="loadList" />
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <table-page
      ref="myTableRef"
      name="proPlan"
      :query="query"
      :columns="columns"
      :operations="operations"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/online/edit"
      @operation="handleOperation"
    >
      <!-- 查询 + 操作插槽内容 -->
      <template #query>
        <div class="flexBetween">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation style="margin-bottom: 12px; display: flex; justify-content: flex-end">
            <template #buttonGroup>
              <my-button type="add" @click="events.add" operationAuth="/base/#/online/edit">新建上线计划</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { assign } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import AddDialog from "./proPlanAdd.vue";
import * as util from "@/utils/common";
import useStore from "@/store";
import { getOpenedProductList } from "@/api/product.ts";
import {
  getProductOnlinePlanList,
  syncProductOnlinePlan,
  deleteProductOnlinePlan,
  clearProduct,
  enabledProductOnlinePlan,
  disabledProductOnlinePlan,
  getMetaRegionList,
  editProductOnlinePlanInfo,
} from "@/api/online.ts";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";

const { common } = useStore();
const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/online/edit");
};
/* 查询 */
const query = ref<any>({});
const queryItems = ref<any>({
  name: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: {
      placeholder: "名称",
    },
  },
  productId: {
    type: "select",
    width: "240px",
    options: [],
    attrs: { placeholder: "产品方案" },
  },
  enabled: {
    type: "select",
    width: "120px",
    options: [
      { label: "启用", value: true },
      { label: "禁用", value: false },
    ],
    attrs: { placeholder: "是否启用" },
  },
  targetRegion: {
    type: "select",
    width: "150px",
    options: [],
    attrs: { placeholder: "上线环境" },
  },
  status: {
    type: "select",
    label: "",
    modelValue: undefined,
    options: [
      { value: 1, label: "待发布", type: "info" },
      { value: 2, label: "已发布", type: "success" },
      { value: 3, label: "已归档", type: "warning" },
    ],
    attrs: {
      placeholder: "状态",
    },
  },
});
// 产品方案相关
const getProductListApi = () => {
  getOpenedProductList().then((res) => {
    queryItems.value.productId.options = res?.data?.map((x: any) => ({
      label: x.name,
      value: x.id,
      code: x.code,
    }));
  });
};
getProductListApi();
const metaRegionList = ref([]);
//列表查询
const loadListData = async (data: any) => {
  if (dataC.isEmpty(metaRegionList.value)) {
    const result = await getMetaRegionList();
    metaRegionList.value.push(...result.content);
    queryItems.value.targetRegion.options = result.content
      .map((item) => ({ value: item.code, label: item.name, envType: item.envType }))
      .filter((item1) => item1.envType == 2);
  }
  return new Promise((resolve: any) => {
    getProductOnlinePlanList(data).then((result) => {
      resolve(result);
    });
  });
};
/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  {
    prop: "name",
    label: "名称",
    width: 180,
    custom: "editLink",
    blod: true,
    customRender: {
      linkClick: (record: any) => {
        events.toFlow(record);
      },
      linkDisabled: (record: any) => {
        return record.status != 2;
      },
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  { prop: "productName", label: "产品方案名称", width: 160, sortable: false },
  { prop: "productVersionNameRender", label: "产品方案版本", width: 160, sortable: false },
  {
    prop: "enabled",
    label: "启用状态",
    width: 110,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: true,
        inactiveValue: false,
        size: "small",
      },
      disabled: (record: any) => record.status != 2,
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.enabled === true) {
            $app
              .$confirm({ title: `确定禁用 ${record.name}?` })
              .then(() => {
                disabledProductOnlinePlan(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`禁用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: `确定启用 ${record.name}?` })
              .then(() => {
                enabledProductOnlinePlan(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`启用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },
  { prop: "targetRegionRender", label: "上线环境", width: 130 },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    custom: "editButton",
    customRender: {
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  {
    prop: "status",
    label: "状态",
    width: 95,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        1: { type: "info", name: "待发布" },
        2: { type: "success", name: "已发布" },
        3: { type: "warning", name: "已归档" },
      },
    },
  },
  { prop: "syncTsRender", label: "发布时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "operation", label: "操作", width: 170, fixed: "right" },
]);
const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.status == 2 || record.status == 3,
    disabledTips: (record: any) => {
      if (record.status == 2) {
        return "方案已发布，不可编辑";
      }
      if (record.status == 3) {
        return "方案已归档，不可编辑";
      }
      return "不可编辑";
    },
  },
  {
    type: "sync",
    label: "发布",
    disabled: (record: any) => record.status == 2 || record.status == 3,
    disabledTips: (record: any) => {
      if (record.status == 2) {
        return "方案正在执行中，不可发布";
      }
      if (record.status == 3) {
        return "方案已归档，不可发布";
      }
      return "不可发布";
    },
  },
  {
    type: "delete",
    label: "归档上线计划",
    btnType: "danger",
    collapsed: true,
    disabled: (record: any) => record.enabled == true || record.status == 3,
    disabledTips: (record: any) => {
      if (record.enabled == true) {
        return "已启用，不可归档";
      }
      if (record.status == 3) {
        return "已归档，不可再归档";
      }
      return "不可归档";
    },
  },
  {
    type: "clear",
    label: "删除方案",
    btnType: "danger",
    collapsed: true,
    disabled: (record: any) => record.status == 1 || record.enabled == true || record.status == 3,
    disabledTips: (record: any) => {
      if (record.status == 1) {
        return "方案待发布，不可删除";
      }
      if (record.enabled == true) {
        return "已启用，不可删除";
      }
      if (record.status == 3) {
        return "已归档，不可删除";
      }
      return "不可删除";
    },
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    proxy.$refs.addRef?.openDialog("edit", record);
  } else {
    typeof events[type](record) == "function" && events[type](record);
  }
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.productVersionNameRender = `${x.productVersionName}(v${util.padNumberToDigits(x.productVersion, 3)})`;
    x.targetRegionRender = dataC.getItemByValue(metaRegionList.value, x.targetRegion, "code")["name"];
    x.syncTsRender = !dataC.isEmpty(x.syncTs) ? timeC.format(x.syncTs, "YYYY-MM-DD hh:mm:ss") : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};

/* events */
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
    console.log(query.value, obj);
  },
  reset: (obj: any) => {},
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  sync: (record: any) => {
    $app.$confirm({ title: `您确认要发布“${record.name}”数据吗？` }).then((res) => {
      syncProductOnlinePlan(record.id).then((res) => {
        $app.$message.success("上线计划上线成功！");
        loadList();
      });
    });
  },
  delete: (record: any) => {
    $app
      .$confirm({
        title: `归档后不能再次启用，确认对“${record.name}”进行归档吗？`,
      })
      .then(() => {
        deleteProductOnlinePlan(record.id).then(() => {
          loadList();
        });
      });
  },
  clear: (record: any) => {
    $app.$confirm({ title: `您确认要清除“${record.name}”的已上线数据吗？` }).then((res) => {
      clearProduct(record.id).then((res) => {
        $app.$message.success("清除数据成功!");
        loadList();
      });
    });
  },
  // 拓扑图
  toFlow: (record: any) => {
    window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/view?id=${record.id}&t=online`);
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(record: any) {
    editProductOnlinePlanInfo(record).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//接口暴露
defineExpose({
  loadList,
});
</script>

<style lang="scss">
.pro-plan-list {
  height: 100%;
}
</style>
