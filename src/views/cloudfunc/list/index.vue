<template>
  <page-wrapper route-name="cloudfunc::">
    <div class="meta-dictionary-main">
      <MetaDictionaryTree
        ref="treeRef"
        :treeData="treeData"
        :anasisList="anasisList"
        @updateTree="getTreeData"
        @updateTable="getTableData"
        :operationAuth="operationAuth"
      />
      <MetaWordTable
        ref="wordTableRef"
        :treeNode="treeNode"
        :treeData="treeData"
         @updateTree="getTreeData"
        :operationAuth="operationAuth"
      />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch } from "vue";
import useCtx from "@/hooks/useCtx";
import MetaWordTable from "./table.vue";
import MetaDictionaryTree from "./tree.vue";
import * as commonApi from "@/api/common";
import { computed } from "vue";
import * as util from "@/utils/common";
const operationAuth = "/base/#/cloudfunc/edit";
const treeRef = ref();
const { $app, proxy, $router } = useCtx();
const routeQuery = computed(() => $app.$route.query);
const wordTableRef = ref();
const treeNode = computed(() => treeRef.value?.getCurrentNode());
let treeData = ref<any>([{ id: "tag", name: "埋点标记" },{ id: "dedup", name: "数据去重" }]);
function findNodeById(nodes: any[], targetId: any) {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === targetId) {
      return nodes[i];
    }
  }
}
const getTableData = () => {
  wordTableRef.value && wordTableRef.value.loadList();
};
const getTreeData = async (key?: any) => {
  
  const categoryId = findNodeById(treeData.value,routeQuery.value.spaceId)?routeQuery.value.spaceId:treeData.value?.[0]?.id
  treeRef.value.setCurrentKey(key||categoryId||treeData.value?.[0]?.id);
};
const anasisList = ref([]);
// 场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  anasisList.value = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
getAnasisList();
</script>
<style scoped lang="scss">
.meta-dictionary-main {
  display: flex;
  height: 100%;
  padding: 20px;
  ::v-deep {
    .left-card {
      overflow: auto;
      .el-card__body {
        padding: 10px;
      }
    }
    .meta-dict-table {
      padding-left: 10px;
      width: calc(100% - 280px);
      .info-card {
        height: 100%;
        .el-card__body {
          padding: 0;
          height: 100%;
        }
      }
    }
  }
}
</style>
