<template>
  <el-card class="left-card">
    <!-- <div class="layout-tab-filter">
      <span class="sub-title">所属区域</span>
      <el-select
        v-model="area"
        placeholder="请选择所属区域"
        style="width: 150px"
        @change="handleAreaChange"
      >
        <el-option
          v-for="item in areaOptions"
          :key="item.code"
          :label="item.name"
          :value="item.code"
        />
      </el-select>
    </div> -->
    <div>
      <div class="title">
        <span>{{ title }}</span><span v-if="type == 'dict'">
          <my-button link @click="events.export(data)"><el-icon>
              <Download />
            </el-icon></my-button>
          <my-button link @click="events.import(data)" :operationAuth="operationAuth"><el-icon>
              <Upload />
            </el-icon></my-button></span>
      </div>
    </div>
    <el-tree ref="treeRef" style="max-width: 600px" :allow-drop="allowDrop" :expand-on-click-node="false"
      @node-drop="handleDrop" :data="treeData" node-key="id" :current-node-key="currentKey"
      :default-expanded-keys="expandArr" :props="defaultProps" @node-expand="handleNodeExpand" :highlight-current="true"
      v-if="treeData && treeData.length">
      <template #default="{ node, data }">
        <span :class="{ 'custom-tree-node': true }">
          <span class="text" :title="node.label" @click="setCurrentKey(data.id)">
            {{ node.label }}
          </span>
        </span>
      </template>
    </el-tree>
    <!-- <el-button type="primary" @click="events.add()" v-else>新增</el-button> -->

  </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, defineEmits, onMounted } from "vue";
import { assign, pick, keys } from "lodash";
import { ElTree } from "element-plus";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import type {
  AllowDropType,
  NodeDropType,
} from "element-plus/es/components/tree/src/tree.type";
import { computed } from "vue";
import { useVModel } from "@vueuse/core";
const props = defineProps({
  treeData: { type: Array, default: 200 },
  title: { type: String },
  type: { type: String },
  operationAuth: { type: String },
  areaType: { type: String },
  areaInfo: { type: Object, default: {} },
  showAreaType: { type: Boolean, default: false },
});
const routeQuery = computed(() => $app.$route.query);
const { word } = useStore();

const defaultForm = {
  parentId: null,
  name: "",
  code: "",
  enabled: "",
};
const emits = defineEmits(["updateTree", "updateTable", "updateAreaType"]);
const localAreaType = useVModel(props, "areaType", emits);
const currentKey = ref("");
const dialogVisible = ref(false);
const areaOptions = ref<any>([]);
const { $app, proxy, $router } = useCtx();
const area = ref("");
const filterText = ref("");
const expandArr = ref([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const data: any[] = [];

const defaultProps = {
  children: "children",
  label: "name",
};
const getCurrentNode = () => {
  return treeRef.value?.getCurrentNode();
};
const setCurrentKey = (key: any) => {
  if (!key) {
    return
  }
  nextTick(() => {
    treeRef.value.setCurrentKey(key);
    // $router.replace({
    //   path: $router.currentRoute.value.path,
    //   query: {
    //     ...$app.$route.query,
    //     nodeName: getCurrentNode().name,
    //     type: key,
    //   },
    // });
  });
};

const handleNodeExpand = (data: any, node: any, el: any) => {
  const expand: boolean = node.expand;
  if (expand) {
    expandArr.value.push(data.id);
  } else {
    expandArr.value.splice(expandArr.value.indexOf(node.id), 1);
  }
};
//事件列表
const events = reactive({

});
const allowDrop = (draggingNode: any, dropNode: any, type: AllowDropType) => {
  if (draggingNode.level === dropNode?.level) {
    if (draggingNode.data.parentId === dropNode.data.parentId) {
      return type === "prev" || type === "next";
    }
  } else {
    return false;
  }
};

onMounted(async () => {
  nextTick(() => {
    emits("updateTree", routeQuery.value.type);
  });
});
defineExpose({
  filterText,
  setCurrentKey,
  getCurrentNode,
  areaOptions,
});
</script>

<style lang="scss" scoped>
.tab {
  position: absolute;
  top: -46px;
  left: 100px;
}

.icon-copy {
  color: $primary-color;
  margin-left: 12px;
  cursor: pointer;
  vertical-align: middle;
}

.icon-more {
  height: 100%;
}

.left-card {
  width: 280px;
  flex-shrink: 0;

  .title {
    font-weight: 550;
    display: flex;
    justify-content: space-between;

    i {
      margin-left: 5px;
    }
  }

  .layout-tab-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    .sub-title {
      margin-right: 8px;
    }
  }

  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 3px;

    .status {
      display: inline-block;
      height: 8px;
      width: 8px;
      border-radius: 8px;
      vertical-align: middle;
      margin-right: 2px;

      &.active {
        background-color: var(--el-color-primary);
      }

      &.error {
        background-color: var(--el-color-danger);
      }
    }

    .text {
      display: inline-block;
      align-content: center;
      @include no-wrap();
      word-break: break-all;
    }
  }

  .expire-node {
    background-color: var(--el-fill-color-light);
  }

  ::v-deep {
    .el-input {
      margin: 12px 0 10px 0;
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep(.el-button + .el-button) {
  margin-left: 0;
}

.operate-popover {
  .items {
    ::v-deep(.el-button) {
      display: block;
      padding: 5px;
      width: 100%;
      text-align: left;
      cursor: pointer;

      &:hover {
        background: var(--el-color-primary-light-9);
      }
    }
  }
}
</style>
