<template>
  <el-dialog 
    title="代码编辑器" 
    v-model="dialogVisible" 
    width="1200px" 
    append-to-body 
    destroy-on-close 
    modal-class="astrolink-flow-dialog" 
    :close-on-click-modal="false">
    <CustomCodemirror :domId="`domId${new Date().getTime()}`" language="java" :initVal="initVal" height="500px" @updateVal="(val: any) => (initVal = val)" />
  
    <template v-if="!readOnly" #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sure">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup name="CodeMirrorBox">
import { ref } from "vue"
import useCtx from "@/hooks/useCtx"
import CustomCodemirror from "@/views/common/paramComp/components/CustomCodemirror.vue";

const { $app, proxy } = useCtx()

withDefaults(
  defineProps<{
    readOnly?: boolean
  }>(),
  {
    readOnly: false,
  }
)

const emit = defineEmits(["getCode"])

const dialogVisible = ref(false)
const initVal = ref("")
const lang = ref("")
const initDesc = ref("")
const isPerview = ref(false)

const openDialog = (defaultValue: string, argProps: any, perview: boolean = true) => {
  dialogVisible.value = true
  initVal.value = defaultValue
  lang.value = argProps.lang || ""
  initDesc.value = argProps.remark || ""
  isPerview.value = perview
}

const closeDialog = () => {
  dialogVisible.value = false
}

const sure = () => {
  emit("getCode", initVal.value, proxy.$refs.TMakrdownMirroiRef && proxy.$refs.TMakrdownMirroiRef.getCodeText())
  closeDialog()
}

defineExpose({ openDialog, closeDialog })
</script>

<style lang="scss" scoped></style>
