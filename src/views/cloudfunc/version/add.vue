<template>
  <my-drawer class="offline-dataset-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm"
    @close="handleClose" size="850">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit"
      label-width="80px">
      <template #script>
        <div class="codemirror-contain">
          <el-button class="ide-btn" type="primary" link size="default" @click="openIDEClick">在IDE中编辑</el-button>
          <el-button class="ide-btn" type="primary" link size="default" @click="openDebugCode">测试代码</el-button>
          <CustomCodemirror ref="customCodemirrorRef" :domId="`script${new Date().getTime()}`"
            language="java" :initVal="ruleForm.script" height="300px"
            @updateVal="(val: any) => (ruleForm.script = val)" />
        </div>
      </template>
      <template #inputParam>
        <param-table v-model="ruleForm.inputArgs" :disabled="false" />
      </template>
    </my-form>
  </my-drawer>
  <CodeMirrorDialog ref="CodeMirrorBoxRef" @getCode="setCodeValue" :readOnly="false" />
  <DebugCode ref="debugCodeRef" :ruleForm="ruleForm" />
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  onMounted,
  nextTick,
  watch,
  watchEffect,
} from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import draggable from "vuedraggable";
import useStore from "@/store";
import * as cloudfuncApi from "@/api/cloudfunc";
import { NAME_RULE, CODE_RULE } from "@/utils/validate";
import CustomCodemirror from "@/views/common/paramComp/components/CustomCodemirror.vue";
import CodeMirrorDialog from "./CodeMirrorBox.vue";
import paramTable from "@/views/common/paramComp/components/paramTable/index.vue";
import DebugCode from "./debug.vue";
import { useI18n } from "vue-i18n";
import { argsTypeList } from "@/views/common/paramComp/utils/constants";
const { t } = useI18n();
// Props
const props = defineProps({
  spaceId: { type: String },
});
const initVal = ref("");
const routeQuery = computed(() => $app.$route.query);
const query = ref<any>({ search: "" });
const { $app, proxy, $router } = useCtx();
const { api } = useStore();
// Dialog相关
const isUpdate = computed(() => formType.value === "edit");
const dialogTitle = computed(() =>
  isUpdate.value ? "编辑云函数版本" : "新增云函数版本"
);
const dialogVisible = ref(false);
const curIndex = ref<number>(0);
// 默认表单
const defaultForm = reactive({
  id: '',
  name: "",
  desc: "",
  language: "groovy",
  script: "",
  scriptId: '',
  mainMethod: 'execute',
  inputArgs: []
});
// 表单相关
const formType = ref("add");
const formRef = ref(null);
const ruleForm = ref(assign({}, defaultForm));
const modelValue = reactive({
  metaSiteFieldList: [], //table根本数据源
});
const operateTableData = ref([]); //存储用户操作后的数据
// 表单规则
const rules = computed(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "change" }],
  language: [{ required: true, message: "脚本语言不能为空", trigger: "change" }],
  script: [{ required: true, message: "脚本代码不能为空", trigger: "change" }],
  mainMethod: [{ required: true, message: "主方法不能为空", trigger: "change" }],
}));

/** 打开调试代码 */
const openDebugCode =async  () => {
  const valid  = await proxy.$refs.formRef.getValidateResult()
  if(ruleForm.value.inputArgs.length===0){
    $app.$message.warning("请添加参数")
    return
  }
  if(valid){
      proxy.$refs.debugCodeRef.debugData.debugCodeClick(ruleForm.value.inputArgs);
  }
}
const changeCodeType = (type: string) => {
  const demoCode: any = {
    javascript: `// JavaScript 示例代码
function execute(input) {
   return {
       number: input.x + input.y
   };
}`,
    groovy: `// groovy 示例代码
def execute(input) {
   return [
       "number": input.x + input.y
   ]
}`,
    python: `# Python 示例代码
def execute(input):
    return {
        'number': input['x'] + input['y']
    }`,
  };
  updateCodemirrorVal(demoCode[type] ?? "");
};
/**
 * 打开代码编辑器
 */
const openIDEClick = () => {
  const codeProps = {
    lang: ruleForm.value?.language || "groovy",
    remark: ruleForm.value?.script,
  };
  proxy.$refs.CodeMirrorBoxRef.openDialog(
    ruleForm.value.script,
    codeProps,
    true
  );
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit();
    }
  });
};
// 表单项
const formItems = ref({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 100, placeholder: NAME_RULE
    },separate: {
        title: "基础信息",
      },
  },
  desc: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  // enabled: {
  //   label: "是否启用",
  //   type: "switch",
  // },
  language: {
    label: "脚本语言",
    type: "select",
    options: [
      // { label: "javascript", value: "javascript" },
      { label: "groovy", value: "groovy" },
      // { label: "python", value: "python" },
    ],
    attrs: { placeholder: "请选择脚本语言" },
    events: {
      change: (val: any) => {
        changeCodeType(val);
      },
    },
  },
  mainMethod: {
    label: "运行函数",
    type: "input",
  },

  script: {
    type: "slot",
    slotName: "script",
    attrs: { class: "no-label" },
    separate: {
        title: "脚本",
      },
  },
  inputParam: {
    type: "slot",
    slotName: "inputParam",
    attrs: { class: "no-label" },
  },

});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "类别 或 名称 或 字段",
    },
  },
});

// 方法：打开窗口
const openDialog = async (type: string, row: any) => {
  //清空table查询条件
  queryItems.value.search.modelValue = "";
  query.value.search = "";
  //设置表单类型，渲染表单
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (!isUpdate.value) {
      //表单数据
      ruleForm.value = assign({}, defaultForm);
      changeCodeType(ruleForm.value.language);
    } else {
      //表单数据
      ruleForm.value = pick({ ...defaultForm, ...row }, keys(defaultForm));
      updateCodemirrorVal(ruleForm.value.script);

    }
    ruleForm.value.scriptId = routeQuery.value.scriptId;

  });
};

// 方法：关闭窗口
const handleClose = () => {
  formRef.value?.resetForm();
  dialogVisible.value = false;
};
/**
 * 更新代码编辑器的数据
 */
const updateCodemirrorVal = (val: string) => {
  if (!proxy.$refs.customCodemirrorRef) return;
  proxy.$refs.customCodemirrorRef?.updateCodemirrorVal(val);
};
const setCodeValue = (str: string) => {
  ruleForm.value.script = str;
  updateCodemirrorVal(str);
};
// 方法：提交表单
const submit = async () => {
  const params = { ...ruleForm.value, spaceId: props.spaceId };
  if (isUpdate.value) {
    await cloudfuncApi.updateMetaFuncVersion(routeQuery.value.region, params);
    $app.$message.success("修改成功");
  } else {
    await cloudfuncApi.createMetaFuncVersion(routeQuery.value.region, params);
    $app.$message.success("新增成功");
  }
  emit("reload");
  handleClose();
};
//初始化站点信息
onMounted(async () => {
});
// 事件声明
const emit = defineEmits(["reload"]);
// 接口暴露
defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.codemirror-contain {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.offline-dataset-edit-table {
  height: calc(100vh - 390px);

  .table-page-wrapper {
    .table-wrapper {
      height: calc(100% - 50px) !important;

      .el-table {
        height: 100%;
      }
    }
  }
}
</style>
