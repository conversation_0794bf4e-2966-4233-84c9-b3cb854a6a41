<template>

  <el-drawer v-model="debugData.drawerTag" custom-class="scriptV2-debug-drawer" title="测试代码" append-to-body size="680px"
    :show-close="true" :close-on-click-modal="false" :before-close="debugData.close" destroy-on-close>
    <div>
      <div class="flexBetweenStart">
        <h3 class="common-part-title">输入</h3>
        <div class="right-button">
          <span style="margin-right: 12px;">是否记录输入 <el-switch v-model="debugData.debugSwitch" /></span>
          <el-button type="primary" @click="debugData.format" size="small">格式化</el-button>
        </div>
      </div>
      <div class="codemirror-contain">
        <CustomCodemirror ref="customCodemirrorRef" :domId="`domId${dataC.generateUUID()}`" :options="debugData.options"
          height="400px" @updateVal="(val) => (debugData.options.value = val)" />
      </div>
      <el-button style="width: 100%" type="primary" size="default" @click="debugData.runClick">运行</el-button>
      <div class="flexBetweenStart">
        <h3 class="common-part-title">输出</h3>
      </div>
      <el-input type="textarea" v-model="debugData.outputJson" v-loading="debugData.wsLoading" readonly
        :autosize="{ minRows: 6, maxRows: 8 }" resize="none" :show-word-limit="false"> </el-input>
      <my-table :data="debugData.storeageData" :columns="debugData.storeageColumns" :with-pagination="false"
        :withOrder="false" :withSort="false"  :operations="operations"
        @operation="handleOperation" />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch, watchEffect } from "vue";
import { dataC, timeC } from "turing-plugin";

import * as cloudfuncApi from "@/api/cloudfunc";
import useCtx from "@/hooks/useCtx";
import CustomCodemirror from "@/views/common/paramComp/components/CustomCodemirror.vue";

// Props
const props = defineProps({
  type: { type: String },
  region: { type: String },
  readOnly: { type: Boolean, default: false },
  ruleForm: { type: Object, default: () => ({}) },
});
const customCodemirrorRef = ref<any>();
const inputJson = ref<any>();
const { $app } = useCtx();
const routeQuery = computed(() => $app.$route.query);
/** 调试代码弹框的数据 */
const debugData = reactive<any>({
  drawerTag: false,
  wsInstance: null,
  debugSwitch: false,
  options: {
    value: "",
    language: "json",
    lineNumbers: "off",
    folding: false,
    glyphMargin: false,
    renderLineHighlight: "none",
    minimap: { enabled: false },
    readOnly: props.readOnly,
  },
  storeageData: [],
  storeageColumns: [{ label: '输入', prop: 'inputArgs' }, { label: '输出', prop: 'outputArgs' }, { prop: "operation", label: "操作", width: 110, fixed: "right" }],
  outputJson: "",
  wsLoading: false,
  format: () => {
    customCodemirrorRef.value?.updateCodemirrorVal(JSON.stringify(JSON.parse(debugData.options.value), null, 4));
  },
  runClick: () => {
    inputJson.value = JSON.parse(debugData.options.value)
    cloudfuncApi.debugMetaFuncVersion(routeQuery.value.region, { language: props.ruleForm.language, script: props.ruleForm.script, mainMethod: props.ruleForm.mainMethod, args: JSON.parse(debugData.options.value) , type: routeQuery.value.type, code: routeQuery.value.code, debug: debugData.debugSwitch, scriptVersionId:props.ruleForm.id }).then((res) => {
      debugData.outputJson = JSON.stringify(res.data, null, 4);
      debugData.getStoreageData()
    });
  },
  // 打开调试代码弹框
  debugCodeClick: (data) => {
    let json: any = {}
    if (inputJson.value) {
      json = inputJson.value
    } else {
      json = {};
      data.forEach((e: any) => {
        if (e.style == 'dynamic') {
          json[e.key] = {};
        } else {
          json[e.key] = e.value || e.defaultValue;
        }
      });
    }
    debugData.options.value = JSON.stringify(json, null, 4);
    debugData.drawerTag = true;
    debugData.getStoreageData()
  },
  getStoreageData: () => {
    cloudfuncApi.getVersionCallPage(routeQuery.value.region, {page:1,size:10,scriptVersionId:props.ruleForm.id,sort:"createdDate,desc"}).then((res) => {
      debugData.storeageData = res.content.map((e: any) => {
        return {
          ...e,
          inputArgs: JSON.stringify(e.inputArgs.args, null, 4),
          outputArgs: JSON.stringify(e.outputArgs.args, null, 4),
        }
      });
    });
  },
  // 关闭调试代码弹框
  close: () => {
    debugData.drawerTag = false;
  },
});
const operations = [
  {
    type: "apply",
    label: "应用",
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type == "apply") {
    customCodemirrorRef.value?.updateCodemirrorVal(JSON.stringify(JSON.parse(record.inputArgs), null, 4));
    debugData.outputJson = JSON.stringify(JSON.parse(record.outputArgs), null, 4);

  }
};

// 事件声明
const emit = defineEmits(["reload"]);
// 接口暴露
defineExpose({ debugData });

</script>

<style lang="scss" scoped>
.scriptV2-debug-drawer {
  .el-drawer__header {
    margin-bottom: 0;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
  }
}

.codemirror-contain {
  border: 1px solid #e8e8e8;
  margin: 12px 0;
  border-radius: 8px;
  overflow: hidden;

  :deep(.overflow-guard > .margin) {
    display: none;
  }
}

.flexBetweenStart {
  @include flexBetween();
  align-items: flex-start;
  margin: 12px 0;
}
::v-deep(.table-wrapper) {
  padding: 0 !important;
}
</style>
