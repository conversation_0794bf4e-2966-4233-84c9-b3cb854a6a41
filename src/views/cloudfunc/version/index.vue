<template>
  <page-wrapper route-name="cloudfunc::version::">
    <div class="dataset-version">
      <el-card class="info-card" :style="{ height: activeCollapse == 1 ? '170px' : '60px' }">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>&nbsp;
            </template>
            <el-descriptions column="2">
              <el-descriptions-item label="名称 : " label-class-name="bold">{{
                `${routeQuery.name}(${routeQuery.code})`
              }}</el-descriptions-item>
              <el-descriptions-item label="创建人 : " label-class-name="bold">{{ routeQuery.createdBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间 : " label-class-name="bold">
                {{routeQuery.createdDate}}
              </el-descriptions-item>
              <el-descriptions-item  label="环境 : " label-class-name="bold">{{routeQuery.regionName}}</el-descriptions-item>
              <el-descriptions-item label="关联产品方案: " label-class-name="bold">{{routeQuery.prodCodeRender}}</el-descriptions-item>
              <el-descriptions-item :span="2" label="描述 : " label-class-name="bold">{{routeQuery.desc }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card class="table-card" :style="{ height: activeCollapse == 1 ? 'calc(100vh - 320px)' : 'calc(100vh - 210px)' }">
        <el-row justify="space-between">
          <el-col :span="12">
            <div class="el-descriptions">
              <div class="el-descriptions__header">
                <div class="el-descriptions__title">
                  <span>版本信息</span>&nbsp;
                  <el-button link type="primary" @click="events.refreshVersion">
                    <el-icon size="18">
                      <Refresh />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button type="primary" @click="events.addVersion" :disabled="!testAuth()">
              <el-icon> <CirclePlus /> </el-icon>&nbsp;新建版本
            </el-button>
          </el-col>
        </el-row>
        <cloudfunc-version-table
          ref="cloudfuncVersionTableRef"
          @edit-data="events.openVersionEditWindow"
        ></cloudfunc-version-table>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import datasetEdit from "../config/DatasetEdit.vue";
import siteIndex from "../site/SiteIndex.vue";
import cloudfuncVersionTable from "./table.vue";
import dataDedup from "@/views/common/dedup/DataDedup.vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";

const { $router, $app, proxy, $auth } = useCtx();
const routeQuery =computed(()=> $app.$route.query);
const testAuth = () => {
  return $auth.testAuth("/base/#/cloudfunc/edit");
}
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
const activeCollapse = ref([1]);

//初始化站点信息
onMounted(() => {
});
//事件列表
const events = reactive({
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  addVersion: () => {
    proxy.$refs["cloudfuncVersionTableRef"].openWindow("add");
  },
  refreshVersion: () => {
    proxy.$refs["cloudfuncVersionTableRef"].loadList();
  },
  openVersionEditWindow: (type: string, item: any) => {
    proxy.$refs["siteIndexRef"].openWindow(type, item);
  },
});
</script>
<style lang="scss">
.dataset-version {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;
      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;
      .el-collapse {
        height: 100%;
        .el-collapse-item {
          height: 100%;
          .el-collapse-item__wrap {
            height: 100%;
            .el-collapse-item__content {
              height: 100%;
              .el-descriptions {
                height: 100%;
                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }
  }
}
</style>
