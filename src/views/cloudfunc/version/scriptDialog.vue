<template>
  <my-drawer v-model="dialogVisible" title="脚本" :showConfirm="false" @close="handleClose">
    <CustomCodemirror
      ref="customCodemirrorRef"
      :domId="`domId${new Date().getTime()}`"
      language="java"
      :initVal="record.script"
      height="100%"
      @updateVal="(val: any) => (record.script = val)"
    />
  </my-drawer>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  onMounted,
  nextTick,
  watch,
  watchEffect,
} from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import CustomCodemirror from "@/views/common/paramComp/components/CustomCodemirror.vue";
// Props
const props = defineProps({
  spaceId: { type: String },
});
const initVal = ref("");
const query = ref<any>({ search: "" });
const { $app, proxy, $router } = useCtx();
const { api } = useStore();
const dialogVisible = ref(false);
const record = ref({
  language: "javascript",
  script: "",
});

// 方法：打开窗口
const openDialog = async (row: any) => {
    dialogVisible.value = true;
    record.value = row;
    nextTick(() => {
      updateCodemirrorVal(row.script);
    });
};

// 方法：关闭窗口
const handleClose = () => {
  dialogVisible.value = false;
};
/**
 * 更新代码编辑器的数据
 */
const updateCodemirrorVal = (val: string) => {
  if (!proxy.$refs.customCodemirrorRef) return;
  proxy.$refs.customCodemirrorRef?.updateCodemirrorVal(val);
};

//初始化站点信息
onMounted(async () => {
});
// 事件声明
const emit = defineEmits(["reload"]);
// 接口暴露
defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.codemirror-contain {
  width: 500px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}
.offline-dataset-edit-table {
  height: calc(100vh - 390px);

  .table-page-wrapper {
    .table-wrapper {
      height: calc(100% - 50px) !important;
      .el-table {
        height: 100%;
      }
    }
  }
}
</style>
