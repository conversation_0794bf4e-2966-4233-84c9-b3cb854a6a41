<template>
  <my-dialog class="description-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" label-width="60" style="margin-top: 10px"> </my-form>
  </my-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import useValidate from "@/hooks/validate";
import useStore from "@/store";

const { $app, proxy } = useCtx();
const { api } = useStore();
const record = ref<any>({});
//弹窗相关
const dialogTitle = computed(() => {
  return `修改基本信息`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
  record.value = {};
};

const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      emit("save-data", {
        id: ruleForm.value.id,
        name: ruleForm.value.name.trim(),
        description: ruleForm.value.description,
      },record.value);
    }
  });
};
// 表单相关
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  description: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const { validateNameRule, validateCodeRule } = useValidate();
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, "请输入名称") }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      rows: 5,
    },
  },
});
//打开窗口
const openWindow = async (row: any) => {
  dialogVisible.value = true;
  nextTick(() => {
    ruleForm.value = assign({}, defaultForm, row);
    record.value = row;
  });
};
const closeWindow = () => {
  handleClose();
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
  closeWindow,
});
</script>
<style lang="scss">
.description-edit {
  .el-dialog__header {
    padding: 5px !important;
  }

  .el-dialog__body {
    padding: 5px !important;
  }
}
</style>
