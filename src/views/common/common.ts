import {ref} from 'vue'
import {getRegionList} from "@/api/scene.ts";

// 获取区域列表
export const getRegionListApi = async (type: number) => {
    const regionList = []
    await getRegionList(type).then((res: any) => {
        if (res?.content?.length) {
            res.content.forEach((item: any) => {
                regionList[item.code] = item.name
            })
        }
    })
    console.log('regionList', regionList)
    return regionList
}

export const systemMap: any = {
    '-1': '场景策略',
    '-2': '产品方案',
    '-3':"场景策略、产品方案" 
}