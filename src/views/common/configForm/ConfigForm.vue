<template>
  <div class="container">
    <el-button link type="primary" size="small" @click="addNode(filterConf)" style="margin-bottom: 10px" v-show="!isDisabled">增加过滤条件</el-button>
    <el-button link type="primary" size="small" @click="copyConf()" style="margin-bottom: 10px" v-show="!isDisabled && displayCopy" :disabled="disabledCopy"
      >复制标签管理过滤条件</el-button
    >
    <el-button link type="primary" size="small" @click="mongo()" style="margin-bottom: 10px" v-show="!isDisabled">复制DSL</el-button>
    <div :key="renderKey" class="layer">
      <div class="layer-body" v-for="(level1, index1) in filterConf.nodes">
        <template v-if="level1.nodes && level1.nodes.length">
          <div class="layer">
            <div class="layer-body" v-for="(level2, index2) in level1.nodes">
              <!-- 三级过滤 -->
              <template v-if="level2.nodes && level2.nodes.length">
                <div class="layer">
                  <div class="layer-body" v-for="(level3, index3) in level2.nodes">
                    <FilterField
                      v-model="level2.nodes[index3]"
                      :fields="fields"
                      :metaLabelList="metaLabelList"
                      :isDisabled="isDisabled"
                      @add="addNode(level2, index3)"
                      @delete="deleteNode(level2, index3, level1, index2)"
                    />
                  </div>
                  <div class="layer-line">
                    <div class="layer-line-button" v-show="level2.nodes && level2.nodes.length > 1" @click="changeType(level2)">
                      {{ level2["@type"] === "AND" ? "且" : "或" }}
                    </div>
                  </div>
                </div>
              </template>
              <!-- 二级过滤 -->
              <template v-else>
                <FilterField
                  v-model="level1.nodes[index2]"
                  :fields="fields"
                  :metaLabelList="metaLabelList"
                  :isDisabled="isDisabled"
                  @add="addChildNode(level1.nodes, index2)"
                  @delete="deleteNode(level1, index2, filterConf, index1)"
                />
              </template>
              <el-button
                link
                @click="addNode(level1)"
                type="primary"
                v-if="level1.nodes.length - 1 === index2"
                style="margin-top: 5px"
                size="small"
                v-show="!isDisabled"
                >添加二级过滤条件</el-button
              >
            </div>
            <div class="layer-line">
              <div class="layer-line-button" v-show="level1.nodes && level1.nodes.length > 1" @click="changeType(level1)">
                {{ level1["@type"] === "AND" ? "且" : "或" }}
              </div>
            </div>
          </div>
        </template>
        <!-- 一级过滤 -->
        <template v-else>
          <FilterField
            v-model="filterConf.nodes[index1]"
            :fields="fields"
            :metaLabelList="metaLabelList"
            :isDisabled="isDisabled"
            @add="addChildNode(filterConf.nodes, index1)"
            @delete="deleteNode(filterConf, index1)"
          />
        </template>
      </div>
      <div class="layer-line" v-show="filterConf.nodes && filterConf.nodes.length > 0">
        <div class="layer-line-button" v-show="filterConf.nodes && filterConf.nodes.length > 1" @click="changeType(filterConf)">
          {{ filterConf["@type"] === "AND" ? "且" : "或" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from "vue";
import { cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import FilterField from "./FilterField.vue";
import { copyText } from "@/utils/helpers";
import * as siteApi from "@/api/site";
const { $app, proxy } = useCtx();

const props = defineProps(["conf", "fields", "isDisabled", "metaLabelList", "displayCopy"]);

let filterConf = ref({
  nodes: [],
});
filterConf.value["@type"] = "AND";

const numberType = ["INT", "LONG", "DOUBLE", "FLOAT32"];

const multipleArgs = ["IN", "NOT_IN", "CONTAINS_ANY", "NOT_CONTAINS_ANY"];

const mongoVarArgs = ["EQ", "NE", "LE", "LT", "GE", "GT"];

const renderKey = ref(0);

watch(
  () => props.conf,
  (val) => {
    if (val && val.nodes) {
      let tempVal = cloneDeep(val);
      filterConf.value = {
        ...tempVal,
        nodes: handleNodesPre(tempVal.nodes),
      };
    } else {
      initData();
    }
  },
  { immediate: true, deep: true }
);

const disabledCopy = computed(() => {
  return filterConf.value.nodes.length > 0;
});

function handleNodesPre(nodes) {
  if (nodes && nodes.length) {
    nodes.forEach((item) => {
      if (item.nodes && item.nodes.length) {
        item.nodes = handleNodesPre(item.nodes);
      } else {
        if (item.op === "BETWEEN") {
          item.args1 = item.args[0];
          item.args2 = item.args[1];
        } else if (item.args.length > 1) {
          item.args = item.args.join(",");
        } else {
          item.args = item.args[0];
        }
        if (item.field.includes(".")) {
          let arr = item.field.split(".");
          item.field = arr[0];
          item.field2 = arr[1];
        }
      }
    });
  }
  return nodes;
}

function handleNodesPost(nodes) {
  if (nodes && nodes.length) {
    for (let i = 0; i < nodes.length; i++) {
      let item = nodes[i];
      console.log(item);

      if (item.nodes && item.nodes.length) {
        let temp = handleNodesPost(item.nodes);
        if (!temp) {
          return;
        } else {
          item.nodes = temp;
        }
      } else {
        if (!item.field || !item.op || !item["@type"] || (item.op !== "BETWEEN" && dataC.isEmpty(item.args))) {
          $app.$message.warning("配置中存在必填项为空，请检查！");
          return;
        }
        if (item.field == "levels") {
          item.field = item.field + "." + item.field2;
        }
        delete item.field2;
        item.args = !dataC.isEmpty(item.args) ? item.args : "";
        if (item.op === "BETWEEN") {
          item.args = [item.args1, item.args2];
          delete item.args1;
          delete item.args2;
        } else if (multipleArgs.includes(item.op)) {
          item.args = item.args.split(",");
        } else {
          item.args = [item.args];
        }
        if (item.field != "levels" && item.field != "q_user" && item.op === "BETWEEN" && Number(item.args[0]) < Number(item.args[1])) {
          $app.$message.warning("配置中存在最小值大于最大值，请检查！");
          return;
        }
        if (numberType.includes(item["@type"]) || item["@type"] === "LENGTH") {
          //如果采用的是mongoVarArgs,获取的形如 ${}值
          const eqNeRegex = /\$\{.*?\}/g;
          if (mongoVarArgs.includes(item.op) && eqNeRegex.test(item.args[0])) {
            continue;
          }
          for (let j = 0; j < item.args.length; j++) {
            //正则检查
            const regex = /^-?\d+(\.\d+)?$/;
            if (!regex.test(item.args[j])) {
              $app.$message.warning(`存在不符合规则的非数值类型，请检查过滤设置Args！`);
              return;
            }
            //转换检查
            let a = parseFloat(item.args[j]);
            if (isNaN(a)) {
              $app.$message.warning(`存在不符合规则的非数值类型，请检查过滤设置Args！`);
              return;
            }
            //如果不为LONG类型(LONG类型太长时在前端强转会丢失精度)
            if (item["@type"] != "LONG") {
              item.args[j] = a;
            }
          }
        }
      }
    }
  }
  return nodes;
}

function getSaveFilterConf() {
  let conf = cloneDeep(filterConf.value);
  let nodes = handleNodesPost(conf.nodes);

  return nodes
    ? {
        ...conf,
        nodes,
      }
    : false;
}

function addNode(filterConf, index) {
  let node = {
    field: "",
    args: "",
    op: "",
  };
  node["@type"] = "";
  if (index >= 0) {
    filterConf.nodes.splice(index + 1, 0, node);
    return;
  }
  filterConf.nodes.push(node);
}

function copyConf() {
  emit("copy-conf");
}

function mongo() {
  const expr = getSaveFilterConf();
  if (expr) {
    siteApi.mongo(expr).then((result) => {
      copyText(result.data);
    });
  }
}

function setConf(val) {
  let tempVal = cloneDeep(val);
  if (dataC.isEmpty(filterConf.value.nodes)) {
    filterConf.value["@type"] = tempVal["@type"];
  }
  filterConf.value.nodes.unshift(...handleNodesPre(tempVal.nodes));
  renderKey.value += 1;
}

function changeType(node) {
  if (!props.isDisabled) {
    node["@type"] = node["@type"] === "AND" ? "OR" : "AND";
  }
}

function addChildNode(nodes, index) {
  let parent = nodes[index];
  let newNode = {
    field: "",
    args: "",
    op: "",
  };
  newNode["@type"] = "";

  //没有子过滤条件时
  if (!parent.nodes) {
    let temp = cloneDeep(parent);
    parent.nodes = [temp, newNode];
    parent["@type"] = "AND";
    delete parent.field;
    delete parent.args;
    delete parent.op;
    return;
  }
  //有子过滤条件时
  parent.nodes.push(newNode);
}

function deleteNode(conf, index, parent, parentIndex) {
  if (conf.nodes.length == 2 && parent) {
    let keepNode = cloneDeep(index === 0 ? conf.nodes[1] : conf.nodes[0]);
    if (!keepNode.nodes) {
      // let tempNode = cloneDeep(conf.nodes.splice(index, 1))
      conf["@type"] = keepNode["@type"];
      conf.field = keepNode.field;
      conf.field2 = keepNode.field2;
      conf.op = keepNode.op;
      conf.args = keepNode.args;
      conf.args1 = keepNode.args1;
      conf.args2 = keepNode.args2;
      delete conf.nodes;
      renderKey.value += 1;
      return;
    }
  }
  conf.nodes.splice(index, 1);
  if (!conf.nodes.length && parent) {
    parent.nodes.splice(parentIndex, 1);
  }
  renderKey.value += 1;
}

function initData() {
  filterConf.value = {
    nodes: [],
  };
  filterConf.value["@type"] = "AND";
}

//事件声明
const emit = defineEmits(["copy-conf", "mongo"]);
//接口暴露
defineExpose({
  initData,
  getSaveFilterConf,
  setConf,
});
</script>

<style lang="scss" scoped>
.container {
  padding: 0 20px;
  .layer {
    position: relative;
    margin-bottom: 10px;
    .layer-body {
      padding-left: 40px;
      margin-bottom: 5px;
    }
    .layer-line {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #dcdfe6;
      left: 14px;
      .layer-line-button {
        width: 24px;
        height: 24px;
        position: absolute;
        left: -12px;
        top: 50%;
        margin-top: -12px;
        text-align: center;
        line-height: 24px;
        background: #d1e9f3;
        color: #087fad;
        cursor: pointer;
      }
    }

    .layer-line::before,
    .layer-line::after {
      content: "";
      position: absolute;
      border: 2px solid #dcdfe6;;
    }

    .layer-line::before {
      top: 0;
      left: 0;
      border-right: none;
      border-bottom: none;
      height: 2px;
      width: 10px;
    }

    .layer-line::after {
      bottom: 0;
      left: 0;
      border-right: none;
      border-top: none;
      height: 2px;
      width: 10px;
    }
  }
}
</style>
