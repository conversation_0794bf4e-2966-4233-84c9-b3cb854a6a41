<template>
  <el-row :gutter="8" class="filter-field">
    <!-- field start -->
    <el-col :span="node.field && node.field == 'levels' ? 3 : 6">
      <el-select v-model="node.field" :disabled="isDisabled" @change="changeField(node)" filterable>
        <el-option v-for="item in fields" :label="item.label" :value="item.value" />
      </el-select>
    </el-col>
    <!-- field end -->
    <!-- levels start -->
    <el-col :span="3" v-if="node.field && node.field == 'levels'">
      <el-select v-model="node.field2" :disabled="isDisabled" placeholder="请选择精品等级">
        <el-option v-for="item in metaLabelList" :label="item.name" :value="item.code" />
      </el-select>
    </el-col>
    <!-- levels end -->
    <!-- @type start -->
    <el-col :span="4">
      <el-select v-model="node['@type']" @change="(val) => changeAtType(node, val)" :disabled="isDisabled">
        <el-option v-for="item in getTypeOptions(node.field)" :label="item.label" :value="item.value" />
      </el-select>
    </el-col>
    <!-- @type end -->
    <!-- op start -->
    <el-col :span="4">
      <el-select v-model="node.op" @change="(val) => changeOp(node, val)" :disabled="isDisabled">
        <el-option v-for="item in getRangeOptions(node['@type'])" :label="item.label" :value="item.value" />
      </el-select>
    </el-col>
    <!-- op end -->
    <!-- levels start -->
    <el-col :span="6" v-if="node.field == 'levels'">
      <el-select v-model="node.args" :disabled="isDisabled" placeholder="请选择精品等级">
        <el-option v-for="item in getMetaLabelValueList(node.field2)" :label="item.key" :value="item.value" />
      </el-select>
    </el-col>
    <!-- levels end -->
    <!-- q_user start -->
    <el-col :span="6" v-else-if="node.field == 'q_user'">
      <el-select v-model="node.args" :disabled="isDisabled" placeholder="请选择质量等级">
        <el-option label="S" :value="4" />
        <el-option label="A" :value="3" />
        <el-option label="B" :value="2" />
        <el-option label="C" :value="1" />
      </el-select>
    </el-col>
    <!-- q_user end -->
    <!-- between start -->
    <template v-else-if="node.field != 'levels' && node.field != 'q_user' && node.op === 'BETWEEN'">
      <el-col :span="3">
        <el-input v-model="node.args1" placeholder="最大值" :disabled="isDisabled"></el-input>
      </el-col>
      <el-col :span="3">
        <el-input v-model="node.args2" placeholder="最小值" :disabled="isDisabled"></el-input>
      </el-col>
    </template>
    <!-- between end -->
    <!-- past start -->
    <el-col :span="3" v-else-if="node.field != 'levels' && node.field != 'q_user' && node.op === 'PAST'">
      <el-input type="number" v-model="node.args1" placeholder="请输入数字" :disabled="isDisabled"></el-input>
    </el-col>
    <el-col :span="3" v-else-if="node.field != 'levels' && node.field != 'q_user' && node.op === 'PAST'">
      <el-select v-model="node.args2" :disabled="isDisabled" placeholder="请选择单位">
        <el-option v-for="item in dateType" :label="item.label" :value="item.value" />
      </el-select>
    </el-col>
    <!-- past end -->
    <!-- mongoVar start -->
    <el-col :span="6" v-else-if="node.field != 'levels' && node.field != 'q_user' && node['@type'] != 'LENGTH' && mongoVarArgs.includes(node.op)">
      <el-select v-model.trim="node.args" :disabled="isDisabled" filterable allow-create placeholder="请输入或选择">
        <el-option v-for="item in fieldsFilterByType" :label="item.label" :value="'${' + item.value + '}'" />
      </el-select>
    </el-col>
    <!-- mongoVar end -->
    <!-- args start -->
    <el-col :span="6" v-else>
      <el-tooltip :visible="argsTooltipVisible && node.args.length > 20">
        <template #content>
          <span>{{ node.args }}</span>
        </template>
        <el-input
          v-model.trim="node.args"
          :placeholder="multipleArgs.includes(node.op) ? '多个值用英文逗号分割,请勿出现空格' : '请勿出现空格'"
          :disabled="isDisabled"
          @mouseenter="argsTooltipVisible = true"
          @mouseleave="argsTooltipVisible = false"
        ></el-input>
      </el-tooltip>
    </el-col>
    <!-- args end -->
    <el-col :span="4" v-show="!isDisabled">
      <el-button link type="danger" style="margin-left: 10px" @click="events.delete">
        <el-icon size="18px">
          <Delete />
        </el-icon>
      </el-button>
      <el-button link type="primary" @click="events.add">
        <el-icon size="18px">
          <CirclePlus />
        </el-icon>
      </el-button>
    </el-col>
  </el-row>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from "vue";
import { cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();

const emits = defineEmits(["update:modelValue", "add", "delete"]);

const props = defineProps({
  modelValue: { type: Object },
  fields: { type: Array as any },
  metaLabelList: { type: Array as any },
  isDisabled: { type: Boolean, default: false },
});

// 创建内部响应式数据副本，基于 props.modelValue 来初始化
const internalModelValue = reactive({ ...props.modelValue });

// 内部node对象
const node = reactive({
  field: internalModelValue.field,
  field2: internalModelValue.field2,
  args: internalModelValue.args,
  args1: internalModelValue.args1,
  args2: internalModelValue.args2,
  op: internalModelValue.op,
  "@type": internalModelValue["@type"],
});

// 监听内部响应式数据副本的变化，更新 node 对象的属性
watch(
  internalModelValue,
  (newVal) => {
    node.field = newVal.field;
    node.field2 = newVal.field2;
    node.args = newVal.args;
    node.args1 = newVal.args1;
    node.args2 = newVal.args2;
    node.op = newVal.op;
    node["@type"] = newVal["@type"];
    emits("update:modelValue", cloneDeep(newVal));
  },
  { deep: true }
);

// 监听 node 对象的变化，当变化时更新父组件的值，这里传递整个 node 对象进行更新
watch(
  node,
  () => {
    emits("update:modelValue", cloneDeep(node));
  },
  { deep: true }
);

const numberType = ["INT", "LONG", "DOUBLE", "FLOAT32"];

const multipleArgs = ["IN", "NOT_IN", "CONTAINS_ANY", "NOT_CONTAINS_ANY"];

const mongoVarArgs = ["EQ", "NE", "LE", "LT", "GE", "GT"];

const dateType = [
  { label: "日", value: "day" },
  { label: "周", value: "week" },
  { label: "月", value: "month" },
  { label: "年", value: "year" },
];

function changeField(node) {
  node["@type"] = "";
  changeAtType(node, "");
}

function changeAtType(node, val) {
  node.op = "";
  node.args = "";
  delete node.args1;
  delete node.args2;
}

function changeOp(node, op) {
  if (op === "BETWEEN" || op === "PAST") {
    delete node.args;
    node.args1 = "";
    node.args2 = "";
  } else if (op === "LATEST") {
    delete node.args1;
    delete node.args2;
    node.args = 1;
  } else {
    delete node.args1;
    delete node.args2;
    node.args = "";
  }
}

const getTypeOptions = (val: String) => {
  //查询选择的Field
  const list = props.fields.filter((item) => item.value === val);
  if (list.length === 0) return [];
  //将type改为全大写
  const type = list[0].type.toUpperCase();
  const result = [{ label: `内容(${type})`, value: type }];
  if ("STRING" == type) {
    result.push({ label: "长度", value: "LENGTH" });
  }
  return result;
};

const getRangeOptions = (val: String) => {
  if (val == "LENGTH") {
    return [
      { label: "等于", value: "EQ" },
      { label: "不等于", value: "NE" },
      { label: "小于等于", value: "LE" },
      { label: "小于", value: "LT" },
      { label: "大于等于", value: "GE" },
      { label: "大于", value: "GT" },
      { label: "两者之间", value: "BETWEEN" },
    ];
  } else if (numberType.includes(val)) {
    return [
      { label: "等于", value: "EQ" },
      { label: "不等于", value: "NE" },
      { label: "小于等于", value: "LE" },
      { label: "小于", value: "LT" },
      { label: "大于等于", value: "GE" },
      { label: "大于", value: "GT" },
      { label: "两者之间", value: "BETWEEN" },
    ];
  } else if (val == "STRING") {
    return [
      { label: "等于", value: "EQ" },
      { label: "不等于", value: "NE" },
      { label: "在列表中", value: "IN" },
      { label: "不在列表中", value: "NOT_IN" },
      { label: "包含正则", value: "REGEX" },
      { label: "不包含正则", value: "NOT_REGEX" },
      { label: "包含", value: "CONTAINS" },
      { label: "不包含", value: "NOT_CONTAINS" },
      { label: "包含列表中任一个", value: "CONTAINS_ANY" },
      { label: "不包含列表中任一个", value: "NOT_CONTAINS_ANY" },
      { label: "以开始", value: "START_WITH" },
    ];
  } else if (val == "DATE") {
    return [
      { label: "按时间倒序取N条 ", value: "LATEST" },
      { label: "按时间倒序取时间范围内", value: "PAST" },
    ];
  } else {
    return [];
  }
};

// 获取元Label值列表
const getMetaLabelValueList = (code: String) => {
  if (dataC.isEmpty(code) || dataC.isEmpty(props.metaLabelList)) return [];
  return dataC.getItemByValue(props.metaLabelList, code, "code").values;
};

const events = reactive({
  add: () => {
    emits("add");
  },
  delete: () => {
    emits("delete");
  },
});

// 控制args过长时tooltip显示
const argsTooltipVisible = ref(false);

// 根据type过滤fields获取列表
const fieldsFilterByType = computed(() => {
  return props.fields.filter((item) => {
    return item.type.toUpperCase() == node["@type"] && node.field != item.value;
  });
});
</script>

<style lang="scss" scoped></style>
