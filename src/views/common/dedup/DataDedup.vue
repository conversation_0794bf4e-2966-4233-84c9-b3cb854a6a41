<template>
  <my-drawer class="data-dedup" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
      <template #dedupConfig>
        <dedup-config ref="dedupConfigRef" v-model="ruleForm.dedupConfig" :showMetaDedup="true" />
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys, cloneDeep } from "lodash";
import DedupConfig from "./DedupConfig.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";

const { $app, proxy } = useCtx();
//弹窗相关
const dialogTitle = computed(() => {
  return `去重`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const formData = cloneDeep(ruleForm.value);
      formData.script = proxy.$refs["dedupConfigRef"].getScript();
      formData.scriptParam = proxy.$refs["dedupConfigRef"].getScriptParam();
      if (formData.scriptParam && Object.keys(formData.scriptParam).length === 0) {
        $app.$message.warning("去重规则不可为空!");
        return;
      }
      if (dataC.isEmpty(formData.script)) {
        $app.$message.warning("去重脚本不可为空!");
        return;
      }
      if (!formData.scriptParam || dataC.isEmpty(formData.scriptParam)) {
        //$app.$message.warning("去重参数不可为空!");
        return;
      } else if (formData.scriptParam["scene"] != "医疗") {
        if (typeof formData.scriptParam["fields"] !== "string") {
          $app.$message.warning(`fields类型错误!`);
          return;
        }
        const paramList = formData.scriptParam["fields"].split(",");
        for (const param of paramList) {
          if (dataC.isEmpty(formData.scriptParam[param])) {
            $app.$message.warning(`${param}不可为空!`);
            return;
          }
        }
      }
      if (!dataC.isEmpty(formData.scriptParam["levels"]) && formData.scriptParam["levels"] instanceof Array) {
        formData.scriptParam["levels"] = formData.scriptParam["levels"].join(",");
      }
      emit("dedup-data", formData);
    }
  });
};
// 表单相关
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  dedupConfig: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({});
// 表单项
const formItems = ref<any>({
  dedupConfig: {
    label: undefined,
    type: "slot",
    slotName: "dedupConfig",
    attrs: {
      class: "no-label",
    },
  },
});
//打开窗口
const openWindow = async (row: any) => {
  dialogVisible.value = true;
  nextTick(() => {
    ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    const obj = cloneDeep({...dataC.safeObject(row.dedupConfig)});
    proxy.$refs["dedupConfigRef"].handleUpdate(obj)
  });

};
//关闭窗口
const closeWindow = async (row: any) => {
  handleClose();
};
//事件声明
const emit = defineEmits(["dedup-data"]);
//接口暴露
defineExpose({
  openWindow,
  closeWindow,
});
</script>
<style lang="scss">
.data-dedup {
}
</style>
