<template>
  <div class="dedup-config" style="width: 100%">
    <template v-if="showMetaDedup">
      <h3 class="common-part-title" style="margin-bottom: 16px; margin-top: 10px">去重规则</h3>
      <my-select v-model="metaDedupId" :options="ruleOptions" value-in-label @change="ruleChange" :disabled="readOnly || metaDedupIdDisabled" />
    </template>
    <template v-if="!dataC.isEmpty(metaDedupId)">
      <h3 class="common-part-title" style="margin-bottom: 16px; margin-top: 10px">入参</h3>
      <paramUse :key="pramUseKey" :inputArgs="inputArgs" :readOnly="readOnly" />
      <h3 class="common-part-title" style="margin-bottom: 16px; margin-top: 10px">脚本</h3>
      <CustomCodemirror ref="codeMirrorDef" readOnly :domId="`domId${new Date().getTime()}`" :initVal="`加载脚本中...`" height="500px" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, nextTick } from "vue";
import { cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import paramUse from "@/views/common/paramComp/paramUse.vue";
import CustomCodemirror from "@/views/common/paramComp/components/CustomCodemirror.vue";

const { api } = useStore();
const { $app, proxy } = useCtx();

// Props 和 Emits
const props = defineProps({
  modelValue: { type: Array as any }, // 父组件 v-model 绑定的值
  readOnly: { type: Boolean, default: false },
  showMetaDedup: { type: Boolean, default: true },
});
const emits = defineEmits(["update:modelValue"]);

// 响应式变量
const metaDedupId = ref<string>("");
const metaDedupName = ref<string>("");
const metaDedupIdDisabled = ref<boolean>(false);
const inputArgs = ref<any[]>([]);
const pramUseKey = ref(0);
const script = ref<string>("");
const ruleOptions = ref<any[]>([]);
const oldData = ref<any>();
const newData = ref<any>();

// 更新父组件的 v-model
const updateValue = () => {
  const obj = {
    metaDedupId: metaDedupId.value,
    inputArgs: inputArgs.value,
    script: script.value,
    metaDedupName: metaDedupName.value,
  };
  emits("update:modelValue", JSON.stringify(obj));
};

// 获取去重规则选项
const getRuleOptions = async () => {
  const region = await api.getVerifyRegion();
  ruleOptions.value = await api.getMetaDedupOptions(region);
};

// 处理规则变更
const ruleChange = (obj: any = {}, type?: string) => {
  if (dataC.isEmpty(obj)) {
    resetConfig();
  } else {
    metaDedupId.value = obj.value;
    metaDedupName.value = obj.label;
    inputArgs.value = obj.inputArgs.map((v: any) => ({
      ...v,
      value: type !== "old" ? v.defaultValue : v.value,
    }));
    script.value = obj.script;
    pramUseKey.value += 1;
  }
  updateValue();
  nextTick(() => {
    proxy.$refs["codeMirrorDef"]?.updateCodemirrorVal(script.value);
  });
};

// 重置配置
const resetConfig = () => {
  metaDedupId.value = "";
  metaDedupIdDisabled.value = false;
  metaDedupName.value = "";
  inputArgs.value = [];
  script.value = "";
};

// 处理更新逻辑
const handleUpdate = async (oldVal: any) => {
  resetConfig();
  await getRuleOptions();

  if (dataC.isEmpty(oldVal.metaDedupId)) return;

  newData.value = cloneDeep(dataC.getItemByValue(ruleOptions.value, oldVal.metaDedupId, "value"));
  oldData.value = cloneDeep({
    ...oldVal,
    value: oldVal.metaDedupId,
    label: oldVal.metaDedupName,
    script: oldVal.script || oldVal.dedupScript,
    disabled: true,
  });

  if (dataC.isEmpty(newData.value?.value)) {
    await handleDeletedRule();
  } else if (hasRuleChanged()) {
    await handleChangedRule();
  } else if (oldData.value?.label !== newData.value?.label) {
    handleRenamedRule();
  } else {
    ruleChange(oldData.value, "old");
  }
};

// 处理已删除规则
const handleDeletedRule = async () => {
  try {
    await $app.$confirm({ title: `当前去重规则已被删除，是否继续使用？` });
    ruleOptions.value.push(oldData.value);
    metaDedupIdDisabled.value = true;
    ruleChange(oldData.value, "old");
  } catch {
    ruleChange();
  }
};

// 检查数组是否相等
const arraysAreEqual = (array1: any[], array2: any[]) => {
  return array1.every((item1) => array2.some((item2) => item1.key === item2.key));
};

// 检查规格变更
const hasRuleChanged = () => {
  // 检查 inputArgs 是否发生变化
  const inputArgsChanged = !arraysAreEqual(oldData.value?.inputArgs || [], newData.value?.inputArgs || []);

  // 检查脚本是否发生变化
  const scriptChanged = oldData.value?.script !== newData.value?.script;

  // 如果 inputArgs 或脚本发生变化，则返回 true
  return inputArgsChanged || scriptChanged;
};

// 处理规则变更
const handleChangedRule = async () => {
  try {
    await $app.$confirm({ title: `当前去重规则发生更改，是否需要更新？` });
    ruleChange(newData.value);
  } catch {
    ruleChange(oldData.value, "old");
  }
};

// 处理规则重命名
const handleRenamedRule = () => {
  if (oldData.value?.label) {
    $app.$message.warning("当前去重规则名称发生改变");
  }
  ruleChange(oldData.value, "old");
};

// 监控表单变化
watch(() => metaDedupId.value, updateValue);
watch(() => inputArgs.value, updateValue, { deep: true });
watch(() => script.value, updateValue);

// 获取脚本和参数
const getScript = () => script.value;
const getScriptParam = () => {
  const param: Record<string, any> = {};
  for (const item of inputArgs.value) {
    if (item.required && dataC.isEmpty(item.value)) {
      $app.$message.warning(`${item.name}不可为空!`);
      return false;
    }
    param[item.key] = item.value;
  }
  return param;
};

// 暴露接口
defineExpose({
  getScript,
  getScriptParam,
  handleUpdate,
});
</script>

<style scoped></style>
