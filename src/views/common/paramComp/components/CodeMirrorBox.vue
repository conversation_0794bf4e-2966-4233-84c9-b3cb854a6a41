<template>
  <el-dialog 
    title="代码编辑器" 
    v-model="dialogVisible" 
    width="1200px" 
    append-to-body 
    destroy-on-close 
    modal-class="astrolink-flow-dialog" 
    :close-on-click-modal="false">
    <el-tabs v-model="currentTab">
      <el-tab-pane label="代码块" :name="1"></el-tab-pane>
      <el-tab-pane :label="isPerview ? '代码说明' : '代码说明【markdown语法】'" :name="2"></el-tab-pane>
    </el-tabs>
    <CustomCodemirror v-show="currentTab === 1" :domId="`domId${new Date().getTime()}`" :language="lang" :initVal="initVal" height="500px" @updateVal="(val: any) => (initVal = val)" />
    <TMakrdownMirror ref="TMakrdownMirroiRef" v-show="currentTab === 2" :defaultVal="initDesc" :mode="isPerview ? 'preview' : 'editable'" @copyCodeSuccess="$app.$message.success('复制成功')" />
    <template v-if="!readOnly" #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sure">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup name="CodeMirrorBox">
import { ref } from "vue"
import useCtx from "@/hooks/useCtx"
import CustomCodemirror from "./CustomCodemirror.vue"
import { TMakrdownMirror } from "skynet-pandora-ui"

const { $app, proxy } = useCtx()

withDefaults(
  defineProps<{
    readOnly?: boolean
  }>(),
  {
    readOnly: false,
  }
)

const emit = defineEmits(["getCode"])

const dialogVisible = ref(false)
const initVal = ref("")
const lang = ref("")
const initDesc = ref("")
const isPerview = ref(false)
const currentTab = ref(1)

const openDialog = (defaultValue: string, argProps: any, perview: boolean = true) => {
  currentTab.value = 1
  dialogVisible.value = true
  initVal.value = defaultValue
  lang.value = argProps.lang || ""
  initDesc.value = argProps.remark || ""
  isPerview.value = perview
}

const closeDialog = () => {
  dialogVisible.value = false
}

const sure = () => {
  emit("getCode", initVal.value, proxy.$refs.TMakrdownMirroiRef && proxy.$refs.TMakrdownMirroiRef.getCodeText())
  closeDialog()
}

defineExpose({ openDialog, closeDialog })
</script>

<style lang="scss" scoped></style>
