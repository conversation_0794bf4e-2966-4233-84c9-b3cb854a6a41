<template>
  <div class="card-list" ref="tempListBox">
    <div class="top flexBetween">
      <div class="card-tilte">指令模板</div>
      <div class="top-search-box">
        <el-input class="search-input" v-model="name" placeholder="请输入指令模板名称" :prefix-icon="Search" @input="searchNameEvt" style="width: 264px;"></el-input>
        <span class="m-l-24">标签</span>
        <el-select class="search-input" v-model="activeTag" placeholder="请选择" clearable filterable @change="searchNameEvt" style="width: 160px;margin-left: 8px;">
            <el-option v-for="item in tagList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </div>
    </div>
    <div class="list-box" v-if="agentList && agentList?.length > 0">
      <div class="left">
        <div v-for="item in agentList" :key="item.id" class="card-item ellipsis"
          @click="changeCurrent(item)" :class="item.name === currentCommand?.name ? 'active' : ''">
          <div class="ellipsis" :title="item.name">{{ item.name }}</div>
        </div>
      </div>
      <div class="right">{{ currentCommand?.content }}</div>
    </div>
    <div v-else style="background: #ffffff;max-height: 176px;">
      <my-empty :size="70"  />
    </div>
    <div class="btn-group">
      <el-button @click="closeEvt()">取消</el-button>
      <el-button type="primary" @click="selectItem(currentCommand)">确定</el-button>
    </div>
  </div>
</template>
   
<script setup name="AtDialog" lang="ts">
import { Search } from '@element-plus/icons-vue'
import { onMounted, onUnmounted, ref, watch, } from 'vue'
import useCtx from "@/hooks/useCtx";
const { proxy } = useCtx();

const props = defineProps({
  agentList: {
    type: Array<any>,
    default: () => ([]),
  },
  tagList: {
    type: Array<any>,
    default: () => ([]),
  }
})

const emits = defineEmits(['selectItem', 'closeEvt', 'searchEvt'])

const name = ref('')
const activeTag = ref('')
const currentCommand = ref<any>({})

const changeCurrent = (item:any) => {
  currentCommand.value = item
}
// 检索
const searchNameEvt = () => {
  emits('searchEvt', {'name.contains': name.value, 'labelId.equals': activeTag.value})
}
const selectItem = (item:any) => {
  emits('selectItem', item)
}
const closeEvt = () => {
  emits('closeEvt', false)
}

watch(()=> props.agentList, (val)=> {
  currentCommand.value = val[0] || {}
}, {
  immediate: true,
  deep: true
})

const outSideLinstener = (event:any)=> {
 // 检查点击事件是否发生在弹框外
 const popup = proxy.$refs.tempListBox
  if (event.target !== popup && !popup.contains(event.target)) {
    closeEvt() // 如果是，关闭弹框
  }
}

onMounted(()=> {
  document.addEventListener('click', outSideLinstener);
})
onUnmounted(()=> {
  document.removeEventListener('click', outSideLinstener)
})

</script>
   
<style scoped lang="scss">
.card-list {
  position: absolute;
  top: 100px;
  left: 20px;
  border: 1px solid #dee3eb;
  border-radius: 8px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
  z-index: 99;
  width: calc(100% - 40px);
  background-size: 100%;
  background-color: #ffffff;
  background-image: url("../assets/images/card-bg.png") no-repeat;
  .search-input {
    border: none;

    :deep(.el-input__wrapper) {
      box-shadow: none;
      background: #ffffff;
      border-radius: 4px;

      &:hover {
        box-shadow: none;
      }
    }
  }
  .m-l-24{
    margin-left: 24px;
  }

  .top {
    padding: 10px 16px;
    color: #303133;
    font-size: 14px;
    font-weight: 500;
    .top-search-box{
      display: flex;
      align-items: center;
      justify-content: space-between;
     
    }
    .card-tilte{
        font-weight: 500;
        color: $text-color;
        font-size: 16px;
      }
  }

  .list-box {
    margin: 0 16px;
    display: flex;
    font-size: 14px;
    background: #ffffff;
    border: 1px solid $border-color;
    border-radius: 6px;
    padding: 0 8px;
    max-height: 176px;
    overflow-y: auto;

    .left {
      padding: 8px;
      padding-left: 0;
      width: 132px;
      border-right: 1px solid $border-color-split;
      overflow-y: auto;

      .card-item {
        padding: 6px 12px;
        border-radius: 4px;
        font-weight: 500;
        color: #30405d;
        cursor: pointer;

        &:hover {
          background: rgba(75, 114, 239, 0.08);
        }
      }

      .active {
        color: $primary-color;
        background: rgba(75, 114, 239, 0.08);
      }
    }

    .right {
      padding: 8px 8px 16px;
      flex: 1;
      color: $text-color-secondary;
      line-height: 22px;
      overflow-y: auto;
    }
  }

  .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .output-no-data {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }

    span {
      font-size: 14px;
      color: #a8abb2;
      line-height: 26px;
    }
  }
  .btn-group{
    padding: 12px 20px;
    display: flex;
    justify-content: flex-end;
    background: #ffffff;
  }
}</style>