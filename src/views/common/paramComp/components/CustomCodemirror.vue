<!--
 * @Author: baozhang4 <EMAIL>
 * @Date: 2024-10-21 15:15:14
 * @LastEditors: baozhang4 <EMAIL>
 * @LastEditTime: 2024-10-21 17:28:16
 * @FilePath: /astrolink-platform-ui/src/components/CustomCodemirror.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div :id="domId" :style="{ width: width, height: height }"></div>
</template>
<script lang="ts" setup name="CustomCodemirror">
import { onMounted } from "vue";
import * as monaco from "monaco-editor";
import editorWorker from "monaco-editor/esm/vs/editor/editor.worker?worker";
import jsonWorker from "monaco-editor/esm/vs/language/json/json.worker?worker";
import cssWorker from "monaco-editor/esm/vs/language/css/css.worker?worker";
import htmlWorker from "monaco-editor/esm/vs/language/html/html.worker?worker";
import tsWorker from "monaco-editor/esm/vs/language/typescript/ts.worker?worker";

// 在初始化之前，先设置MonacoEnvironment
// @ts-ignore
self.MonacoEnvironment = {
  getWorker: function (workerId: string, label: string) {
    if (label === "json") {
      return new jsonWorker();
    }
    if (label === "css" || label === "scss" || label === "less") {
      return new cssWorker();
    }
    if (label === "html" || label === "handlebars" || label === "razor") {
      return new htmlWorker();
    }
    if (label === "typescript" || label === "javascript") {
      return new tsWorker();
    }
    return new editorWorker();
  },
};

const props = defineProps({
  initVal: {
    type: String,
    default: "dracula",
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
  domId: {
    type: String,
    default: "editorBox",
  },
  language: {
    type: String,
    default: "javascript",
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "600px",
  },
  options: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["updateVal"]);
let editorInstance: any;

const initEditor = () => {
  // 初始化编辑器，确保dom已经渲染
  editorInstance = monaco.editor.create(document.getElementById(props.domId) as HTMLElement, {
    value: props.initVal,
    language: props.language,
    automaticLayout: true,
    theme: "vs-dark",
    tabSize: 4,
    fontSize: 12,
    readOnly: props.readOnly,
    ...props.options,
  });
  editorInstance.onDidChangeModelContent(() => {
    emit("updateVal", editorInstance.getValue());
  });
};

const updateCodemirrorVal = (val: string) => {
  editorInstance.setValue(val);
};

onMounted(() => {
  initEditor();
});

defineExpose({ updateCodemirrorVal });
</script>

<style lang="scss"></style>
