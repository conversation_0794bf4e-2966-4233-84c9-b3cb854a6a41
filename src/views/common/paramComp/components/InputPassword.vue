<template>
  <input 
    v-if="isInput" 
    class="custom-password-input" 
    v-model="inputShowVal" 
    @input="inputEvent" 
    @blur="inputBlur" 
    :disabled="disabled" />
  <div v-else class="pass-text">
    <span>{{ "*".repeat(12) }}</span>
    <el-icon v-if="!disabled" class="close-icon" @click="closeClick"><CircleClose /></el-icon>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, onMounted } from "vue";
import { CircleClose } from "@element-plus/icons-vue";
import { JSEncrypt } from "jsencrypt";

const props = defineProps(["inputVal", "disabled", "passwordPublicKey"]);
const emit = defineEmits(["changeVal"]);


function getEncrypt(str: string, publicKey: string): string {
  let encrypt = new JSEncrypt();
  encrypt.setPublicKey(publicKey);
  return encrypt.encrypt(str) as string;
}

/** 输入框显示的值 */
const inputShowVal = ref("");
/** 输入框真实的值 */
const inputRealVal = ref("");
/** 是否能输入，回显的数据是不能编辑的，只显示对应数量的* */
const isInput = ref(true);

watch(
  () => props.inputVal,
  (val) => {
    inputShowVal.value = "*".repeat(val.length);
    inputRealVal.value = val;
  },
  { immediate: true }
);

onMounted(() => {
  isInput.value = !Boolean(props.inputVal);
});

/** input输入框的input事件 */
const inputEvent = (e: any) => {
  const ind = e.target.selectionStart - 1;
  let value = inputRealVal.value;
  const showValue = inputShowVal.value;
  const isAdd = showValue.length > value.length;
  const num = Math.abs(value.length - showValue.length);
  if (isAdd) {
    value = value.slice(0, ind - num + 1) + showValue.slice(ind - num + 1, ind + 1) + value.slice(ind - num + 1);
  } else {
    value = value.slice(0, ind + 1) + value.slice(ind + num + 1);
  }
  inputRealVal.value = value;
  emit("changeVal", inputRealVal.value);
  inputShowVal.value = "*".repeat(value.length);
  nextTick(() => {
    const elem = e.target;
    elem.setSelectionRange && elem.setSelectionRange(ind + 1, ind + 1);
  });
};

const inputBlur = () => {
  isInput.value = !Boolean(inputRealVal.value);
  if (inputRealVal.value) {
    // 加密
    const RSAres = getEncrypt(inputRealVal.value, props.passwordPublicKey);
    emit("changeVal", RSAres);
  }
};

const closeClick = () => {
  isInput.value = true;
  emit("changeVal", "");
};
</script>

<style scoped lang="scss">
.custom-password-input {
  width: 100%;
  height: 32px;
  line-height: 30px;
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  padding: 1px 11px;

  &:focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

.pass-text {
  width: 100%;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  padding: 1px 11px;

  background-color: var(--el-disabled-bg-color);
  box-shadow: 0 0 0 1px var(--el-disabled-border-color) inset;
  color: var(--el-disabled-text-color);
  -webkit-text-fill-color: var(--el-disabled-text-color);
  cursor: not-allowed;

  .close-icon {
    display: none;
    color: #bababa;
    cursor: pointer;
  }

  &:hover {
    .close-icon {
      display: block;
    }
  }
}
</style>
