<template>
  <el-dialog 
    title="模板编辑器" 
    v-model="dialogTag" 
    width="1200px" 
    append-to-body 
    modal-class="astrolink-flow-dialog"
    destroy-on-close 
    :close-on-click-modal="false">
    <div class="temp-box">
      <el-card 
        class="left-list" 
        shadow="never" 
        v-if="level === 1 && leftList && leftList.length > 0">
        <template #header>
          <div class="card-header flex">
            <span style="font-weight: 600">变量字段</span>
            <span>【点击插入】</span>
          </div>
        </template>
        <div v-for="(item, index) in leftList" :key="index" class="item">
          <p :draggable="true" @dragend="dragendStart($event, item)" @click="addItem(item)">
            {{ `${item.name}（${item.key}）` }}
          </p>
        </div>
      </el-card>
      <el-card class="right-contain" header="模板" shadow="never">
        <template #header>
          <div class="temp-header-box">
            <div class="temp-title"><span class="title-tip"></span>模板</div>
          </div>
        </template>
        <CommandCard 
          v-if="showCommand" 
          :agent-list="selectList" 
          :tagList="tagList" 
          @selectItem="selectCommandItem" 
          @searchEvt="getTemList"
          @closeEvt="showCommand = false">
        </CommandCard>
        <el-input 
          id="textareaBox" 
          class="textarea-no-border" 
          type="textarea" 
          v-model="rightText" 
          :rows="21" 
          resize="none"
          :maxlength="defauleConfig.maxlength" 
          :placeholder="defauleConfig.placeholder" 
          show-word-limit 
          @blur="inputBlur"
          @input="handelInput">
        </el-input>
      </el-card>
    </div>
    <template v-if="!readOnly" #footer>
      <span>
        <el-button @click="dialogTag = false">取消</el-button>
        <el-button type="primary" @click="sure">确认</el-button>
      </span>
    </template>
    <el-dialog title="保存提示" 
      v-model="sureSave.dialogTag" width="500px" 
      :close-on-click-modal="false" 
      append-to-body
      modal-class="scene-agents-dialog">
      <el-input v-model="sureSave.name" placeholder="请输入指令名称"></el-input>
      <template #footer>
        <span>
          <el-button @click="sureSave.cancel">取消</el-button>
          <el-button type="primary" @click="sureSave.submit">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script lang="ts" setup name="Temps">
import { reactive, ref } from "vue";
import useCtx from "@/hooks/useCtx"
import CommandCard from "./CommandCard.vue"
import { getPromptList, handleAddPrompt, getTaglistPromot } from "../utils/api"

withDefaults(
  defineProps<{
    readOnly?: boolean,
     level?: number; // 层级
  }>(),
  {
    readOnly: false,
    level: 1,
  }
)

const emit = defineEmits(['change'])

const { $app } = useCtx()

const dialogTag = ref(false)

const leftList = ref<Array<any>>([])

const rightText = ref('')
const engineType = ref<string>('default')

const defauleConfig = reactive({
  placeholder: '',
  maxlength: 4000
})

const positionObj = reactive({
  start: -1,
  end: -1
})

const openDialog = (list: Array<any>, val: string, item: any, type?: string) => {
  getTemList("");
  getTaglist('PROMPT')
  rightText.value = val
  leftList.value = list
  dialogTag.value = true
  engineType.value = type || 'default'
  if (item.props) {
    item.props?.placeholder && (defauleConfig.placeholder = item.props.placeholder)
    item.props?.maxLength && (defauleConfig.maxlength = item.props.maxLength)
  } else {
    defauleConfig.placeholder = ''
    defauleConfig.maxlength = 4000
  }
}

const closeDialog = () => {
  dialogTag.value = false
}

const dragendStart = ($event: any, item: any) => {
}

const addItem = (item: any) => {
  if (positionObj.start === -1 || positionObj.end === -1) return $app.$message.warning('请在右侧选择位置！')
  const start = rightText.value.substring(0, positionObj.start)
  const end = rightText.value.substring(positionObj.end)
  if (engineType.value === 'default')
    rightText.value = start + `{{${item.key}}}` + end
  else
    rightText.value = start + `\${${item.key}}` + end
  positionObj.start = -1
  positionObj.end = -1
}

const inputBlur = () => {
  const inputDom: any = document.getElementById('textareaBox')
  positionObj.start = inputDom.selectionStart
  positionObj.end = inputDom.selectionEnd
}

// 快捷键展开指令列表
const showCommand = ref(false)
const selectList = ref<any>([])
// 获取指令模板
const getTemList = (params: any) => {
  getPromptList({
    ...params,
    page: 1,
    size: 10000,
  }).then((res) => {
    const { content = [] } = res
    selectList.value = content
  })
}
const handelInput = (value: string) => {
  if (value.includes('/') && value[value.length - 1] === '/') { // 输入/后展示指令选择列表、
    
    showCommand.value = true;
  } else {
    showCommand.value = false;
  }
}
// 输入/快捷选择指令
const selectCommandItem = (item: any) => {
  rightText.value = `${item.content}`;
  showCommand.value = false
}
// const ondragenter = ($event: any) => {
//   console.log(document.getElementById('textareaBox'));
//   const inputDom: any = document.getElementById('textareaBox')
//   inputDom?.focus()
//   inputDom?.setSelectionRange()
//   console.log($event);
// }

const sure = () => {
  emit('change', rightText.value)
  closeDialog()
}

// 保存指令
const savePrompt = () => {
  if(!rightText.value) {
    $app.$message.warning("请填写指令内容")
    return;
  }
  const param = {
    name: 'test',
    content: rightText.value,
  }
  handleAddPrompt(param).then((res) => {
    if (res && res.id) {
      $app.$message.success("保存成功")
      sureSave.cancel()
    } else {
      $app.$message.error(res.message)
    }
  })
}
const sureSave = reactive<any>({
  dialogTag: false,
  currentObj: {},
  name: "",
  title: "",
  cancel: () => {
    sureSave.dialogTag = false
    sureSave.name = ""
  },
  submit: () => {
    savePrompt()
  },
})

// 获取模板标签
const tagList = ref<any>([])
const getTaglist = (val:string) => {
  getTaglistPromot(val).then((res:any) => {
    tagList.value.push(...res);
  });
};

defineExpose({ openDialog, closeDialog })

</script>

<style lang="scss" scoped>
.temp-box {
  display: flex;
  height: 540px;

  .left-list {
    width: 290px;
    max-height: 100%;
    overflow-y: auto;
    margin-right: 12px;

    .card-header {
      h1 {
        font-size: 20px;
        font-weight: 600;
        margin-right: 6px;
      }
    }

    .item {
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #ffffff;
      border: 1px solid #e2e7ee;
      border-radius: 4px;
      box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.05);
      margin-bottom: 8px;
      user-select: all;

      p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover {
        cursor: pointer;
        border-color: $primary-color;
        box-shadow: 0px 1px 6px 0px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .right-contain {
    position: relative;
    flex: 1;
    .temp-header-box{
      display: flex;
      justify-content: space-between;
      .temp-title{
        display: flex;
        align-items: center;
        font-weight: 500;
        text-align: LEFT;
        color: $text-color;
        line-height: 22px;
        .title-tip{
          display: inline-block;
          width: 3px;
          height: 14px;
          background: $primary-color;
          margin-right: 8px;
        }
      }
      .add-prompt-btn{
        color: $primary-color;
        fill: $primary-color;
        &:hover {
          border-color: transparent;
          background-color: transparent;
        }
      }
      :deep(.el-button.is-link.is-disabled){
        opacity: 0.5;
      }
    }
  }
}</style>

<style lang="scss">
// 无需边框的textarea输入框
.textarea-no-border > .el-textarea__inner {
  text-align: justify;
  padding: 0;
  box-shadow: none;
  border-radius: 0;
  &:hover {
    box-shadow: none;
  }
}
</style>
