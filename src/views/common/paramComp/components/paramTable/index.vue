<template>
  <div class="paramTable">
    <div class="query-wrapper">
      <h3 class="common-part-title">{{ title }}</h3>
      <my-button type="primary" plain @click="events.add">新增参数</my-button>
    </div>
    <Table :columns="columns" :tableData="tableData" :operations="operations" @operation="handleOperation">
      <template #typeName="scope">
        {{ argsTypeList.find(e => e.value === scope.row.type)?.label }}
      </template>
    </Table>
    <paramAdd ref="paramAddRef" @update="events.updateTableData" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import paramAdd from "@/views/common/paramComp/paramAdd.vue";
import { argsTypeList } from "@/views/common/paramComp/utils/constants";
import Table from "./Table.vue";

// 实现v-model
const emits = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: { type: Array as any }, // 父组件v-model绑定的值
  title: { type: String, default: "入参" },
});

const tableData = ref<Array<any>>(props.modelValue || []);
watch(
  () => props.modelValue,
  (nu) => {
    if (nu) {
      tableData.value = [...nu];
    } else {
      tableData.value = [];
    }
  }
);

const updateValue = () => {
  emits("update:modelValue", tableData.value);
};

const { proxy, $app } = useCtx();
const { t } = useI18n();
const columns = ref([
  { prop: "key", label: "参数标识" },
  { prop: "name", label: "参数名称" },
  { prop: "type", label: "参数类型", slotName: "typeName" },
  { prop: "desc", label: "参数描述" },
  { prop: "operation", label: "操作", width: 120 },
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  { type: "delete", label: t("btn.delete"), btnType: "danger" },
];

const curIndex = ref<number>(0);
const handleOperation = (data: any) => {
  const { type, record, index } = data;
  if (type === "edit") {
    curIndex.value = index;
    proxy.$refs.paramAddRef.openDrawer(record);
  } else if (type === "delete") {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { n: record.key }),
      })
      .then(() => {
        tableData.value.splice(index, 1);
        updateValue();
      });
  }
};

const events = reactive({
  add: () => {
    curIndex.value = -1;
    proxy.$refs.paramAddRef.openDrawer(null);
  },
  updateTableData: (data: any) => {
    console.log(999999, data)
    if (
      Boolean(
        tableData.value.find(
          (e, i) => curIndex.value !== i && e.key === data.key
        )
      )
    ) {
      return $app.$message.warning("参数标识已存在！");
    }
    if (curIndex.value === -1) {
      tableData.value.push(data);
    } else {
      tableData.value[curIndex.value] = data;
    }
    updateValue();
    proxy.$refs.paramAddRef.closeDrawer();
  },
});
</script>

<style lang="scss" scoped>
.paramTable {
  width: 100%;
  .query-wrapper {
    @include flexBetween();
    padding: 10px 0;
  }
}
</style>