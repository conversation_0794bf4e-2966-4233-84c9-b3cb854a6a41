<template>
  <div class="corrugation-contain">
    <span class="span" v-for="(item, index) in spanList" :key="index" :style="{ 'animation-delay': item }"></span>
  </div>
</template>
<script lang="ts" setup >
import { ref } from "vue"


const spanList = ref<Array<string>>(['-2s', '-1.9s', '-1.8s', '-1.7s', '-1.6s', '-1.5s', ' -1.4s', '-1.3s', '-1.2s', '-1.1s'])
</script>

<style lang="scss" scoped>
.corrugation-contain {
  width: fit-content;

  .span {
    background-color: var(--el-color-primary);
    width: 2px;
    height: 2px;
    float: right;
    margin-left: 5px;
    animation: liner 0.2s ease-in-out;
    animation-iteration-count: infinite;
    animation-direction: alternate;
  }

  @keyframes liner {
    0% {
      transform: scaleY(1);
    }

    100% {
      transform: scaleY(6);
    }
  }
}
</style>
