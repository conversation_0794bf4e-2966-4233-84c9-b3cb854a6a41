<!-- 播放音频 -->
<template>
  <div class="flex">
    <template v-for="item in btnList" :key="item.key">
      <svg-icon v-if="currentStatus === item.key" class="btn-icon" :name="item.name" :width="item.width" :height="item.height" :fill="item.currentColor" @mouseover="item.currentColor = item.hoverColor" @mouseout="item.currentColor = item.defaultColor" @click="btnClick(item)"></svg-icon>
      <Corrugation v-if="item.key === 'stop' && currentStatus === 'stop'" />
    </template>
  </div>
</template>

<script lang="ts" setup name="PlayVoice">
import { onBeforeUnmount, onMounted, ref } from "vue"
import useCtx from "@/hooks/useCtx"
import Corrugation from "./Corrugation.vue"
import { downloadFile } from "../../utils/api"

const props = defineProps({
  fileID: {
    type: String,
    required: true,
  },
})
const { $app } = useCtx()
let audioInstance: any
const currentStatus = ref("play")

const btnList = ref([
  {
    key: "play",
    name: "play-icon",
    currentColor: "#878787",
    defaultColor: "#878787",
    hoverColor: "#4b72ef",
    width: 24,
    height: 24,
  },
  {
    key: "stop",
    name: "tingzhi-icon",
    currentColor: "#4b72ef",
    defaultColor: "#4b72ef",
    hoverColor: "#4b72ef",
    width: 26,
    height: 26,
  },
])

const btnClick = (e: { key: string }) => {
  if (!props.fileID) return $app.$message.error("文件id为空")
  currentStatus.value = e.key === "play" ? "stop" : "play"
  if (currentStatus.value === "stop") {
    audioInstance.play()
    audioInstance.onended = () => {
      currentStatus.value = "play"
    }
  } else {
    audioInstance.pause()
    audioInstance.currentTime = 0
  }
}

onMounted(() => {
  if (!props.fileID) {
    $app.$message.error("文件id为空")
  } else {
    audioInstance = new Audio(downloadFile(props.fileID))
  }
})

onBeforeUnmount(() => {
  // 释放资源
  audioInstance.pause()
  audioInstance = null
})
</script>

<style lang="scss" scoped></style>
