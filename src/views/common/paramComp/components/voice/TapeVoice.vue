<!-- 录制音频 -->
<template>
  <div class="flex">
    <template v-for="item in btnList" :key="item.key">
      <svg-icon v-if="currentStatus === item.key" class="btn-icon" :name="item.name" :width="item.width" :height="item.height" :fill="item.currentColor" @mouseover="item.currentColor = item.hoverColor" @mouseout="item.currentColor = item.defaultColor" @click="btnClick(item)"></svg-icon>
      <template v-if="item.key === 'stop' && currentStatus === 'stop'">
        <Corrugation />
        <span style="margin-left: 16px">{{ timeNum + "s" }}</span>
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup name="TapeVoice">
import { dataC } from 'turing-plugin'
import { onBeforeUnmount, onMounted, ref } from "vue"
import Corrugation from "./Corrugation.vue"
import Media from "./recorder-core"
import { uploadFile } from "../../utils/api"

const emit = defineEmits(["upload-file"])

const currentStatus = ref("play")
const loading = ref(false)

const btnList = ref([
  {
    key: "play",
    name: "microphone-icon",
    currentColor: "#878787",
    defaultColor: "#878787",
    hoverColor: "#4b72ef",
    width: 32,
    height: 32,
  },
  {
    key: "stop",
    name: "tingzhi-icon",
    currentColor: "#4b72ef",
    defaultColor: "#4b72ef",
    hoverColor: "#4b72ef",
    width: 26,
    height: 26,
  },
])
let mediaInstance: any
const timeNum = ref(30)
// 开始60秒倒计时
const startCountdown = () => {
  const currentTime = Date.now()
  const timer = setInterval(() => {
    timeNum.value = 30 - Math.floor((Date.now() - currentTime) / 1000)
    if (timeNum.value === 0) {
      clearInterval(timer)
      btnClick({ key: "stop" })
    }
  }, 1000)
}

const btnClick = (e: { key: string }) => {
  if (loading.value) return
  currentStatus.value = e.key === "play" ? "stop" : "play"
  if (currentStatus.value === "play") {
    loading.value = true
    // 结束录音，调用上传接口
    mediaInstance
      .stop(true)
      .then((blob: Blob, duration: number) => {
        const fileName = dataC.generateUUID() + ".wav"
        const file = new File([blob], fileName, { type: blob.type })
        uploadFile(fileName, file)
          .then((res) => {
            emit("upload-file", res.id);
          })
          .finally(() => (loading.value = false));
      })
      .catch(() => {
        loading.value = false
      })
  } else {
    // 开始录音
    mediaInstance.start()
    startCountdown()
  }
}

onMounted(() => {
  // 实例初始化
  mediaInstance = Media()
  mediaInstance.init({ type: "wav" })
})

onBeforeUnmount(() => {
  // 释放资源
  mediaInstance && mediaInstance.close()
  mediaInstance = null
})
</script>

<style lang="scss" scoped>
.btn-icon {
  cursor: pointer;

  &:hover {
    fill: $primary-color;
  }
}
</style>
