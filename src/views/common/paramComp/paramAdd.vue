<template  v-if="todo">
  <my-drawer 
    v-model="drawerTag" 
    :title="isAdd ? '新增参数' : '编辑参数'" 
    :destroy-on-close="true" 
    append-to="#appBaseGlobalId"
    size="620px"
    :showClose="(!readOnly && isCanvas) || !isCanvas"
    :showConfirm="(!readOnly && isCanvas) || !isCanvas"
    @confirm="sureAdd"
    @close="closeDrawer" 
    custom-class="astrolink-flow-dialog">
    <el-form 
      ref="formRef" 
      :model="form" 
      :rules="rules" 
      label-width="110px">
      <el-form-item label="参数标识" prop="key">
        <el-input v-model.trim="form.key" placeholder="请输入参数标识，如arg1" :disabled="readOnly"></el-input>
      </el-form-item>
      <el-form-item label="参数名称" prop="name">
        <el-input v-model.trim="form.name" placeholder="请输入参数名称，如参数1" :disabled="readOnly"></el-input>
      </el-form-item>
      <el-form-item label="是否必填" prop="required">
        <el-switch v-model="form.required" :active-value="true" :inactive-value="false" :disabled="readOnly"> </el-switch>
      </el-form-item>
      <el-form-item label="参数描述" prop="desc">
        <el-input v-model="form.desc" type="textarea" placeholder="请输入参数描述" :disabled="readOnly"></el-input>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择类型" clearable style="width: 100%" @change="typeChange" :disabled="readOnly">
          <el-option
            v-for="item in argsTypeOpts.filter((e: any) => !((e.value === 'dynamic' && props.isCanvas && !props.isStartEnd) || (e.value === 'dynamic' && props.isTool)))"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="显示样式" prop="style">
        <el-select v-model="form.style" placeholder="请选择类型" clearable style="width: 100%" @change="argsInit" :disabled="readOnly">
          <el-option v-for="item in argsTypeOpts.find((e: any) => e.value === form.type)?.style" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-form-item>

      <template v-for="(item, index) in args" :key="index">
        <template v-if="['select', 'checkbox'].includes(form.style)">
          <el-form-item label="数据来源">
            <el-radio-group v-model="item[form.style].props.isCustomApi">
              <el-radio-button :label="false">自定义配置</el-radio-button>
              <el-radio-button :label="true">API获取</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </template>

        <template v-if="form.style === 'radio' || (['select', 'checkbox'].includes(form.style) && !item[form.style].props.isCustomApi)">
          <el-form-item label="选项配置">
            <div v-for="(e, idx) in item[form.style].props.options" :key="e.uuid" class="list-item">
              <el-input v-model="e.value" placeholder="选项值" :disabled="readOnly"></el-input>
              <span style="margin: 0 4px">-</span>
              <el-input v-model="e.name" placeholder="选项名称" :disabled="readOnly"></el-input>
              <el-icon v-if="!readOnly" @click="delSelectItem(index, form.style, idx)">
                <Delete />
              </el-icon>
            </div>
            <el-button v-if="!readOnly" type="primary" @click="addSelectItem(index, form.style)" :icon="Plus">添加选项</el-button>
          </el-form-item>
        </template>

        <template v-if="['text', 'textarea'].includes(form.style)">
          <template v-if="form.type !== 'list'">
            <el-form-item label="最大长度" v-if="item[form.style]?.props && Object.prototype.hasOwnProperty.call(item[form.style]?.props, 'maxLength')">
              <el-input-number v-model="item[form.style].props.maxLength" :step="1"  :min="0" controls-position="right" placeholder="请输入最大长度" :step-strictly="true" :disabled="readOnly" style="width: 180px">
              </el-input-number>
            </el-form-item>
            <!-- <el-form-item label="最小长度">
              <el-input-number v-model="item[form.style].props.minLength" :step="1" controls-position="right"
                placeholder="请输入最小长度" :step-strictly="true" :disabled="readOnly">
              </el-input-number>
            </el-form-item> -->
          </template>

          <el-form-item :label="form.type === 'list' && index !== 0 ? ' ' : '默认值'">
            <div class="list-item">
              <el-input
                v-model="item[form.style].defaultValue"
                :placeholder="form.type === 'list' ? '请输入列表项' : '请输入默认值'"
                clearable
                :type="form.style"
                v-model:maxlength="item[form.style].props.maxLength"
                v-model:minlength="item[form.style].props.minLength"
                :disabled="readOnly"
              ></el-input>
              <el-icon @click="delListItem(index)" v-if="!readOnly && form.type === 'list'">
                <Delete />
              </el-icon>
            </div>
          </el-form-item>
        </template>

        <template v-else-if="['number'].includes(form.style)">
          <el-form-item label="最大值" v-if="item[form.style]?.props && Object.prototype.hasOwnProperty.call(item[form.style]?.props, 'max')">
            <el-input-number
              v-model="item[form.style].props.max"
              :step="1"
              :min="item[form.style].props?.min"
              controls-position="right"
              placeholder="请输入最大值"
              :step-strictly="form.type === 'int'"
              :value-on-clear="100"
              :disabled="readOnly"
              @change="
                () => {
                  if (item[form.style].defaultValue > item[form.style].props.max) {
                    item[form.style].defaultValue = item[form.style].props.max;
                  }
                }
              "
            >
            </el-input-number>
          </el-form-item>
          <el-form-item label="最小值" v-if="item[form.style]?.props && Object.prototype.hasOwnProperty.call(item[form.style]?.props, 'min')">
            <el-input-number
              v-model="item[form.style].props.min"
              :step="1"
              :max="item[form.style].props?.max"
              controls-position="right"
              placeholder="请输入最小值"
              :step-strictly="form.type === 'int'"
              :value-on-clear="0"
              :disabled="readOnly"
              @change="
                () => {
                  if (item[form.style].defaultValue < item[form.style].props.min) {
                    item[form.style].defaultValue = item[form.style].props.min;
                  }
                }
              "
            >
            </el-input-number>
          </el-form-item>
          <el-form-item label="默认值">
            <el-input-number
              v-model="item[form.style].defaultValue"
              :step="1"
              controls-position="right"
              placeholder="请输入默认值"
              :max="item[form.style].props.max"
              :min="item[form.style].props.min"
              :step-strictly="form.type === 'int'"
              :disabled="readOnly"
            >
            </el-input-number>
          </el-form-item>
        </template>

        <template v-else-if="['select'].includes(form.style)">
          <el-form-item label="接口地址" v-if="item[form.style].props.isCustomApi">
            <el-input v-model="item[form.style].props.apiUrl" placeholder="请输入接口地址" clearable :disabled="readOnly"></el-input>
          </el-form-item>
          <el-form-item label="默认值" v-else>
            <el-select v-model="item[form.style].defaultValue" placeholder="请输入默认值" clearable :multiple="form.type === 'list'" style="width: 100%" :disabled="readOnly">
              <el-option v-for="e in item[form.style].props.options.filter((t: any) => t.name && t.value)" :key="e.uuid" :label="e.name" :value="e.value"> </el-option>
            </el-select>
          </el-form-item>
        </template>

        <template v-else-if="['switch'].includes(form.style)">
          <el-form-item label="默认值">
            <el-switch v-model="item[form.style].defaultValue" :active-value="true" :inactive-value="false" :disabled="readOnly"></el-switch>
          </el-form-item>
        </template>

        <template v-else-if="['upload'].includes(form.style)">
          <el-form-item label="文件大小限制">
            <el-input-number v-model="item[form.style].props.fileSize" controls-position="right" placeholder="请输入文件最大限制" :disabled="readOnly" :max="200" :min="0.01"> </el-input-number>
            <span style="margin-left: 8px">MB</span>
          </el-form-item>
          <el-form-item label="文件扩展名限制">
            <el-select v-model="item[form.style].props.extensions" placeholder="请选择文件扩展名" clearable multiple style="width: 100%" :disabled="readOnly">
              <el-option v-for="item in fileType" :key="item" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        </template>

        <template v-else-if="['code', 'template'].includes(form.style)">
          <el-form-item label="默认值">
            <pre class="show-code">{{ item[form.style].defaultValue }}</pre>
            <el-button type="primary" @click="openDialog(item[form.style], form.style)" :disabled="readOnly">{{ form.style === "code" ? "打开代码编辑器" : "打开模板编辑器" }}</el-button>
          </el-form-item>
        </template>

        <template v-else-if="['radio'].includes(form.style)">
          <el-form-item label="默认值">
            <el-radio-group v-model="item[form.style].defaultValue">
              <el-radio v-for="(e, idx) in item[form.style].props.options.filter((t: any) => t.name && t.value)" :key="idx" :label="e.value"> :disabled="readOnly"
                {{ e.name }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <template v-else-if="['checkbox'].includes(form.style)">
          <el-form-item label="接口地址" v-if="item[form.style].props.isCustomApi">
            <el-input v-model="item[form.style].props.apiUrl" placeholder="请输入接口地址" clearable :disabled="readOnly"></el-input>
          </el-form-item>
          <el-form-item label="默认值" v-else>
            <el-checkbox-group v-model="item[form.style].defaultValue" :disabled="readOnly">
              <el-checkbox v-for="(e, idx) in item[form.style].props.options.filter((t: any) => t.name && t.value)" :key="idx" :label="e.value">
                {{ e.name }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </template>

        <template v-else-if="['datetime'].includes(form.style)">
          <el-form-item label="显示格式配置">
            <el-select
              v-model="item[form.style].props.optDefault"
              placeholder="请选择日期显示格式"
              clearable
              :disabled="readOnly"
              style="width: 100%"
              @change="
                () => {
                  item[form.style].defaultValue = '';
                  form.value = null;
                }
              "
            >
              <el-option v-for="x in item[form.style].props.options" :key="x.value" :label="x.name" :value="x.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="默认值">
            <el-date-picker
              v-model="item[form.style].defaultValue"
              :type="item[form.style].props.optDefault === 'YYYY-MM-DD' ? 'date' : 'datetime'"
              :value-format="item[form.style].props.optDefault"
              placeholder="默认值"
              style="width: 100%"
              :disabled="readOnly"
            />
          </el-form-item>
        </template>

        <template v-else-if="['voice'].includes(form.style) && !isOutput">
          <el-form-item label="默认值">
            <div class="voice-contain-box flex">
              <template v-if="item[form.style].defaultValue">
                <PlayVoice :fileID="item[form.style].defaultValue" />
                <el-icon class="close-icon" @click="() => (item[form.style].defaultValue = '')"><Close /></el-icon>
              </template>
              <TapeVoice v-else @upload-file="(fileID) => (item[form.style].defaultValue = fileID)" />
            </div>
          </el-form-item>
        </template>

        <template v-if="form.style !== 'password' && form.style && form.style !== 'dynamic' && !isOutput && item[form.style]?.props && form.type !== 'list'">
          <el-form-item label="校验规则" prop="rules">
            <el-select
              v-model="item[form.style].props.rules"
              placeholder="请输入规则校验，可选择内置也可输入自定义"
              :disabled="readOnly"
              clearable
              filterable
              allow-create
              default-first-option
              :reserve-keyword="false"
            >
              <el-option v-for="item in rulesOptions" :key="item.value" :label="item.value" :value="item.value">
                <span>{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </template>
      </template>

      <el-form-item v-if="!readOnly && form.type === 'list' && form.style === 'text'" label=" ">
        <el-button type="primary" @click="addListItem" :disabled="readOnly">添加列表项</el-button>
      </el-form-item>

      <el-form-item label="显示条件" prop="condition">
        <el-popover placement="bottom" :width="480" trigger="hover" :hide-after="0" effect="dark" :show-after="500">
          <template #reference>
            <el-input v-model="form.condition" placeholder="请输入显示条件，例如：{{name}} == '张三'" :disabled="readOnly"></el-input>
          </template>
          <pre style="white-space: pre-wrap">{{ tipText }}</pre>
        </el-popover>
      </el-form-item>
      <el-form-item label="是否显示" prop="visible" v-if="!props.isCanvas">
        <el-switch v-model="form.visible" :active-value="true" :inactive-value="false" :disabled="readOnly"> </el-switch>
      </el-form-item>
      <el-form-item label="是否无引用" prop="visible">
        <el-switch v-model="form.norefer" :active-value="true" :inactive-value="false" :disabled="readOnly"> </el-switch>
      </el-form-item>
      <el-form-item v-if="isLocation && !isOutput" label="传入方法" prop="location" :rules="[{ required: true, message: '请选择传入方法', trigger: 'change' }]">
        <el-select v-model="form.location" :disabled="readOnly">
          <el-option v-for="item in incomingMethods" :key="item.id" :label="item.name" :value="item.id"> </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item v-if="(!readOnly && isCanvas) || !isCanvas" label=" " style="margin-top: 16px">
        <el-button type="primary" @click="sureAdd">确认</el-button>
        <el-button @click="closeDrawer">取消</el-button>
      </el-form-item> -->
    </el-form>
  </my-drawer>
  <CodeMirrorBox ref="CodeMirrorBoxRef" @getCode="setDefaultValue" />
  <TempMirrorBox ref="TempMirrorBoxRef" @change="setDefaultValue" />
</template>

<script lang="ts" setup name="Params">
import { reactive, ref, nextTick, computed } from "vue";
import useCtx from "@/hooks/useCtx";
import { Delete, Plus, Close } from "@element-plus/icons-vue";
import { argsTypeList, defaultArgs, fileType, rulesOptions, incomingMethods } from './utils/constants'
// 引入组件
import CodeMirrorBox from "./components/CodeMirrorBox.vue";
import TempMirrorBox from "./components/TempMirrorBox.vue";
import TapeVoice from "./components/voice/TapeVoice.vue";
import PlayVoice from "./components/voice/PlayVoice.vue";


const { proxy, $app } = useCtx();


const generateVarName = (prefix = "a") => {
  let d = Math.floor(Math.random() * 10e10).toString(16);
  const tempStr = prefix + d;
  return tempStr;
}

const props = withDefaults(
  defineProps<{
    isCanvas?: boolean;
    readOnly?: boolean;
    isOutput?: boolean;
    isStartEnd?: boolean;
    isLocation?: boolean;
    isTool?: boolean;
  }>(),
  {
    isCanvas: false,
    readOnly: false,
    isOutput: false,
    isStartEnd: false,
    isLocation: false,
    isTool: false,
  }
);

const emit = defineEmits(["update"]);

const tipText = `条件配置说明：
判断条件书写（支持JavaScript的判断语法），变量使用{{}}包裹。例如：
1、需要某个一级参数【参数标识为method】值为POST时，则填写：{{method}} == "POST"
2、需要某个一级参数【参数标识为args】下面的二级参数【参数标识为type】值为2时，则填写：{{args.type}} == "2"
详细写法请参考平台文档的组件说明
`;

const argsTypeOpts = computed(() => {
  const a = JSON.parse(JSON.stringify(argsTypeList));
  if (props.isOutput) {
    a.forEach((e: any) => {
      e.style = [
        {
          label: "markdown",
          value: "markdown",
        },
        {
          label: "html",
          value: "html",
        },
        {
          label: "voice",
          value: "voice",
        },
      ];
    });
  }
  return a;
});

const drawerTag = ref(false);

const form = reactive<any>({
  key: "",
  name: "",
  required: true,
  desc: "",
  type: "string",
  style: "",
  visible: true,
  defaultValue: "",
  props: {},
  condition: "",
  value: "",
  location: "",
  rules: "",
  norefer:false
});
const rules = reactive({
  key: [
    { required: true, message: "请输入参数标识", trigger: "blur" },
    {
      validator: function (rule: any, value: any, callback: any) {
        if (value && /^[a-zA-Z0-9_-]+$/.test(value) == false) {
          callback(new Error("当前仅支持字母、数字、中划线和下划线，请重新输入"));
        } else if (/^\d+$/.test(value)) {
          callback(new Error("参数标识不能为纯数字"));
        } else if (value.startsWith("condition")) {
          callback(new Error("参数标识不能以系统内置关键字condition开头"));
        }
        callback();
      },
      trigger: ["change", "blur"],
    },
  ],
  name: [{ required: !props.isCanvas, message: "请输入参数名称", trigger: "blur" }],
  required: [{ required: true, message: "请选择是否必填", trigger: "change" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  style: [{ required: true, message: "请选择显示样式", trigger: "change" }],
  visible: [{ required: true, message: "请选择是否显示", trigger: "change" }],
});

const args = ref<any>([JSON.parse(JSON.stringify(defaultArgs))]);

/** 下拉框增加项 */
const addSelectItem = (index: number, style: string) => {
  args.value[index][style].props.options.push({
    uuid: generateVarName(),
    name: "",
    value: "",
  });
};

/** 下拉框删除项 */
const delSelectItem = (index: number, style: string, idx: number) => {
  args.value[index][style].props.options.splice(idx, 1);
  if (Object.prototype.toString.call(args.value[index][style].defaultValue === "[object Array]")) {
    args.value[index][style].defaultValue = [];
  } else {
    args.value[index][style].defaultValue = "";
  }
};

/** 增加列表项 */
const addListItem = () => {
  args.value.push(JSON.parse(JSON.stringify(defaultArgs)));
};

/** 删除列表项 */
const delListItem = (index: number) => {
  args.value.splice(index, 1);
};

let currentTemp: any = null;

// /** 打开代码编辑器 */
const openDialog = (item: any, style: string) => {
  currentTemp = item;
  if (style === "template") {
    let dynamicArg: Array<any> = [];
    proxy.$refs.TempMirrorBoxRef.openDialog(dynamicArg, item.defaultValue, item);
  } else {
    proxy.$refs.CodeMirrorBoxRef.openDialog(item.defaultValue, item?.props, false);
  }
};

const setDefaultValue = (str: string, codeDesc: string) => {
  if (currentTemp) {
    currentTemp.defaultValue = str;
    if (codeDesc) {
      if (!currentTemp?.props) currentTemp.props = {};
      currentTemp.props.remark = codeDesc;
    }
  }
};

/** 类型change事件 */
const typeChange = () => {
  argsInit();
  const current = argsTypeList.find((e: any) => e.value === form.type);
  if (current && !props.isOutput) {
    form.style = "";
    form.style = current.style[0].value;
  }
  form.value = "";
};

/** 显示样式change事件 */
const argsInit = (tag = true) => {
  args.value = [JSON.parse(JSON.stringify(defaultArgs))];
  if (form.type === "list" && form.style === "select") {
    args.value[0].select.defaultValue = [];
  }
  if (tag) {
    /** 类型和显示样式change则重置form.value */
    form.value = null;
    if (form.style === "datetime") {
      form.props = args.value[0][form.style].props;
      form.defaultValue = args.value[0][form.style].defaultValue;
    }
  }
};
const hasUniqueNameAndValue=(arr)=> {
  const names = new Set();
  const values = new Set();

  for (const item of arr) {
    if (names.has(item.name) || values.has(item.value)) {
      // 如果name或value已存在于集合中，则找到重复项
      return false;
    }
    names.add(item.name);
    values.add(item.value);
  }

  // 如果没有重复的name和value，则返回true
  return true;
}
/** 确定新增 */
const sureAdd = () => {
  proxy.$refs.formRef.validate((valid: boolean) => {
    if (valid) {
      if (!props.isOutput) {
        if (form.type === "list" && form.style === "text") {
          form.defaultValue = args.value.map((e: any) => e[form.style].defaultValue);
          // args会出现多行
        } else if (form.style) {
          const current = JSON.parse(JSON.stringify(args.value[0][form.style]));
          form.defaultValue = current.defaultValue;
          if (current.props && current.props.options) {
            // options排空
            current.props.options = current.props.options.filter((e: any) => e.value && e.name);
          }
          form.props = current.props;
        }
        if (form.style === "number" && form?.props?.max === 0) {
          return $app.$message.error("最大值不可为0");
        }
      } else if (form.type === "dynamic") {
        form.props = { args: [] };
      }
      if((form.style === 'radio'||form.props?.isCustomApi===false)&&!hasUniqueNameAndValue(form.props.options)){
        return $app.$message.error("请确认选项配置的值或者名称是否重复");
      }
      emit("update", JSON.parse(JSON.stringify({ ...form })));
    }
  });
};

/** 当前操作数据的下标，用于区分是新增还是编辑 */
const isAdd = ref(true);

const openDrawer = (data: any) => {
  drawerTag.value = true;
  isAdd.value = true;
  nextTick(() => {
    proxy.$refs.formRef && proxy.$refs.formRef.resetFields();
    form.style = props.isOutput ? "markdown" : "text";
    if (data) {
      isAdd.value = false;
      const currentObj = JSON.parse(JSON.stringify(data));
      for (const key in currentObj) {
        if (Object.prototype.hasOwnProperty.call(currentObj, key)) {
          // 就这样吧，兼容老数据没有props字段，或者有props字段，里面没有rules字段的
          if (key === "props") {
            !currentObj[key] && (currentObj[key] = {});
            !currentObj[key].rules && (currentObj[key].rules = "");
          }
          form[key] = currentObj[key];
        }
      }
      if (!props.isOutput) {
        if (form.type === "list" && form.style === "text") {
          // args会出现多行
          if (form.defaultValue && Object.prototype.toString.call(form.defaultValue) === "[object Array]") {
            args.value = form.defaultValue.map((e: string) => {
              return {
                text: {
                  defaultValue: e,
                  props: {
                    maxLength: null,
                    minLength: null,
                    rules: "",
                  },
                },
              };
            });
          }
        } else {
          argsInit(false);
          if (form.style) {
            args.value[0][form.style].defaultValue = form.defaultValue;
            args.value[0][form.style].props = form.props;
          }
        }
      }
    }
  });
};

const closeDrawer = () => {
  proxy.$refs.formRef && proxy.$refs.formRef.resetFields();
  args.value = [JSON.parse(JSON.stringify(defaultArgs))];
  if (form.type === "list" && form.style === "select") {
    args.value[0].select.defaultValue = [];
  }
  drawerTag.value = false;
};



defineExpose({ openDrawer, closeDrawer });
</script>

<style lang="scss" scoped>
.list-item {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 6px;

  i {
    margin-left: 6px;
    cursor: pointer;
  }
}

.show-code {
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.voice-contain-box {
  width: 100%;

  .close-icon {
    margin-left: 24px;
    cursor: pointer;
    display: none;
  }

  &:hover {
    .close-icon {
      display: block;
    }
  }
}
</style>
