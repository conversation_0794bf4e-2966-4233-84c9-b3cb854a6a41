<template>
  <div class="param-use-box" v-if="inputArgs.length > 0">
    <template v-for="(element, index) in inputArgs">
      <div class="param-item" v-show="element.visible">
        <template v-if="dataC.isEmpty(element.desc)">
          <div style="display: flex; align-items: center; margin-bottom: 3px">
            <h4 :class="{ 'param-item-required': element.required }">{{ element.name }}</h4>
            <el-tag type="info" size="small">{{ element.type }}</el-tag>
          </div>
        </template>
        <template v-if="!dataC.isEmpty(element.desc)">
          <el-popover placement="left" :width="300">
            <template #reference>
              <div style="display: flex; align-items: center; margin-bottom: 3px">
                <h4 :class="{ 'param-item-required': element.required }">{{ element.name }}</h4>
                <el-tag type="info" size="small">{{ element.type }}</el-tag>
              </div>
            </template>
            <h4>{{ element.desc }}</h4>
          </el-popover>
        </template>
        <ShowDomByStyle
          ref="showDomByStyleRef"
          v-bind="{
            elementObj: element,
            readOnly,
            inputArgs,
          }"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { dataC } from "turing-plugin";
import ShowDomByStyle from "./show-dom-by-style.vue";
defineProps({
  inputArgs: {
    type: Array as any,
    default() {
      return [];
    },
  },
  readOnly: { type: Boolean, default: false },
});
</script>

<style lang="scss" scoped>
.param-use-box {
  border: 1px dashed $border-color;
  padding: 16px;
  margin-top: 16px;
  border-radius: 6px;
  background-color: $body-bg;
  .param-item {
    margin-bottom: 10px;
  }
}

.param-item-required::before {
  color: var(--el-color-danger);
  content: "*";
  margin-right: 4px;
}
</style>
