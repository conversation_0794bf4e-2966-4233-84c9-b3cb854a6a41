<!-- 通过参数的style字段来动态展示具体的dom元素 -->
<template>
  <div style="width: 100%">
    <template v-if="element.style === 'text'">
      <el-input
        v-if="element.type === 'string'"
        :type="element.style"
        :rows="element?.props?.rows || 4"
        :maxlength="element?.props?.maxLength || -1"
        v-model="element[propsVal]"
        :placeholder="element?.props?.placeholder ?? '请输入'"
        clearable
        :disabled="readOnly"
      />
      <div class="list-contain" v-else-if="element.type === 'list'">
        <div v-for="(listItem, listIdx) in element.value" :key="listIdx" class="flex" style="margin-bottom: 10px">
          <el-input v-model="element.value[listIdx]" placeholder="请输入" :disabled="readOnly" clearable style="width: calc(100% - 50px)"></el-input>
          <el-button link type="danger" v-if="!readOnly && listIdx != 0" @click="() => element.value.splice(listIdx, 1)" :icon="Delete"></el-button>
          <el-button link type="primary" v-if="!readOnly && listIdx == element.value.length - 1" @click="() => element.value.push('')" :icon="CirclePlus"></el-button>
          <!---当删除按钮不显示时，占个宽度方便计算宽度-->
          <div v-if="!(!readOnly && listIdx != 0)" style="width: 22px"></div>
        </div>
      </div>
    </template>
    <el-input
      v-else-if="element.style === 'textarea'"
      :type="element.style"
      :rows="element?.props?.rows || 4"
      :maxlength="element?.props?.maxLength || -1"
      v-model="element[propsVal]"
      :placeholder="element?.props?.placeholder ?? '请输入'"
      clearable
      :disabled="readOnly"
    />

    <InputPassword
      v-else-if="element.style === 'password'"
      :inputVal="element[propsVal]"
      @changeVal="(realVal: any) => (element[propsVal] = realVal)"
      :disabled="readOnly"
      :passwordPublicKey="passwordPublicKey"
    />
    <template v-else-if="element.style === 'number'">
      <el-input-number
        v-if="[undefined, null, ''].includes(element?.props?.min)"
        v-model="element[propsVal]"
        :step="1"
        :max="element?.props?.max"
        :controls="true"
        controls-position="right"
        :value-on-clear="element?.props?.min ?? 0"
        :step-strictly="element.type === 'int'"
        style="width: 100%"
        :placeholder="element?.props?.placeholder ?? '请输入'"
        :disabled="readOnly"
      >
      </el-input-number>
      <el-input-number
        v-else
        v-model="element[propsVal]"
        :step="1"
        :max="element?.props?.max"
        :min="element?.props?.min"
        :controls="true"
        controls-position="right"
        :value-on-clear="element?.props?.min ?? 0"
        :step-strictly="element.type === 'int'"
        style="width: 100%"
        :placeholder="element?.props?.placeholder ?? '请输入'"
        :disabled="readOnly"
      >
      </el-input-number>
    </template>
    <el-select
      v-else-if="element.style === 'select'"
      v-model="element[propsVal]"
      style="width: 100%"
      :multiple="element.type === 'list'"
      :placeholder="element?.props?.placeholder ?? '请选择'"
      clearable
      filterable
      :disabled="readOnly"
    >
      <el-option v-for="(opt, idx) in element?.props?.options" :key="idx" :label="opt.name" :value="opt.value"> </el-option>
    </el-select>
    <el-radio-group v-else-if="element.style === 'radio'" v-model="element[propsVal]" :disabled="readOnly">
      <el-radio v-for="(opt, idx) in element?.props?.options" :key="idx" :label="opt.value">
        {{ opt.name }}
      </el-radio>
    </el-radio-group>
    <template v-else-if="element.style === 'datetime'">
      <el-date-picker
        v-model="element[propsVal]"
        :type="element?.props?.optDefault === 'YYYY-MM-DD' ? 'date' : 'datetime'"
        :value-format="element?.props?.optDefault"
        :placeholder="element?.props?.placeholder ?? '请选择'"
      />
    </template>
    <el-switch v-else-if="element.style === 'switch'" v-model="element[propsVal]" :active-value="true" :inactive-value="false" :disabled="readOnly">
    </el-switch>
    <el-checkbox-group v-else-if="element.style === 'checkbox'" v-model="element[propsVal]" :disabled="readOnly">
      <el-checkbox v-for="cbg in element.props.options" :key="cbg.value" :label="cbg.value">
        {{ cbg.name }}
      </el-checkbox>
    </el-checkbox-group>
    <div v-else-if="element.style === 'upload'">
      <el-container v-loading="element.props.uploadLoading" element-loading-text="文件上传中...">
        <el-upload
          class="custom-upload-class"
          drag
          :auto-upload="false"
          :show-file-list="false"
          :on-change="
            (rawFile) => {
              return beforeAvatarUpload(rawFile);
            }
          "
          :disabled="readOnly"
          style="width: 100%"
        >
          <div class="link-text flexCenter">
            <!-- <el-icon class="el-icon--upload">
              <Plus />
            </el-icon> -->
            点击上传
          </div>
          <div class="el-upload__text">或将文件拖拽到此处</div>
          <template #tip>
            <div class="el-upload__tip">
              <p v-if="element.props.extensions">
                {{ `文件类型：${dowithExtension(element.props.extensions)}` }}
              </p>
              <p v-if="element.props.fileSize">{{ `文件大小：${element.props.fileSize}MB` }}</p>
            </div>
          </template>
        </el-upload>
      </el-container>
      <p v-if="element[propsVal] && Object.prototype.toString.call(element[propsVal]) === '[object String]'" class="ellipsis text-p flexBoxBetween">
        <span class="ellipsis">
          {{ element.props.fileName }}
        </span>
        <el-icon
          v-if="!readOnly"
          class="del-dynmaic-icon"
          style="margin-left: 0"
          @click="
            () => {
              element[propsVal] = '';
              element.props.fileName = '';
            }
          "
        >
          <CloseBold />
        </el-icon>
      </p>
    </div>
    <el-input
      v-else-if="element.style === 'template'"
      type="textarea"
      resize="none"
      v-model="element[propsVal]"
      :placeholder="element?.props?.placeholder"
      :maxlength="element?.props?.maxLength"
      :show-word-limit="true"
      :autosize="{ minRows: 6, maxRows: 14 }"
      @click="openTemplateDialog"
    >
    </el-input>
    <template v-else-if="element.style === 'code'">
      <div class="codemirror-contain">
        <CustomCodemirror
          ref="customCodemirrorRef"
          :domId="`domId${+new Date()}`"
          :initVal="element[propsVal]"
          height="200px"
          @updateVal="(val: any) => (element[propsVal] = val)"
          :options="{}"
        />
      </div>
    </template>
  </div>
  <TempMirrorBox ref="TempMirrorBoxRef" @change="setDefaultValue" :readOnly="readOnly" />
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useVModel } from "@vueuse/core";
import { dataC, fileC } from "turing-plugin";
import { CloseBold, Delete, CirclePlus } from "@element-plus/icons-vue";
import { uploadFile, getAPIData } from "./utils/api";
import TempMirrorBox from "./components/TempMirrorBox.vue";
import CustomCodemirror from "./components/CustomCodemirror.vue";
import InputPassword from "./components/InputPassword.vue";
import useCtx from "@/hooks/useCtx";
const { proxy, $app } = useCtx();

const passwordPublicKey = ref("");
const props = defineProps(["elementObj", "readOnly", "inputArgs"]);

const emit = defineEmits([]);
const propsVal = ref<string>("value");
const element = useVModel(props, "elementObj", emit);

/**
 * 文件上传
 */
const beforeAvatarUpload = (rawFile: any) => {
  const params = element.value.props;
  if (!dataC.isEmpty(params.extensions) && !params.extensions.includes("." + fileC.getFileSuffix(rawFile.name))) {
    $app.$message.warning("请上传正确的文件类型！");
    return false;
  } else if (rawFile.size === 0) {
    $app.$message.warning("文件不能为空！");
    return false;
  } else if (rawFile.size / 1024 / 1024 > 200 || (params.fileSize && rawFile.size / 1024 / 1024 > params.fileSize)) {
    $app.$message.warning("文件大小超过限制大小！");
    return false;
  }
  element.value.props.uploadLoading = true;
  // 上传
  uploadFile(rawFile.name, rawFile.raw)
    .then((res) => {
      element.value[propsVal.value] = res.id;
      element.value.props.fileName = rawFile.name;
      element.value.props.uploadLoading = false;
    })
    .catch(() => {
      element.value.props.uploadLoading = false;
    });
  return true;
};

/**
 * 处理下，兼容下老数据为字符串的情况
 */
const dowithExtension = (temp: any) => {
  if (Object.prototype.toString.call(temp) === "[object Array]") {
    return temp.join("，");
  }
  return temp;
};

/**
 * 文本编辑器
 */
const openTemplateDialog = () => {
  //根据不同的模板引擎，决定模版参数输入样式
  let engineType = props.inputArgs.find((item: any) => item.key === "engine")?.value;
  let dynamicArg: any = [];
  if (props.inputArgs) {
    props.inputArgs.forEach((e: any) => {
      if (e.type === "dynamic" && e.style === "dynamic") {
        dynamicArg = dynamicArg.concat(e.children);
      }
    });
  }
  proxy.$refs.TempMirrorBoxRef.openDialog(dynamicArg, element.value[propsVal.value], element.value, engineType);
};

/**
 * 模版编辑器的确定事件
 */
const setDefaultValue = (val: string) => {
  element.value[propsVal.value] = val;
};

/**
 * 更新代码编辑器的数据
 */
const updateCodemirrorVal = (val: string) => {
  if (!proxy.$refs.customCodemirrorRef) return;
  proxy.$refs.customCodemirrorRef?.updateCodemirrorVal(val);
};

onMounted(() => {
  if (element.value.style === "select" && element.value.props.isCustomApi) {
    getAPIData(element.value.props.apiUrl).then((res) => {
      element.value.props.options = res;
    });
  }
});

defineExpose({ updateCodemirrorVal });
</script>

<style scoped lang="scss">
.codemirror-contain {
  border-radius: 8px;
  overflow: hidden;
}
.list-contain {
  ::v-deep .el-button + .el-button {
    margin-left: 0;
  }
  ::v-deep .el-icon {
    font-size: 16px;
  }
}
</style>

<style lang="scss">
.el-dialog.is-fullscreen.astrolink-flow-custom-dialog {
  padding: 0;
}
</style>
