import axios from "@/api/axios";
import qs from "qs";
import { dataC } from 'turing-plugin'
import defaultImg from "../assets/files/default-img.dataurl?raw";
import defaultBg from "../assets/files/default-bg.dataurl?raw";

const baseUrl = '/'
/**
 * 查询指令模板
 */
export function getPromptList(params: any): Promise<any> {
  const url = baseUrl + "/prompt?" + qs.stringify(params);
  return axios.get(url);
}
/** 获取promoto标签 */
export function getTaglistPromot(val: any) {
  const url = baseUrl + `/labels/unPage?kindCode.equals=${val}`;
  return axios.get(url);
}
/**
 * 新增指令
 */
export function handleAddPrompt(data: any): Promise<any> {
  const url = baseUrl + "/prompt";
  return axios.post(url, data);
}

/**
 * 上传文件
 * @param data
 * @returns
 */
export function uploadFile(fileName: string, file: any): Promise<any> {
  const formData = new FormData();
  formData.append("traceId", dataC.generateUUID());
  formData.append("fileName", fileName);
  formData.append("file", file);
  const url = `${
    (window as any).SYSTEM_CONFIG_BASEURL || ""
  }/proxyApi/turing-planet/turing/planet/v2/file/uploadFile`;
  return axios.post(url, formData);
}

/**
 * 下载文件【回显图片】
 * @param id
 * @returns
 */
export function downloadFile(id: string, type = 1) {
  if (dataC.isEmpty(id)) return type === 1 ? defaultImg : defaultBg;
  // 老数据兼容
  if (
    id.startsWith("http") ||
    id.includes("/images/avatar-") ||
    id.startsWith("data:image/")
  )
    return id;
  return `${
    (window as any).SYSTEM_CONFIG_BASEURL || ""
  }/proxyApi/turing-planet/turing/planet/v2/file/download/${id}`;
}


/**
 * 根据用户输入的API地址获取动态的数据
 * @param url
 * @returns
 */
export function getAPIData(apiUrl: string): Promise<any> {
  return axios.get(apiUrl);
}
