export const argsTypeList: Array<any> = [
  {
    label: '字符串',
    value: 'string',
    style: [
      {
        label: '文本框',
        value: 'text'
      },
      {
        label: '密码框',
        value: 'password'
      },
      {
        label: '多行文本框',
        value: 'textarea'
      },
      {
        label: '下拉框',
        value: 'select'
      },
      {
        label: '文件上传',
        value: 'upload'
      },
      {
        label: '代码编辑器',
        value: 'code'
      },
      {
        label: '模板编辑器',
        value: 'template'
      },
      {
        label: '单选框',
        value: 'radio'
      },
      {
        label: '日期',
        value: 'datetime'
      },
      {
        label: '音频',
        value: 'voice'
      },
      {
        label: '数据源写入',
        value: 'dataSourceWrite'
      },
    ] as any
  },
  {
    label: '整数',
    value: 'int',
    style: [
      {
        label: '数字输入框',
        value: 'number'
      }
    ]
  },
  {
    label: '浮点数',
    value: 'float',
    style: [
      {
        label: '数字输入框',
        value: 'number'
      }
    ]
  },
  {
    label: '布尔值',
    value: 'boolean',
    style: [
      {
        label: '开关项',
        value: 'switch'
      }
    ]
  },
  {
    label: '列表',
    value: 'list',
    style: [
      {
        label: '文本框',
        value: 'text'
      },
      {
        label: '复选框',
        value: 'checkbox'
      },
      {
        label: '下拉框',
        value: 'select'
      },{
        label: '限制条件',
        value: 'limit'
      },
      {
        label: '去重规则',
        value: 'distinct'
      },
      {
        label: '排序条件',
        value: 'sort'
      },

      {
        label: '数据源读取',
        value: 'dataSourceRead'
      }
    ]
    // 列表
  },
  {
    label: '动态类型',
    value: 'dynamic',
    style: [
      {
        label: '动态添加参数',
        value: 'dynamic'
      }
    ]
  }
]

export const defaultArgs: any = {
  text: {
    defaultValue: '',
    props: {
      maxLength: null,
      minLength: null,
      rules: ''
    }
  },
  password: {
    defaultValue: '',
    props: {
      maxLength: null,
      minLength: null,
      rules: ''
    }
  },
  textarea: {
    defaultValue: '',
    props: {
      maxLength: null,
      minLength: null,
      rules: ''
    }
  },
  number: {
    defaultValue: 0,
    props: {
      max: 100,
      min: 0,
      rules: ''
    }
  },
  select: {
    defaultValue: '',
    props: {
      isCustomApi: false,
      apiUrl: '',
      options: [],
      rules: ''
    }
  },
  switch: {
    defaultValue: false,
    props: {
      rules: ''
    }
  },
  radio: {
    defaultValue: '',
    props: {
      options: [],
      rules: ''
    }
  },
  datetime: {
    defaultValue: '',
    props: {
      optDefault: 'YYYY-MM-DD',
      options: [
        {
          name: '年-月-日',
          value: 'YYYY-MM-DD'
        },
        {
          name: '年-月-日 时:分:秒',
          value: 'YYYY-MM-DD HH:mm:ss'
        }
      ],
      rules: ''
    }
  },
  voice: {
    defaultValue: '',
    props: {
      rules: ''
    }
  },
  checkbox: {
    defaultValue: [],
    props: {
      isCustomApi: false,
      apiUrl: '',
      options: [],
      rules: ''
    }
  },
  upload: {
    defaultValue: '',
    props: {
      fileSize: 200,
      extensions: [],
      fileName: '',
      uploadLoading: false,
      rules: ''
    }
  },
  code: {
    defaultValue: '',
    props: {
      remark: '',
      rules: ''
    }
  },
  template: {
    defaultValue: '',
    props: {
      rules: ''
    }
  },
  dynamic: {
    defaultValue: '',
    props: {
      args: [],
      rules: ''
    }
  },
  process: {
    defaultValue: '',
    props: {
      args: [],
      rules: ''
    }
  },
  filter: {
    defaultValue: {},
  },
  limit: {
    defaultValue: [],
  },
  distinct: {
    defaultValue: [],
  },
  sort: {
    defaultValue: [],
  },
  dataSourceWrite: {
    defaultValue: [],
    props: {
      isCustomApi: false,
      apiUrl: '',
      options: [],
      rules: ''
    }
  },
  dataSourceRead: {
    defaultValue: '',
    props: {
      rules: ''
    }
  }
}

// txt，md，doc，docx，ppt，pptx，xls，xlsx，pdf
export const fileType: Array<string> = ['.txt', '.md', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx', '.pdf', '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.pcm', '.wav', '.mp3', '.mp4']

// 校验规则选项
export const rulesOptions = [
  { label: "手机号", value: "^(?:(?:\\+|00)86)?1[3-9]\\d{9}$" },
  { label: "24小时制时间", value: `^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d$` },
  { label: "统一社会信用代码", value: "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$" },
]


export const incomingMethods = [
  { name: "Body", id: "Body" },
  { name: "Header", id: "Header" },
  { name: "Path", id: "Path" },
  { name: "Query", id: "Query" },
]