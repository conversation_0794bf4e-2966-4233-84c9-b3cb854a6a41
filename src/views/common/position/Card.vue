<template>
  <div class="card_wrapper">
    <!-- 标题 -->
    <h2 class="title" :title="delHtmlTag(result_title)" v-html="result_title" @click.stop.native="handleRedirect"></h2>
    <!-- 卡片内容 -->
    <div class="card_content">
      <!-- 图片 -->
      <a class="link" v-if="result_url && result_link" :href="result_url" target="_blank" :title="delHtmlTag(result_title)">
        <img class="preview_img" :src="result_link" :alt="delHtmlTag(result_title)" />
      </a>
      <img class="preview_img" v-if="!result_url && result_link" :src="result_link" :alt="delHtmlTag(result_title)" />
      <!-- 概要 -->
      <div class="summary_content">
        <div class="content">
          <span class="name">content: </span>
          <span class="summary" v-html="data?.content && (contentInfo.showMore ? (contentInfo.isMore ? contentInfo.short_summary : contentInfo.summary) : data?.content)" />
          <el-link type="primary" class="show_more_summary" @click.stop.native="contentInfo.isMore = !contentInfo.isMore" v-if="data?.content?.length > 140">{{
            contentInfo.isMore ? "[更多]" : "[折叠]"
          }}</el-link>
        </div>
        <div class="time_content">
          <span class="time_info">{{ formatTime(data) }}</span>
          <span class="link_info">{{ data.site }}</span>
        </div>
        <ul class="debug_info_wrapper" @click.stop>
          <li v-if="showId" :title="`id:${data.id}`">
            <span class="name">id：</span>
            <span>{{ data.id }}</span>
          </li>
          <!-- <li v-if="typeInfo.indexField == '_semtrc_index'">
            <span class="name">召回方式：</span>
            <span>向量召回</span>
          </li>
          <li v-if="typeInfo.indexField == '_text_index'">
            <span class="name">召回方式：</span>
            <span>文本召回</span>
          </li> -->
          <li v-if="data._indexName">
            <el-popover placement="bottom" :width="250" trigger="hover">
              <template #reference>
                <span
                  ><span class="name">结果来源：</span>
                  <span
                    >{{ data._indexName }} <span style="color: #3a99fe">({{ data._indexCode }})</span></span
                  ></span
                >
              </template>
              <span
                >{{ data._indexName }}<span style="color: #3a99fe">({{ data._indexCode }})</span></span
              >
            </el-popover>
          </li>
          <li v-if="data[typeInfo.scoreField]">
            <span class="name">得分（位次）：</span>
            <span>{{ Number(data[typeInfo.scoreField]).toFixed(7) }} ({{ data[typeInfo.indexField] }})</span>
            <el-popover v-if="!dataC.isEmpty(data._eval_props)" placement="right" :width="250" trigger="hover">
              <template #reference>
                <el-icon class="icon-tip"><Notification /></el-icon>
              </template>
              <div v-for="str in getPropsStrList(data._eval_props)">
                {{ str }}
              </div>
            </el-popover>
          </li>
          <li class="feedback" v-if="mode == 'ceping'">
            <span class="name">反馈</span>
            <span>
              <Popover
                :data="data"
                :resultData="resultData"
                type="zan"
                :texts="['该优质结果未出现在最终结果, 于', '节点被过滤,详情如下']"
                :mode="mode"
                :markRecordId="markRecordId"
                :strategyId="strategyId"
                :targetId="targetId"
              />
              <Popover
                :data="data"
                :resultData="resultData"
                type="cai"
                :texts="['该劣质结果出现在最终结果，于', '节点打分过高,详情如下']"
                :mode="mode"
                :markRecordId="markRecordId"
                :strategyId="strategyId"
                :targetId="targetId"
              />
            </span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, computed, ref, onMounted } from "vue";
import { keys } from "lodash";
import { getCharactersLimit } from "@/utils/common";
import { dataC, timeC } from "turing-plugin";
import * as cepingApi from "@/api/eval-manage";
import useCtx from "@/hooks/useCtx";

import Popover from "./popover.vue";
const contentInfo = ref({});
const { $router, proxy, $app } = useCtx();
const props = defineProps({
  data: { type: Object },
  index: { type: Number },
  type: { type: String },
  typeInfo: { type: Object },
  showOptions: { type: Object },
  keyEnum: { type: Object },
  evaluateLoading: { type: Boolean },
  showId: { type: Boolean },
  mode: { type: String },
  resultData: { type: Object },
  markRecordId: { type: String },
  strategyId: { type: String },
  targetId: { type: String },
});

// 计算属性重构
const result_title = computed(() => props.data?.title);
const query_result_name = computed(() => props.data?.name);
const result_url = computed(() => props.data?.url);
const result_link = computed(() => props.data?.imageLink);

// 生命周期钩子改造
onMounted(() => {
  contentInfo.value = getCharactersLimit(props.data.content, 140);
});

const handleRedirect = () => {
  window.open(result_url.value, "_blank");
};

const getPropsStrList = (props: any) => {
  const res = keys(props).map((key) => {
    return `${key}：${JSON.stringify(props[key])}`;
  });
  return res;
};

const formatTime = (data) => {
  return data.post_ts ? timeC.format(data && data.post_ts * 1000, "YYYY-MM-DD") : "暂无时间";
};

const delHtmlTag = (str) => {
  return str?.replace(/<[^>]+>/g, "") || "";
};

const formatRecallSource = (recallSource) => {
  const recallSourceMap = new Map([
    [0, "文本"],
    [1, "向量"],
    [2, "文本+向量"],
  ]);
  return recallSourceMap.get(recallSource);
};
</script>

<style lang="scss" scoped>
// Variables
$primary-color: #4b72ef;
$primary-hover-color: #2557d5;
$highlight-color: #f53c54;
$text-color: #1e1f26;
$secondary-text-color: #7c8198;
$border-color: #ccc;
$bg-light: #f8fafd;

// Common styles
.name {
  color: var(--el-color-primary);
  display: inline-block;
}

:deep(.el-descriptions__label) {
  color: $primary-color;
  font-weight: 500;
}

// Card wrapper styles
.card_wrapper {
  width: calc(100% - 15px);
  cursor: pointer;

  .title {
    font-size: 18px;
    font-weight: 500;
    color: $primary-color;
    display: inline-block;

    ::v-deep(b) {
      color: $highlight-color;
    }

    &:hover {
      cursor: pointer;
      text-decoration: underline;
      color: $primary-hover-color;
    }
  }

  // Card content styles
  .card_content {
    display: flex;

    .preview_img {
      width: 154px;
      height: 107px;
      border-radius: 10px;
      object-fit: cover;
      margin-right: 10px;
    }

    // Summary content styles
    .summary_content {
      width: 100%;

      .content {
        box-sizing: content-box;
        max-height: 200px;
        overflow: auto;
      }

      .summary {
        width: 100%;
        word-break: break-all;
        font-size: 14px;
        font-weight: 400;
        text-align: left;
        color: $text-color;
        line-height: 20px;
        margin-bottom: 4px;
        display: inline;

        ::v-deep(b) {
          color: $highlight-color;
        }
      }

      .time_content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
        font-weight: 400;
        color: $secondary-text-color;
        line-height: 20px;
      }

      // Debug info styles
      .debug_info_wrapper {
        background: $bg-light;
        border-radius: 4px;
        width: 100%;
        padding: 3px 3px;
        cursor: default;
        overflow-x: scroll;

        li {
          display: inline-block;
          width: 100%;
          font-size: 12px;
          font-weight: 400;
          color: $secondary-text-color;
          line-height: 17px;
          white-space: nowrap;
          // @include no-wrap();

          &.feedback {
            height: 20px;
            border-top: 1px solid $border-color;
          }
        }
      }
    }
  }

  .disabled-opacity-operate {
    opacity: 0.2;
    pointer-events: none;
  }

  .link {
    width: 100%;
    margin-bottom: 4px;
  }
}

// Utility classes
.label {
  font-size: 14px;
  color: $secondary-text-color;
}

.show_more_summary {
  margin-left: 10px;
  font-size: 12px;
  vertical-align: baseline;
}

.icon-tip {
  color: $primary-color;
  margin-left: 10px;
}
</style>