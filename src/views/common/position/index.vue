<template>
  <div class="posiiton-container" v-if="resultData && resultData.length && !loading">
    <div class="search">
      <div>
        <my-query :queryItems="queryItems" :refreshBtn="{ show: false }" @search="handleFilter" @reset="resetQuery" />
      </div>

      <div class="reset">
        <my-button @click="handleDefault" type="primary" v-if="$app.$route.query?.searchId || $app.$route.query?.url">重置</my-button>
      </div>
    </div>

    <div class="content">
      <div class="content-item" v-for="(traceLogItem, index) in resultData">
        <div class="name">
          <span v-if="traceLogItem.mockDocs">mock:<el-switch v-model="traceLogItem.mock" /></span>
          <span>{{ traceLogItem.name }}</span>
          <span>({{ traceLogItem?.[traceLogItem.mock ? "mockDocs" : "docs"]?.length || 0 }} 条)</span>
        </div>
        <div class="docs" ref="scrollableElements">
          <div
            v-for="(doc, index1) in traceLogItem[traceLogItem.mock ? 'mockDocs' : 'docs']"
            class="list-card"
            :class="{
              'active-list-card': activeUrl == doc.doc[searchKey],
            }"
            @click="handleNodeToLine(doc, index)"
          >
            <!-- {{activeUrl}} , {{doc.doc[searchKey]}} , {{searchKey}} -->
            <div class="list-card-index">{{ index1 + 1 }}</div>
            <Card
              :data="doc.doc"
              :resultData="resultData"
              :index="index1"
              :showId="true"
              :keyEnum="keyEnum"
              :typeInfo="traceLogItem"
              :mode="routeQuery.mode"
              :markRecordId="routeQuery.markRecordId"
              :strategyId="routeQuery.strategyId"
              :targetId="routeQuery.targetId"
            />
          </div>
          <my-empty
            :size="120"
            v-if="!traceLogItem[traceLogItem.mock ? 'mockDocs' : 'docs'] || !traceLogItem[traceLogItem.mock ? 'mockDocs' : 'docs']?.length"
          />
        </div>
      </div>
    </div>
  </div>
  <my-empty :size="120" v-if="!resultData" />
  <div v-loading="loading" v-if="loading" class="loading-position"></div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import * as evaluationApi from "@/api/eval-evaluation";
import useCtx from "@/hooks/useCtx";
import { assign, cloneDeep } from "lodash";
import useStore from "@/store";
import Card from "./Card.vue";
import { ElMessage, ElLoading } from "element-plus";
import { computed } from "vue";
import { onUnmounted } from "vue";
import * as cepingApi from "@/api/eval-manage";
import * as util from "@/utils/common";
import { dataC } from "turing-plugin";
import { da } from "element-plus/es/locale";

const { $router, proxy, $app } = useCtx();
const { api } = useStore();
const siteTableRef = ref();
let searchKey = ref("searchId");
const props = defineProps(["transformListData", "loadDataApi"]);
const activeUrl = ref($app.$route.query.searchId || $app.$route.query.url);
const routeQuery = ref($app.$route.query);
let timeout: any = "";
let loading = ref(false);
const isCeping = computed(() => routeQuery.value.mode == "ceping");

const keyEnum = [
  { value: "_indexCode", label: "结果来源" },
  { value: "_indexName", label: "结果来源（中文）" },
  { value: "post_ts", label: "发布时间" },
  { value: "site", label: "站点" },
  { value: "indexField", label: "召回方式" },
  { value: "_textrecall_score", label: "影响重排的各因素得分详情" },
  { value: "id", label: "id" },
];
const queryItems = ref<any>({
  search: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: {
      placeholder: "请输入标题搜索",
    },
  },
});
const handleFilter = (val: any) => {
  // 遍历每个召回组（如 ES向量召回-v2、ES文本召回-v2）
  for (const group of resultData.value) {
    // 遍历每组中的 docs
    for (const docDto of group.docs) {
      // 精确匹配标题
      if (docDto.doc.title === val.search) {
        activeUrl.value = docDto.doc[searchKey.value]; // 找到则返回 URL
      }
    }
  }
  nextTick(() => {
    scrollToActiveListCard();
  });
};
const resetQuery = (obj: any) => {
  activeUrl.value = "";
  for (let key in obj) {
    queryItems.value[key].modelValue = obj[key].modelValue;
  }
};

//traceList 列表
let resultData = ref([]);
const handleNodeToLine = (data: any, index: any) => {
  ElMessage.closeAll();
  searchKey.value = data.doc.searchId ? "searchId" : "url";
  activeUrl.value = data.doc[searchKey.value];
  nextTick(() => {
    scrollToActiveListCard(index);
  });
};
const handleDefault = () => {
  searchKey.value = $app.$route.query.searchId ? "searchId" : "url";
  activeUrl.value = $app.$route.query[searchKey.value];
  nextTick(() => {
    scrollToActiveListCard();
  });
};
/**
 * 方法什么作用？
 */
const scrollToActiveListCard = (columnIndex?: any) => {
  ElMessage.closeAll();
  let top = 0;
  const delayInterval = 1000; // 设置每个列滚动操作的延迟时间间隔（毫秒）

  if (columnIndex || columnIndex === 0) {
    let clickCard = proxy.$refs.scrollableElements[columnIndex].querySelector(".active-list-card");
    let clickContainer = proxy.$refs.scrollableElements[columnIndex];
    let elementOffsetTop = clickCard.offsetTop;
    let containerScrollTop = clickContainer.scrollTop;
    top = elementOffsetTop - containerScrollTop;
  }

  (proxy.$refs.scrollableElements || []).forEach((container: any, index: any) => {
    timeout = setTimeout(() => {
      const activeCard = container.querySelector(".active-list-card");
      if (index != columnIndex && activeCard) {
        container.scrollTo({
          top: activeCard.offsetTop - container.offsetTop - top + 40,
          behavior: "smooth",
        });
      }
      if (!activeCard) {
        ElMessage.closeAll();
        $app.$message.warning(resultData.value[index].name + "  没有查找到结果");
      }
    }, index * delayInterval);
  });
};

/**
 * 解析全链路结果
 *
 */
const transformListData = (res: any) => {
  return res.map((traceNode: any, columnIndex: number) => {
    (traceNode.docs || []).map((traceRecord: any, rowIndex: any) => {
      parseDoc(traceRecord, false, rowIndex, columnIndex);
    });

    //todo 解析mockdoc
    (traceNode.mockDocs || []).map((traceRecord: any, rowIndex: any) => {
      parseDoc(traceRecord, true, rowIndex, columnIndex);
    });

    return traceNode;
  });
};

/**
 *data {doc:{},markResult:{}}
 */
function parseDoc(data: any, isMock: boolean, rowIndex: any, columnIndex: any) {
  data.doc.url = data.doc.url || util.displayUrl(data.doc.protocol, data.doc.domain, data.doc.path);
  //用于检索
  data.doc.searchId = data.doc.id;
  data.doc.mock = isMock;
  data.doc.rowIndex = rowIndex;
  data.doc.columnIndex = columnIndex;
  if (isCeping.value) {
    data.doc.markResult = data.markResult ? data.markResult : {};
    data.doc._popoverVisible = false;
  }
}

const getTraceinfo = async (params?: any) => {
  const routeQuery = $router.currentRoute.value.query;
  const strategyId = routeQuery.strategyId;
  loading.value = true;
  resultData.value = [];
  //获取全链路信息
  props
    .loadDataApi(params)
    .then((res: any) => {
      // console.log("result:", res);
      // console.log("routeQuery", routeQuery);

      //根据strategyId或tag过滤数据
      let filtedStrategy;
      if (strategyId) {
        console.log("走标注、任务模式");

        filtedStrategy = res.data.targets.find((strategy) => strategy.strategyId === strategyId);
        resultData.value = transformListData(filtedStrategy.traceList || []);
      } else if (routeQuery.tag) {
        //体验模式
        console.log("体验模式");
        const tag = routeQuery.tag;
        filtedStrategy = res.data.targets.find((strategy) => strategy.metadata.tag === tag);
        resultData.value = transformListData(filtedStrategy.traceList || []);
      } else {
        console.log("其他");
        filtedStrategy = res;
        resultData.value = transformListData(filtedStrategy.trace || []);
      }
      // console.log("filtedStrategy:",filtedStrategy);
      // console.log("resultData", resultData.value);

      if (activeUrl.value) {
        nextTick(() => {
          handleDefault();
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(() => {
  getTraceinfo();
});
onUnmounted(() => {
  clearTimeout(timeout);
});
//接口暴露
defineExpose({ getTraceinfo });
</script>
<style lang="scss" scoped>
.posiiton-container {
  overflow: auto;
  height: calc(100vh - 65px);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .search {
    display: flex;
    justify-content: flex-end;
    height: auto;
  }

  .arrow-to-right {
    width: 55px;
    height: 30px;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    background: url("@/assets/images/arrowL.png") no-repeat;
    background-size: cover;
    background-position: center center;
  }
  .content {
    flex: 1;
    min-height: 0;
    display: flex;
    position: relative;
    overflow-x: auto;
    .content-item {
      flex: 1 0 330px; /* 简写：flex-grow | flex-shrink | flex-basis */
      min-width: 330px; /* 强制最小宽度 */
      height: 100%;
      display: flex;
      flex-direction: column;

      .name {
        height: 32px;
        margin-bottom: 10px;
        font-weight: bold;
        cursor: pointer;
        span:nth-child(2) {
          margin-left: 10px;
        }
      }

      .docs {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
        padding-left: 2px;
        overflow-y: auto;
        position: relative;
        .list-card {
          min-width: 220px;
          margin-bottom: 15px;
          width: calc(100% - 20px);

          padding: 10px 10px;
          box-shadow: var(--el-box-shadow-light);
          display: flex;
          .list-card-index {
            padding: 0 5px;
            font-weight: 800;
            margin-left: -10px;
          }
        }
        .active-list-card {
          background-color: #c4e4fc;
        }
      }
    }
  }
}
.loading-position {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>
<style lang="scss">
.position-dialog {
  .el-dialog__header {
    padding-bottom: 0;
  }
}
</style>
