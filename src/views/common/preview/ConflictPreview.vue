<template>
  <page-wrapper route-name="conflict-preview::">
    <el-col class="conlict-preview">
      <preview-table ref="previewTableRef"></preview-table>
    </el-col>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";

const { $router, $app, proxy } = useCtx();

const id = $router.currentRoute.value.query.id;
const siteId = $router.currentRoute.value.query.siteId;//查询正文时必须传这个

const loadList = () => {
  proxy.$refs["previewTableRef"].loadList("fromSiteVersionConflict", id, { siteId: siteId });
};
//初始化
onMounted(() => {
  loadList();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.conflict-preview {
  height: 100%;
}
</style>
