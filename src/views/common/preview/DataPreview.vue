<template>
  <div>
    <FullscreenPage title="数据预览" @close="events.close" class="data-preview-full-screen-page">
      <template #header>
        <div v-if="withSelect" style="display: flex">
          <el-button type="primary" @click="events.auditOperate('accept')" v-if="waitAudit || failAudit">审核通过</el-button>
          <el-button type="warning" @click="events.auditOperate('reject')" v-if="waitAudit || successAudit">审核不通过</el-button>
          <el-button type="danger" @click="events.auditOperate('purge')" v-if="failAudit">彻底删除</el-button>
          <el-button type="success" @click="events.auditOperate('transfer')" v-if="successAudit">入库</el-button>
          <div class="total-box" v-if="selectTotal > 0">
            {{ $t("title.selected") }}
            <span>{{ selectTotal }}</span>
            {{ $t("title.item") }}
          </div>
        </div>
      </template>
      <div class="data-preview flexBetween">
        <div class="left-wrap">
          <template v-if="withSelect">
            <div class="top">
              <el-checkbox v-model="selectCur" :value="true" />
              <el-dropdown trigger="click">
                <el-button link :icon="ArrowDownBold"> </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="events.selectAll">
                      <div>
                        <el-icon v-if="selectAll"><Select /></el-icon>
                        <span :style="{ 'margin-left': selectAll ? '0' : '19px' }">选择全部</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item @click="events.selectCur">
                      <div>
                        <el-icon v-if="!selectAll && selectCur"><Select /></el-icon>
                        <span :style="{ 'margin-left': !selectAll && selectCur ? '0' : '19px' }">选择当前页</span>
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item @click="events.clearAll"><span style="margin-left: 19px">清除选择</span></el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <ul class="menu-wrap" :class="{ 'has-pagination': page.total > page.size }">
            <li
              class="menu-item"
              v-for="(item, index) of viewTableData"
              :key="item._id"
              @click="refreshContent(item._id, modelValue.contentFrom)"
              :class="{ active: item._id == modelValue.showDataItemId }"
              :title="item.name"
            >
              <el-checkbox v-if="withSelect" :value="true" v-model="item.selected" />
              {{ (page.page - 1) * page.size + index + 1 }}.&nbsp;{{ item.title || item._id }}
            </li>
          </ul>
          <div class="pagination-wrap">
            <el-pagination
              :current-page="page.page"
              :page-size="page.size"
              :total="page.total"
              layout="prev, pager, next"
              :pager-count="4"
              @size-change="events.handleSizeChange"
              @current-change="events.handleCurrentChange"
            />
          </div>
        </div>
        <div class="center-wrap height-adaptive" :style="isRegion ? 'width: calc(100% - 314px); margin-right: 0;' : 'width: calc(100% - 680px);flex:1'">
          <h2 class="title" @click="events.originData(showDataItem)">{{ showDataItem.title || showDataItem._id }}</h2>
          <div class="main" v-if="!isRegion">
            {{ modelValue.content.mongo }}
          </div>
          <div class="main" v-if="isRegion">
            <el-row>
              <el-radio-group v-model="modelValue.contentOrigin">
                <el-radio-button label="mongo" value="mongo" />
                <el-radio-button label="es" value="es" />
              </el-radio-group>
            </el-row>
            <div style="height: calc(100% - 55px); margin-top: 20px">
              <json-viewer :value="contentDoc" expanded :expand-depth="5" copyable boxed sort></json-viewer>
            </div>
          </div>
        </div>
        <div class="right-wrap" v-if="!isRegion">
          <div class="top">
            <el-tooltip>
              <template #content>
                <span>未从数据侧获取到的字段为蓝色标识，数据侧暂未被使用的业务字段为红色标识</span>
              </template>
              <div>文档属性：缺失字段以<span style="color: #029dfe">蓝色</span>标识,多余字段以<span style="color: #fe4848">红色</span>标识</div>
            </el-tooltip>
          </div>
          <div class="main">
            <div class="detail-item" v-for="item in siteFieldList">
              <div>
                <label v-if="item.tag == 'A'">{{ `${item.name}(${item.fieldName})` }} : </label>
                <label v-if="item.tag == 'B'" style="color: #029dfe">{{ `${item.name}(${item.fieldName})` }} : </label>
                <label v-if="item.tag == 'C'" style="color: #fe4848">{{ `${item.name}(${item.fieldName})` }} : </label>
                {{ showDataItem[item.fieldName] }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </FullscreenPage>
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, watch, computed } from "vue";
import { keys, assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import FullscreenPage from "@/components/layout/FullscreenPage.vue";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";
import * as previewApi from "@/api/preview";
import * as tdocAuditApi from "@/api/tdoc-audit";
import * as util from "@/utils/common";
import * as previewConfig from "./config";
import JsonViewer from "vue-json-viewer";
import { ElMessageBox, ElSwitch } from "element-plus";
import { ArrowDownBold } from "@element-plus/icons-vue";
import useCtx from "@/hooks/useCtx";
const { $app, $router } = useCtx();
const props = defineProps({
  //多选相关
  withSelect: { type: Boolean, default: false },
});
//数据项
const modelValue = reactive({
  fromType: "", //来源类型
  extraParams: {}, //查询时需要的参数
  dataItemList: [], //数据列表
  showDataItemId: "", //展示的数据Id
  //展示的数据正文
  content: {
    mongo: "",
    mongoDoc: {},
    esDoc: {},
  },
  contentOrigin: "mongo",
  contentFrom: "", //full存量，hot实时
  //渲染数据需要的信息
  propertyList: [],
  metaLabelList: [],
  metaSiteFieldList: [],
});
const waitAudit = computed(() => {
  return modelValue.extraParams.tab == 0;
});
const successAudit = computed(() => {
  return modelValue.extraParams.tab == 1;
});
const failAudit = computed(() => {
  return modelValue.extraParams.tab == 2;
});
const selectTotal = computed(() => {
  const tableData = modelValue.dataItemList;
  if (selectAll.value) {
    return modelValue.extraParams.total;
  } else {
    return tableData.filter((item) => {
      return item.selected;
    }).length;
  }
});
const selectCur = computed({
  get() {
    const tableData = modelValue.dataItemList;
    if (tableData.length == 0) return false;
    const curTableData = tableData.slice((page.page - 1) * page.size, page.page * page.size);
    return curTableData.every((item) => item.selected == true);
  },
  set(val) {
    if (val) {
      events.selectCur();
    } else {
      events.clearAll();
    }
  },
});
const selectAll = computed({
  get() {
    const tableData = modelValue.dataItemList;
    if (tableData.length == 0) return false;
    return tableData.every((item) => item.selected == true);
  },
  set(val) {},
});
//监听显示的doc，动态更新propertyList
watch(
  () => modelValue.showDataItemId,
  (newVal, oldVal) => {
    const newDataItem = dataC.getItemByValue(modelValue.dataItemList, newVal, "_id");
    const oldDataItem = dataC.getItemByValue(modelValue.dataItemList, oldVal, "_id");
    if (modelValue.fromType == "fromOfflineDatasetVersion" || (modelValue.fromType == "fromRegion" && modelValue.extraParams.type == 1)) {
      //离线数据集从离线数据集配置拿
      datasetApi.getDataset(modelValue.extraParams.datasetId).then((result) => {
        modelValue.propertyList = result.fields.map((item) => {
          return item.fieldName;
        });
      });
    } else {
      //非离线数据集根据site从站点配置拿
      if (dataC.isEmpty(newDataItem.site)) modelValue.propertyList = [];
      if (newDataItem.site != oldDataItem.site) {
        //获取站点属性
        siteApi.getSiteByName(showDataItem.value.site).then((result) => {
          modelValue.propertyList = !dataC.isEmpty(result.properties) ? result.properties.split(",") : [];
        });
      }
    }
  }
);
const viewTableData = computed(() => {
  if (dataC.isEmpty(modelValue.dataItemList)) return [];
  return modelValue.dataItemList.slice((page.page - 1) * page.size, page.page * page.size);
});
//是否是增量审核相关
const isTdocAudit = computed(() => {
  return modelValue.fromType == "fromTdocAudit";
});
//是否是索引库相关
const isRegion = computed(() => {
  return modelValue.fromType == "fromRegion";
});
const contentDoc = computed(() => {
  if (modelValue.contentOrigin == "mongo") return modelValue.content.mongoDoc;
  if (modelValue.contentOrigin == "es") return modelValue.content.esDoc;
});
//选中的数据
const showDataItem = computed(() => {
  return dataC.getItemByValue(modelValue.dataItemList, modelValue.showDataItemId, "_id");
});
//站点属性
const siteFieldList = computed(() => {
  //返回值  勾选有，实际有则标记为A，勾选有，实际没有则标记为B，勾选没有，实际有，则标记为C
  const list = [];
  //勾选的属性 modelValue.propertyList
  //实际的属性 showDataItem.value.propertyList
  const itemKeys = showDataItem.value.propertyList?.split(",") || [];
  //先增加勾选的属性，如果勾选的属性有值，则标记为A，否则标记为B
  modelValue.propertyList.forEach((item) => {
    const siteField = dataC.getItemByValue(modelValue.metaSiteFieldList, item, "fieldName");
    if (itemKeys.includes(item)) {
      list.push({ name: siteField.name ? siteField.name : item, fieldName: siteField.fieldName ? siteField.fieldName : item, tag: "A" });
    } else {
      list.push({ name: siteField.name ? siteField.name : item, fieldName: siteField.fieldName ? siteField.fieldName : item, tag: "B" });
    }
  });
  //再添加文档中没有被勾选的属性，discardList中的属性和以“_”开头的属性除外，添加标记C
  const discardList = ["timestamp", "content_str_gzip"];
  itemKeys.forEach((item) => {
    if (!modelValue.propertyList.includes(item) && !discardList.includes(item) && !item.startsWith("_")) {
      list.push({ name: item, fieldName: item, tag: "C" });
    }
  });
  return list;
});
//分页参数
const page = reactive({
  page: 1,
  size: 20,
  total: 0,
});
//获取正文-site dataset
const getPreviewContent = () => {
  let previewFrom = "";
  let previewFromId = "";
  if (modelValue.fromType == "fromSite" || modelValue.fromType == "fromSiteVersionConflict") {
    previewFrom = "site";
    previewFromId = modelValue.extraParams.siteId;
  } else if (modelValue.fromType == "fromRule" || modelValue.fromType == "fromSiteVersion") {
    previewFrom = "siteVersion";
    previewFromId = modelValue.extraParams.siteVersionId;
  } else if (
    modelValue.fromType == "fromDatasetVersion" ||
    modelValue.fromType == "fromDatasetVersionSite" ||
    modelValue.fromType == "fromIndexPublishFail" ||
    modelValue.fromType == "fromOfflineDatasetVersion"
  ) {
    previewFrom = "datasetVersion";
    previewFromId = modelValue.extraParams.datasetVersionId;
  }
  previewApi.getPreviewContent(modelValue.showDataItemId, previewFrom, previewFromId).then((result) => {
    modelValue.content.mongo = result.data;
  });
};
//获取正文-增量审核
const getPreviewContentFromTdocAudit = () => {
  tdocAuditApi.getPreviewContent(modelValue.showDataItemId, modelValue.extraParams.auditId).then((result) => {
    modelValue.content.mongo = result.data;
  });
};
//获取正文-索引库
const getPreviewContentFromRegion = () => {
  previewApi
    .getPreviewContentFromRegion(modelValue.extraParams.indexId, modelValue.extraParams.region, modelValue.showDataItemId, modelValue.contentFrom == "hot")
    .then((result) => {
      modelValue.content.mongoDoc = result.data.mongoDoc;
      modelValue.content.esDoc = result.data.esDoc;
    });
};
//事件列表
const events = reactive({
  handleCurrentChange: (val: number) => {
    page.page = val;
    events.clearAll();
  },
  handleSizeChange: (val: number) => {
    page.size = val;
    events.clearAll();
  },
  originData: (x: any) => {
    window.open(x.url || util.displayUrl(x.protocol, x.domain, x.path), "_blank");
  },
  close: () => {
    emits("close");
  },
  selectAll: () => {
    const tableData = modelValue.dataItemList;
    tableData.forEach((item) => {
      item.selected = true;
    });
  },
  selectCur: () => {
    events.clearAll();
    const tableData = modelValue.dataItemList;
    const curTableData = tableData.slice((page.page - 1) * page.size, page.page * page.size);
    curTableData.forEach((item) => {
      item.selected = true;
    });
  },
  clearAll: () => {
    const tableData = modelValue.dataItemList;
    tableData.forEach((item) => {
      item.selected = false;
    });
  },
  auditOperate: (operate: String) => {
    const tableData = modelValue.dataItemList;
    const ids = tableData
      .filter((item) => {
        return item.selected == true;
      })
      .map((item) => {
        return item._id;
      });
    if (ids.length == 0) {
      $app.$message.warning("您尚未选择数据！");
      return;
    }
    const handleAuditOperate = (operate, additionalParams = {}) => {
      const obj = {
        tab: modelValue.extraParams.tab,
        selectedAll: selectAll.value,
        auditId: modelValue.extraParams.auditId,
        ids: ids,
        ...additionalParams,
      };
      const auditOperateDto = assign({}, modelValue.extraParams.query, obj);
      tdocAuditApi.auditOperate(operate, auditOperateDto).then((result) => {
        $app.$message.success("操作成功");
        $router.go(-1);
      });
    };
    if (operate === "transfer") {
      const buildTask = ref(false);
      ElMessageBox({
        title: "确认入库?",
        message: () =>
          h(ElSwitch, {
            activeText: "增量数据自动更新",
            modelValue: buildTask.value,
            "onUpdate:modelValue": (val: boolean) => {
              buildTask.value = val;
            },
          }),
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      }).then(() => {
        handleAuditOperate(operate, { buildTask: buildTask.value });
      });
    } else {
      $app.$confirm({ title: `确认操作？` }).then(() => {
        handleAuditOperate(operate);
      });
    }
  },
});
//进入时需要传递的信息
const setMetaLabelList = (metaLabelList: Array<any>) => {
  modelValue.metaLabelList = metaLabelList;
};
const setMetaSiteFieldList = (metaSiteFieldList: Array<any>) => {
  modelValue.metaSiteFieldList = metaSiteFieldList;
};
const refreshList = (fromType: string, extraParams: any, tableData: Array<any>, pageData: any) => {
  modelValue.fromType = fromType;
  modelValue.extraParams = extraParams;
  modelValue.dataItemList = tableData;
  page.page = pageData.pageNum;
  page.size = pageData.pageSize;
  page.total = pageData.total;
};
//刷新正文，和文档属性，contentFrom表示数据来源，full代表存量，hot代表实时
const refreshContent = (dataItemId: string, contentFrom: string) => {
  modelValue.showDataItemId = dataItemId;
  modelValue.contentFrom = contentFrom;
  if (isTdocAudit.value) {
    getPreviewContentFromTdocAudit();
  } else if (isRegion.value) {
    getPreviewContentFromRegion();
  } else {
    getPreviewContent();
  }
};
//事件声明
const emits = defineEmits(["close"]);
//接口暴露
defineExpose({
  setMetaLabelList,
  setMetaSiteFieldList,
  refreshList,
  refreshContent,
});
</script>
<style lang="scss" scoped>
.data-preview {
  height: 100%;
  > div {
    height: 100%;
    border: 1px solid $primary-color;
    border-radius: 8px;
  }
  .left-wrap {
    width: 300px;

    .top {
      padding: 6px 24px;
      border-bottom: 1px solid $primary-color;
    }
    .menu-wrap {
      padding: 16px 12px;
      height: calc(100% - 52px);
      overflow-y: auto;
      &.has-pagination {
        @include calc-height(82px);
      }
      > .menu-item {
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        @include no-wrap();
        & + .menu-item {
          margin-top: 5px;
        }
        &.active {
          background-color: rgba(75, 114, 239, 0.05);
          color: $primary-color;
        }
        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
    .pagination-wrap {
      height: 52px;
      padding: 10px;
      float: right;
    }
  }
  .right-wrap {
    width: 380px;

    .top {
      border-bottom: 1px solid $primary-color;
      height: 40px;
      line-height: 40px;
      padding: 0 16px;
      font-weight: bold;
    }
    .main {
      @include calc-height(40px);
      overflow-y: auto;
      padding: 16px;
      //color: $text-color-third;
      .detail-item {
        margin-bottom: 12px;
        label {
          font-weight: bold;
          //color: $text-color;
        }
      }
    }
  }
  .center-wrap {
    padding: 16px;
    margin: 0 16px;
    .title {
      padding: 12px 0;
      text-decoration: underline;
      color: $primary-color;
    }
    .title:hover {
      cursor: pointer;
    }
    .main {
      height: 100%;
      overflow: auto;
      background-color: rgba(75, 114, 239, 0.05);
      padding: 16px;
      border-radius: 8px;
      font-size: 16px;
      line-height: 28px;
    }
  }
}
</style>

<style lang="scss">
.data-preview {
  .el-pagination {
    background-color: transparent;
    > button,
    > ul,
    > ul > li {
      background-color: transparent !important;
    }
  }

  .jv-container {
    .jv-code.boxed {
      max-height: calc(100vh - 235px);
      height: calc(100vh - 235px);
    }
  }

  .el-checkbox:last-of-type {
    height: 20px;
  }
}
.data-preview-full-screen-page {
  .fullscreen-page__header {
    justify-content: left !important;
  }

  .data-preview-query {
    margin-left: 185px;

    .el-form-item {
      margin-bottom: 0 !important;
    }
  }

  .total-box {
    height: 32px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: $text-color-secondary;
    margin-left: 12px;
    > b,
    > span {
      display: inline-block;
    }
    & > b {
      color: $text-color;
      margin-right: 16px;
    }
    & > span {
      color: $primary-color;
      margin: 0 6px;
    }
  }
}
</style>
