<template>
  <page-wrapper route-name="conflict-preview::">
    <el-col class="conlict-preview">
      <preview-table ref="previewTableRef" :taskId="taskId" :withSort="false" :withExport="true"></preview-table>
    </el-col>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";

const { $router, $app, proxy } = useCtx();

const taskId = $router.currentRoute.value.query.taskId;
const datasetVersionId = $router.currentRoute.value.query.datasetVersionId;//查询正文时必须传这个

const loadList = () => {
  proxy.$refs["previewTableRef"].loadList("fromIndexPublishFail", taskId, { datasetVersionId: datasetVersionId });
};
//初始化
onMounted(() => {
  loadList();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.conflict-preview {
  height: 100%;
}
</style>
