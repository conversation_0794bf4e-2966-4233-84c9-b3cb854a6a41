<template>
  <div class="preview-table">
    <data-preview ref="dataPreviewRef" v-if="showDataPreview" @close="showDataPreview = false" :withSelect="withSelect" />
    <MyTable
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :withSort="withSort"
      :withSelect="withSelect"
    >
      <template #query>
        <div class="table-preview-query flexBetween">
          <my-query
            ref="myQueryRef"
            :queryItems="queryItems"
            :refreshBtn="{ show: true }"
            :settingBtn="{ show: true }"
            @search="events.search"
            @reset="events.reset"
          >
            <template #queryLen>
              <el-input
                v-model="queryItems.lenValue.modelValue"
                type="number"
                style="width: 250px"
                placeholder="请输入长度"
                @keydown.enter="events.querySlotSearch"
                clearable
              >
                <template #prepend>
                  <el-select v-model="queryItems.lenKey.modelValue" style="width: 100px" placeholder="操作" clearable>
                    <el-option label="小于" value="lessThan" />
                    <el-option label="小于等于" value="lessThanOrEqual" />
                    <el-option label="大于" value="greaterThan" />
                    <el-option label="大于等于" value="greaterThanOrEqual" />
                  </el-select>
                </template>
              </el-input>
            </template>
            <template #queryQScore>
              <el-input
                v-model="queryItems.qScoreValue.modelValue"
                type="number"
                style="width: 220px"
                placeholder="请输入得分"
                @keydown.enter="events.querySlotSearch"
                clearable
              >
                <template #prepend>
                  <el-select v-model="queryItems.qScoreKey.modelValue" style="width: 100px" placeholder="操作" clearable>
                    <el-option label="小于" value="lessThan" />
                    <el-option label="小于等于" value="lessThanOrEqual" />
                    <el-option label="大于" value="greaterThan" />
                    <el-option label="大于等于" value="greaterThanOrEqual" />
                  </el-select>
                </template>
              </el-input>
            </template>
            <template #queryLevel>
              <el-select v-model="queryItems.levelKey.modelValue" style="width: 150px" placeholder="请选择精品等级" @change="levelKeySelectChange" clearable>
                <el-option v-for="item in queryItems.levelKey.options" :label="`${item.name}(${item.code})`" :value="item.code" />
              </el-select>
              <el-select v-model="queryItems.levelValue.modelValue" style="width: 70px" placeholder="" @change="events.querySlotSearch" clearable>
                <el-option v-for="item in getMetaLabelValueList(queryItems.levelKey.modelValue)" :label="item.key" :value="item.value" />
              </el-select>
            </template>
          </my-query>
          <my-operation>
            <template v-if="withExport" #buttonGroup>
              <div></div>
              <my-button type="export" @click="events.exportExcel" style="margin-left: 5px; margin-bottom: 12px">导出</my-button>
            </template>
            <template v-if="withSelect" #buttonGroup>
              <div class="flex query-mode">
                <el-select v-model="queryMode" @change="events.queryModeChange" style="width: 130px">
                  <el-option label="基础条件查询" value="base" />
                  <el-option label="规则条件查询" value="rule" />
                </el-select>
              </div>
              <div class="flex button-group">
                <el-button type="primary" @click="events.auditOperate('accept')" v-if="waitAudit || failAudit">审核通过</el-button>
                <el-button type="warning" @click="events.auditOperate('reject')" v-if="waitAudit || successAudit">审核不通过</el-button>
                <el-button type="danger" @click="events.auditOperate('purge')" v-if="failAudit">彻底删除</el-button>
                <el-button type="success" @click="events.auditOperate('transfer')" v-if="successAudit">入库</el-button>
                <div class="total-box" v-if="selectTotal > 0">
                  {{ $t("title.selected") }}
                  <span>{{ selectTotal }}</span>
                  {{ $t("title.item") }}
                </div>
              </div>
            </template>
          </my-operation>
        </div>
      </template>
      <template #pagination>
        <!-- <template v-if="modelValue.fromType != 'fromSiteVersionConflict' && modelValue.fromType != 'fromRegion'"> -->
        <template v-if="modelValue.fromType != 'fromSiteVersionConflict'">
          <div style="font-size: 16px; font-weight: 700; margin-right: 15px">
            <span>数据总量 : {{ util.formatNumber(modelValue.dataItemCount) }}</span>
            <span style="margin-left: 20px">筛选后数据总量 : {{ util.formatNumber(modelValue.dataItemFilterCount) }}</span>
          </div>
        </template>
      </template>
      <template #title="scope">
        <div class="flex">
          <el-button link type="info" @click="events.originData(scope.row)">
            <el-icon>
              <Link />
            </el-icon>
          </el-button>
          <el-button link type="primary" @click="events.previewData(scope.row)" class="btn-title">{{ scope.row.title }}</el-button>
        </div>
      </template>
      <template #id="scope">
        <div class="flex">
          <el-button link type="primary" @click="events.previewData(scope.row)" class="btn-title">{{ scope.row._id }}</el-button>
          <el-icon class="icon-copy" @click="copyText(scope.row._id)">
            <CopyDocument />
          </el-icon>
        </div>
      </template>
    </MyTable>
  </div>
</template>

<script lang="ts" setup>
import { h, ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import { keys, assign, cloneDeep } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import { copyText, getText } from "@/utils/helpers";
import useCtx from "@/hooks/useCtx";
import MyTable from "./MyTable.vue";
import dataPreview from "./DataPreview.vue";
import * as siteApi from "@/api/site";
import * as previewApi from "@/api/preview";
import * as tdocAuditApi from "@/api/tdoc-audit";
import * as util from "@/utils/common";
import * as previewConfig from "./config";
import { ElMessageBox, ElSwitch } from "element-plus";

const { $app, proxy, $router } = useCtx();
const { api } = useStore();
const showDataPreview = ref<boolean>(false);

const myTableRef = ref(null);

const props = defineProps({
  //排序相关
  withSort: { type: Boolean, default: true },
  //多选相关
  withSelect: { type: Boolean, default: false },
  //导出相关
  withExport: { type: Boolean, default: false },
});
//列配置
const columns = ref(previewConfig.getColumns("default"));
//查询面板
const query = ref<any>(previewConfig.getQuery());
const queryItems = ref<any>(previewConfig.getQueryItems("default"));
const queryMode = ref("base"); //base和rule
//获取元Label值列表
const getMetaLabelValueList = (code: String) => {
  if (dataC.isEmpty(code) || dataC.isEmpty(queryItems.value.levelKey.options)) return [];
  return dataC.getItemByValue(queryItems.value.levelKey.options, code, "code").values;
};
//当key修改时value清空
const levelKeySelectChange = (value: String) => {
  queryItems.value.levelValue.modelValue = "";
  //执行clearable时查询table
  if (value == undefined) events.querySlotSearch();
};
//列表查询
const loadListData = async (data: any) => {
  modelValue.dataItemFilterCount = "计算中";
  if (dataC.isEmpty(modelValue.metaLabelList)) {
    const list = await api.getMetaLabelList();
    modelValue.metaLabelList = list;
  }
  return new Promise((resolve: any) => {
    //先把数据查询出来
    previewConfig.loadListData(modelValue.fromType, modelValue.fromId, modelValue.extraParams, data).then((result) => {
      resolve({
        content: result.content,
      });
      //再查数据总量，防止两个请求同时落到数据库上面导致查询压力很大
      getDataItemFilterCount(query.value);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    //记录初始的属性字段
    const propertyList = [];
    keys(x).forEach((item) => {
      propertyList.push(item);
    });
    x.propertyList = propertyList.join(",");
    //渲染字段
    x.url = x.url || util.displayUrl(x.protocol, x.domain, x.path);
    x.q_user = util.displayQualityDataPreview(x.q_user);
    x.levels = util.displayLevels(x.levels, modelValue.metaLabelList);
    x.post_ts = timeC.format(x.post_ts * 1000, "YYYY-MM-DD hh:mm:ss");
    x.crawl_ts = timeC.format(x.crawl_ts * 1000, "YYYY-MM-DD hh:mm:ss");
    x.index_ts = timeC.format(x.index_ts * 1000, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
const modelValue = reactive({
  fromType: "",
  fromId: "",
  extraParams: {},
  dataItemCount: "计算中",
  dataItemFilterCount: "计算中",
  siteInfo: { properties: "" },
  metaLabelList: [],
  metaSiteFieldList: [],
});
const waitAudit = computed(() => {
  return modelValue.extraParams.tab == 0;
});
const successAudit = computed(() => {
  return modelValue.extraParams.tab == 1;
});
const failAudit = computed(() => {
  return modelValue.extraParams.tab == 2;
});
const selectTotal = computed(() => {
  return myTableRef.value?.getSelectTotal() || 0;
});
const selectAll = computed(() => {
  return myTableRef.value?.getSelectAll() || false;
});
//事件列表
const events = reactive({
  querySlotSearch: () => {
    proxy.$refs["myQueryRef"].search();
  },
  validateNumber: (numberStr, numberLabel) => {
    if (dataC.isEmpty(numberStr)) return true;
    const maxNumberStr = "9223372036854775807";
    if (isNaN(Number(numberStr))) {
      $app.$message.warning(`${numberLabel}输入的值不合法`);
      return false;
    } else if (Number(numberStr) < 0 || numberStr.length > maxNumberStr.length || (numberStr.length == maxNumberStr.length && numberStr > maxNumberStr)) {
      $app.$message.warning(`${numberLabel}输入的值不在有效范围内`);
      return false;
    }
    return true;
  },
  validateUrl: (urlStr) => {
    if (dataC.isEmpty(urlStr)) return true;
    try {
      new URL(urlStr);
    } catch (error) {
      $app.$message.warning(`url不符合规范`);
      return false;
    }
    return true;
  },
  search: (obj: any) => {
    //校验查询项_id,docId
    if ("_id" in obj && !events.validateNumber(obj._id, "_id")) return;
    if ("docId" in obj && !events.validateNumber(obj.docId, "_id")) return;
    //校验查询项url
    if ("url" in obj && !events.validateUrl(obj.url)) return;
    //处理查询项
    query.value = assign({}, query.value, obj);
    //len, q_score, levels 特殊处理
    delete query.value.lenKey;
    delete query.value.lenValue;
    delete query.value.qScoreKey;
    delete query.value.qScoreValue;
    delete query.value.levelKey;
    delete query.value.levelValue;
    keys(query.value).forEach((key) => {
      if (key.includes("len.") || key.includes("q_score.")) {
        delete query.value[key];
      }
    });
    query.value.levels = "";
    if (!dataC.isEmpty(obj.lenKey) && !dataC.isEmpty(obj.lenValue)) {
      query.value[`len.${obj.lenKey}`] = obj.lenValue;
    }
    if (!dataC.isEmpty(obj.qScoreKey) && !dataC.isEmpty(obj.qScoreValue)) {
      query.value[`q_score.${obj.qScoreKey}`] = obj.qScoreValue;
    }
    if (!dataC.isEmpty(obj.levelKey) && !dataC.isEmpty(obj.levelValue)) {
      query.value.levels = `${obj.levelKey}:${obj.levelValue}`;
    }
    //记录数据来源的值
    if (!dataC.isEmpty(obj.from)) {
      queryItems.value.from.modelValue = obj.from;
    }
  },
  reset: (obj: any) => {
    const slotKey = ["lenKey", "lenValue", "qScoreKey", "qScoreValue", "levelKey", "levelValue"];
    slotKey.forEach((key) => {
      if (key in queryItems.value) {
        queryItems.value[key].modelValue = queryItems.value[key].defaultValue ?? queryItems.value[key].modelValue;
      }
    });
  },
  previewData: (record: any) => {
    showDataPreview.value = true;
    nextTick(() => {
      proxy.$refs["dataPreviewRef"].setMetaLabelList(modelValue.metaLabelList);
      proxy.$refs["dataPreviewRef"].setMetaSiteFieldList(modelValue.metaSiteFieldList);
      proxy.$refs["dataPreviewRef"].refreshList(
        modelValue.fromType,
        assign({ query: query.value }, modelValue.extraParams),
        proxy.$refs["myTableRef"].getTableData(),
        proxy.$refs["myTableRef"].getPage()
      );
      proxy.$refs["dataPreviewRef"].refreshContent(record._id, query.value.from);
    });
  },
  originData: (record: any) => {
    window.open(record.url, "_blank");
  },
  exportExcel: () => {
    if (modelValue.fromType == "fromIndexPublishFail") {
      previewConfig.exportListData(modelValue.fromType, modelValue.fromId, modelValue.extraParams, query.value).then((result) => {
        util.downloadFile(result, "失败数据.xlsx");
      });
      $app.$message.success(`已导出`);
    } else {
      ElMessageBox.prompt("请输入导出条数,可导出的范围为1至10000条", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputPattern: /^([1-9]\d{0,3}|10000)$/,
        inputErrorMessage: "请输入1至10000之间的整数",
        inputValue: "1000", // 设置默认值为 1000
      }).then(({ value }) => {
        previewConfig.exportListData(modelValue.fromType, modelValue.fromId, assign({ limit: value }, modelValue.extraParams), query.value).then((result) => {
          util.downloadFile(result, "doc数据.xlsx");
        });
        $app.$message.success(`已导出`);
      });
    }
  },
  auditOperate: (operate: String) => {
    const tableData = myTableRef.value.getTableData();
    const ids = tableData
      .filter((item) => {
        return item.selected == true;
      })
      .map((item) => {
        return item._id;
      });
    if (ids.length == 0) {
      $app.$message.warning("您尚未选择数据！");
      return;
    }
    const handleAuditOperate = (operate, additionalParams = {}) => {
      const obj = {
        tab: modelValue.extraParams.tab,
        selectedAll: selectAll.value,
        auditId: modelValue.extraParams.auditId,
        ids: ids,
        ...additionalParams,
      };
      const auditOperateDto = assign({}, query.value, obj);
      tdocAuditApi.auditOperate(operate, auditOperateDto).then((result) => {
        $app.$message.success("操作成功");
        $router.go(-1);
      });
    };
    if (operate === "transfer") {
      const buildTask = ref(false);
      ElMessageBox({
        title: "确认入库?",
        message: () =>
          h(ElSwitch, {
            activeText: "增量数据自动更新",
            modelValue: buildTask.value,
            "onUpdate:modelValue": (val: boolean) => {
              buildTask.value = val;
            },
          }),
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      }).then(() => {
        handleAuditOperate(operate, { buildTask: buildTask.value });
      });
    } else {
      $app.$confirm({ title: `确认操作？` }).then(() => {
        handleAuditOperate(operate);
      });
    }
  },
  queryModeChange: (newVal) => {
    queryItems.value.siteVersionId.hidden = queryMode.value == "base";
    keys(queryItems.value).forEach((key) => {
      if (key != "siteVersionId" && !key.endsWith("Key")) {
        queryItems.value[key].hidden = queryMode.value == "rule";
      }
    });
    proxy.$refs["myQueryRef"].reset();
  },
});
const loadList = async (fromType: string, fromId: string, extraParams: any) => {
  modelValue.fromType = fromType;
  modelValue.fromId = fromId;
  modelValue.extraParams = extraParams;
  //查询项的值
  query.value = previewConfig.getQuery();
  //查询项的显示
  const queryItemsConfig = previewConfig.getQueryItems(fromType, extraParams);
  if (!dataC.isEmpty(queryItemsConfig.from)) {
    //如果是索引库查询，实时显示数据来源筛选项，非实时隐藏数据来源筛选项
    queryItemsConfig.from.hidden = dataC.isEmpty(extraParams.realtime) || !extraParams.realtime;
    //切换存量和实时时，数据总量也要跟着发生改变
    if (!queryItemsConfig.from.hidden) {
      queryItemsConfig.from.events = {
        change: (val: any) => {
          extraParams.from = val;
          getDataItemCount();
        },
      };
    }
    //获取上一次数据来源from的modelValue,不读取默认值,读取上一次的值
    if (!dataC.isEmpty(queryItems.value.from)) {
      queryItemsConfig.from.modelValue = queryItems.value.from.modelValue;
    }
  }
  queryItems.value = queryItemsConfig;
  //需要对精品等级做筛选时
  if (!dataC.isEmpty(queryItems.value.levelKey)) {
    if (dataC.isEmpty(modelValue.metaLabelList)) {
      const list = await api.getMetaLabelList();
      modelValue.metaLabelList = list;
    }
    queryItems.value.levelKey.options = modelValue.metaLabelList;
  }
  //需要对站点版本规则做筛选时
  if (!dataC.isEmpty(queryItems.value.ruleId)) {
    const result = await siteApi.getRuleList(fromId, 1, 1000, `lastModifiedDate,desc`);
    queryItems.value.ruleId.options = result.content.map((item) => {
      return { label: item.name, value: item.id };
    });
  }
  //需要对失败原因做筛选时
  if (!dataC.isEmpty(queryItems.value.errorCode)) {
    const result = await previewApi.getErrCode();
    queryItems.value.errorCode.options = result.data;
  }
  //需要对站点版本进行筛选时,还需要根据queryMode控制查询项的显示
  if (!dataC.isEmpty(queryItems.value.siteVersionId)) {
    const result = await tdocAuditApi.getCondSiteVersionList(extraParams.datasetVersionId, extraParams.idxDbInstId, extraParams.site);
    queryItems.value.siteVersionId.options = result.data;
    ///根据queryMode控制查询项的显示
    events.queryModeChange();
  }
  //列的显示
  columns.value = previewConfig.getColumns(fromType, extraParams);
  //获取数据总量
  getDataItemCount();
};
//获取数据总量
const getDataItemCount = () => {
  modelValue.dataItemCount = "计算中";
  previewConfig.getDataItemCount(modelValue.fromType, modelValue.fromId, modelValue.extraParams).then((result) => {
    modelValue.dataItemCount = result.data;
  });
};
//获取筛选后数据总量
const getDataItemFilterCount = (data: any) => {
  previewConfig.getDataItemFilterCount(modelValue.fromType, modelValue.fromId, modelValue.extraParams, data).then((result) => {
    modelValue.dataItemFilterCount = result.data;
    modelValue.extraParams.total = result.data;
  });
};
//初始化属性元数据信息
const getMetaSiteFieldList = () => {
  api.getMetaSiteFieldList().then((result) => {
    modelValue.metaSiteFieldList.push(...result);
  });
};
//初始化
onMounted(() => {
  getMetaSiteFieldList();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scope>
.preview-table {
  height: 100%;

  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .icon-copy {
    color: $primary-color;
    margin-left: 5px;
    cursor: pointer;
  }

  .el-checkbox:last-of-type {
    height: 20px;
  }

  .table-preview-query {
    align-items: stretch !important;
    .t-query {
      height: auto;
      .el-form {
        height: 100%;
      }
    }
    .custom-operation {
      height: auto;
      .btn-box {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .query-mode {
          width: 100%;
          justify-content: flex-end;
          margin-bottom: 12px;
        }
        .button-group {
          margin-bottom: 12px;
        }
      }
    }
    .total-box {
      font-size: 16px;
      color: $text-color-secondary;
      margin-left: 6px;
      white-space: nowrap;
      > b,
      > span {
        display: inline-block;
      }
      & > b {
        color: $text-color;
        margin-right: 16px;
      }
      & > span {
        color: $primary-color;
        margin: 0 6px;
      }
    }
  }
}
</style>
