import { keys, cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import * as previewApi from "@/api/preview";
import * as tdocAuditApi from "@/api/tdoc-audit";

//数据预览类型
type PreviewType =
  | "fromSite"
  | "fromRule"
  | "fromSiteVersion"
  | "fromSiteVersionConflict"
  | "fromDatasetVersion"
  | "fromDatasetVersionSite"
  | "fromRegion"
  | "fromIndexPublishFail"
  | "fromTdocAudit"
  | "fromOfflineDatasetVersion"
  | "default";

//查询项配置
const queryItems: any = {
  _id: {
    type: "input",
    label: "_id",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入_id",
      maxlength: 19,
    },
  },
  docId: {
    type: "input",
    label: "_id",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入_id",
      maxlength: 19,
    },
  },
  site: {
    type: "input",
    label: "site",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入site",
    },
  },
  url: {
    type: "input",
    label: "url",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入url",
    },
  },
  title: {
    type: "input",
    label: "标题",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入标题",
    },
  },
  s: {
    type: "select",
    label: "去重标记",
    labelWidth: "80px",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    options: [
      { value: 0, label: "无重复标记" },
      { value: 1, label: "有重复标记" },
    ],
    attrs: {
      placeholder: "请选择去重标记",
    },
  },
  levelKey: { modelValue: "", defaultValue: "", options: "", hidden: true },
  levelValue: { type: "slot", slotName: "queryLevel", label: "精品等级", labelWidth: "80px", modelValue: "", defaultValue: "" },
  errorCode: {
    label: "失败原因",
    labelWidth: "80px",
    type: "select",
    modelValue: [],
    defaultValue: [],
    width: "220px",
    options: [],
    attrs: {
      placeholder: "请选择失败原因",
      multiple: true,
    },
  },
  qScoreKey: { modelValue: "", defaultValue: "", hidden: true },
  qScoreValue: { type: "slot", slotName: "queryQScore", label: "质量得分", labelWidth: "80px", modelValue: "", defaultValue: "" },
  q_user: {
    type: "select",
    label: "质量等级",
    labelWidth: "80px",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    options: [
      { value: 4, label: "S" },
      { value: 3, label: "A" },
      { value: 2, label: "B" },
      { value: 1, label: "C" },
    ],
    attrs: {
      placeholder: "请选择质量等级",
      clearable: true,
    },
  },
  lenKey: { modelValue: "", defaultValue: "", hidden: true },
  lenValue: { type: "slot", slotName: "queryLen", label: "len", labelWidth: "50px", modelValue: "", defaultValue: "" },
  gid: {
    type: "input",
    label: "gid",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入gid",
    },
  },
  ruleId: {
    type: "select",
    label: "规则名称",
    labelWidth: "80px",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    options: [],
    attrs: {
      placeholder: "请选择规则",
    },
  },
  domain: {
    type: "input",
    label: "domain",
    labelWidth: "80px",
    modelValue: "",
    defaultValue: "",
    width: "220px",
    attrs: {
      placeholder: "请输入domain",
    },
  },
  path: {
    type: "input",
    label: "path",
    labelWidth: "50px",
    modelValue: "",
    defaultValue: "",
    width: "250px",
    attrs: {
      placeholder: "请输入path",
    },
  },
  from: {
    modelValue: "full",
    defaultValue: "full",
    type: "radioButton",
    label: "数据来源",
    labelWidth: "80px",
    options: [
      { value: "full", label: "存量" },
      { value: "hot", label: "实时" },
    ],
  },
  siteVersionId: {
    label: "站点版本",
    labelWidth: "80px",
    type: "select",
    modelValue: [],
    defaultValue: [],
    width: "220px",
    options: [],
    attrs: {
      placeholder: "请选择站点版本",
    },
  },
};
type QueryItemsConfig = {
  [key: string]: Array<string>;
};
const queryItemsConfig: QueryItemsConfig = {
  fromSite: ["_id", "url", "title", "qScoreKey", "qScoreValue", "lenKey", "lenValue", "domain", "path", "gid"],
  fromRule: ["_id", "url", "title", "s", "levelKey", "levelValue", "qScoreKey", "qScoreValue", "q_user", "lenKey", "lenValue", "domain", "path", "gid"],
  fromSiteVersion: ["_id", "url", "title", "s", "levelKey", "levelValue", "qScoreKey", "qScoreValue", "q_user", "lenKey", "lenValue", "domain", "path", "gid"],
  fromSiteVersionConflict: ["ruleId"],
  fromIndexPublishFail: ["docId", "errorCode"],
  fromDatasetVersion: [
    "_id",
    "site",
    "url",
    "title",
    "s",
    "levelKey",
    "levelValue",
    "qScoreKey",
    "qScoreValue",
    "q_user",
    "lenKey",
    "lenValue",
    "domain",
    "path",
    "gid",
  ],
  fromDatasetVersionSite: [
    "_id",
    "url",
    "title",
    "s",
    "levelKey",
    "levelValue",
    "qScoreKey",
    "qScoreValue",
    "q_user",
    "lenKey",
    "lenValue",
    "domain",
    "path",
    "gid",
  ],
  fromRegion: ["_id", "url", "title", "levelKey", "levelValue", "qScoreKey", "qScoreValue", "q_user", "lenKey", "lenValue", "domain", "path", "gid", "from"],
  fromTdocAudit: ["_id", "url", "title", "qScoreKey", "qScoreValue", "lenKey", "lenValue", "domain", "path", "siteVersionId"],
  fromOfflineDatasetVersion: ["_id", "title"],
  default: keys(queryItems),
};

//列配置
const columns: any = {
  _id: { prop: "_id", label: "_id", width: 220, slotName: "id" },
  site: { prop: "site", label: "site", width: 200, withCopy: true, showOverflowTooltip: true },
  title: {
    prop: "title",
    label: "标题",
    slotName: "title",
    minWidth: 400,
    showOverflowTooltip: false,
    sortable: false,
  },
  q_score: { prop: "q_score", label: "质量得分", width: 230 },
  len: { prop: "len", label: "len", width: 120 },
  q_user: { prop: "q_user", label: "质量等级", width: 120 },
  levels: { prop: "levels", label: "精品等级", width: 120, sortable: false },
  q_tc: { prop: "q_tc", label: "TC相似度", width: 120 },
  domain: { prop: "domain", label: "domain", width: 180, withCopy: true },
  path: { prop: "path", label: "path", width: 180, withCopy: true },
  gid: { prop: "gid", label: "gid", width: 220, withCopy: true },
  s: {
    prop: "s",
    label: "去重标记",
    width: 120,
    custom: "status",
    customRender: {
      options: {
        0: { type: "primary", name: "无重复标记" },
        1: { type: "warning", name: "有重复标记" },
      },
    },
  },

  conflictRules: { prop: "conflictRules", label: "冲突规则", minWidth: 500 },
  errorCode: {
    prop: "errorCode",
    label: "失败原因",
    width: 120,
    custom: "status",
    customRender: {
      options: queryItems.errorCode.options,
    },
  },
  post_ts: { prop: "post_ts", label: "发布时间", width: 180 },
  index_ts: { prop: "index_ts", label: "索引时间", width: 180 },
};
type ColumnsConfig = {
  [key: string]: Array<string>;
};
const columnsConfig: ColumnsConfig = {
  fromSite: ["_id", "title", "q_score", "len", "domain", "path", "gid", "post_ts"],
  fromRule: ["_id", "title", "q_score", "q_user", "len", "levels", "post_ts", "domain", "path", "gid", "s"],
  fromSiteVersion: ["_id", "title", "q_score", "q_user", "len", "levels", "post_ts", "domain", "path", "gid", "s"],
  fromSiteVersionConflict: ["_id", "title", "conflictRules"],
  fromIndexPublishFail: ["_id", "site", "title", "q_score", "q_user", "len", "levels", "post_ts", "domain", "path", "gid"],
  fromDatasetVersion: ["_id", "site", "title", "q_score", "q_user", "len", "levels", "post_ts", "domain", "path", "gid", "s"],
  fromDatasetVersionSite: ["_id", "site", "title", "q_score", "q_user", "len", "levels", "post_ts", "domain", "path", "gid", "s"],
  fromRegion: ["_id", "site", "title", "q_score", "q_user", "len", "levels", "domain", "path", "gid", "post_ts", "index_ts"],
  fromTdocAudit: ["_id", "title", "q_score", "len", "domain", "path", "gid", "post_ts"],
  fromOfflineDatasetVersion: ["_id", "title"],
  default: ["_id", "domain", "title", "post_ts"],
};

//数据预览策略接口
interface PreviewStrategy {
  getQueryItems(extraParams: any): any;
  getColumns(extraParams: any): Array<any>;
  loadListData(fromId: string, extraParams: any, data: any): Promise<any>;
  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any>;
  getDataItemCount(fromId: string, extraParams: any): Promise<any>;
  exportListData(fromId: string, extraParams: any, data: any): Promise<any>;
}

function readQueryItems(type: string) {
  const cloneQueryItems = cloneDeep(queryItems);
  const arr: Array<string> = queryItemsConfig[type] || [];
  const obj: any = {};
  arr.forEach((item) => {
    obj[item] = cloneQueryItems[item];
  });
  return obj;
}

function readColumns(type: string) {
  const arr: Array<string> = columnsConfig[type] || [];
  const list: Array<any> = [];
  arr.forEach((item) => {
    list.push(columns[item]);
  });
  return list;
}

class DefaultStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("default");
  }

  getColumns() {
    return readColumns("default");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return Promise.resolve();
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }
}

class FromSiteStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromSite");
  }

  getColumns() {
    return readColumns("fromSite");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromSite(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromSite(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromSite(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.exportPreviewListFromSite(fromId, data, extraParams.limit);
  }
}

class FromRuleStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromRule");
  }

  getColumns() {
    return readColumns("fromRule");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromRule(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromRule(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromRule(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.exportPreviewListFromRule(fromId, data, extraParams.limit);
  }
}

class FromSiteVersionStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromSiteVersion");
  }

  getColumns() {
    return readColumns("fromSiteVersion");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromSiteVersion(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromSiteVersion(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromSiteVersion(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.exportPreviewListFromSiteVersion(fromId, data, extraParams.limit);
  }
}

class FromSiteVersionConflictStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromSiteVersionConflict");
  }

  getColumns() {
    return readColumns("fromSiteVersionConflict");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromSiteVersionConflict(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return Promise.resolve();
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }
}

class FromDatasetVersionStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromDatasetVersion");
  }

  getColumns() {
    return readColumns("fromDatasetVersion");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromDatasetVersion(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromDatasetVersion(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromDatasetVersion(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.exportPreviewListFromDatasetVersion(fromId, data, extraParams.limit);
  }
}

class FromDatasetVersionSiteStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromDatasetVersionSite");
  }

  getColumns() {
    return readColumns("fromDatasetVersionSite");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromDatasetVersionSite(fromId, data.page, data.size, data.sort, data, extraParams.site);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromDatasetVersionSite(fromId, data, extraParams.site);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromDatasetVersionSite(fromId, {}, extraParams.site);
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.exportPreviewListFromDatasetVersionSite(fromId, data, extraParams.site, extraParams.limit);
  }
}

class FromRegionStrategy implements PreviewStrategy {
  getQueryItems(extraParams: any) {
    if (Number(extraParams.type) == 1) {
      //如果来源离线数据集，和离线数据集保持一致
      return readQueryItems("fromOfflineDatasetVersion");
    } else {
      return readQueryItems("fromRegion");
    }
  }

  getColumns() {
    return readColumns("fromRegion");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromRegion(fromId, data.page, data.size, data.sort, data, extraParams);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    let params = { ...data, idxDbInstId: fromId, ...extraParams };
    if (extraParams.site) {
      params.site = extraParams.site;
    }
    return previewApi.getCountFromRegion(params);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    let params = { ...extraParams, idxDbInstId: fromId };
    if (extraParams.site) {
      params.site = extraParams.site;
    }
    return previewApi.getCountFromRegion(params);
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }
}

class FromIndexPublishFailStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromIndexPublishFail");
  }

  getColumns() {
    return readColumns("fromIndexPublishFail");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromIndexPublishFail(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromFail(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromFail(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.exportPreviewListFromIndexPublishFail(fromId, data);
  }
}

class FromTdocAuditStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromTdocAudit");
  }

  getColumns() {
    return readColumns("fromTdocAudit");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return tdocAuditApi.getPreviewListPageFromTdocAudit(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return tdocAuditApi.getCountFromTdocAudit(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return tdocAuditApi.getCountFromTdocAudit(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }
}

class FromOfflineDatasetVersionStrategy implements PreviewStrategy {
  getQueryItems() {
    return readQueryItems("fromOfflineDatasetVersion");
  }

  getColumns() {
    return readColumns("fromOfflineDatasetVersion");
  }

  loadListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getPreviewListPageFromDatasetVersion(fromId, data.page, data.size, data.sort, data);
  }

  getDataItemFilterCount(fromId: string, extraParams: any, data: any): Promise<any> {
    return previewApi.getCountFromDatasetVersion(fromId, data);
  }

  getDataItemCount(fromId: string, extraParams: any): Promise<any> {
    return previewApi.getCountFromDatasetVersion(fromId, {});
  }

  exportListData(fromId: string, extraParams: any, data: any): Promise<any> {
    return Promise.resolve();
  }
}

class PreviewStrategyFactory {
  static getStrategy(fromType: PreviewType): PreviewStrategy {
    switch (fromType) {
      case "fromSite":
        return new FromSiteStrategy();
      case "fromRule":
        return new FromRuleStrategy();
      case "fromSiteVersion":
        return new FromSiteVersionStrategy();
      case "fromSiteVersionConflict":
        return new FromSiteVersionConflictStrategy();
      case "fromDatasetVersion":
        return new FromDatasetVersionStrategy();
      case "fromDatasetVersionSite":
        return new FromDatasetVersionSiteStrategy();
      case "fromRegion":
        return new FromRegionStrategy();
      case "fromIndexPublishFail":
        return new FromIndexPublishFailStrategy();
      case "fromTdocAudit":
        return new FromTdocAuditStrategy();
      case "fromOfflineDatasetVersion":
        return new FromOfflineDatasetVersionStrategy();
      default:
        return new DefaultStrategy();
    }
  }
}

//对外暴露的方法
//查询配置
export function getQuery() {
  return { _id: "", site: "", url: "", title: "", s: "", len: "", levels: "", q_score: "", q_user: "", gid: "" };
}

export function getQueryItems(fromType: PreviewType, extraParams: any) {
  const strategy = PreviewStrategyFactory.getStrategy(fromType);
  return strategy.getQueryItems(extraParams);
}

function getQueryItemsDynamic(fields: Array<any>) {
  const res: any = {};
  fields
    .filter((item) => {
      return item.filter == 1;
    })
    .forEach((item) => {
      res[item.fieldName] = {
        type: "input",
        label: item.name,
        labelWidth: "80px",
        modelValue: "",
        width: "220px",
        attrs: {
          placeholder: `请输入${item.name}`,
        },
      };
    });
  return res;
}

export function getColumns(fromType: PreviewType, extraParams: any) {
  const fields = extraParams?.fields || [];
  if (!dataC.isEmpty(fields)) {
    return getColumnsDynamic(fields);
  } else {
    const strategy = PreviewStrategyFactory.getStrategy(fromType);
    return strategy.getColumns(extraParams);
  }
}

function getColumnsDynamic(fields: Array<any>) {
  const list = fields.map((item) => {
    let column: any = {
      prop: item.fieldName,
      label: item.name,
      showOverflowTooltip: true,
    };
    if (item.fieldName == "_id") {
      column = cloneDeep(columns["_id"]);
    } else if (item.fieldName == "title") {
      column.minWidth = 200;
    } else {
      column.width = 200;
    }
    return column;
  });
  if (list.length > 0 && list.every((item) => item.minWidth == undefined)) {
    list[list.length - 1].minWidth = list[list.length - 1].width;
    delete list[list.length - 1].width;
  }
  return list;
}

export function loadListData(fromType: PreviewType, fromId: string, extraParams: any, queryData: any): Promise<any> {
  const strategy = PreviewStrategyFactory.getStrategy(fromType);
  return strategy.loadListData(fromId, getDistinctExtraParams(extraParams), getDistinctQueryData(queryData));
}

export function getDataItemFilterCount(fromType: PreviewType, fromId: string, extraParams: any, queryData: any): Promise<any> {
  const strategy = PreviewStrategyFactory.getStrategy(fromType);
  return strategy.getDataItemFilterCount(fromId, getDistinctExtraParams(extraParams), getDistinctQueryData(queryData));
}

export function getDataItemCount(fromType: PreviewType, fromId: string, extraParams: any): Promise<any> {
  const strategy = PreviewStrategyFactory.getStrategy(fromType);
  return strategy.getDataItemCount(fromId, getDistinctExtraParams(extraParams));
}

export function exportListData(fromType: PreviewType, fromId: string, extraParams: any, queryData: any): Promise<any> {
  const strategy = PreviewStrategyFactory.getStrategy(fromType);
  return strategy.exportListData(fromId, getDistinctExtraParams(extraParams), getDistinctQueryData(queryData));
}

function getDistinctExtraParams(extraParams: any) {
  const data = cloneDeep(extraParams);
  //fields是用来做动态列的，要删除
  delete data.fields;
  return data;
}

function getDistinctQueryData(queryData: any) {
  const data = cloneDeep(queryData);
  //后端接收"_id"有问题，所以采用"id"传输
  (data.id = data._id) && delete data._id;
  return data;
}
