<template>
  <div class="task-history-table">
    <table-page
      ref="myTableRef"
      :name="'task-history'+ dataType"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart" v-if="queryDisplay">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
        </div>
      </template>
      <template #process="scope">
        <status-dot v-if="dataC.isEmpty(scope.row.taskDetailList)" type="info" name="待构建" />
        <task-process
          v-if="!dataC.isEmpty(scope.row.taskDetailList)"
          :id="scope.row.id"
          :taskId="scope.row.taskId"
          :taskType="scope.row.taskType"
          :taskStatus="scope.row.taskStatus"
          :taskList="scope.row.taskDetailList"
          :disabled="true"
        ></task-process>
      </template>
      <template #beginTime="scope">
        {{ getText(scope.row.beginTimeRender) }}
        <el-icon v-show="!dataC.isEmpty(scope.row.beginTime)" class="icon-copy" @click="copyText(scope.row.beginTime)">
          <CopyDocument />
        </el-icon>
      </template>
      <template #endTime="scope">
        {{ getText(scope.row.endTimeRender) }}
        <el-icon v-show="!dataC.isEmpty(scope.row.endTime)" class="icon-copy" @click="copyText(scope.row.endTime)">
          <CopyDocument />
        </el-icon>
      </template>
      <template #queryBeginTime="scope">
        {{ getText(scope.row.queryBeginTimeRender) }}
        <el-icon v-show="!dataC.isEmpty(scope.row.queryBeginTime)" class="icon-copy" @click="copyText(scope.row.queryBeginTime)">
          <CopyDocument />
        </el-icon>
      </template>
      <template #queryEndTime="scope">
        {{ getText(scope.row.queryEndTimeRender) }}
        <el-icon v-show="!dataC.isEmpty(scope.row.queryEndTime)" class="icon-copy" @click="copyText(scope.row.queryEndTime)">
          <CopyDocument />
        </el-icon>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import { keys, assign } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import { dataC, timeC } from "turing-plugin";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import taskProcess from "@/views/common/task/TaskProcess.vue";
import * as taskApi from "@/api/task";
const { $app, proxy } = useCtx();
const { api } = useStore();
const props = defineProps({
  dataType: { type: Number, default: null },
  queryDisplay: { type: Boolean, default: true },
});
const modelValue = reactive({
  taskTypeList: [],
  dataTaskTypeList: [],
});
// 任务类型
const taskTypeComputed = computed(() => {
  const res = {};
  modelValue.taskTypeList.forEach((item) => {
    res[item.value] = {
      type: "info",
      name: item.label,
    };
  });
  return res;
});
// 数据任务类型
const dataTaskTypeComputed = computed(() => {
  return modelValue.dataTaskTypeList;
});
//列配置
const columns = ref([
  { prop: "taskId", label: "任务ID", width: 220, withCopy: true, sortable: false },
  {
    prop: "taskType",
    label: "任务类型",
    width: 150,
    custom: "status",
    customRender: {
      options: taskTypeComputed,
    },
    sortable: false,
  },
  {
    prop: "isAuto",
    label: "触发方式",
    width: 120,
    custom: "status",
    customRender: {
      options: {
        1: { type: "warning", name: "自动触发" },
        0: { type: "primary", name: "人工触发" },
      },
    },
    sortable: false,
  },
  {
    prop: "isIncremental",
    label: "是否增量",
    width: 120,
    custom: "status",
    customRender: {
      options: {
        1: { type: "warning", name: "是" },
        0: { type: "primary", name: "否" },
      },
    },
    sortable: false,
  },
  {
    prop: "process",
    label: "执行结果",
    slotName: "process",
    minWidth: 150,
    showOverflowTooltip: false,
    sortable: false,
  },
  { prop: "beginTime", label: "开始时间", width: 190, slotName: "beginTime", sortable: false },
  { prop: "endTime", label: "结束时间", width: 190, slotName: "endTime", sortable: false },
  { prop: "queryBeginTime", label: "查询起始时间", width: 190, slotName: "queryBeginTime", sortable: false },
  { prop: "queryEndTime", label: "查询终止时间", width: 190, slotName: "queryEndTime", sortable: false },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
]);
//查询面板
const query = ref<any>({
  refId: props.refId,
  dataType: props.dataType,
});
const queryItems = ref<any>({
  refId: {
    modelValue: props.refId,
    type: "input",
    label: "关联ID",
    width: "240px",
    attrs: {
      placeholder: "请输入关联ID",
    },
  },
  // dataType: {
  //   modelValue: props.dataType,
  //   type: "select",
  //   label: "任务类型",
  //   width: "150px",
  //   options: dataTaskTypeComputed,
  //   attrs: {
  //     placeholder: "请选择任务类型",
  //   },
  // },
});
//列表查询
const loadListData = async (data: any) => {
  return new Promise((resolve: any) => {
    taskApi.getTaskHistoryListPage(data.refId, data.dataType, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.taskType = x.type;
    x.taskStatus = x.status;
    x.isAuto = x.isAuto ? 1 : 0;
    x.isIncremental = x.isIncremental ? 1 : 0;
    x.beginTimeRender = timeC.format(x.beginTime, "YYYY-MM-DD hh:mm:ss");
    x.endTimeRender = timeC.format(x.endTime, "YYYY-MM-DD hh:mm:ss");
    x.queryBeginTimeRender = timeC.format(x.queryBeginTime, "YYYY-MM-DD hh:mm:ss");
    x.queryEndTimeRender = timeC.format(x.queryEndTime, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
});
const loadList = (refId: string) => {
  query.value.refId = refId; //更新query会自动触发列表查询
  queryItems.value.refId.modelValue = refId;
  proxy.$refs.myTableRef.loadData();
};
onMounted(() => {
  taskApi.getTaskTypeList().then((result) => {
    modelValue.taskTypeList = result.data;
  });
  taskApi.getDataTaskTypeList().then((result) => {
    modelValue.dataTaskTypeList = result.data;
  });
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.task-history-table {
  height: 100%;

  .icon-copy {
    color: $primary-color;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
