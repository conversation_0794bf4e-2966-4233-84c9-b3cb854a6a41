<template>
  <span v-if="dataC.isEmpty(props.taskList)">-</span>
  <el-popover v-if="!dataC.isEmpty(props.taskList)" placement="right" :width="450">
    <template #reference>
      <status-dot v-if="taskStatus != 2" :type="getTaskDotType()" :name="getTaskName()"></status-dot>
      <div v-if="taskStatus == 2" class="flex">
        <span class="task-process-span">
          {{ !dataC.isEmpty(activeTask) ? activeTask.name : "" }}
        </span>
        <el-progress :text-inside="true" :stroke-width="16" :percentage="getPercent(activeTask)" :status="getStatus(activeTask)" style="flex: 1"> </el-progress>
        <el-button link type="primary" @click="cancel" class="task-process-btn" :disabled="disabled"> 取消 </el-button>
      </div>
    </template>
    <el-timeline>
      <el-timeline-item v-for="(item, index) in taskList" :key="index" type="primary" hide-timestamp>
        <el-row :gutter="10">
          <el-col :span="24">
            <span v-if="item.status == 0">{{ `${item.name}(初始化)` }}</span>
            <span v-else-if="item.status == 1">{{ `${item.name}(执行中)` }}</span>
            <span v-else-if="item.status == 2">{{ `${item.name}(已结束)` }}</span>
            <span v-else-if="item.status == 3">{{ `${item.name}(已取消)` }}</span>
            <span v-else>{{ `${item.name}(未知状态)` }}</span>
            <span v-show="!dataC.isEmpty(item.errorCode)" style="margin-left: 10px; color: red">
              <el-tooltip :content="getMessage(item)">
                <el-icon>
                  <Warning />
                </el-icon>
              </el-tooltip>
            </span>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <span> 处理速度: {{ getSpeed(item) }} </span>
          </el-col>
          <el-col :span="12">
            <span> 预估剩余时间: {{ getEstimatedTime(item) }} </span>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <span>{{ timeC.format(item.beginTime, "YYYY-MM-DD hh:mm:ss") }}</span>
          </el-col>
          <el-col :span="12">
            <span>{{ timeC.format(item.endTime, "YYYY-MM-DD hh:mm:ss") }}</span>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <span> 处理数量: {{ item.done }} </span>
          </el-col>
          <el-col :span="12">
            <span> 数据总量: {{ item.total }} </span>
          </el-col>
        </el-row>
        <el-row v-if="(!dataC.isEmpty(item.failed) && item.failed > 0) || (!dataC.isEmpty(item.conflict) && item.conflict > 0)" :gutter="10">
          <el-col :span="12">
            <span> 失败数量: {{ item.failed }} </span>
            <my-button
              v-if="!dataC.isEmpty(item.failed) && item.failed > 0 && allowRetry && taskStatus == 3 && activeIndex == taskList.length - 1"
              link
              type="primary"
              @click="refreshLosetData(taskType)"
              :disabled="disabled"
              >重试
            </my-button>
            <my-button v-if="!dataC.isEmpty(item.failed) && item.failed > 0 && allowRetry" link type="primary" @click="failData(taskType)">详情</my-button>
          </el-col>
          <el-col :span="12">
            <span>
              冲突数量: {{ item.conflict }}
              <my-button v-if="!dataC.isEmpty(item.conflict) && item.conflict > 0" link type="primary" @click="conflictData()">详情</my-button>
            </span>
          </el-col>
        </el-row>
        <el-progress :text-inside="true" :stroke-width="16" :percentage="getPercent(item)" :status="getStatus(item)" />
      </el-timeline-item>
    </el-timeline>
  </el-popover>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as taskApi from "@/api/task";
import * as idxDbApi from "@/api/idx-db";
import { syncIdxDbOnlinePlan, replayIdxDbOnlinePlan } from "@/api/online.ts";
const props = defineProps(["id", "taskId", "taskType", "taskStatus", "taskList", "siteId", "datasetVersionId", "disabled"]);

const { $router, $app, proxy } = useCtx();

//允许重试的类型
const allowRetry = computed(() => {
  //3索引库发布；8上线计划同步；9上线计划回放
  return props.taskType == 3 || props.taskType == 8 || props.taskType == 9;
});

//当前子任务的序号
const activeIndex = computed(() => {
  //为空返回-1
  if (dataC.isEmpty(props.taskList)) {
    return -1;
  }
  //返回第一个不是已结束的
  for (let i = 0; i < props.taskList.length; i++) {
    if (props.taskList[i].status != 2) {
      return i;
    }
  }
  //返回最后一个
  return props.taskList.length - 1;
});
//当前子任务
const activeTask = computed(() => {
  if (activeIndex.value == -1) {
    return undefined;
  }
  return props.taskList[activeIndex.value];
});
//任务状态枚举
const dotEnums = reactive({
  1: { type: "info", name: "任务不存在" },
  2: { type: "primary", name: "执行中" },
  3: { type: "success", name: "成功" },
  4: { type: "danger", name: "失败" },
  5: { type: "warning", name: "已取消" },
});
//获取任务dot类型
const getTaskDotType = () => {
  return dotEnums[props.taskStatus]?.type;
}
//任务类型枚举
const taskTypeEnums = reactive({
  1: {name: "站点版本去重"},
  2: {name: "站点版本构建"},
  3: {name: "索引库构建"},
  4: {name: "数据集版本构建"},//离线数据集
  6: {name: "数据集版本构建"},//爬虫数据集
  7: {name: "数据集版本去重"},
  8: {name: "索引库同步"},
  9: {name: "索引库回放"}
})
//渲染任务name
const getTaskName = () => {
  return `${taskTypeEnums[props.taskType]?.name}${dotEnums[props.taskStatus]?.name}`;
}
//获取处理速率
const getSpeed = (item: any) => {
  const speed = !dataC.isEmpty(item.rate) && item.rate > 0 ? item.rate * 60 : 0;
  if (speed < 10000) {
    return `${speed.toFixed(0)}/分钟`;
  } else {
    return `${(speed / 10000).toFixed(0)}万/分钟`;
  }
};
//获取剩余时间
const getEstimatedTime = (item: any) => {
  const time = !dataC.isEmpty(item.estimatedCompletionTime) && item.estimatedCompletionTime > 0 ? item.estimatedCompletionTime : 0;
  if (time < 60) {
    return `${time.toFixed(0)}秒`;
  } else {
    return `${(time / 60).toFixed(0)}分钟`;
  }
};
const divideAndKeepTwoDecimals = (numerator: number, denominator: number) => {
  const result = numerator / denominator;
  return Math.floor(result * 10000) / 10000;
};
//获取百分比
const getPercent = (item: any) => {
  if (dataC.isEmpty(item)) {
    //item都为null返回0
    return 0;
  } else if (item.status == 2 && ((item.done == 0 && item.total == 0) || item.done >= item.total)) {
    //已结束状态下, 已处理和总量都为0 或者已处理大于等于总量
    return 100;
  } else {
    //total刚开始会没有值，直接给一亿
    const done = Number(!dataC.isEmpty(item.done) ? item.done : 0);
    const total = Number(dataC.isEmpty(item.total) || item.total == 0 ? 100_000_000 : item.total);
    const res = divideAndKeepTwoDecimals(done, total);
    return (res * 100).toFixed(2);
  }
  return 0;
};
//获取状态
const getStatus = (item: any) => {
  if (dataC.isEmpty(item) || !dataC.isEmpty(item.errorCode)) {
    //有异常码,返回错误状态
    return "exception";
  } else if ((!dataC.isEmpty(item.failed) && item.failed > 0) || (!dataC.isEmpty(item.conflict) && item.conflict > 0)) {
    //失败数量或者冲突数量数量大于0，返回警告状态
    return "warning";
  } else if (!dataC.isEmpty(item.done) && item.done >= item.total && item.done >= 0) {
    //已处理大于等于总量，返回成功状态
    return "success";
  } else {
    //默认返回初始状态
    return "primary";
  }
};
//获取异常信息
const getMessage = (item: any) => {
  return `【${item.errorCode}】: ${item.message}`;
};
//任务取消
const cancel = () => {
  $app.$confirm({ title: `确定取消任务 ?` }).then(() => {
    taskApi.cancelTask(props.taskId).then((result) => {
      emit("cancel-task");
      $app.$message.success("已取消任务");
    });
  });
};
//冲突数据
const conflictData = () => {
  const routeInfo = {
    name: "conflict-preview",
    query: {
      id: props.id,
      siteId: props.siteId,
    },
  };
  const resolvedRoute = $router.resolve(routeInfo);
  window.open(resolvedRoute.href, "_blank");
};
//失败数据
const failData = (taskType: any) => {
  const routeInfo = {
    name: taskType == 3 ? "fail-preview" : "online-fail-preview",
    query: {
      taskId: props.taskId,
      datasetVersionId: props.datasetVersionId,
    },
  };
  const resolvedRoute = $router.resolve(routeInfo);
  console.log(resolvedRoute);

  window.open(resolvedRoute.href, "_blank");
};
const refreshLosetData = (taskType: any) => {
  let tips = "";
  let request: ((id: string, publish: any) => Promise<any>) | ((arg0: any, arg1: {}) => Promise<any>);
  let params = {};
  if (activeTask.value.failed / activeTask.value.total > 0.3) {
    tips = tips + "失败数量超过30%,";
  }
  if (activeTask.value.failed > 5000_000) {
    tips = tips + "失败数量超过500W,";
  }
  if (taskType == 3) {
    //索引库发布
    request = idxDbApi.publishIdxDbInst;
  } else if (taskType == 8) {
    //上线计划同步
    request = syncIdxDbOnlinePlan;
  } else if (taskType == 9) {
    //上线计划回放
    request = replayIdxDbOnlinePlan;
  }
  $app.$confirm({ title: `${tips}确定进行失败重试?` }).then(() => {
    request(props.id, { failedRetry: true, idx: activeIndex.value }).then((result) => {
      $app.$message.success(`已添加失败重试任务`);
      emit("cancel-task");
    });
  });
};
//事件声明
const emit = defineEmits(["cancel-task"]);
</script>
<style lang="scss">
.task-process-span {
  width: 70px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.task-process-btn {
  margin-left: 5px;
  width: 40px;
}
.task-process-step {
  padding: 0;
  background: none;

  .el-step.is-simple {
    .el-step__head {
      font-size: 14px;
    }

    .el-step__title {
      font-size: 14px;
      line-height: 14px;
    }
  }
}
</style>
<style scoped lang="scss">
::v-deep {
  .el-button.is-link {
    vertical-align: baseline;
  }
}
</style>
