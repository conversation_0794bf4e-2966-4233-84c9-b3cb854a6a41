<template>
  <my-drawer class="dataset-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800px">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
    </my-form>
  </my-drawer>
</template>
  
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useValidate from '@/hooks/validate'
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as datasetApi from "@/api/dataset";
import { NAME_RULE, CODE_RULE } from '@/utils/validate';
const { $app, proxy } = useCtx();
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}数据集`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const params = cloneDeep(ruleForm.value);
      params.name = params.name.trim();
      params.enName = params.enName.trim();
      if (isUpdate.value) {
        datasetApi.modifyDataset(params).then((result) => {
          $app.$message.success("修改成功");
          emit("save-data");
          handleClose();
        });
      } else {
        datasetApi.insertDataset(params).then((result) => {
          $app.$message.success("新增成功");
          emit("save-data");
          handleClose();
        });
      }
    }
  })
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  enName: "",
  code: "",
  description: "",
};
/* 校验 */
const { validateNameRule, validateCodeRule } = useValidate()
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur" ,validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, '请输入名称') },{ min: 2, max: 100, message: '长度需要在 2 到 100之间', trigger: 'blur' }],
  enName: [{ required: true,trigger: "blur" , validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, '请输入英文名') },{ min: 2, max: 100, message: '长度需要在 2 到 100之间', trigger: 'blur' }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 100,
      placeholder: NAME_RULE
    },
  },
  enName: {
    label: "英文名",
    type: "input",
    attrs: {
      maxlength: 100,
      placeholder: CODE_RULE,
    },
  },
  code: {
    label: "编码",
    type: "input",
    hidden: () => {
      return formType.value === "add";
    },
    attrs: {
      disabled: isUpdate,
      maxlength: 255,
      placeholder: "请输入编码",
      class: 'required-label'
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  });
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
</style>
  