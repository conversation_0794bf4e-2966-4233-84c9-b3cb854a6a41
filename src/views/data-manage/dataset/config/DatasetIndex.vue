<template>
  <page-wrapper route-name="dataset::">
    <div class="dataset">
      <dataset-edit ref="datasetEditRef" @save-data="loadList"></dataset-edit>
      <dataset-table ref="datasetTableRef" @edit-data="openWindow" @version-data="openVersionPage"></dataset-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import datasetTable from "./DatasetTable.vue";
import datasetEdit from "./DatasetEdit.vue";
import useCtx from "@/hooks/useCtx";

const { $router } = useCtx();
const datasetEditRef = ref(null);
const datasetTableRef = ref(null);
const routeName = 'dataset';

//打开编辑窗口
const openWindow = (type: string, item: any) => {
  datasetEditRef.value.openWindow(type, item);
};
//查询列表数据
const loadList = () => {
  datasetTableRef.value.loadList();
};
//打开版本页面
const openVersionPage = (datasetId: string, datasetName: string) => {
  $router.push({
    name: `${routeName}::version`,
    query: {
      datasetId: datasetId,
      metaLabel: [datasetName]
    },
  });
};
</script>
<style scoped lang="scss">
</style>