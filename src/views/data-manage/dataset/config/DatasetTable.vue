<template>
  <div class="dataset-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/dataset/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="export" @click="events.exportExcel" style="margin-left: 10px">导出</my-button>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">新建数据集</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { keys, assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as datasetApi from "@/api/dataset";
import * as util from "@/utils/common";

const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/dataset/edit");
};
//列配置
const columns = ref([
  {
    prop: "name",
    label: "名称",
    width: 250,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.version(record);
      },
    },
  },
  { prop: "enName", label: "英文名", width: 180 },
  { prop: "code", label: "编码", width: 160, withCopy: true },
  { prop: "description", label: "描述", minWidth: 400 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    attrs: {
      placeholder: "名称 或 英文名 或 编码",
    },
  },
});
//查询缓存
const loadHistoryQuery = (data: any) => {
  const historyQuery = dataC.safeObject(localStorage.getItem("lynxiao-base-dataset-table"));
  if (dataC.isEmpty(historyQuery)) {
    return;
  }
  keys(queryItems.value).forEach((key) => {
    queryItems.value[key].modelValue = historyQuery[key] ?? queryItems.value[key].defaultValue;
    data[key] = queryItems.value[key].modelValue;
  });
};
//列表查询
const loadListData = (data: any) => {
  loadHistoryQuery(data);
  return new Promise((resolve: any) => {
    datasetApi.getDatasetListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
    localStorage.setItem("lynxiao-base-dataset-table", JSON.stringify(query.value));
  },
  reset: (obj: any) => {},
  add: () => {
    emit("edit-data", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除${record.name}吗?`,
      })
      .then(() => {
        datasetApi.removeDataset(record.id).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
  version: (record: any) => {
    emit("version-data", record.id, record.name);
  },
  exportExcel: (record: any) => {
    datasetApi.exportExcel().then((res) => {
      util.downloadFile(res, "数据集.xlsx");
    });
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.dataset-table {
  height: 100%;
}
</style>
