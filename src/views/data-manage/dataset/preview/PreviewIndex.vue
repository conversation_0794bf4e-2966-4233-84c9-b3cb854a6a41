<template>
  <el-col class="dataset-preview-index">
    <el-row :gutter="10">
      <el-col :span="4">
        <el-card>
          <el-input v-model="filterText" placeholder="请输入名称" @keyup.enter="filterStr = filterText">
            <template #append>
              <el-button :icon="Search" @click="filterStr = filterText" />
            </template>
          </el-input>
          <el-tree ref="treeRef" :data="sitesTree" node-key="id" default-expand-all :expand-on-click-node="false">
            <template #default="{ node, data }">
              <span class="tree-node" :class="{ active: data.id == modelValue.showItemId }" @click="showDataItem(node, data)">
                <span>
                  {{ data.name }}
                </span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>
      <el-col :span="20">
        <el-card>
          <el-tabs v-show="modelValue.showItemId == 'root'" v-model="activeName" @tab-change="events.tabChange">
            <el-tab-pane label="数据统计" name="statistic" class="dataset-preview-pane">
              <div class="preview-table-parent">
                <site-table ref="siteTableRef" :datasetVersionId="datasetVersionId"></site-table>
              </div>
            </el-tab-pane>
            <el-tab-pane label="数据预览" name="view" class="dataset-preview-pane">
              <div class="preview-table-parent">
                <preview-table ref="viewTableRef" :withExport="true"></preview-table>
              </div>
            </el-tab-pane>
            <!-- <el-tab-pane label="关联数据" name="relate" class="dataset-preview-pane">
              <div class="preview-table-parent">
                <site-table-v-2 ref="siteTableV2Ref" :datasetVersionId="datasetVersionId"></site-table-v-2>
              </div>
            </el-tab-pane> -->
          </el-tabs>
          <div v-show="modelValue.showItemId != 'root'" class="dataset-preview-not-pane">
            <preview-table ref="previewTableRef" :withExport="true"></preview-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-col>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import siteTable from "./SiteTable.vue";
import siteTableV2 from "./SiteTableV2.vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import { Search } from "@element-plus/icons-vue";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";

const { $router, $app, proxy } = useCtx();

const datasetId = $router.currentRoute.value.query.datasetId;
const datasetVersionId = $router.currentRoute.value.query.datasetVersionId;

const activeName = ref("statistic");

//筛选框数据(前端搜索)
const filterText = ref("");
const filterStr = ref("");
//表单数据项
const modelValue = reactive({
  sites: [],
  datasetVersionData: {},
  siteId: "",
  showItemId: "root",
});
//初始化站点列表:来源于数据库
const getSiteVersionList = () => {
  return new Promise((resolve: any) => {
    siteApi.getSiteVersionListByDatasetVersionId(datasetVersionId).then((result) => {
      resolve(result.content);
    });
  });
};
//获取数据集版本详细信息:来源于数据库
const getDatasetVersion = () => {
  return new Promise((resolve: any) => {
    datasetApi.getDatasetVersion(datasetVersionId).then((result) => {
      resolve(result);
    });
  });
};
//树形结构数据
const sitesTree = computed(() => {
  let list = [
    {
      id: "root",
      name: "全部站点",
      children: [],
    },
  ];
  list[0].children.push(
    ...modelValue.sites
      .map((item) => {
        return {
          id: item.siteVersionId,
          name: `${item.siteDTO.name}-${item.siteDTO.site}`,
          siteId: item.siteDTO.id,
          site: item.siteDTO.site,
        };
      })
      .filter((item) => item.name.includes(filterStr.value))
  );
  return list;
});
//刷新列表
const showDataItem = (node, data) => {
  modelValue.showItemId = data.id;
  if (node.level == 1) {
    return;
  }
  if (node.level == 2) {
    modelValue.siteId = data.siteId;
    proxy.$refs["previewTableRef"].loadList("fromDatasetVersionSite", datasetVersionId, { site: data.site, datasetVersionId: datasetVersionId });
    return;
  }
};
//事件列表
const events = reactive({
  tabChange: (tabPaneName: string) => {
    if (tabPaneName == "statistic") {
    } else if (tabPaneName == "view") {
      proxy.$refs["viewTableRef"].loadList("fromDatasetVersion", datasetVersionId, { datasetVersionId: datasetVersionId });
    } else if (tabPaneName == "relate") {
      window.dispatchEvent(new Event("resize"));
    }
  },
});
//初始化
onMounted(async () => {
  //给DatasetVersion的每个site设置siteVersion选中
  modelValue.datasetVersionData = await getDatasetVersion();
  const ids = modelValue.datasetVersionData.siteVersionIds;
  const list = await getSiteVersionList();
  list.forEach((item) => {
    const temp = item.siteVersionList.map((item) => {
      return item.id;
    });
    const res = temp.filter((x) => ids.some((y) => y == x));
    item.siteVersionId = !dataC.isEmpty(res) ? res[0] : "";
  });
  modelValue.sites.push(...list);
  //给modelValue.siteId一个默认值
  if (!dataC.isEmpty(modelValue.sites)) {
    modelValue.siteId = modelValue.sites[0].siteDTO.id;
  }
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({});
</script>
<style lang="scss">
.dataset-preview-index {
  height: 100%;
  padding: 12px 16px;

  .el-card__body {
    padding: 10px;
  }

  .el-tree {
    height: calc(100vh - 182px);
    --el-tree-node-hover-bg-color: none;

    > .el-tree-node.is-expanded.is-focusable {
      height: 100%;
      > .el-tree-node__children {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }

    .active {
      background-color: $info-bg;
      border-radius: 5px;
    }
  }

  .tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;

    :hover {
      color: $primary-color;
    }
  }

  .el-tree-node__content {
    padding: 20px 0;
  }

  .el-tabs__header {
    padding-left: 16px;
    height: 40px;
  }

  .dataset-preview-pane {
    padding: 16px;
    height: calc(100vh - 190px);

    .preview-table-parent {
      height: calc(100vh - 200px);
    }
  }

  .dataset-preview-not-pane {
    height: calc(100vh - 150px);
  }
}
</style>
