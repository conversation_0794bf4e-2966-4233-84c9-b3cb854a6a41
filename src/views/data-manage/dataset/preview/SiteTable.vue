<template>
  <div class="dataset-preview-site-table">
    <myTable ref="myTableRef" :columns="columns" :tableData="tableData" :transformListData="transformListData" :defaultPageSizes="[100, 200]">
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="{}" :refreshBtn="{ show: true }" @reset="events.reset" />
          <div style="font-size: 16px; font-weight: 700; margin-top: 10px">
            <span style="margin-left: 20px">数据总量(去重前) : {{ util.formatNumber(modelValue.dataItemCountBefore) }}</span>
            <span style="margin-left: 20px">数据总量(去重后) : {{ util.formatNumber(modelValue.dataItemCountAfter) }}</span>
            <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
          </div>
        </div>
      </template>
    </myTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from "vue";
import { keys, assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";
import * as previewApi from "@/api/preview";
import * as util from "@/utils/common";
import axios from "axios";

const props = defineProps(["datasetVersionId"]);
const { $app, proxy } = useCtx();

const tableData = ref([]);
//列配置
const columns = ref([
  { prop: "name", label: "名称", width: 160 },
  { prop: "site", label: "Site", width: 160 },
  { prop: "description", label: "描述", minWidth: 200 },
  { prop: "applicableScenarios", label: "适用场景", minWidth: 200 },
  {
    prop: "countBeforeRender",
    label: "数据量(去重前)",
    width: 150,
    fixed: "right",
  },
  {
    prop: "countAfterRender",
    label: "数据量(去重后)",
    width: 150,
    fixed: "right",
  },
]);
//获取数据集版本详细信息:来源于数据库
const getDatasetVersion = () => {
  return new Promise((resolve: any) => {
    datasetApi.getDatasetVersion(props.datasetVersionId).then((result) => {
      resolve(result);
    });
  });
};
//获取Tag的Key列表
const getSiteTagList = () => {
  return new Promise((resolve: any) => {
    siteApi.getSiteTagList().then((result) => {
      resolve(result.data);
    });
  });
};
//列表查询
const loadListData = async () => {
  modelValue.dataItemCountBefore = "统计中";
  modelValue.dataItemCountAfter = "统计中";
  //获取数据集版本信息
  if (dataC.isEmpty(modelValue.datasetVersionData)) {
    const datasetVersionData = await getDatasetVersion();
    modelValue.datasetVersionData = datasetVersionData;
  }
  //增加站点标签列
  if (dataC.isEmpty(modelValue.siteTagList)) {
    const siteTagList = await getSiteTagList();
    modelValue.siteTagList = siteTagList;
    columns.value.splice(
      columns.value.length - 2,
      0,
      ...siteTagList.map((item) => {
        return {
          prop: item.siteKey,
          label: item.siteKey,
          width: 120,
        };
      })
    );
  }
  return new Promise((resolve: any) => {
    siteApi.getSiteVersionListByDatasetVersionId(props.datasetVersionId).then((result) => {
      //返回数据,直接赋值会让vue响应不及时，这里采用先删再增的方法
      tableData.value.splice(0, tableData.value.length);
      tableData.value.push(...result.content);
      loadList();
      //增加数据总量查询
      getCountList();
    });
  });
};
//获取数据总量数据
const getCountList = () => {
  //获取每个站点版本的数据总量
  const ids = modelValue.datasetVersionData.siteVersionIds;

  tableData.value.forEach((item) => {
    // 简化获取siteVersionId的逻辑
    item.siteVersionId = item.siteVersionList.find((v) => ids.includes(v.id))?.id || "";

    // 获取去重前数据量
    previewApi.getCountFromDatasetVersionSite(props.datasetVersionId, {}, item.siteDTO.site).then((result) => {
      // 直接更新当前item，无需再次遍历tableData
      item.countBefore = result.data;
      item.countBeforeRender = util.formatNumber(result.data);

      modelValue.dataItemCountBefore = tableData.value.reduce((sum, item) => {
        return sum + Number(item.countBefore || 0);
      }, 0);
      loadList();
    });

    // 获取去重后数据量
    previewApi.getCountFromDatasetVersionSite(props.datasetVersionId, { s: 0 }, item.siteDTO.site).then((result) => {
      // 直接更新当前item，无需再次遍历tableData
      item.countAfter = result.data;
      item.countAfterRender = util.formatNumber(result.data);

      modelValue.dataItemCountAfter = tableData.value.reduce((sum, item) => {
        return sum + Number(item.countAfter || 0);
      }, 0);
      loadList();
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    keys(x.siteDTO).forEach((key) => {
      x[key] = x.siteDTO[key];
    });
    if (!dataC.isEmpty(x.labels)) {
      modelValue.siteTagList.forEach((item) => {
        const temp = dataC.getItemByValue(x.labels, item.siteKey, "siteKey")["siteValue"];
        if (!dataC.isEmpty(temp) && temp instanceof Array) {
          x[item.siteKey] = temp.join(",");
        }
      });
    }
    return x;
  });
};
//事件列表
const events = reactive({
  reset: (obj: any) => {
    if (new Date().getTime() - modelValue.refreshTime > 1000) {
      loadListData();
      modelValue.refreshTime = new Date().getTime();
    }
  },
  exportExcel: () => {
    const titles = columns.value.map((col) => col.label);
    const contents = tableData.value.map((item) => columns.value.map((col) => item[col.prop]));
    const parameters = [
      { name: "数据总量(去重前)", value: modelValue.dataItemCountBefore },
      { name: "数据总量(去重后)", value: modelValue.dataItemCountAfter },
    ];
    datasetApi.exportSites({ titles, contents, parameters }).then((res) => util.downloadFile(res, "统计信息.xlsx"));
  },
});
//数据项
const modelValue = reactive({
  datasetVersionData: null,
  siteTagList: [],
  dataItemCountBefore: 0,
  dataItemCountAfter: 0,
  refreshTime: 0,
});
const loadList = () => {
  proxy.$refs["myTableRef"].loadData();
};
//初始化
onMounted(() => {
  nextTick(() => {
    loadListData();
  });
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.dataset-preview-site-table {
  height: 100%;

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
