<template>
  <div class="dataset-preview-site-table">
    <myTable ref="myTableRef" :columns="columns" :tableData="tableData" :transformListData="transformListData" :defaultPageSizes="[100, 200]">
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="{}" :refreshBtn="{ show: true }" @reset="events.reset" />
          <div style="font-size: 16px; font-weight: 700; margin-top: 10px">
            <span style="margin-left: 20px">数据总量 : {{ util.formatNumber(modelValue.dataItemCount) }}</span>
          </div>
        </div>
      </template>
    </myTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from "vue";
import { keys, assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";
import * as previewApi from "@/api/preview";
import * as util from "@/utils/common";
import axios from 'axios'
const props = defineProps(["datasetVersionId"]);

const { $app, proxy } = useCtx();

const tableData = ref([]);
//列配置
const columns = ref([
  { prop: "name", label: "名称", width: 160 },
  { prop: "site", label: "Site", width: 160 },
  { prop: "description", label: "描述", minWidth: 200 },
  { prop: "applicableScenarios", label: "适用场景", minWidth: 200 },
  { prop: "countRender", label: "数据总量", width: 120, fixed: "right" },
]);
//获取数据集版本详细信息:来源于数据库
const getDatasetVersion = () => {
  return new Promise((resolve: any) => {
    datasetApi.getDatasetVersion(props.datasetVersionId).then((result) => {
      resolve(result);
    });
  });
};
//获取Tag的Key列表
const getSiteTagList = () => {
  return new Promise((resolve: any) => {
    siteApi.getSiteTagList().then((result) => {
      resolve(result.data);
    });
  });
};
//列表查询
const loadListData = async () => {
  modelValue.dataItemCount = 0;
  //获取数据集版本信息
  if (dataC.isEmpty(modelValue.datasetVersionData)) {
    const datasetVersionData = await getDatasetVersion();
    modelValue.datasetVersionData = datasetVersionData;
  }
  //增加站点标签列
  if (dataC.isEmpty(modelValue.siteTagList)) {
    const siteTagList = await getSiteTagList();
    modelValue.siteTagList = siteTagList;
    columns.value.splice(
      columns.value.length - 1,
      0,
      ...siteTagList.map((item) => {
        return {
          prop: item.siteKey,
          label: item.siteKey,
          width: 120,
        };
      })
    );
  }
  return new Promise((resolve: any) => {
    siteApi.getSiteVersionListByDatasetVersionId(props.datasetVersionId).then((result) => {
      //返回数据
      tableData.value = result.content;
      loadList();
      //增加数据总量查询
      getCountList();
    });
  });
};
//获取数据总量数据
const getCountList = () => {
  //获取每个站点的数据总量
  const ids = modelValue.datasetVersionData.siteVersionIds;
  tableData.value.forEach((item) => {
    const temp = item.siteVersionList.map((item) => {
      return item.id;
    });
    const res = temp.filter((x) => ids.some((y) => y == x));
    item.siteVersionId = !dataC.isEmpty(res) ? res[0] : "";
    previewApi.getCountFromSiteVersion(item.siteVersionId, { s: 0 }).then((result) => {
      tableData.value.forEach((x: any) => {
        if (x.siteVersionId == item.siteVersionId) {
          x.count = result.data;
          x.countRender = util.formatNumber(x.count);
          modelValue.dataItemCount = modelValue.dataItemCount + result.data;
          loadList();
        }
      });
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    keys(x.siteDTO).forEach((key) => {
      x[key] = x.siteDTO[key];
    });
    if (!dataC.isEmpty(x.labels)) {
      modelValue.siteTagList.forEach((item) => {
        const temp = dataC.getItemByValue(x.labels, item.siteKey, "siteKey")["siteValue"];
        if (!dataC.isEmpty(temp) && temp instanceof Array) {
          x[item.siteKey] = temp.join(",");
        }
      });
    }
    return x;
  });
};
//事件列表
const events = reactive({
  reset: (obj: any) => {
    loadListData();
  },
});
//数据项
const modelValue = reactive({
  datasetVersionData: null,
  siteTagList: [],
  dataItemCount: 0,
});
const loadList = () => {
  proxy.$refs["myTableRef"].loadData();
};
//初始化
onMounted(() => {
  loadListData();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.dataset-preview-site-table {
  height: 100%;

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
