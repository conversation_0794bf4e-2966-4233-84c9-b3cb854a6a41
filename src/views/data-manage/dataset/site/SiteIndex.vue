<template>
  <my-drawer
    class="dataset-site-edit"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
    :showConfirm="ruleForm.status != 2 && testAuth()"
    :showClose="ruleForm.status != 2 && testAuth()"
    size="800px"
  >
    <el-form ref="formRef" :model="ruleForm" :rules="rules" label-width="100px" :disabled="ruleForm.status == 2 || !testAuth()">
      <el-form-item label="名称 :" prop="name">
        <el-input v-model="ruleForm.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="版本 :" prop="versionRender" v-if="isUpdate"> v{{ ruleForm.versionRender }} </el-form-item>
      <el-form-item label="描述 :" prop="description">
        <el-input type="textarea" v-model="ruleForm.description" placeholder="请输入描述" />
      </el-form-item>
      <el-divider border-style="dashed" style="margin: 15px" />
      <el-col :span="24" v-for="(site, siteIndex) in ruleForm.sites" class="item-row">
        <el-card>
          <template #header>
            <span>【{{ siteIndex + 1 }}】&nbsp;{{ site.siteDTO.name }}-{{ site.siteDTO.site }}</span>
          </template>
          <el-form-item label="规则版本 :" :prop="`sites.${siteIndex}.siteVersionId`" :rules="{ required: true, message: '必选', trigger: 'change' }">
            <el-select v-model="site.siteVersionId" placeholder="请选择站点版本">
              <el-option
                v-for="siteVersion in site.siteVersionList"
                :label="`${siteVersion.name}(v${util.padNumberToDigits(siteVersion.version, 3)})`"
                :value="siteVersion.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="版本描述 :">
            {{ getSiteVersionDescription(site.siteVersionId, site.siteVersionList) }}
          </el-form-item>
        </el-card>
      </el-col>
    </el-form>
  </my-drawer>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";
import * as util from "@/utils/common";

const { $router, $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/dataset/edit");
}
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value === "add") return "新增数据集版本";
  if (formType.value === "edit") return "编辑数据集版本";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  proxy.$refs["formRef"].resetFields();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  submit();
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const defaultForm = {
  sites: [],
  datasetId: "",
  id: "",
  name: "",
  versionRender: "",
  description: "",
  siteVersionIds: [],
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  versionRender: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
//打开窗口
const openWindow = (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
      getSiteVersionListByDatasetVersionId(row.id);
      ruleForm.value.status = row.status;
    } else {
      ruleForm.value = assign({}, defaultForm, row);
      getSiteVersionListByDatasetId(row.datasetId);
      ruleForm.value.status = 1;
    }
    //给DatasetVersion的每个site设置siteVersion选中
    ruleForm.value.siteVersionIds = !dataC.isEmpty(ruleForm.value.siteVersionIds) ? row.siteVersionIds : [];
    proxy.$refs["formRef"].resetFields();
  });
};
//提交数据
const submit = (form: any) => {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (dataC.isEmpty(ruleForm.value.sites)) {
        $app.$message.warning(`至少需要一个站点!`);
        return;
      }
      const formData = cloneDeep(ruleForm.value);
      formData.siteVersionIds = formData.sites.map((item) => {
        return item.siteVersionId;
      });
      delete formData.versionRender;
      delete formData.sites;
      if (isUpdate.value) {
        datasetApi.modifyDatasetVersion(formData).then((result) => {
          $app.$message.success("修改成功");
          emit("save-data");
          handleClose();
        });
      } else {
        datasetApi.insertDatasetVersion(formData).then((result) => {
          $app.$message.success("新增成功");
          emit("save-data");
          handleClose();
        });
      }
    }
  });
};
//初始化站点列表-修改
const getSiteVersionListByDatasetVersionId = (datasetVersionId: string) => {
  siteApi.getSiteVersionListByDatasetVersionId(datasetVersionId).then((result) => {
    const list = result.content;
    list.forEach((item) => {
      //和数据集版本的siteVersionList匹配，设置每个站点被选中的站点版本ID
      const temp = item.siteVersionList.map((item) => {
        return item.id;
      });
      const res = temp.filter((x) => ruleForm.value.siteVersionIds.some((y) => y == x));
      item.siteVersionId = !dataC.isEmpty(res) ? res[0] : "";
    });
    ruleForm.value.sites = list;
  });
};
//初始化站点列表-新增
const getSiteVersionListByDatasetId = (datasetId: string) => {
  siteApi.getSiteVersionListByDatasetId(datasetId).then((result) => {
    const list = result.content;
    list.forEach((item) => {
      //倒叙排列
      item.siteVersionList.reverse();
      //选择最新的
      item.siteVersionId = item.siteVersionList[0].id;
    });
    ruleForm.value.sites = list;
  });
};
//根据站点版本id获取描述
const getSiteVersionDescription = (siteVersionId: string, siteVersionList: array) => {
  return dataC.getItemByValue(siteVersionList, siteVersionId, "id").description;
};
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
.dataset-site-edit {
}
</style>
