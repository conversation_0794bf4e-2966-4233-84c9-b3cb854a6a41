<template>
  <page-wrapper route-name="dataset::version::">
    <dataset-edit ref="datasetEditRef" @save-data="getDataset"></dataset-edit>
    <site-index ref="siteIndexRef" @save-data="events.refreshVersion"></site-index>
    <data-dedup ref="dataDedupRef" @dedup-data="events.dedupDatasetVersion"></data-dedup>
    <div class="dataset-version">
      <el-card class="info-card" :style="{ height: activeCollapse == 1 ? '170px' : '60px' }">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>&nbsp;
              <el-button link type="primary" @click.native.stop="events.edit" :disabled="!testAuth()">
                <el-icon size="18">
                  <Edit />
                </el-icon>
              </el-button>
            </template>
            <el-descriptions column="2">
              <el-descriptions-item label="名称 : " label-class-name="bold">{{
                `${modelValue.datasetData.name}(${modelValue.datasetData.enName})`
              }}</el-descriptions-item>
              <el-descriptions-item label="编码 : " label-class-name="bold">{{ modelValue.datasetData.code }}</el-descriptions-item>
              <el-descriptions-item label="创建人 : " label-class-name="bold">{{ modelValue.datasetData.createdBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间 : " label-class-name="bold">
                {{ !dataC.isEmpty(modelValue.datasetData.createdDate) ? timeC.format(modelValue.datasetData.createdDate, "YYYY-MM-DD hh:mm:ss") : "" }}
              </el-descriptions-item>
              <el-descriptions-item span="2" label="描述 : " label-class-name="bold">{{ modelValue.datasetData.description }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card class="table-card" :style="{ height: activeCollapse == 1 ? 'calc(100vh - 305px)' : 'calc(100vh - 195px)' }">
        <el-row justify="space-between">
          <el-col :span="12">
            <div class="el-descriptions">
              <div class="el-descriptions__header">
                <div class="el-descriptions__title">
                  <span>版本信息</span>&nbsp;
                  <el-button link type="primary" @click="events.refreshVersion">
                    <el-icon size="18">
                      <Refresh />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button type="primary" @click="events.addVersion" :disabled="!testAuth()">
              <el-icon> <CirclePlus /> </el-icon>&nbsp;新建数据集版本
            </el-button>
          </el-col>
        </el-row>
        <dataset-version-table
          ref="datasetVersionTableRef"
          :datasetId="datasetId"
          :datasetName="modelValue.datasetData.name"
          @edit-data="events.openVersionEditWindow"
          @preview-data="events.openPreviewPage"
          @dedup-data="events.openDataDedupWindow"
        ></dataset-version-table>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import datasetEdit from "../config/DatasetEdit.vue";
import siteIndex from "../site/SiteIndex.vue";
import datasetVersionTable from "./VersionTable.vue";
import dataDedup from "@/views/common/dedup/DataDedup.vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as datasetApi from "@/api/dataset";

const { $router, $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/dataset/edit");
}
const datasetId = $router.currentRoute.value.query.datasetId;
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
const routeName = "dataset::version";

const activeCollapse = ref([1]);

const modelValue = reactive({
  datasetData: {},
});
//初始化属性:来源于数据库
const getDataset = () => {
  datasetApi.getDataset(datasetId).then((result) => {
    modelValue.datasetData = result;
  });
};
//初始化站点信息
onMounted(() => {
  getDataset();
});
//事件列表
const events = reactive({
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  edit: () => {
    proxy.$refs["datasetEditRef"].openWindow("edit", modelValue.datasetData);
  },
  addVersion: () => {
    proxy.$refs["siteIndexRef"].openWindow("add", {
      datasetId: modelValue.datasetData.id,
    });
  },
  openVersionEditWindow: (type: string, item: any) => {
    proxy.$refs["siteIndexRef"].openWindow(type, item);
  },
  openDataDedupWindow: (item: any) => {
    proxy.$refs["dataDedupRef"].openWindow(item);
  },
  dedupDatasetVersion: (form: any) => {
    datasetApi.dedupDatasetVersion(form.id, form).then((result) => {
      $app.$message.success("已添加去重任务");
      events.refreshVersion();
      proxy.$refs["dataDedupRef"].closeWindow();
    });
  },
  refreshVersion: () => {
    proxy.$refs["datasetVersionTableRef"].loadList();
  },
  openPreviewPage: (datasetVersionId: string, datasetVersionName: string) => {
    $router.push({
      name: `${routeName}::preview`,
      query: {
        datasetId: datasetId,
        datasetVersionId: datasetVersionId,
        metaLabel: [metaLabel[0], datasetVersionName],
      },
    });
  },
});
</script>
<style lang="scss">
.dataset-version {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;
      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;
      .el-collapse {
        height: 100%;
        .el-collapse-item {
          height: 100%;
          .el-collapse-item__wrap {
            height: 100%;
            .el-collapse-item__content {
              height: 100%;
              .el-descriptions {
                height: 100%;
                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }
  }
}
</style>
