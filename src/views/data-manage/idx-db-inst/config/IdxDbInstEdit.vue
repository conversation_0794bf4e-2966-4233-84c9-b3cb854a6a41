<template>
  <my-drawer class="idx-db-inst-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" :disabled="hasTask">
      <template #dataset>
        <el-col>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="" label-width="0" prop="datasetId">
                <el-select v-model="ruleForm.datasetId" @change="datasetChange" placeholder="请选择数据集" filterable>
                  <el-option v-for="item in modelValue.datasetList" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="" label-width="0" prop="datasetVersionId">
                <el-select v-model="ruleForm.datasetVersionId" @change="datasetVersionChange" placeholder="请选择数据集版本">
                  <el-option v-for="item in modelValue.datasetVersionList" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </template>
      <template #dedupConfig>
        <dedup-config v-model="ruleForm.dedupConfig" :readOnly="isUpdate" />
      </template>
    </my-form>
  </my-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import DedupConfig from "@/views/common/dedup/DedupConfig.vue";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as idxDbApi from "@/api/idx-db";
import * as datasetApi from "@/api/dataset";
import * as util from "@/utils/common";
import { CODE_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";

const { $app, proxy } = useCtx();
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新建索引库";
  if (formType.value == "edit") return "编辑索引库";
  if (formType.value == "publish") return "发布索引库";
  return "索引库";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
//是否为发布
const isPublish = computed(() => {
  return formType.value === "publish";
});
//是否执行过发布
const hasTask = computed(() => {
  return modelValue.taskStatus != 1;
});
const isRealtime = computed(() => {
  return ruleForm.value.realtime == true;
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  enName: "",
  datasetId: "",
  datasetCode: "",
  datasetVersionId: "",
  idxDbId: "",
  code: "",
  clusterId: "",
  hotClusterId: "",
  realtime: false,
  region: "",
  description: "",
  dedupConfig: "",
};
const extraForm = {};
let ruleForm = ref<any>(assign({}, defaultForm, extraForm));
const { validateCodeRule } = useValidate();
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  enName: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, "英文名不能为空") }],
  datasetId: [{ required: true, message: "数据集不能为空", trigger: "change" }],
  datasetVersionId: [{ required: true, message: "数据集版本不能为空", trigger: "change" }],
  idxDbId: [{ required: true, message: "特征库模板不能为空", trigger: "change" }],
  realtime: [{ required: true, message: "支持实时不能为空", trigger: "change" }],
  region: [{ required: true, message: "验证环境不能为空", trigger: "change" }],
  clusterId: [{ required: true, message: "集群不能为空", trigger: "change" }],
  hotClusterId: [{ required: true, message: "集群不能为空", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
    },
    separate: {
      title: "基础信息",
    },
  },
  enName: {
    label: "英文名",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: CODE_RULE,
      disabled: computed(() => isUpdate.value),
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
    },
  },
  dataset: {
    label: "数据集",
    type: "slot",
    slotName: "dataset",
    attrs: {
      class: "required-label",
    },
    separate: {
      title: "配置信息",
    },
  },
  idxDbId: {
    label: "特征库模板",
    type: "select",
    options: [],
    attrs: {
      clearable: false,
    },
  },
  code: {
    label: "编码",
    type: "input",
    hidden: () => {
      return formType.value === "add";
    },
    attrs: {
      disabled: () => {
        return formType.value === "edit" || formType.value === "publish";
      },
      maxlength: 255,
      class: "required-label",
    },
  },
  region: {
    label: "验证环境",
    type: "radio",
    options: [],
    events: {
      change: (val: any) => {
        regionChange(val);
      },
    },
  },
  realtime: {
    label: "支持实时",
    type: "radio",
    options: [
      { value: false, label: "非实时" },
      { value: true, label: "实时" },
    ],
  },
  clusterId: {
    label: computed(() => {
      console.log(formItems.value.realtime);
      return isRealtime.value ? "存量集群" : "集群";
    }),
    type: "select",
    options: [],
    attrs: {
      filterable: false,
      clearable: false,
    },
  },
  hotClusterId: {
    label: "实时集群",
    type: "select",
    options: [],
    attrs: {
      filterable: false,
      clearable: false,
    },
    hidden: () => {
      return !isRealtime.value;
    },
  },
});
//数据集修改
const datasetChange = (datasetId: string, clear: boolean = true) => {
  const datasetData = dataC.getItemByValue(modelValue.datasetList, datasetId);
  ruleForm.value.datasetCode = datasetData.code;
  getDatasetVersionList(datasetId);
  if (clear) {
    ruleForm.value.datasetVersionId = "";
  }
};
//区域修改
const regionChange = (region: string, clear: boolean = true) => {
  idxDbApi.getRegionEsInfoList(region).then((result) => {
    formItems.value.clusterId.options = result.data
      .filter((item) => {
        return item.type == "full";
      })
      .map((item) => ({
        label: `${item.name}(${util.getEsInfoRender(item)})`,
        value: item.id,
      }));
    formItems.value.hotClusterId.options = result.data
      .filter((item) => {
        return item.type == "hot";
      })
      .map((item) => ({
        label: `${item.name}(${util.getEsInfoRender(item)})`,
        value: item.id,
      }));
  });
  if (clear) {
    ruleForm.value.clusterId = "";
    ruleForm.value.hotClusterId = "";
  }
};
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit" || type === "publish") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm, extraForm)));
      modelValue.taskStatus = row.taskStatus;
      datasetChange(ruleForm.value.datasetId, false);
      regionChange(ruleForm.value.region, false);
    } else {
      ruleForm.value = assign({}, defaultForm, extraForm, row);
      modelValue.taskStatus = 1;
    }
  });
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  form.name = form.name.trim();
  form.enName = form.enName.trim();
  if (isUpdate.value) {
    idxDbApi.modifyIdxDbInst(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else if (isPublish.value) {
    if (hasTask.value) {
      idxDbApi.publishIdxDbInst(form.id).then((result) => {
        $app.$message.success("已添加发布索引库任务");
        emit("save-data");
        handleClose();
      });
    } else {
      idxDbApi.modifyIdxDbInst(form).then((result) => {
        emit("save-data");
        idxDbApi.publishIdxDbInst(form.id).then((result) => {
          $app.$message.success("已添加发布索引库任务");
          emit("save-data");
          handleClose();
        });
      });
    }
  } else {
    idxDbApi.insertIdxDbInst(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//数据项
const modelValue = reactive({
  datasetList: [],
  datasetVersionList: [],
  taskStatus: 1,
});
//获取数据集列表
const getDatasetList = () => {
  api.getMetaDatasetList().then((result) => {
    modelValue.datasetList = result.map((item) => ({
      label: `${item.name}(${item.enName})`,
      value: item.id,
      id: item.id,
      code: item.code,
    }));
  });
};
//获取数据集版本列表
const getDatasetVersionList = (datasetId: any) => {
  datasetApi.getDatasetVersionList(datasetId).then((result) => {
    modelValue.datasetVersionList = result.content
      .filter((item) => {
        return item.status == 2;
      })
      .map((item) => ({
        label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
        value: item.id,
      }));
  });
};
//获取特征库模板列表
const getMetaIdxDbTemList = () => {
  api.getMetaIdxDbTemList().then((result) => {
    formItems.value.idxDbId.options = result.map((item) => ({
      label: item.name,
      value: item.id,
      code: item.code,
      idxConfig: item.idxConfig,
    }));
  });
};
//获取验证环境列表
const getMetaRegionList = () => {
  api.getMetaRegionList().then((result) => {
    formItems.value.region.options = result
      .filter((item) => {
        return item.envType == 1;
      })
      .map((item) => ({
        label: item.name,
        value: item.code,
      }));
  });
};
//初始化
onMounted(() => {
  getDatasetList();
  getMetaIdxDbTemList();
  getMetaRegionList();
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>
