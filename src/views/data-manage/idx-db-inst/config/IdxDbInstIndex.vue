<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="idx-db-inst">
      <idx-db-inst-edit ref="idxDbInstEditRef" @save-data="events.loadInstTableList"></idx-db-inst-edit>
      <idx-db-inst-table ref="idxDbInstTableRef" @edit-data-inst="events.openInstEditWindow" @sync-data="events.openInstSyncWindow" @dedup-data="events.openInstDedupWindow" @statistic-data="events.openStatisticPage"></idx-db-inst-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import idxDbInstTable from "./IdxDbInstTable.vue";
import idxDbInstEdit from "./IdxDbInstEdit.vue";
import useCtx from "@/hooks/useCtx";

const { $app, proxy, $router } = useCtx();
const routeName = "idx-db";

const events = reactive({
  loadInstTableList: () => {
    proxy.$refs["idxDbInstTableRef"].loadList();
  },
  openInstEditWindow: (type: string, item: any) => {
    proxy.$refs["idxDbInstEditRef"].openWindow(type, item);
  },
  openStatisticPage: (item: any) => {
    $router.push({
      name: `${routeName}::statistic`,
      query: {
        idxDbInstId: item.id,
        metaLabel: [item.name]
      },
    });
  },
});
</script>
<style lang="scss">
.idx-db-inst {
}
</style>