<template>
  <div class="idx-db-inst-table">
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <table-page
      ref="myTableRef"
      name="idx-db-inst-table"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/idx-db/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="export" @click="events.exportExcel" style="margin-left: 10px">导出</my-button>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">新建索引库</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #process="scope">
        <status-dot v-if="dataC.isEmpty(scope.row.taskDetailList)" type="info" name="待构建" />
        <task-process
          v-if="!dataC.isEmpty(scope.row.taskDetailList)"
          :id="scope.row.id"
          :taskId="scope.row.taskId"
          :datasetVersionId="scope.row.datasetVersionId"
          :taskType="scope.row.taskType"
          :taskStatus="scope.row.taskStatus"
          :taskList="scope.row.taskDetailList"
          @cancel-task="loadList"
          @publish-task="events.publish(scope.row)"
          :disabled="!testAuth()"
        ></task-process>
      </template>
    </table-page>
    <my-drawer direction="ltr" class="mock-add" width="1500px" v-model="historyVisible" title="历史任务" :showConfirm="false" @close="historyVisible = false">
      <HistoryTask ref="historyTaskRef" :dataType="2" :queryDisplay="false"> </HistoryTask>
    </my-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";
import useCtx from "@/hooks/useCtx";
import taskProcess from "@/views/common/task/TaskProcess.vue";
import * as idxDbApi from "@/api/idx-db";
import * as taskApi from "@/api/task";
import IntervalClient from "@/utils/interval-client";
import * as util from "@/utils/common";
import * as previewApi from "@/api/preview";
import HistoryTask from "@/views/common/task/TaskHistory.vue";
const { $app, proxy, $auth } = useCtx();
const historyVisible = ref(false);
const testAuth = () => {
  return $auth.testAuth("/base/#/idx-db/edit");
};
const { api } = useStore();
//列配置
const columns = ref([
  {
    prop: "name",
    label: "名称",
    width: 250,
    custom: "editLink",
    blod: true,
    customRender: {
      linkClick: (record: any) => {
        events.statistic(record);
      },
      linkDisabled: (record: any) => {
        return dataC.isEmpty(record.idInRegion);
      },
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  { prop: "enName", label: "英文名", width: 160 },
  { prop: "code", label: "编码", minWidth: 280, withCopy: true },
  {
    prop: "enabled",
    label: "启用状态",
    width: 110,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: true,
        inactiveValue: false,
        size: "small",
      },
      disabled: (record: any) => !record.enabled && record.taskStatus != 3,
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.enabled === true) {
            $app
              .$confirm({ title: `确定禁用 ${record.name}?` })
              .then(() => {
                idxDbApi.disabledIdxDbInst(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`禁用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: `确定启用 ${record.name}?` })
              .then(() => {
                idxDbApi.enabledIdxDbInst(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`启用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },
  {
    prop: "process",
    label: "任务进度",
    slotName: "process",
    width: 250,
    showOverflowTooltip: false,
    sortable: false,
  },
  { prop: "indexDbCount", label: "数据总量", width: 120, sortable: false },
  { prop: "realtimeRender", label: "支持实时", width: 120 },
  { prop: "regionRender", label: "环境", width: 120 },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    custom: "editButton",
    customRender: {
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "operation", label: "操作", width: 180, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  search: "",
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    attrs: {
      placeholder: "名称 或 英文名 或 编码",
    },
  },
  enabled: {
    type: "select",
    label: "",
    modelValue: "",
    defaultValue: "",
    options: [
      { value: true, label: "已启用" },
      { value: false, label: "已禁用" },
    ],
    attrs: {
      placeholder: "启用状态",
    },
  },
  realtime: {
    type: "select",
    label: "",
    modelValue: "",
    defaultValue: "",
    options: [
      { value: true, label: "实时" },
      { value: false, label: "非实时" },
    ],
    attrs: {
      placeholder: "支持实时",
    },
  },
});
//查询缓存
const loadHistoryQuery = (data: any) => {
  const historyQuery = dataC.safeObject(localStorage.getItem("lynxiao-base-idx-db-inst-table"));
  if (dataC.isEmpty(historyQuery)) {
    return;
  }
  keys(queryItems.value).forEach((key) => {
    queryItems.value[key].modelValue = historyQuery[key] ?? queryItems.value[key].defaultValue;
    data[key] = queryItems.value[key].modelValue;
  });
};
const makeRequestWithId = (item) => {
  if (item.taskStatus == 3) {
    return previewApi.getPreviewCapacityFromRegion(item.id, item.idInRegion, item.region);
  }
};
const getMangoData = (content) => {
  Promise.allSettled(content.map((item) => makeRequestWithId(item))).then((res) => {
    content.map((item1, index1) => {
      item1.mango = res[index1]?.value?.content?.dbCapacity?.mongo;
    });
  });
};
const refreshMango = async (item, index) => {
  const tableData = proxy.$refs["myTableRef"].getTableData();
  const res = await previewApi.getPreviewCapacityFromRegion(item.id, item.idInRegion, item.region);
  tableData[index].mango = res?.content?.dbCapacity?.mongo;
  proxy.$refs["myTableRef"].setTableData(tableData);
};
//列表查询
const loadListData = async (data: any) => {
  loadHistoryQuery(data);
  if (dataC.isEmpty(modelValue.metaRegionList)) {
    const list = await api.getMetaRegionList();
    modelValue.metaRegionList.push(...list);
  }
  return new Promise((resolve: any) => {
    idxDbApi.getIdxDbInstListPage(data).then((result) => {
      //增加任务监控
      getIntervalClinet(result.content);
      // getMangoData(result.content)
      //返回数据
      resolve(result);
    });
  });
};
//定时任务监控
const intervalClinet = ref(null);
const getIntervalClinet = (tableData: Array<any>) => {
  //如果已有定时任务对象，则停止并创建新的
  intervalClinet.value?.disconnect();
  //如果列表为空 则不创建新的
  if (dataC.isEmpty(tableData)) return;
  // 获取定时任务对象并启动,以持续刷新任务信息
  intervalClinet.value = new IntervalClient(3000, true);
  intervalClinet.value.onHandler(getTaskProgressListByTask, tableData).connect();
};
const getTaskProgressListByTask = (tableData: Array<any>) => {
  const taskIdList = tableData
    .filter((x) => {
      return !dataC.isEmpty(x.taskId);
    })
    .map((x: any) => {
      return x.taskId;
    });
  if (dataC.isEmpty(taskIdList)) return;
  taskApi.getTaskProgressListByTask(taskIdList).then((result) => {
    if (!(result.content instanceof Array)) return;
    const list = tableData.map((x: any) => {
      const task = dataC.getItemByValue(result.content, x.taskId, "taskId");
      if (!dataC.isEmpty(task.taskId)) {
        x.taskId = task.taskId;
        x.taskStatus = task.status;
        x.taskType = task.type;
        x.taskDetailList = task.taskDetailList;
      }
      return x;
    });
    proxy.$refs["myTableRef"]?.setTableData(list);
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.indexDbCount = util.formatNumber(x.indexDbCount);
    x.realtimeRender = x.realtime ? "实时" : "非实时";
    x.regionRender = dataC.getItemByValue(modelValue.metaRegionList, x.region, "code")["name"];
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
    //只有未构建的可以编辑
    disabled: (record: any) => record.taskStatus != 1,
    disabledTips: "已构建，不可编辑",
  },
  {
    type: "publish",
    label: "构建",
    //非执行中的 且为禁用状态可以构建
    disabled: (record: any) =>
      //record.enabled ||
      record.taskStatus == 2,
    disabledTips: (record: any) => {
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可构建";
      }
      // if (record.enabled) {
      //   return "已启用，不可构建";
      // }
      return "不可构建";
    },
  },
  {
    type: "delete",
    label: "删除索引库",
    btnType: "danger",
    collapsed: true,
    //非执行状态  可删除索引库
    disabled: (record: any) => record.taskStatus == 2,
    disabledTips: "任务正在执行中，不可删除",
  },
  {
    type: "clear",
    label: "清空索引库数据",
    btnType: "danger",
    collapsed: true,
    //非执行状态  可删除数据
    disabled: (record: any) => record.taskStatus == 2,
    disabledTips: "任务正在执行中，不可删除",
  },
  {
    type: "forceComplete",
    label: "强制结束任务",
    btnType: "warning",
    collapsed: true,
    //执行状态 可强制完成
    disabled: (record: any) => record.taskStatus != 2,
    disabledTips: "没有执行中的任务",
  },
  {
    type: "history",
    label: "历史任务",
    btnType: "primary",
    auth: "all",
    collapsed: true,
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  forceComplete: (record: any) => {
    $app
      .$confirm({
        title: `确认强制结束任务?`,
      })
      .then(() => {
        taskApi.forceCompleteTask(record.taskId).then((result) => {
          $app.$message.success(`强制结束任务成功`);
        });
      });
  },
  history: (record: any) => {
    historyVisible.value = true;
    if (dataC.isEmpty(proxy.$refs.historyTaskRef)) {
      nextTick(() => {
        proxy.$refs.historyTaskRef.loadList(record.id);
      });
    } else {
      proxy.$refs.historyTaskRef.loadList(record.id);
    }
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
    localStorage.setItem("lynxiao-base-idx-db-inst-table", JSON.stringify(query.value));
  },
  reset: (obj: any) => {},
  statistic: (record: any) => {
    emit("statistic-data", record);
  },
  add: () => {
    emit("edit-data-inst", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data-inst", "edit", record);
  },
  publish: (record: any) => {
    if (record.taskDetailList?.[record.taskDetailList?.length - 1 || 0]?.failed) {
      $app
        .$confirm({
          title: `有失败数量，您确认要构建 ${record.name} 数据?`,
        })
        .then(() => {
          emit("edit-data-inst", "publish", record);
        });
    } else {
      emit("edit-data-inst", "publish", record);
    }
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        idxDbApi.removeIdxDbInst(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
  clear: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name} 数据?`,
      })
      .then(() => {
        idxDbApi.clearIdxDbInst(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 数据成功`);
        });
      });
  },
  exportExcel: (record: any) => {
    idxDbApi.exportExcel().then((res) => {
      util.downloadFile(res, "索引库.xlsx");
    });
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(record: any) {
    idxDbApi.modifyIdxDbInstBase(record).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
});
const modelValue = reactive({
  metaRegionList: [],
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => {});
//销毁
onUnmounted(() => {
  intervalClinet.value?.disconnect();
});
//事件声明
const emit = defineEmits(["edit-data-inst", "statistic-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.idx-db-inst-table {
  height: 100%;
}
</style>
