<template>
  <el-col class="idx-db-preview-index">
    <el-row :gutter="10">
      <el-col :span="4" v-if="!isOfflineDataset">
        <el-card>
          <el-input v-model="filterText" placeholder="请输入名称" @keyup.enter="filterStr = filterText">
            <template #append>
              <el-button :icon="Search" @click="filterStr = filterText" />
            </template>
          </el-input>
          <el-tree ref="treeRef" :data="sitesTree" node-key="site" default-expand-all :expand-on-click-node="false">
            <template #default="{ node, data }">
              <span class="tree-node" :class="{ active: data.site == modelValue.site }" @click="showDataItem(node, data)">{{ data.name }}</span>
            </template>
          </el-tree>
        </el-card>
      </el-col>
      <el-col :span="!isOfflineDataset ? 20 : 24">
        <el-card>
          <div class="preview-table-parent">
            <preview-table ref="previewTableRef"></preview-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-col>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import { Search } from "@element-plus/icons-vue";
import * as idxDbApi from "@/api/idx-db";
import * as datasetApi from "@/api/dataset";
import * as previewApi from "@/api/preview";

const { $router, $app, proxy } = useCtx();

const idxDbInstId = $router.currentRoute.value.query.idxDbInstId;

//筛选框数据(前端搜索)
const filterText = ref("");
const filterStr = ref("");
//表单数据项
const modelValue = reactive({
  idxDbInstData: {},
  sites: [],
  site: "root",
});
const isOfflineDataset = computed(() => {
  return modelValue.idxDbInstData.type == 1;
});
//初始化站点列表:来源于数据库
const getSiteList = async () => {
  previewApi.getPreviewCapacityFromRegion(idxDbInstId, modelValue.idxDbInstData.idInRegion, modelValue.idxDbInstData.region).then((result) => {
    modelValue.sites = result.content.siteCapacity;
  });
  const datasetData = await datasetApi.getDataset(modelValue.idxDbInstData.datasetId);
  const fields =
    datasetData.fields?.filter((item) => {
      return item.show;
    }) || [];
  loadList("fromRegion", idxDbInstId, {
    indexId: modelValue.idxDbInstData.idInRegion,
    region: modelValue.idxDbInstData.region,
    site: "",
    realtime: modelValue.idxDbInstData.realtime,
    type: modelValue.idxDbInstData.type,
    datasetId: modelValue.idxDbInstData.datasetId,
    fields: fields,
  });
};
//树形结构数据
const sitesTree = computed(() => {
  let list = [
    {
      site: "root",
      name: "全部站点",
      children: [],
    },
  ];
  list[0].children.push(...modelValue.sites.filter((item) => item.name.includes(filterStr.value)));
  return list;
});
//刷新列表
const showDataItem = (node, data) => {
  modelValue.site = data.site;
  if (node.level == 1) {
    loadList("fromRegion", idxDbInstId, {
      indexId: modelValue.idxDbInstData.idInRegion,
      region: modelValue.idxDbInstData.region,
      site: "",
      realtime: modelValue.idxDbInstData.realtime,
    });
    return;
  }
  if (node.level == 2) {
    loadList("fromRegion", idxDbInstId, {
      indexId: modelValue.idxDbInstData.idInRegion,
      region: modelValue.idxDbInstData.region,
      site: modelValue.site,
      realtime: modelValue.idxDbInstData.realtime,
    });
    return;
  }
};
const loadList = (type: string, id: string, extraParams: any) => {
  proxy.$refs["previewTableRef"].loadList(type, id, extraParams);
};
//初始化
onMounted(async () => {
  modelValue.idxDbInstData = await idxDbApi.getIdxDbInst(idxDbInstId);
  getSiteList();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.idx-db-preview-index {
  height: 100%;

  .el-tree {
    height: calc(100vh - 242px);
    --el-tree-node-hover-bg-color: none;

    > .el-tree-node.is-expanded.is-focusable {
      height: 100%;
      > .el-tree-node__children {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }

    .active {
      background-color: $info-bg;
      border-radius: 5px;
    }
  }

  .tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
  }

  .el-tree-node__content {
    padding: 20px 0;
  }

  .preview-table-parent {
    height: calc(100vh - 210px);
  }
}
</style>
