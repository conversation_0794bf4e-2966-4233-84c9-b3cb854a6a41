<template>
  <div class="idx-db-statistic-site-table">
    <myTable ref="myTableRef" :columns="columns" :tableData="tableData" :transformListData="transformListData" :defaultPageSizes="[100, 200]"> </myTable>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from "vue";
import { keys, assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";
import * as previewApi from "@/api/preview";
import * as util from "@/utils/common";

const props = defineProps(["datasetVersionId"]);

const { $app, proxy } = useCtx();
const tableData = ref([]);
//列配置
const columns = ref([
  { prop: "name", label: "名称", width: 200 },
  { prop: "site", label: "Site", width: 160 },
  { prop: "description", label: "描述", minWidth: 200 },
  { prop: "applicableScenarios", label: "适用场景", minWidth: 200 },
  { prop: "countRender", label: "数据总量", width: 120, fixed: "right" },
]);
//获取数据集版本详细信息:来源于数据库
const getDatasetVersion = () => {
  return new Promise((resolve: any) => {
    datasetApi.getDatasetVersion(props.datasetVersionId).then((result) => {
      resolve(result);
    });
  });
};
//列表查询
const loadListData = async () => {
  //获取数据集版本信息
  if (dataC.isEmpty(modelValue.datasetVersionData)) {
    const datasetVersionData = await getDatasetVersion();
    modelValue.datasetVersionData = datasetVersionData;
  }
  return new Promise((resolve: any) => {
    siteApi.getSiteVersionListByDatasetVersionId(props.datasetVersionId).then((result) => {
      //返回数据,直接赋值会让vue响应不及时，这里采用先删再增的方法
      tableData.value.splice(0, tableData.value.length);
      tableData.value.push(...result.content);
      loadList();
      //增加数据总量查询
      getCountList();
    });
  });
};
//获取数据总量数据
const getCountList = () => {
  modelValue.count = "统计中";

  //获取每个站点的数据总量
  const ids = modelValue.datasetVersionData.siteVersionIds;
  tableData.value.forEach((item) => {
    const temp = item.siteVersionList.map((item) => {
      return item.id;
    });
    const res = temp.filter((x) => ids.some((y) => y == x));
    item.siteVersionId = !dataC.isEmpty(res) ? res[0] : "";
    previewApi.getCountFromDatasetVersionSite(props.datasetVersionId, { s: 0 }, item.siteDTO.site).then((result) => {
      tableData.value.forEach((x: any) => {
        if (x.siteVersionId == item.siteVersionId) {
          x.countRender = util.formatNumber(result.data);
        }
      });

      modelValue.count = tableData.value.reduce((sum, item) => {
        return sum + Number(item.countRender?.replace(/,/g, "") || 0);
      }, 0);
      loadList();
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    keys(x.siteDTO).forEach((key) => {
      x[key] = x.siteDTO[key];
    });
    return x;
  });
};
//数据项
const modelValue = reactive({
  datasetVersionData: null,
  count: 0,
});
const loadList = () => {
  proxy.$refs["myTableRef"].loadData();
};
//初始化
onMounted(() => {
  nextTick(() => {
    loadListData();
  });
});
const getCount = () => {
  return modelValue.count;
};
const getColumns = () => {
  return columns.value;
};
const getTableData = () => {
  return tableData.value;
};
//接口暴露
defineExpose({
  loadList,
  loadListData,
  getCount,
  getColumns,
  getTableData,
});
</script>
<style lang="scss">
.idx-db-statistic-site-table {
  height: calc(100% - 60px);

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
