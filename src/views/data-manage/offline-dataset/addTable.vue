<template>
  <my-drawer class="offline-dataset-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="1050">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit" label-width="80px"> </my-form>
    <div class="offline-dataset-edit-table">
      <my-table
        ref="myTableRef"
        :columns="columns"
        :query="query"
        :tableData="tableData"
        :transformListData="transformListData"
        :withPagination="false"
        :withOrder="false"
      >
        <template #query>
          <div class="flexBetweenStart">
            <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          </div>
        </template>
        <template #label-selected>
          <el-checkbox v-model="selectedAll" @change="events.selectedAll" label="选择" />
        </template>
        <template #selected="scope">
          <el-checkbox :value="true" v-model="scope.row.selected" @change="events.selectedChange(scope.row)" />
        </template>
        <template #label-show>
          <el-checkbox v-model="showAll" @change="events.showAll" label="预览" />
        </template>
        <template #show="scope">
          <el-checkbox :value="true" v-model="scope.row.show" @change="events.showChange(scope.row)" />
        </template>
      </my-table>
    </div>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch, watchEffect } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as offlineDatasetApi from "@/api/offline-dataset";
import * as datasetApi from "@/api/dataset";
import { NAME_RULE, CODE_RULE } from "@/utils/validate";
import { table } from "node:console";
// Props
const props = defineProps({
  spaceId: { type: String },
});
const query = ref<any>({ search: "" });
const { $app, proxy, $router } = useCtx();
const { api } = useStore();
// Dialog相关
const isUpdate = computed(() => formType.value === "edit");
const existVersion = ref(false);
const selectedAll = ref(false);
const showAll = ref(false);
const dialogTitle = computed(() => (!isUpdate.value ? "新增离线数据集" : !existVersion.value ? "编辑离线数据集(无数据)" : "编辑离线数据集(有数据)"));
const dialogVisible = ref(false);
const columns = computed(() => {
  const list =
    isUpdate.value && existVersion.value ? [] : [{ prop: "selected", labelSlotName: "label-selected", width: 75, slotName: "selected", sortable: false }];
  list.push(
    ...[
      { prop: "show", labelSlotName: "label-show", width: 75, slotName: "show", sortable: false },
      { prop: "category", label: "类别", width: 110 },
      { prop: "fieldName", label: "字段", width: 140 },
      { prop: "type", label: "类型", width: 100 },
      { prop: "name", label: "名称", minWidth: 120 },
      { prop: "vectorRender", label: "支持向量", width: 110 },
      { prop: "tokenizationRender", label: "支持分词", width: 110 },
      { prop: "filterRender", label: "支持过滤", width: 110 },
    ]
  );
  return list;
});
// 默认表单
const defaultForm = reactive({
  id: "",
  name: "",
  fields: [],
  encryption: false,
  description: "",
  enName: "",
});
// 表单相关
const formType = ref("add");
const formRef = ref(null);
const myTableRef = ref();
const ruleForm = ref(assign({}, defaultForm));
const modelValue = reactive({
  metaSiteFieldList: [], //table根本数据源
});
const operateTableData = ref([]); //存储用户操作后的数据
const tableData = computed(() => {
  //页面显示的table数据
  return modelValue.metaSiteFieldList
    .filter((item) => {
      if (isUpdate.value && existVersion.value) {
        const field = dataC.getItemByValue(operateTableData.value, item.fieldName, "fieldName");
        //没被选择的直接不要
        if (!field.selected) return false;
        //被搜索项命中的才返回
        return item.category?.includes(query.value.search) || item.name?.includes(query.value.search) || item.fieldName?.includes(query.value.search);
      } else {
        //被选中的直接返回
        const field = dataC.getItemByValue(operateTableData.value, item.fieldName, "fieldName");
        if (field.selected) return true;
        //被搜索项命中的也返回
        return item.category?.includes(query.value.search) || item.name?.includes(query.value.search) || item.fieldName?.includes(query.value.search);
      }
    })
    .map((item) => {
      //根据用户操作后的数据回显table
      const field = dataC.getItemByValue(operateTableData.value, item.fieldName, "fieldName");
      const res = { ...item, selected: field.selected, show: field.show };
      return res;
    });
});
// 表单规则
const rules = computed(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  enName: [{ required: true, trigger: "blur", message: "英文名不能为空" }],
}));
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  //是否选择全选与否
  selectedAll: (newVal: boolean) => {
    operateTableData.value.forEach((item) => {
      item.selected = newVal;
    });
  },
  selectedChange: (row: string) => {
    //记录用户的选中操作
    operateTableData.value.forEach((item) => {
      if (item.fieldName == row.fieldName) {
        item.selected = row.selected;
      }
    });
  },
  //是否预览全选与否
  showAll: (newVal: boolean) => {
    operateTableData.value.forEach((item) => {
      item.show = newVal;
    });
  },
  showChange: (row: string) => {
    //记录用户的是否预览操作
    operateTableData.value.forEach((item) => {
      if (item.fieldName == row.fieldName) {
        item.show = row.show;
      }
    });
  },
});
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit();
    }
  });
};
// 表单项
const formItems = ref({
  name: {
    label: "名称",
    type: "input",
    attrs: { maxlength: 100, placeholder: NAME_RULE },
  },
  enName: {
    label: "英文名",
    type: "input",
    attrs: { maxlength: 100, placeholder: CODE_RULE },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  encryption: {
    label: "是否加密",
    type: "switch",
    disabled: () => isUpdate.value && existVersion.value,
  },
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "类别 或 名称 或 字段",
    },
  },
});
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any, index: number) => {
    x.showRender = x.show ? "是" : "-";
    x.universalRender = x.universal ? "是" : "-";
    x.vectorRender = x.vector ? "是" : "-";
    x.tokenizationRender = x.tokenization ? "是" : "-";
    x.filterRender = x.filter ? "是" : "-";
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

// 方法：打开窗口
const openDialog = async (type: string, row: any) => {
  //清空table查询条件
  queryItems.value.search.modelValue = "";
  query.value.search = "";
  //设置表单类型，渲染表单
  formType.value = type;
  //如果是编辑，则查询有无版本
  if (isUpdate.value) {
    const list = await datasetApi.getDatasetVersionList(row.id);
    existVersion.value = list.totalElements > 0;
  }
  dialogVisible.value = true;
  nextTick(() => {
    //先清空用户的操作数据
    operateTableData.value.forEach((item) => {
      item.selected = false;
      item.show = false;
    });
    //表单数据
    ruleForm.value = pick({ ...defaultForm, ...row }, keys(defaultForm));
    //table数据
    operateTableData.value.forEach((item) => {
      const field = dataC.getItemByValue(ruleForm.value.fields, item.fieldName, "fieldName");
      if (!dataC.isEmpty(field.fieldName)) {
        item.selected = true;
        item.show = field.show;
      }
    });
    //设置全选项的选中情况
    selectedAll.value = operateTableData.value.every((item) => item.selected);
    showAll.value = operateTableData.value.every((item) => item.show);
  });
};

// 方法：关闭窗口
const handleClose = () => {
  formRef.value?.resetForm();
  dialogVisible.value = false;
};
// 方法：提交表单
const submit = async () => {
  ruleForm.value.fields = operateTableData.value
    .filter((item) => {
      return item.selected;
    })
    .map((item: any) => {
      return {
        fieldName: item.fieldName,
        name: item.name,
        filter: item.filter,
        show: item.show,
      };
    });
  if (dataC.isEmpty(ruleForm.value.fields)) {
    $app.$message.warning("字段不可为空!");
    return;
  }
  const params = { ...ruleForm.value, spaceId: props.spaceId, type: 1 };
  if (isUpdate.value) {
    await offlineDatasetApi.editDic(ruleForm.value.id, params);
    $app.$message.success("修改成功");
  } else {
    await offlineDatasetApi.addDic(params);
    $app.$message.success("新增成功");
  }
  emit("reload");
  handleClose();
};
//初始化站点信息
onMounted(async () => {
  const res = await api.getMetaSiteFieldList();
  const list = res.map((item) => {
    return { ...item, selected: false, show: false };
  });
  modelValue.metaSiteFieldList = cloneDeep(list);
  operateTableData.value = cloneDeep(list);
});
// 事件声明
const emit = defineEmits(["reload"]);
// 接口暴露
defineExpose({ openDialog });
</script>

<style lang="scss">
.offline-dataset-edit-table {
  height: calc(100vh - 390px);
  .table-page-wrapper {
    .table-wrapper {
      height: calc(100% - 50px) !important;
      .el-table {
        height: 100%;
      }
    }
    .el-checkbox__label {
      font-weight: bold;
    }
  }
}
</style>
