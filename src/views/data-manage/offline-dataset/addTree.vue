<template>
  <my-drawer
    class="dataset-edit"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
    size="800px"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      @submit="submit"
    >
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as metaWordApi from "@/api/offline-dataset";

const props = defineProps({  
  treeData: { type: Array },
  treeNode: { type: Object },
  type: { type: String },
  showAreaType: { type: Boolean, default: false },
});
const { $app, proxy } = useCtx();
const { word } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  parentId: null,
  name: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  parentId: {
    label: "父级菜单",
    type: "select",
    options: [],
    hidden: () => {
      return ruleForm.value.parentId == null;
    },
    attrs: {
      disabled: true,
      maxlength: 255,
      placeholder: "请输入父级菜单",
    },
  },
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入名称",
    },
  },
});
//打开窗口
const openDialog = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    } else {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    }
  });
};
//提交数据
const submit = (form: any) => {
  if (isUpdate.value) {
    metaWordApi.editTree(ruleForm.value.id, form).then(() => {
      $app.$message.success("修改成功");
      emit("reload", props.treeNode);
      handleClose();
    });
  } else {
    metaWordApi.createTree(form).then(() => {
      $app.$message.success("新增成功");
      emit("reload");
      handleClose();
    });
  }
};
const flattenTree = (tree: any[]) => {
  let result: any[] = [];

  function flattenNode(node: any) {
    result.push({ label: node.name, value: node.id }); // 将当前节点添加到结果数组中
    if (node.children && node.children.length > 0) {
      // 如果节点有子节点，递归扁平化子节点
      node.children.forEach(flattenNode);
    }
  }

  tree.forEach(flattenNode); // 对树中的每个节点应用扁平化
  return result;
};
// watch监听
watch(
  () => props.treeData,
  (val) => {
    formItems.value.parentId.options = flattenTree(val as any[]);
  },
  { deep: true }
);
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["reload"]);
//接口暴露
defineExpose({
  openDialog,
});
</script>
<style lang="scss"></style>
