<template>
  <page-wrapper route-name="offline-dataset::">
    <div class="meta-dictionary-main">
    <MetaDictionaryTree
      ref="treeRef"
      :treeData="treeData"
      :anasisList="anasisList"
      @updateTree="getTreeData"
      @updateTable="getTableData"
      :operationAuth="operationAuth"
    />
    <MetaWordTable
      ref="wordTableRef"
      :treeNode="treeNode"
      :treeData="treeData"
      @updateTree="getTreeData"
      :operationAuth="operationAuth"
    />
  </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import MetaWordTable from "./table.vue";
import MetaDictionaryTree from "./tree.vue";
import * as metaWordDbApi from "@/api/offline-dataset";
import * as commonApi from "@/api/common";
import { computed } from "vue";
import * as util from "@/utils/common";
const { word } = useStore();
const operationAuth ='/base/#/evaluation-reslut/edit'
const treeRef = ref();
const { $app, proxy, $router } = useCtx();
const routeQuery =computed(()=> $app.$route.query);
const wordTableRef = ref();
const treeNode = computed(()=>treeRef.value?.getCurrentNode());
let treeData = ref<any>([]);
  function findNodeById(nodes, targetId) {
  for (const node of nodes) {
    if (node.id === targetId) return node;
    if (node.children?.length) {
      const found = findNodeById(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
}
const getTreeData = async (key?: any) => {
  console.log(1234);
  
  const filterText = treeRef.value.filterText;
  const params = { search: filterText };
  const treeRes: any = await metaWordDbApi.getTree(params);
  treeData.value = treeRes.data;
  
  const categoryId = findNodeById(treeRes.data,routeQuery.value.spaceId)?routeQuery.value.spaceId:treeRes.data?.[0]?.id
  treeRef.value.setCurrentKey(key||categoryId||treeRes.data?.[0]?.id);
};
const getTableData = () => {
  wordTableRef.value && wordTableRef.value.loadList();
};
const anasisList = ref([])
// 场景策略
const getAnasisList = async (name='') => {
  const res = await commonApi.getSceneVersion({ name });
  anasisList.value = res.data.map((item: any) => ({
    ...item,
    label:`${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
getAnasisList()

</script>
<style scoped lang="scss">
.meta-dictionary-main {
  display: flex;
  height: 100%;
  padding: 10px;
  ::v-deep {
    .left-card {
      overflow: auto;
      .el-card__body {
        padding: 10px;
      }
    }
    .meta-dict-table {
      padding-left: 10px;
      width: calc(100% - 375px);
      .info-card {
        height: 100%;
        .el-card__body {
          padding: 0;
          height: 100%;
        }
      }
    }
  }
}
</style>
