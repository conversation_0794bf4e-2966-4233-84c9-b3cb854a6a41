<template>
  <div class="offline-dataset-preview-index">
    <preview-table ref="previewTableRef"></preview-table>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import * as datasetApi from "@/api/dataset";

const { $router, proxy } = useCtx();

const datasetId = $router.currentRoute.value.query.datasetId;
const datasetVersionId = $router.currentRoute.value.query.datasetVersionId;

const loadList = async () => {
  //额外参数{ datasetId, datasetVersionId }是给查询正文用的,fields是做动态显示列用的
  //先渲染一个空列的的table
  const emptyFields = [];
  proxy.$refs["previewTableRef"].loadList("fromOfflineDatasetVersion", datasetVersionId, { datasetId, datasetVersionId, emptyFields });
  //再根据远程信息渲染一个实际的table
  const datasetData = await datasetApi.getDataset(datasetId);
  const fields =
    datasetData.fields?.filter((item) => {
      return item.show;
    }) || [];
  proxy.$refs["previewTableRef"].loadList("fromOfflineDatasetVersion", datasetVersionId, { datasetId, datasetVersionId, fields });
};
//初始化
onMounted(() => {
  loadList();
});
</script>
<style lang="scss">
.offline-dataset-preview-index {
  height: 100%;
  padding: 12px 16px;
}
</style>
