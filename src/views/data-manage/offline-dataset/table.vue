<template>
  <div class="meta-dict-table" v-show="treeNode?.id">
    <el-card class="info-card">
      <table-page
        ref="myWordTableRef"
        :columns="columns"
        :query="query"
        :loadDataApi="loadListData"
        :transformListData="transformListData"
        :loadImmediately="false"
        :operationAuth="operationAuth"
        :operations="operations"
        @operation="handleOperation"
      >
        <template #query>
          <div class="flexBetweenStart">
            <my-query
              :queryItems="queryItems"
              :refresh-btn="{ show: true }"
              @search="events.search"
              @reset="events.reset"
            />
            <my-operation>
              <template #buttonGroup>
                <my-button type="add" @click="events.add"
                  >新建离线数据集</my-button
                >
                <my-button type="export" @click="events.export"
                  >导出</my-button
                >
              </template>
            </my-operation>
          </div>
        </template>
        <template #header>
          <div class="header">
            <span>{{ treeNode?.name }}</span>
          </div>
        </template>
      </table-page>
    </el-card>
    <AddDialog
      ref="addRef"
      @reload="loadList"
      :spaceId="treeNode?.id"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaWordDbApi from "@/api/offline-dataset";
import useStore from "@/store";
import { assign, pick, keys, cloneDeep } from "lodash";
import * as util from "@/utils/common";
import * as commonApi from "@/api/common";
import { useI18n } from "vue-i18n";
import AddDialog from "./addTable.vue";
const { $app, proxy, $router } = useCtx();
const { word } = useStore();
const { t } = useI18n();
const props = defineProps({
  treeNode: { type: Object, default: {} },
  treeData: { type: Array },
  operationAuth: { type: String },
});

//列配置
const defaultColumns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "name",
    label: "名称",
    minWidth: 240,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        $router.push({
          name: `offline-dataset::version`,
          query: {
            datasetId: record.id,
            metaLabel: [record.name],
            spaceId: props.treeNode.id,
          },
        });
      },
    },
  },
  // 文本可复制
  {
    prop: "enName",
    label: "英文名",
    minWidth: 190,
  },
  {
    prop: "code",
    label: "编码",
    minWidth: 190,
    withCopy: true,
  },
  {
    prop: "description",
    label: "描述",
    minWidth: 240,
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 150, fixed: "right" },
]);

const operations = [
  { type: "copy", label: t("btn.copy") },
  { type: "edit", label: t("btn.edit") },
  {
    type: "delete",
    label: t("btn.delete"),
    btnType: "danger",
    disabled: (record: any) => record.relates,
    disabledTips: "存在已关联产品的版本禁止删除",
  },
];
//事件列表
const events = reactive({
  copy: (record: any) => {
    const regionRecord = cloneDeep(record);
    regionRecord.name = `${regionRecord.name}-副本`;
    regionRecord.enName = `${regionRecord.enName}-副本`;
    delete regionRecord.id;
    proxy.$refs.addRef?.openDialog("add", regionRecord);
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: (type: string, parentId: any, level: number, data?: any) => {
    const params = { ...data, level, parentId };
    proxy.$refs.addRef?.openDialog("add", params);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除"${record.name}"这条数据吗？`,
      })
      .then(() => {
        metaWordDbApi.deleteDic(record.id).then(() => {
          loadList();
        });
      });
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  export: () => {
    metaWordDbApi
      .exportDic({ spaceId: props.treeNode.id, type: 1 })
      .then((result) => {
        util.downloadFile(result, `${props.treeNode.name}.xlsx`);
      });
  },
});
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
const columns = ref([]);
//查询面板
const query = ref<any>({});
const value1 = ref(false);
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "名称 或 英文名 或 编码",
    },
  },
});
const handleParams = (params) => {
  if (params.time) {
    params.startTime = params.time[0];
    params.endTime = params.time[1];
    delete params.time;
  }
  return params;
};
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = {
      ...data,
      spaceId: props.treeNode.id,
      type: 1,
    };
    if (!props.treeNode.id) {
      return;
    }
    metaWordDbApi.getDicPage(handleParams(params)).then((result) => {
      const arr = util.generateTableColumns(result.content);
      columns.value = defaultColumns.value.concat([...arr]);
      result.content = result.content.map((item: any) => ({
        ...item,
        ...item.extendMap,
      }));
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

const loadList = () => {
  proxy.$refs.myWordTableRef?.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "updateTree"]);
//接口暴露
defineExpose({
  loadList,
});
// watch监听
watch(
  () => props.treeNode,
  (val: any) => {
    if (!val) {
      return;
    }
    value1.value = val.enabled;
    loadList();
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.meta-dict-table {
  height: 100%;
  .header {
    span {
      margin-right: 20px;
    }
    span:first-child {
      font-weight: 550;
    }
    ::v-deep {
      .el-switch {
        height: 0px;
        margin-left: 10px;
      }
    }
  }
}
::v-deep {
  .t-query {
    width: 80%;
    flex: 1;
  }
}
</style>
