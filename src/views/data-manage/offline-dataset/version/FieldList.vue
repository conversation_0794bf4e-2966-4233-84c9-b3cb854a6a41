<template>
  <my-drawer v-model="dialogVisible" :title="dialogTitle" :width="1000" :showConfirm="false" :showClose="false" class="offline-dataset-field-drawer">
    <div class="offline-dataset-field-list">
      <my-table ref="myTableRef" :columns="columns" :tableData="tableData" :transformListData="transformListData" :withPagination="false"> </my-table>
    </div>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";

const { $app, proxy } = useCtx();
const { api } = useStore();

const props = defineProps({
  fields: { type: Array, default: [] },
});

const modelValue = reactive({
  metaSiteFieldList: [],
});

const tableData = computed(() => {
  const siteFields = modelValue.metaSiteFieldList;
  siteFields.forEach((item) => {
    const field = dataC.getItemByValue(props.fields, item.fieldName, "fieldName");
    if (!dataC.isEmpty(field.fieldName)) {
      item.selected = true;
      item.show = field.show;
    }
  });
  return siteFields.filter((item) => {
    return item.selected;
  });
});

const dialogTitle = computed(() => {
  return "字段配置";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  dialogVisible.value = false;
};

const columns = ref([
  { prop: "showRender", label: "是否预览", width: 110 },
  { prop: "category", label: "类别", width: 110 },
  { prop: "fieldName", label: "字段", width: 140 },
  { prop: "type", label: "类型", width: 100 },
  { prop: "name", label: "名称", width: 120 },
  { prop: "vectorRender", label: "支持向量", width: 110 },
  { prop: "tokenizationRender", label: "支持分词", width: 110 },
  { prop: "filterRender", label: "支持过滤", width: 110 },
]);
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.showRender = x.show ? "是" : "-";
    x.universalRender = x.universal ? "是" : "-";
    x.vectorRender = x.vector ? "是" : "-";
    x.tokenizationRender = x.tokenization ? "是" : "-";
    x.filterRender = x.filter ? "是" : "-";
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
const openDialog = (productId: string) => {
  dialogVisible.value = true;
  nextTick(() => {
    proxy.$refs.myTableRef.loadData();
  });
};

//初始化站点信息
onMounted(async () => {
  modelValue.metaSiteFieldList = await api.getMetaSiteFieldList();
});

defineExpose({ openDialog });
</script>

<style lang="scss">
.offline-dataset-field-drawer {
  .el-drawer__body {
    padding: 0;
    .offline-dataset-field-list {
      height: 100%;
    }
  }
}
</style>
