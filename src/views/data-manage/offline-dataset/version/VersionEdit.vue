<template>
  <my-drawer
    class="offline-dataset-version-edit"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
    :showConfirm="ruleForm.status != 2 && testAuth()"
    :showClose="ruleForm.status != 2 && testAuth()"
    size="800px"
  >
    <el-form ref="formRef" :model="ruleForm" :rules="rules" label-width="100px" :disabled="ruleForm.status == 2 || !testAuth()">
      <el-form-item label="名称 :" prop="name">
        <el-input v-model="ruleForm.name" placeholder="请输入名称" clearable />
      </el-form-item>
      <el-form-item label="版本 :" prop="versionRender" v-if="isUpdate"> v{{ ruleForm.versionRender }} </el-form-item>
      <el-form-item label="描述 :" prop="description">
        <el-input type="textarea" v-model="ruleForm.description" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
  </my-drawer>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";
import * as datasetApi from "@/api/dataset";
import * as util from "@/utils/common";

const { $router, $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/offline-dataset/edit");
};
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value === "add") return "新增离线数据集版本";
  if (formType.value === "edit") return "编辑离线数据集版本";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  proxy.$refs["formRef"].resetFields();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  submit();
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const defaultForm = {
  datasetId: "",
  id: "",
  name: "",
  versionRender: "",
  description: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  versionRender: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
//打开窗口
const openWindow = (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
      ruleForm.value.status = row.status;
    } else {
      ruleForm.value = assign({}, defaultForm, row);
      ruleForm.value.status = 1;
    }
    proxy.$refs["formRef"].resetFields();
  });
};
//提交数据
const submit = (form: any) => {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      const formData = cloneDeep(ruleForm.value);
      //offline:true,告知后端是离线数据集
      formData.offline = true;
      delete formData.versionRender;
      if (isUpdate.value) {
        datasetApi.modifyDatasetVersion(formData).then((result) => {
          $app.$message.success("修改成功");
          emit("save-data");
          handleClose();
        });
      } else {
        datasetApi.insertDatasetVersion(formData).then((result) => {
          $app.$message.success("新增成功");
          emit("save-data");
          handleClose();
        });
      }
    }
  });
};
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
.offline-dataset-version-edit {
}
</style>
