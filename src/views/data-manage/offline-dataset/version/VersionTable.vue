<template>
  <div class="offline-dataset-version-table">
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <table-page
      ref="myTableRef"
      name="offline-dataset-version-table"
      :columns="columns"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :defaultSort="{ prop: 'version', order: 'desc' }"
      operationAuth="/base/#/offline-dataset/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #process="scope">
        <task-process
          :id="scope.row.id"
          :taskId="scope.row.taskId"
          :taskType="scope.row.taskType"
          :taskStatus="scope.row.taskStatus"
          :taskList="scope.row.taskDetailList"
          @cancel-task="loadList"
          :disabled="!testAuth()"
        ></task-process>
      </template>
    </table-page>
    <my-drawer direction="ltr" class="mock-add" width="1500px" v-model="historyVisible" title="历史任务" :showConfirm="false" @close="historyVisible = false">
      <HistoryTask ref="historyTaskRef" :dataType="3" :queryDisplay="false"> </HistoryTask>
    </my-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, onUnmounted, nextTick } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";
import taskProcess from "@/views/common/task/TaskProcess.vue";
import * as datasetApi from "@/api/dataset";
import * as util from "@/utils/common";
import * as taskApi from "@/api/task";
import IntervalClient from "@/utils/interval-client";
import HistoryTask from "@/views/common/task/TaskHistory.vue";
const props = defineProps(["datasetId", "datasetName"]);

const { $app, proxy, $auth } = useCtx();
const historyVisible = ref(false);
const testAuth = () => {
  return $auth.testAuth("/base/#/offline-dataset/edit");
};
//列配置
const columns = ref([
  {
    prop: "nameRender",
    label: "名称",
    width: 250,
    custom: "editLink",
    blod: true,
    customRender: {
      linkClick: (record: any) => {
        emit("preview-data", record.id, record.nameRender);
      },
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  {
    prop: "description",
    label: "描述",
    minWidth: 200,
    custom: "editButton",
    customRender: {
      btnClick: (record: any) => {
        events.openDescriptionEditWindow(record);
      },
      btnDisabled: (record: any) => {
        return !testAuth();
      },
    },
  },
  {
    prop: "process",
    label: "任务进度",
    slotName: "process",
    width: 250,
    showOverflowTooltip: false,
    sortable: false,
  },
  {
    prop: "status",
    label: "发布状态",
    width: 120,
    custom: "status",
    customRender: {
      options: {
        1: { type: "info", name: "草稿" },
        2: { type: "success", name: "已发布" },
      },
    },
  },
  { prop: "publishTsRender", label: "发布时间", width: 180 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 220, fixed: "right" },
]);
//列表查询
const loadListData = (data: any) => {
  const { page, size } = data;
  return new Promise((resolve: any) => {
    datasetApi.getDatasetVersionListPage(props.datasetId, data.page, data.size, data.sort).then((result) => {
      //增加任务监控
      getIntervalClinet(result.content);
      //返回数据
      resolve(result);
    });
  });
};
//定时任务监控
const intervalClinet = ref(null);
const getIntervalClinet = (tableData: Array<any>) => {
  //如果已有定时任务对象，则停止并创建新的
  intervalClinet.value?.disconnect();
  //如果列表为空 则不创建新的
  if (dataC.isEmpty(tableData)) return;
  // 获取定时任务对象并启动,以持续刷新任务信息
  intervalClinet.value = new IntervalClient(3000, true);
  intervalClinet.value.onHandler(getTaskProgressListByTask, tableData).connect();
};
//获取任务信息
const getTaskProgressListByTask = (tableData: Array<any>) => {
  const taskIdList = tableData
    .filter((x) => {
      return !dataC.isEmpty(x.taskId);
    })
    .map((x: any) => {
      return x.taskId;
    });
  if (dataC.isEmpty(taskIdList)) return;
  taskApi.getTaskProgressListByTask(taskIdList).then((result) => {
    if (!(result.content instanceof Array)) return;
    const list = tableData.map((x: any) => {
      const task = dataC.getItemByValue(result.content, x.taskId, "taskId");
      if (!dataC.isEmpty(task.taskId)) {
        x.taskStatus = task.status;
        x.taskType = task.type;
        x.taskDetailList = task.taskDetailList;
      }
      return x;
    });
    proxy.$refs["myTableRef"].setTableData(list);
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.versionRender = util.padNumberToDigits(x.version, 3);
    x.nameRender = `${x.name}(v${x.versionRender})`;
    x.publishTsRender = timeC.format(x.publishTs, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
    //只有草稿状态的版本可编辑
    disabled: (record: any) => record.status == 2,
    disabledTips: "已发布，不可编辑",
  },
  {
    type: "compute",
    label: "构建",
    //已发布或者任务正在执行中，不可构建
    disabled: (record: any) => record.status == 2 || record.taskStatus == 2 || record.taskStatus == 3,
    disabledTips: (record: any) => {
      if (record.status == 2) {
        return "已发布，不可构建";
      }
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可构建";
      }
      if (record.taskStatus == 3) {
        return "任务已完成，不可构建";
      }
      return "不可构建";
    },
  },
  {
    type: "dedup",
    label: "去重",
    //上一次构建任务处于成功状态,且为草稿状态 可去重
    disabled: (record: any) =>
      record.taskStatus == 2 || (record.taskType == 6 && record.taskStatus != 3),
    disabledTips: (record: any) => {
      if (record.taskStatus == 2) {
        return "任务正在执行中，不可去重";
      }
      if (record.taskType == 6 && record.taskStatus != 3) {
        return "上次构建任务未成功，不可去重";
      }
      if (dataC.isEmpty(record.taskType)) {
        return "未执行构建任务，不可去重";
      }
      return "不可去重";
    },
  },
  {
    type: "publish",
    label: "发布版本",
    collapsed: true,
    //只有草稿状态的版本，且构建任务或去重任务的状态为完成状态可发布
    disabled: (record: any) => record.status == 2 || record.taskStatus != 3,
    disabledTips: (record: any) => {
      if (record.status == 2) {
        return "已发布，不可多次发布";
      }
      if (record.taskType == 6 && record.taskStatus != 3) {
        return "上次构建任务未成功，不可发布";
      }
      if (record.taskType == 7 && record.taskStatus != 3) {
        return "上次去重任务未成功，不可发布";
      }
      return "不可发布";
    },
  },
  { type: "copy", label: "复制版本", collapsed: true },
  {
    type: "delete",
    label: "删除版本",
    btnType: "danger",
    collapsed: true,
    //非执行状态  可删除版本
    disabled: (record: any) => record.taskStatus == 2,
    disabledTips: "任务正在执行中，不可删除",
  },
  {
    type: "clear",
    label: "删除数据",
    btnType: "danger",
    collapsed: true,
    //非执行状态  可删除规则数据
    disabled: (record: any) => record.taskStatus == 2,
    disabledTips: "任务正在执行中，不可删除",
  },
  {
    type: "history",
    label: "历史任务",
    btnType: "primary",
    auth: "all",
    collapsed: true,
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  history: (record: any) => {
    historyVisible.value = true;
    if (dataC.isEmpty(proxy.$refs.historyTaskRef)) {
      nextTick(() => {
        proxy.$refs.historyTaskRef.loadList(record.id);
      });
    } else {
      proxy.$refs.historyTaskRef.loadList(record.id);
    }
  },
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  compute: (record: any) => {
    $app.$confirm({ title: `确定构建 ${record.nameRender} ?` }).then(() => {
      //offline:true,告知后端是离线数据集
      datasetApi.computeDatasetVersion(record.id, { offline: true }).then((result) => {
        loadList();
        $app.$message.success("已添加构建任务");
      });
    });
  },
  dedup: (record: any) => {
    emit("dedup-data", record);
  },
  publish: (record: any) => {
    $app.$confirm({ title: `确定发布 ${record.nameRender} ?` }).then(() => {
      datasetApi.publishDatasetVersion(record.id).then((result) => {
        loadList();
        $app.$message.success(`发布 ${record.nameRender} 成功`);
      });
    });
  },
  copy: (record: any) => {
    $app.$confirm({ title: `确定复制 ${record.nameRender}?` }).then(() => {
      datasetApi.copyDatasetVersion(record.id).then((result) => {
        loadList();
        $app.$message.success(`复制 ${record.nameRender} 成功`);
      });
    });
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.nameRender}?`,
      })
      .then(() => {
        datasetApi.removeDatasetVersion(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.nameRender} 成功`);
        });
      });
  },
  clear: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.nameRender} 数据吗?`,
      })
      .then(() => {
        datasetApi.clearDatasetVersion(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.nameRender} 数据成功`);
        });
      });
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(record: any) {
    datasetApi.modifyDatasetVersionInfo(record).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"].loadData();
};
//初始化
onMounted(() => {});
//销毁
onUnmounted(() => {
  intervalClinet.value?.disconnect();
});
//事件声明
const emit = defineEmits(["edit-data", "preview-data", "dedup-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.offline-dataset-version-table {
  height: calc(100% - 25px);

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
