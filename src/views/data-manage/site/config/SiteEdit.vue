<template>
  <my-drawer class="site-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800px">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
      <template #domains>
        <el-button link type="primary" @click="events.insertDomain" v-if="dataC.isEmpty(ruleForm.domainList)">
          <el-icon size="18px">
            <CirclePlus />
          </el-icon>
        </el-button>
        <el-col>
          <el-row v-for="(item, index) in ruleForm.domainList" gutter="10" class="item-row">
            <el-col :span="21">
              <el-form-item label="" label-width="0" :prop="`domainList[${index}]`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-input v-model="ruleForm.domainList[index]"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button link type="danger" @click="events.removeDomain(index)" style="margin-left: 10px">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button link type="primary" @click="events.insertDomain" v-if="ruleForm.domainList.length == index + 1" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
      <template #labels>
        <el-button link type="primary" @click="events.insertLabel" v-if="dataC.isEmpty(ruleForm.labels)">
          <el-icon size="18px">
            <CirclePlus />
          </el-icon>
        </el-button>
        <el-col>
          <el-row v-for="(item, index) in ruleForm.labels" gutter="10" class="item-row">
            <el-col :span="11">
              <el-form-item label="" label-width="0" :prop="`labels.${index}.siteKey`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-select v-model="item.siteKey" filterable allow-create placeholder="请选择或输入Key" @change="item.siteValue = []">
                  <el-option v-for="item1 in modelValue.siteTagList" :label="item1.siteKey" :value="item1.siteKey" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="" label-width="0" :prop="`labels.${index}.siteValue`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-select
                  v-model="item.siteValue"
                  filterable
                  allow-create
                  multiple
                  :multiple-limit="modelValue.singleSelectList.includes(item.siteKey) ? 1 : 0"
                  placeholder="请选择或输入Value"
                >
                  <el-option v-for="item2 in getSitaTagValueList(item.siteKey)" :label="item2" :value="item2" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button link type="danger" @click="events.removeLabel(index)" style="margin-left: 10px">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button link type="primary" @click="events.insertLabel" v-if="ruleForm.labels.length == index + 1" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
      <template #property>
        <el-col>
          <el-row justify="end">
            <el-button @click="events.clearSelectPropertyList" style="margin-right: 10px"> 清空已选择属性 </el-button>
            <el-dropdown @command="events.quickSelectPropertyList">
              <el-button>
                快速选择类别&nbsp;
                <el-icon size="18px">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="item in modelValue.metaSiteFieldCategoryList" :command="item">{{ item }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-row>
          <el-checkbox-group v-model="ruleForm.propertyList">
            <el-row>
              <el-col :span="6" v-for="item in modelValue.metaSiteFieldList">
                <el-checkbox :label="item.fieldName" :value="item.fieldName" />
              </el-col>
            </el-row>
          </el-checkbox-group>
        </el-col>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as siteApi from "@/api/site";
import * as metaSiteFieldApi from "@/api/meta-site-field";

const { $app, proxy } = useCtx();
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}站点`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  dialogVisible.value = false;
  //加定时器是为了让用户感知不到操作
  setTimeout(() => {
    formRef.value.resetForm();
    getSiteTagList();
  }, 500);
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  site: "",
  description: "",
  applicableScenarios: "",
  domains: "",
  labels: [],
  properties: "",
};
const extraForm = {
  domainList: [],
  propertyList: [],
};
let ruleForm = ref<any>(assign({}, defaultForm, extraForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  site: [{ required: true, message: "Site不能为空", trigger: "blur" }],
  propertyList: [
    {
      type: "array",
      required: true,
      message: "属性不能为空",
      trigger: "change",
    },
  ],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
    },
  },
  site: {
    label: "Site",
    type: "input",
    attrs: {
      disabled: isUpdate,
      maxlength: 255,
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  applicableScenarios: {
    label: "适用场景",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  domainList: { label: "domains", type: "slot", slotName: "domains" },
  labels: { label: "标签", type: "slot", slotName: "labels" },
  propertyList: { label: "属性", type: "slot", slotName: "property" },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm, extraForm)));
      ruleForm.value.domainList = !dataC.isEmpty(row.domains) ? row.domains.split(",") : [];
      ruleForm.value.labels = !dataC.isEmpty(row.labels) ? row.labels : [];
      ruleForm.value.propertyList = !dataC.isEmpty(row.properties) ? row.properties.split(",") : [];
    } else {
      ruleForm.value = assign({}, defaultForm, extraForm);
      ruleForm.value.domainList = [];
      ruleForm.value.labels = [];
      ruleForm.value.propertyList = [];
    }
  });
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  if (!checkDuplicatesInDomainList(form.domainList)) return false;
  if (!checkDuplicatesInLabels(form.labels)) return false;
  form.name = form.name.trim();
  form.domains = form.domainList.join(",");
  form.properties = form.propertyList.join(",");
  if (isUpdate.value) {
    siteApi.modifySite(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    siteApi.insertSite(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
// 检查字符串数组中是否有重复元素，并找出重复元素
function checkDuplicatesInDomainList(domainList: Array) {
  let uniqueDomains = new Set();
  for (let domain of domainList) {
    if (uniqueDomains.has(domain)) {
      $app.$message.warning(`domain:"${domain}"重复!`);
      return false;
    } else {
      uniqueDomains.add(domain);
    }
  }
  return true;
}

// 检查对象数组中对象的 key 是否有重复元素，并找出重复元素
function checkDuplicatesInLabels(labels: Array) {
  let uniqueKeys = new Set();
  for (let label of labels) {
    let key = label.siteKey;
    if (uniqueKeys.has(key)) {
      $app.$message.warning(`标签Key:"${key}"重复!`);
      return false;
    } else {
      uniqueKeys.add(key);
    }
  }
  return true;
}
//事件列表
const events = reactive({
  insertDomain: () => {
    ruleForm.value.domainList.push("");
  },
  removeDomain: (index) => {
    ruleForm.value.domainList.splice(index, 1);
  },
  insertLabel: () => {
    ruleForm.value.labels.push({ siteKey: "", siteValue: [] });
  },
  removeLabel: (index) => {
    ruleForm.value.labels.splice(index, 1);
  },
  clearSelectPropertyList: () => {
    ruleForm.value.propertyList.splice(0, ruleForm.value.propertyList.length);
  },
  quickSelectPropertyList: (command: string) => {
    const list = modelValue.metaSiteFieldList.filter((item) => {
      return item.category == command;
    });
    list.forEach((item) => {
      if (!ruleForm.value.propertyList.includes(item.fieldName)) {
        ruleForm.value.propertyList.push(item.fieldName);
      }
    });
  },
});
//对象集合
const modelValue = reactive({
  metaSiteFieldList: [],
  metaSiteFieldCategoryList: [],
  siteTagList: [],
  singleSelectList: ["QA", "切片", "实时需求", "站点等级"],
});
//获取元站点属性列表
const getMetaSiteFieldList = async () => {
  const list: any = await api.getMetaSiteFieldList();
  modelValue.metaSiteFieldList.push(...list);
};
//获取站点属性类别列表
const getMetaSiteFieldCategoryList = () => {
  metaSiteFieldApi.getMetaSiteFieldCategoryList().then((result) => {
    modelValue.metaSiteFieldCategoryList.push(...result.data);
  });
};
//获取Tag的Key列表
const getSiteTagList = () => {
  siteApi.getSiteTagList().then((result) => {
    modelValue.siteTagList = result.data;
  });
};
//获取Tag的Value列表
const getSitaTagValueList = (key: string) => {
  return dataC.getItemByValue(modelValue.siteTagList, key, "siteKey")["siteValue"];
};
//初始化
onMounted(() => {
  getMetaSiteFieldList();
  getMetaSiteFieldCategoryList();
  getSiteTagList();
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
.site-edit {
}
</style>
