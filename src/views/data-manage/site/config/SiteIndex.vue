<template>
  <page-wrapper route-name="site::">
    <div class="site">
      <site-edit ref="siteEditRef" @save-data="loadList"></site-edit>
      <site-table ref="siteTableRef" @edit-data="openWindow" @version-data="openVersionPage"></site-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import siteTable from "./SiteTable.vue";
import siteEdit from "./SiteEdit.vue";
import useCtx from "@/hooks/useCtx";

const { $router } = useCtx();
const siteEditRef = ref(null);
const siteTableRef = ref(null);
const routeName = "site";

//打开编辑窗口
const openWindow = (type: string, item: any) => {
  siteEditRef.value.openWindow(type, item);
};
//查询列表数据
const loadList = () => {
  siteTableRef.value.loadList();
};
//打开版本页面
const openVersionPage = (siteId: string, siteName: string) => {
  $router.push({
    name: `${routeName}::version`,
    query: {
      siteId: siteId,
      metaLabel: [siteName],
    },
  });
};
</script>
<style scoped lang="scss"></style>
