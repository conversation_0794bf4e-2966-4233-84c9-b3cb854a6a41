<template>
  <div class="site-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/site/edit"
      :operations="operations"
      @operation="handleOperation"
      :withStorePagination="true"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query ref="myQueryRef" :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="export" @click="events.exportExcel" style="margin-left: 10px">导出</my-button>
              <my-button type="add" @click="events.add" style="margin-left: 20px" :disabled="!testAuth()">新建站点</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { assign, cloneDeep, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as siteApi from "@/api/site";
import * as util from "@/utils/common";

const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/site/edit");
}
//列配置
const columns = ref([
  {
    prop: "name",
    label: "名称",
    width: 160,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.version(record);
      },
    },
  },
  { prop: "site", label: "Site", width: 160, withCopy: true },
  { prop: "description", label: "描述", minWidth: 200 },
  { prop: "applicableScenarios", label: "适用场景", minWidth: 200 },
  { prop: "count", label: "数据总量", width: 120, sortable: false },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  keywords: "",
  labels: "",
  scenarios: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    width: "240px",
    attrs: {
      placeholder: "名称 或 Site",
    },
  },
  labels: {
    label: "",
    type: "cascader",
    modelValue: [],
    defaultValue: [],
    width: "480px",
    options: [],
    attrs: {
      placeholder: "标签",
      props: {
        multiple: true,
        value: "value",
        label: "label",
        children: "children",
      },
    },
  },
  scenarios: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    width: "240px",
    attrs: {
      placeholder: "适用场景",
    },
  },
});
//加工选择的标签数据
const getLabels = (labels: array) => {
  if (dataC.isEmpty(labels)) return "";
  const result = {};
  labels.forEach(([key, value]) => {
    if (!result[key]) {
      result[key] = [];
    }
    result[key].push(value);
  });
  return encodeURIComponent(
    JSON.stringify(
      keys(result).map((key) => {
        return {
          siteKey: key,
          siteValue: result[key],
        };
      })
    )
  );
};
//查询缓存
const loadHistoryQuery = (data: any) => {
  const historyQuery = dataC.safeObject(localStorage.getItem("lynxiao-base-site-table"));
  if (dataC.isEmpty(historyQuery)) {
    return;
  }
  keys(queryItems.value).forEach((key) => {
    queryItems.value[key].modelValue = historyQuery[key] ?? queryItems.value[key].defaultValue;
    data[key] = queryItems.value[key].modelValue;
  });
};
//列表查询
const loadListData = (data: any) => {
  loadHistoryQuery(data);
  return new Promise((resolve: any) => {
    siteApi.getSiteListPage(data.keywords, getLabels(data.labels), data.scenarios, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.count = util.formatNumber(x.count);
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
    localStorage.setItem("lynxiao-base-site-table", JSON.stringify(query.value));
  },
  reset: (obj: any) => {},
  add: () => {
    emit("edit-data", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除${record.name}吗?`,
      })
      .then(() => {
        siteApi.removeSite(record.id).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
  version: (record: any) => {
    emit("version-data", record.id, record.name);
  },
  exportExcel: (record: any) => {
    siteApi.exportExcel().then((res) => {
      util.downloadFile(res, "站点.xlsx");
    });
  },
});
const loadList = () => {
  getSiteTagList();
  proxy.$refs.myTableRef.loadData();
};
//获取Tag的Key列表,动态填充筛选项
const getSiteTagList = () => {
  siteApi.getSiteTagList().then((result) => {
    const list = [];
    result.data.forEach((tag) => {
      const temp = tag.siteValue.map((x) => {
        return { label: x, value: x };
      });
      list.push({
        label: tag.siteKey,
        value: tag.siteKey,
        children: temp,
      });
    });
    queryItems.value.labels.options = list;
  });
};
//初始化
onMounted(() => {
  getSiteTagList();
});
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.site-table {
  height: 100%;

  .t-query {
    width: 100%;
  }
}
</style>
