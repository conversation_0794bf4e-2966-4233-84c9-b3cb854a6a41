<template>
  <el-col class="site-preview-index">
    <el-row :gutter="10">
      <el-col :span="4">
        <el-card>
          <el-input v-model="filterText" placeholder="请输入名称" @keyup.enter="filterStr = filterText">
            <template #append>
              <el-button :icon="Search" @click="filterStr = filterText" />
            </template>
          </el-input>
          <el-tree :data="rulesTree" node-key="id" default-expand-all :expand-on-click-node="false">
            <template #default="{ node, data }">
              <span class="tree-node" :class="{ active: data.id == modelValue.showItemId }" @click="showDataItem(node, data)">
                <span>
                  {{ data.name }}
                </span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>
      <el-col :span="20">
        <el-card>
          <div class="preview-table-parent">
            <preview-table ref="previewTableRef" :withExport="true"></preview-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-col>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import { Search } from "@element-plus/icons-vue";
import * as siteApi from "@/api/site";

const props = defineProps(["siteId", "siteVersionId"]);

const { $app, proxy } = useCtx();

//筛选框数据(前端搜索)
const filterText = ref("");
const filterStr = ref("");
//表单数据项
const modelValue = reactive({
  rules: [],
  showItemId: props.siteVersionId,
});
//初始化列表:来源于数据库
const getRuleList = () => {
  siteApi.getRuleList(props.siteVersionId, 1, 1000, `lastModifiedDate,desc`).then((result) => {
    modelValue.rules = result.content;
  });
  loadList("fromSiteVersion", props.siteVersionId);
};
//树形结构数据
const rulesTree = computed(() => {
  let list = [
    {
      id: props.siteVersionId,
      name: "全部规则",
      children: [],
    },
  ];
  list[0].children.push(...modelValue.rules.filter((item) => item.name.includes(filterStr.value)));
  return list;
});
//刷新列表
const showDataItem = (node, data) => {
  modelValue.showItemId = data.id;
  if (node.level == 1) {
    loadList("fromSiteVersion", props.siteVersionId);
    return;
  }
  if (node.level == 2) {
    loadList("fromRule", data.id);
    return;
  }
};
const loadList = (type: string, id: string) => {
  proxy.$refs["previewTableRef"].loadList(type, id, { siteVersionId: props.siteVersionId });
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
  getRuleList,
});
</script>
<style lang="scss">
.site-preview-index {
  height: 100%;

  .el-card__body {
    padding: 10px;
  }

  //给树设置高度，并且给根节点的子节点设置滚动条
  .el-tree {
    height: calc(100vh - 217px);
    --el-tree-node-hover-bg-color: none;

    > .el-tree-node.is-expanded.is-focusable {
      height: 100%;
      > .el-tree-node__children {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }

    .active {
      background-color: $info-bg;
      border-radius: 5px;
    }
  }

  .tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;

    :hover {
      color: $primary-color;
    }
  }

  .el-tree-node__content {
    padding: 20px 0;
  }

  .preview-table-parent {
    height: calc(100vh - 185px);
  }
}
</style>
