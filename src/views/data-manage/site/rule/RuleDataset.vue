<template>
  <el-form-item label="过滤条件 :" prop="query" class="form-item-top-label">
    <el-col>
      <config-form
        ref="configFormRef"
        :conf="datasetForm.query"
        :fields="fields"
        :metaLabelList="metaLabelList"
        :displayCopy="true"
        @copy-conf="copyConf"
        :isDisabled="disabled"
      ></config-form>
    </el-col>
  </el-form-item>
  <el-form-item label="入库数据集 :">
    <el-button link type="primary" @click="insertItem" v-if="dataC.isEmpty(datasetForm.datasetList) && !disabled">
      <el-icon size="18px">
        <CirclePlus />
      </el-icon>
    </el-button>
    <el-col :span="24">
      <el-row v-for="(item, index) in datasetForm.datasetList" class="item-row">
        <el-col :span="20">
          <el-form-item
            label=""
            label-width="0"
            :prop="`datasetConditions.${datasetFormIndex}.datasetList.${index}.id`"
            :rules="{ required: true, message: '必选', trigger: 'change' }"
          >
            <el-select v-model="item.id" placeholder="请选择" @change="handleDatasetChange">
              <template #header>
                <el-input v-model="datasetSearchInput[index]" placeholder="搜索" :prefix-icon="Search"> </el-input>
              </template>
              <el-option v-for="(item1, index1) in filterDatasetList(index)" :label="`${item1.name}(${item1.enName})`" :value="item1.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="!disabled">
          <el-button link type="danger" @click="removeItem(index)" style="margin-left: 10px">
            <el-icon size="18px">
              <Delete />
            </el-icon>
          </el-button>
          <el-button link type="primary" @click="insertItem" v-if="datasetForm.datasetList.length == index + 1" style="margin-left: 10px">
            <el-icon size="18px">
              <CirclePlus />
            </el-icon>
          </el-button>
        </el-col>
      </el-row>
    </el-col>
  </el-form-item>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import { Search } from "@element-plus/icons-vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import configForm from "@/views/common/configForm/ConfigForm.vue";

const props = defineProps(["datasetForm", "datasetFormIndex", "fields", "metaDatasetList", "metaLabelList", "disabled"]);

const { $app, proxy } = useCtx();
//数据集项搜索
const datasetSearchInput = reactive([""]);
//数据集选择项
const filterDatasetList = (index: number) => {
  return props.metaDatasetList.filter((item) => {
    if (dataC.isEmpty(datasetSearchInput[index])) return true;
    //当前项需要默认返回，不然会导致渲染成数字
    if (item.id == props.datasetForm.datasetList[index].id) return true;
    //实际筛选到的项
    return item.name.includes(datasetSearchInput[index]) || item.enName?.includes(datasetSearchInput[index]);
  });
};
//新增
const insertItem = () => {
  props.datasetForm.datasetList.push({
    id: "",
    code: "",
    name: "",
  });
  //筛选input新增
  datasetSearchInput.push("");
};
//删除
const removeItem = (index) => {
  props.datasetForm.datasetList.splice(index, 1);
  //筛选input删除
  datasetSearchInput.splice(index, 1);
};
//选中的数据集切换
const handleDatasetChange = (val) => {
  const datasetItem = dataC.getItemByValue(props.metaDatasetList, val, "id");
  props.datasetForm.datasetList.forEach((item) => {
    if (item.id == val) {
      item.code = datasetItem.code;
      item.name = datasetItem.name;
    }
  });
};
//获取筛选配置
const getSaveFilterConf = () => {
  return proxy.$refs["configFormRef"].getSaveFilterConf();
};
//引用规则
const copyConf = () => {
  emit("copy-conf");
};
//复制规则
const setConf = (val: any) => {
  proxy.$refs["configFormRef"].setConf(val);
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["copy-conf"]);
//接口暴露
defineExpose({
  getSaveFilterConf,
  setConf,
});
</script>
<style scoped lang="scss">
.form-item-top-label {
  display: flex;
  flex-direction: column;
}

.form-item-top-label .el-form-item__label {
  width: auto;
  margin-bottom: 8px;
}
</style>
