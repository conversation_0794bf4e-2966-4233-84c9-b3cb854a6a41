<template>
  <el-col>
    <el-row :gutter="10" class="rule-edit">
      <el-col :span="4" class="tree-col">
        <el-card>
          <el-input v-model="filterText" placeholder="请输入名称" @keyup.enter="filterStr = filterText">
            <template #append>
              <el-button :icon="Search" @click="filterStr = filterText" />
            </template>
          </el-input>
          <el-tree :data="rulesTree" node-key="id" default-expand-all :expand-on-click-node="false">
            <template #default="{ node, data }">
              <span class="tree-node" :class="{ active: data.id == modelValue.showRuleId }" @click="showRuleItem(data)">
                <span v-if="data.id == 'root'">
                  <el-button link type="primary" @click.native.stop="modelValue.displayAll = true" style="line-height: normal">
                    <el-icon size="18px">
                      <HomeFilled />
                    </el-icon>
                    {{ data.name }}
                  </el-button>
                </span>
                <span v-if="data.id == 'root' && !disabled">
                  <el-button link type="primary" @click.native.stop="insertRule(data)">
                    <el-icon size="18px">
                      <CirclePlus />
                    </el-icon>
                  </el-button>
                </span>
                <span v-if="data.id != 'root'" class="rule-name" :style="{ width: disabled ? 'calc(100% - 5px)' : 'calc(100% - 50px)' }">{{ data.name }}</span>
                <span v-if="data.id != 'root' && !disabled">
                  <el-button v-if="data.id != ''" link type="primary" @click="copyRule(data)">
                    <el-icon size="18px">
                      <CopyDocument />
                    </el-icon>
                  </el-button>
                  <el-button link type="danger" @click="removeRule(data)">
                    <el-icon size="18px">
                      <Delete />
                    </el-icon>
                  </el-button>
                </span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </el-col>
      <el-col :span="20" class="rules-col">
        <el-card
          v-for="(item, index) in modelValue.rules"
          v-show="modelValue.displayAll || (item.id == modelValue.showRuleId && item.name.includes(filterStr))"
          style="margin-bottom: 10px"
        >
          <rule-item
            :ruleData="item"
            :fields="fields"
            :metaLabelList="modelValue.metaLabelList"
            :metaDatasetList="modelValue.metaDatasetList"
            :disabled="disabled"
            @save-rule="refreshRuleItem"
          ></rule-item>
        </el-card>
      </el-col>
    </el-row>
  </el-col>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import ruleItem from "./RuleItem.vue";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import { dataC } from "turing-plugin";
import { Search } from "@element-plus/icons-vue";
import * as siteApi from "@/api/site";

const { $app, proxy } = useCtx();
const { api } = useStore();

const props = defineProps(["siteId", "siteVersionId", "disabled"]);

const siteRuleEditRef = ref(null);

//筛选框数据(前端搜索)
const filterText = ref("");
const filterStr = ref("");
//表单数据项
const modelValue = reactive({
  propertieList: [],
  rules: [],
  showRuleId: -1,
  metaSiteFieldList: [],
  metaLabelList: [],
  metaDatasetList: [],
  displayAll: false,
});
//初始化属性:来源于数据库
const getSite = () => {
  siteApi.getSite(props.siteId).then((result) => {
    modelValue.propertieList = !dataC.isEmpty(result.properties) ? result.properties.split(",") : [];
  });
};
//初始化列表:来源于数据库
const getRuleList = () => {
  siteApi.getRuleList(props.siteVersionId, 1, 1000, `lastModifiedDate,desc`).then((result) => {
    modelValue.rules = result.content;
    //设置查询到的第一项选中
    if (!dataC.isEmpty(modelValue.rules)) {
      modelValue.showRuleId = modelValue.rules[0].id;
    }
  });
};
//初始化属性元数据信息:来源于数据库
const getMetaSiteFieldList = () => {
  api.getMetaSiteFieldList().then((result) => {
    modelValue.metaSiteFieldList.push(...result);
  });
};
//初始化等级字典:来源于数据库
const getMetaLabelList = () => {
  api.getMetaLabelList().then((result) => {
    modelValue.metaLabelList = result;
  });
};
//初始化数据集信息:来源于数据库
const getMetaDatasetList = () => {
  api.getMetaDatasetList().then((result) => {
    modelValue.metaDatasetList.push(...result);
  });
};
//站点所选择的属性信息
const fields = computed(() => {
  const result = [
    ...modelValue.metaSiteFieldList
      .filter((item) => modelValue.propertieList.includes(item.fieldName))
      .map((item) => ({
        label: `${item.name}(${item.fieldName})`,
        value: item.fieldName,
        type: item.type,
      })),
  ];
  return result;
});
//树形结构数据
const rulesTree = computed(() => {
  let list = [
    {
      id: "root",
      name: "全部规则",
      children: [],
    },
  ];
  list[0].children.push(...modelValue.rules.filter((item) => item.name.includes(filterStr.value)));
  return list;
});
//新增规则
const insertRule = () => {
  if (!dataC.isEmpty(modelValue.rules) && dataC.isEmpty(modelValue.rules[modelValue.rules.length - 1].id)) {
    $app.$message.warning("您还有新规则未保存，请先进行保存操作");
    return;
  }
  modelValue.rules.push({
    id: "",
    siteVersionId: props.siteVersionId,
    name: "新规则名称",
    description: "",
    labelCondition: {
      query: "{}",
      levels: { "": "" },
      quality: {
        type: "script",
        value: 4,
      },
      similarity: {
        type: "script",
        value: 1,
      },
    },
    datasetConditions: [
      {
        query: "{}",
        datasetList: [
          {
            id: "",
            name: "",
          },
        ],
      },
    ],
  });
  modelValue.showRuleId = "";
};
//复制规则
const copyRule = (data) => {
  $app.$confirm({ title: `确定复制 ${data.name} ?` }).then(() => {
    siteApi.copyRule(data.id).then((result) => {
      getRuleList();
      modelValue.showRuleId = result.data;
      $app.$message.success("复制成功");
    });
  });
};
//删除规则
const removeRule = (data) => {
  $app
    .$deleteConfirm({
      title: `您确认要删除${data.name}吗?`,
    })
    .then(() => {
      if (dataC.isEmpty(data.id)) {
        modelValue.rules.splice(modelValue.rules.length - 1, 1);
      } else {
        siteApi.removeRule(data.id).then((result) => {
          $app.$message.success("删除成功");
          getRuleList();
        });
      }
    });
};
//展示规则详细
const showRuleItem = (data: any) => {
  if (data.id != "root") {
    modelValue.displayAll = false;
    modelValue.showRuleId = data.id;
  }
};
//更新规则列表
const refreshRuleItem = (item: any, operate: string) => {
  if (operate == "insert") {
    modelValue.rules[modelValue.rules.length - 1] = item;
    modelValue.showRuleId = item.id;
  } else {
    for (let i = 0; i < modelValue.rules.length; i++) {
      if (modelValue.rules[i].id == item.id) {
        modelValue.rules[i] = item;
        break;
      }
    }
  }
  emit("save-rule");
};
//初始化
onMounted(() => {
  getSite();
  getRuleList();
  getMetaSiteFieldList();
  getMetaLabelList();
  getMetaDatasetList();
});
//事件声明
const emit = defineEmits(["save-rule"]);
//接口暴露
defineExpose({
  getSite,
  getRuleList,
  showRuleItem,
});
</script>
<style lang="scss">
.rule-edit {
  height: calc(100vh - 115px);

  .tree-col {
    height: 100%;

    .el-card__body {
      padding: 10px;
    }

    //给树设置高度，并且给根节点的子节点设置滚动条
    .el-tree {
      height: calc(100vh - 217px);
      --el-tree-node-hover-bg-color: none;

      > .el-tree-node.is-expanded.is-focusable {
        height: 100%;
        > .el-tree-node__children {
          height: calc(100% - 40px);
          overflow-y: auto;
        }
      }

      .active {
        background-color: $info-bg;
        border-radius: 5px;
      }
    }

    .el-button + .el-button {
      margin-left: 3px;
    }
  }

  .tree-node {
    width: calc(100% - 40px);
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;

    :hover {
      color: $primary-color;
    }

    .rule-name {
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .el-tree-node__content {
    padding: 20px 0;
  }

  .rules-col {
    height: calc(100vh - 165px);
    overflow: auto;
  }
}
</style>
