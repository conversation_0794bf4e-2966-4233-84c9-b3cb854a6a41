<template>
  <page-wrapper route-name="site::version::rule::">
    <div class="rule-index">
      <el-tabs v-model="activeName" @tab-change="handleTabChange">
        <el-tab-pane label="规则明细" name="ruleEdit">
          <rule-edit ref="ruleEditRef" :siteId="siteId" :siteVersionId="siteVersionId" :disabled="disabled" @save-rule="saveRule"></rule-edit>
        </el-tab-pane>
        <el-tab-pane label="规则详情" name="ruleTable">
          <rule-table ref="ruleTableRef" :siteId="siteId" :siteVersionId="siteVersionId" :disabled="disabled" @edit-rule="editRule"></rule-table>
        </el-tab-pane>
        <el-tab-pane label="数据预览" name="previewIndex">
          <preview-index ref="previewIndexRef" :siteId="siteId" :siteVersionId="siteVersionId" :disabled="disabled"></preview-index>
        </el-tab-pane>
      </el-tabs>
    </div>
  </page-wrapper>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import ruleEdit from "../rule/RuleEdit.vue";
import ruleTable from "../rule/RuleTable.vue";
import previewIndex from "../preview/PreviewIndex.vue";
import useCtx from "@/hooks/useCtx";

const { $router, proxy, $auth } = useCtx();

const siteId = $router.currentRoute.value.query.siteId;
const siteVersionId = $router.currentRoute.value.query.siteVersionId;
const siteVersionStatus = $router.currentRoute.value.query.siteVersionStatus;

const disabled = computed(() => {
  return siteVersionStatus == 2 || !$auth.testAuth("/base/#/site/edit");
});

const activeName = ref("ruleEdit");

const handleTabChange = (tabPaneName: string) => {
  if (tabPaneName == "ruleEdit") {
    proxy.$refs["ruleEditRef"].getSite();
    proxy.$refs["ruleEditRef"].getRuleList();
  } else if (tabPaneName == "ruleTable") {
    proxy.$refs["ruleTableRef"].loadList();
  } else if (tabPaneName == "previewIndex") {
    proxy.$refs["previewIndexRef"].getRuleList();
  }
};
const saveRule = () => {
  activeName.value = "ruleTable";
  proxy.$refs["ruleTableRef"].loadList();
};
const editRule = (record: any) => {
  activeName.value = "ruleEdit";
  proxy.$refs["ruleEditRef"].getRuleList();
  proxy.$refs["ruleEditRef"].showRuleItem(record);
};
//初始化
onMounted(() => {});
//接口暴露
defineExpose({});
</script>
<style lang="scss">
.rule-index {
  overflow: hidden;

  .el-tabs__header {
    padding: 0 15px;
  }
  .el-tabs__content {
    padding: 10px 15px 0 15px;
  }
}
</style>
