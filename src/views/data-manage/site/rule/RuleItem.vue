<template>
  <el-form ref="formRef" :model="modelValue.ruleForm" :rules="rules" label-width="120px" class="rule-item" :disabled="disabled">
    <el-col :span="24">
      <el-form-item label="规则名称 :" prop="name">
        <el-input v-model="modelValue.ruleForm.name" placeholder="请输入规则名称" clearable />
      </el-form-item>
      <el-form-item label="规则描述 :" prop="description">
        <el-input type="textarea" v-model="modelValue.ruleForm.description" placeholder="请输入规则描述" />
      </el-form-item>
    </el-col>
    <el-divider style="margin: 20px 10px" content-position="left">
      <span style="font-size: 18px; font-weight: bold;">标签管理</span>&nbsp;
    </el-divider>
    <el-col :span="24">
      <el-form-item label="过滤条件 :" prop="query" class="form-item-top-label">
        <el-col>
          <config-form ref="configFormRef" :conf="modelValue.ruleForm.labelCondition.query" :fields="fields" :metaLabelList="metaLabelList" :displayCopy="false" :isDisabled="disabled"></config-form>
        </el-col>
      </el-form-item>
      <el-form-item label="精品等级 :" prop="levels">
        <rule-level ref="siteRuleLevelRef" :levelList="modelValue.ruleForm.labelCondition.levelList" :metaLabelList="metaLabelList" :disabled="disabled"></rule-level>
      </el-form-item>
      <el-form-item label="内容质量 :" prop="quality">
        <el-radio-group v-model="modelValue.ruleForm.labelCondition.quality.type">
          <el-radio label="script">规则计算</el-radio>
          <el-radio label="config">设定为</el-radio>
        </el-radio-group>
        <el-select v-model="modelValue.ruleForm.labelCondition.quality.value" placeholder="" :disabled="modelValue.ruleForm.labelCondition.quality.type !='config'"
          style="max-width: 80px; margin-left: 25px;">
          <el-option label="S" :value="4" />
          <el-option label="A" :value="3" />
          <el-option label="B" :value="2" />
          <el-option label="C" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="TC相似度 :" prop="similarity">
        <el-radio-group v-model="modelValue.ruleForm.labelCondition.similarity.type">
          <el-radio label="script">规则计算</el-radio>
          <el-radio label="config">设定为1</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-col>
    <el-divider style="margin: 20px 10px" content-position="left">
      <span style="font-size: 18px; font-weight: bold;">入库管理</span>&nbsp;
      <el-button link type="primary" @click="insertDatasetCondition">
        <el-icon size="18">
          <CirclePlus />
        </el-icon>
      </el-button>
    </el-divider>
    <el-col :span="24" v-for="(item, index) in modelValue.ruleForm.datasetConditions" class="dataset-col">
      <el-divider v-show="index != 0" border-style="dashed" style="margin: 15px;" />
      <rule-dataset ref="ruleDatasetRef" :datasetForm="item" :datasetFormIndex="index" :fields="getDatasetFields()" :metaDatasetList="metaDatasetList" :metaLabelList="metaLabelList" :disabled="disabled" @copy-conf="setConf(index)"></rule-dataset>
      <el-button v-show="index != 0" link type="danger" @click="removeDatasetCondition(index)" class="delete-btn">
        <el-icon size="25">
          <Delete />
        </el-icon>
      </el-button>
    </el-col>
  </el-form>
  <el-row justify="end" v-if="!disabled">
    <el-button type="primary" @click="save">
      <el-icon>
        <Check />
      </el-icon>&nbsp;保存
    </el-button>
  </el-row>
</template>
  
<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import configForm from "@/views/common/configForm/ConfigForm.vue";
import ruleLevel from "./RuleLevel.vue";
import ruleDataset from "./RuleDataset.vue";
import useCtx from "@/hooks/useCtx";
import { assign } from "lodash";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";

const props = defineProps([
  "ruleData",
  "fields",
  "metaLabelList",
  "metaDatasetList",
  "disabled"
]);

const { $app, proxy } = useCtx();

const defaultForm = () => {
  return {
    id: "",
  };
};

//表单数据项
const modelValue = reactive({
  ruleForm: {},
});
//对象转数组
const objToArr = (obj: Object) => {
  return Object.keys(obj).map((key) => ({ key: key, value: obj[key] }));
};
//库数据渲染到表单
const transDataToFrom = (item) => {
  const result = assign({}, item);
  //精品等级列表
  result.labelCondition.levelList = objToArr(result.labelCondition.levels);
  //config
  result.labelCondition.query = JSON.parse(result.labelCondition.query);
  result.datasetConditions.forEach((item) => {
    item.query = JSON.parse(item.query);
  });
  return result;
};
//监听传入对象
watch(
  () => props.ruleData,
  (val) => {
    if (val) {
      modelValue.ruleForm = transDataToFrom(val);
    }
  },
  { immediate: true }
);
//表单规则项
const rules = reactive({
  name: [{ required: true, message: "规则名称不能为空", trigger: "blur" }],
});
//数组转对象
const arrToObj = (arr: Array) => {
  return arr.reduce((acc, cur) => {
    acc[cur.key] = cur.value;
    return acc;
  }, {});
};
//表单数据存库
const transFormToData = (item) => {
  //标签管理
  let labelCondition = {};
  // if (modelValue.ruleForm.labelCondition.levelList.length == 0) {
  //   $app.$message.warning("标签管理请增加精品等级!");
  //   return false;
  // }
  const configFormValue = proxy.$refs["configFormRef"].getSaveFilterConf();
  if (!configFormValue) {
    return false;
  } else {
    labelCondition = assign({}, modelValue.ruleForm.labelCondition, {
      query: JSON.stringify(configFormValue),
      levels: arrToObj(modelValue.ruleForm.labelCondition.levelList),
    });
  }
  //入库管理
  const datasetConditions = [];
  for (
    let index = 0;
    index < modelValue.ruleForm.datasetConditions.length;
    index++
  ) {
    if (modelValue.ruleForm.datasetConditions[index].datasetList.length == 0) {
      $app.$message.warning(`入库管理第${index + 1}项请增加入库数据集!`);
      return false;
    }
    const datasetConfigFormValue =
      proxy.$refs["ruleDatasetRef"][index].getSaveFilterConf();
    if (!datasetConfigFormValue) return false;
    datasetConditions.push(
      assign({}, modelValue.ruleForm.datasetConditions[index], {
        query: JSON.stringify(datasetConfigFormValue),
      })
    );
  }
  return assign({}, item, {
    labelCondition: labelCondition,
    datasetConditions: datasetConditions,
  });
};
//保存
const save = async () => {
  const formData = transFormToData(modelValue.ruleForm);
  if (!formData) return;
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (!formData.id) {
        siteApi.insertRule(formData).then((result) => {
          modelValue.ruleForm.id = result.data;
          emit("save-rule", transFormToData(modelValue.ruleForm), "insert");
          $app.$message.success("新增成功");
        });
      } else {
        siteApi.modifyRule(formData).then((result) => {
          emit("save-rule", formData, "modify");
          $app.$message.success("修改成功");
        });
      }
    }
  });
};
//入库条件Item相关
const insertDatasetCondition = () => {
  modelValue.ruleForm.datasetConditions.push({
    query: "{}",
    datasetList: [
      {
        id: "",
        code: "",
        name: "",
      },
    ],
  });
};
const removeDatasetCondition = (index: number) => {
  $app
    .$deleteConfirm({
      title: `您确认要删除该项入库条件吗?`,
    })
    .then(() => {
      modelValue.ruleForm.datasetConditions.splice(index, 1);
    });
};
const getDatasetFields = () => {
  const list = [];
  list.push(...props.fields);
  // list.push({
  //   label: "精品等级",
  //   value: "levels",
  //   type: "INT",
  // });
  // list.push({
  //   label: "质量等级",
  //   value: "q_user",
  //   type: "INT",
  // });
  // list.push({
  //   label: "TC相似度",
  //   value: "q_tc",
  //   type: "DOUBLE",
  // });
  return list;
};
const setConf = (index: number) => {
  const val = proxy.$refs["configFormRef"].getSaveFilterConf();
  proxy.$refs["ruleDatasetRef"][index].setConf(val);
}
//事件声明
const emit = defineEmits(["save-rule"]);
</script>
<style lang="scss">
.rule-item {
  .form-item-top-label {
    // display: flex;
    // flex-direction: column;

    .el-form-item__label {
      display: none;
      width: auto;
      margin-bottom: 8px;
    }
  }
  .el-divider__text {
    padding: 0 10px;
  }

  .el-divider__text.is-left {
    left: -20px;
  }

  .dataset-col {
    position: relative;

    .delete-btn {
      position: absolute;
      top: 10px;
      right: 0;
      display: none;
    }
  }

  .dataset-col:hover {
    .delete-btn {
      display: block;
    }
  }
}
</style>