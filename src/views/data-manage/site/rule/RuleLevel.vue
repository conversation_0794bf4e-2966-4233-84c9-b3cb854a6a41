<template>
  <el-button link type="primary" @click="insertLevel" v-if="dataC.isEmpty(levelList) && !disabled">
    <el-icon size="18px">
      <CirclePlus />
    </el-icon>
  </el-button>
  <el-col>
    <el-row v-for="(item, index) in levelList" :gutter="10" class="item-row">
      <el-col :span="10">
        <el-form-item label-width="0" :prop="`labelCondition.levelList.${index}.key`" :rules="{ required: true, message: '必选', trigger: 'change'}">
          <el-select v-model="item.key" placeholder="请选择" @change="levelKeySelectChange(index)" filterable>
            <el-option v-for="(item1, index1) in metaLabelList" :label="item1.name" :value="item1.code" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="10">
        <el-form-item label-width="0" :prop="`labelCondition.levelList.${index}.value`" :rules="{ required: true, message: '必选', trigger: 'change'}">
          <el-select v-model="item.value" placeholder="请选择">
            <el-option v-for="(item2, index2) in getMetaLabelValueList(item.key)" :label="item2.key" :value="item2.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="4" v-if="!disabled">
        <el-button link type="danger" @click="removeLevel(index)" style="margin-left: 10px;">
          <el-icon size="18px">
            <Delete />
          </el-icon>
        </el-button>
        <el-button link type="primary" @click="insertLevel" v-if="levelList.length == index + 1" style="margin-left: 10px;">
          <el-icon size="18px">
            <CirclePlus />
          </el-icon>
        </el-button>
      </el-col>
    </el-row>
  </el-col>
</template>
  
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { dataC } from "turing-plugin";

const props = defineProps(["levelList", "metaLabelList", "disabled"]);

//获取元Label值列表
const getMetaLabelValueList = (code: String) => {
  if (dataC.isEmpty(code) || dataC.isEmpty(props.metaLabelList)) return [];
  return dataC.getItemByValue(props.metaLabelList, code, "code").values;
};
//当key修改时value清空
const levelKeySelectChange = (index: Number) => {
  props.levelList[index].value = "";
};
//新增Level
const insertLevel = () => {
  props.levelList.push({
    key: "",
    value: "",
  });
};
//删除Level
const removeLevel = (index) => {
  props.levelList.splice(index, 1);
};
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({});
</script>