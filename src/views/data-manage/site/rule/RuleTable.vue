<template>
  <div class="rule-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :withSort="false"
      operationAuth="/base/#/site/edit"
      :operations="operations"
      @operation="handleOperation"
      :defaultSort="{ prop: 'lastModifiedDate', order: 'desc' }"
      height="calc(100vh - 200px)"
    >
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick } from "vue";
import { assign, cloneDeep } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import ruleTest from "./RuleTest.vue";
import * as siteApi from "@/api/site";
import * as util from "@/utils/common";

const props = defineProps(["siteId", "siteVersionId", "disabled"]);

const { $router, $app, proxy } = useCtx();
const { api } = useStore();
//数据项
const modelValue = reactive({
  metaLabelList: [],
});
//列配置
const columns = ref([
  { prop: "name", label: "规则", width: 160 },
  { prop: "levelsRender", label: "精品等级", minWidth: 200 },
  { prop: "qualityRender", label: "质量等级", width: 100 },
  { prop: "similarityRender", label: "TC相似度", width: 100 },
  { prop: "datasetNameRender", label: "入数据集", width: 200 },
  {
    prop: "test",
    label: "调试状态",
    width: 120,
    custom: "status",
    customRender: {
      options: {
        1: { type: "info", name: "未调试" },
        2: { type: "danger", name: "未通过" },
        3: { type: "success", name: "已通过" },
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 160, fixed: "right" },
]);
//列表查询
const loadListData = async (data: any) => {
  if (dataC.isEmpty(modelValue.metaLabelList)) {
    const list = await api.getMetaLabelList();
    modelValue.metaLabelList.push(...list);
  }
  const { page, size } = data;
  return new Promise((resolve: any) => {
    siteApi.getRuleList(props.siteVersionId, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.levelsRender = util.displayLevels(x.labelCondition.levels, modelValue.metaLabelList);
    x.qualityRender = util.displayQuality(x.labelCondition.quality);
    x.similarityRender = util.displaySimilarity(x.labelCondition.similarity);
    x.datasetNameRender = util.displayDataset(x.datasetConditions);
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [
  {
    type: "test",
    label: "调试",
    disabled: (record: any) => props.disabled,
    disabledTips: "已发布，不可调试",
  },
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => props.disabled,
    disabledTips: "已发布，不可编辑",
  },
  {
    type: "delete",
    label: "删除",
    btnType: "danger",
    disabled: (record: any) => props.disabled,
    disabledTips: "已发布，不可删除",
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  test: (record: any) => {
    const siteId = $router.currentRoute.value.query.siteId;
    const siteVersionId = $router.currentRoute.value.query.siteVersionId;
    const siteVersionStatus = $router.currentRoute.value.query.siteVersionStatus;
    let metaLabel = $router.currentRoute.value.query.metaLabel;
    if (!(metaLabel instanceof Array)) {
      metaLabel = [metaLabel];
    }
    $router.push({
      name: "site::version::rule::test",
      query: {
        siteId: siteId,
        siteVersionId: siteVersionId,
        siteVersionStatus: siteVersionStatus,
        ruleId: record.id,
        metaLabel: [metaLabel[0], metaLabel[1], record.name],
      },
    });
  },
  edit: (record: any) => {
    emit("edit-rule", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除${record.name}吗?`,
      })
      .then(() => {
        siteApi.removeRule(record.id).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"].loadData();
};
//事件声明
const emit = defineEmits(["edit-rule"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.rule-table {
  height: 100%;

  .query-wrapper {
    padding: 0 !important;
  }

  .table-wrapper {
    padding: 0 !important;
  }
}
</style>
