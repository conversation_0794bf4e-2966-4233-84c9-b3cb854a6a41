<template>
  <div class="rule-test">
    <el-row class="top">
      <el-col :span="22">
        <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" label-width="80px"> </my-form>
      </el-col>
      <el-col :span="2">
        <el-button type="primary" plain @click="events.test" style="height: calc(80% - 20px); width: 100%">规则调试</el-button>
        <div style="width: 100%; text-align: center">
          <el-link type="primary" plain @click="events.preview" :icon="Position" style="height: calc(20% - 10px); margin-top: 10px; font-size: 12px">
            &nbsp;站点数据预览
          </el-link>
        </div>
      </el-col>
    </el-row>
    <div class="rule-test-table">
      <table-page
        ref="myTableRef"
        :columns="columns"
        :loadDataApi="loadListData"
        :transformListData="transformListData"
        :withSelection="true"
        :operations="operations"
        @operation="handleOperation"
        @selection-change="handleSelectionChange"
        ><template #query>
          <div class="flexBetweenStart">
            <div style="margin-left: 7px"><el-checkbox v-model="selectAll" :value="true" @change="handleSelectAllChange">全选所有</el-checkbox></div>
            <my-operation :selectedTotal="selectedLength">
              <template #buttonGroup>
                <tooltip-button type="delete" handle="批量删除" :clickable="selectedIds.length > 0" @click="events.batchDelete" :icon="Delete">
                  批量删除
                </tooltip-button>
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
    </div>
    <div class="flexBetweenStart">
      <div></div>
      <div style="margin-bottom: 10px; margin-right: 16px">
        <el-button type="info" @click="events.no" :icon="Close"> 标记为未通过 </el-button>
        <el-button type="primary" @click="events.yes" :icon="Check"> 标记为通过 </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { Pointer, Position, Delete, Check, Close } from "@element-plus/icons-vue";
import { assign, pick, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as siteApi from "@/api/site";
import * as util from "@/utils/common";

const { $router, $app, proxy } = useCtx();
const { api } = useStore();

//弹窗相关
const dialogTitle = computed(() => {
  return `规则调试【${ruleForm.value.ruleName}】`;
});
//表单相关
const formRef = ref<any>(null);
const rules = reactive<FormRules>({
  url: [
    { required: true, message: "URL不能为空", trigger: "change" },
    {
      validator: (rule, value, callback) => {
        const list = value.split(/[\s\n]/);
        list.forEach((item) => {
          try {
            new URL(item);
          } catch (error) {
            callback(new Error("URL不符合规范"));
          }
        });
        callback();
      },
      trigger: "change",
    },
  ],
});
//表单
const ruleForm = ref<any>({
  ruleId: $router.currentRoute.value.query.ruleId,
  url: [],
});
//表单项
const formItems = ref<any>({
  url: {
    label: "URL",
    type: "textarea",
    attrs: {
      rows: 5,
      placeholder: "请输入URL，支持输出多个，多个URL之间请换行",
      labelWidth: "50px",
      class: "no-resize-textarea",
    },
  },
});
//多选项
const selectedIds = ref<any>([]);
const handleSelectionChange = (arr: any) => {
  //获取选中的Id
  selectedIds.value = arr.map((item: any) => item.id);
  //设置全选所有
  if (selectAll.value) {
    selectAll.value = selectedIds.value.length > 0 && selectedIds.value.length == proxy.$refs.myTableRef?.getTableData().length;
  } else {
    selectAll.value = selectedIds.value.length > 0 && selectedIds.value.length == proxy.$refs.myTableRef?.getPage().total;
  }
};
const selectAll = ref<Boolean>(false);
const handleSelectAllChange = (newValue: value) => {
  //如果表格数据为空，不做操作
  if (proxy.$refs.myTableRef?.getPage().total == 0) return;
  //如果勾选了全选所有，但是表格行没有全部选择时，将表格中所有行选择, 取消全选所有时，将所有行取消选择
  if (newValue && selectedIds.value.length < proxy.$refs.myTableRef?.getTableData().length) {
    proxy.$refs.myTableRef.toggleAllSelection();
  } else if (!newValue) {
    proxy.$refs.myTableRef.clearSelection();
  }
};
const selectedLength = computed(() => {
  //如果勾选了全选所有，返回total数量，否则返回当前勾选数量
  if (selectAll.value == true) {
    return proxy.$refs.myTableRef?.getPage().total;
  } else {
    return selectedIds.value.length;
  }
});
//操作
const operations = [{ type: "delete", label: "删除", btnType: "danger" }];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  test: () => {
    formRef.value.submitForm((valid: any) => {
      if (valid) {
        siteApi
          .testRule({
            ruleId: ruleForm.value.ruleId,
            urlList: ruleForm.value.url.split(/[\s\n]/),
          })
          .then((result) => {
            loadList();
          });
      }
    });
  },
  preview: () => {
    const siteId = $router.currentRoute.value.query.siteId;
    let metaLabel = $router.currentRoute.value.query.metaLabel;
    if (!(metaLabel instanceof Array)) {
      metaLabel = [metaLabel];
    }
    const routeInfo = {
      name: "site::version",
      query: {
        siteId: siteId,
        metaLabel: [metaLabel[0]],
        tab: "site-preview",
      },
    };
    const resolvedRoute = $router.resolve(routeInfo);
    window.open(resolvedRoute.href, "_blank");
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除吗?`,
      })
      .then(() => {
        siteApi.deleteRuleTestTraceList(ruleForm.value.ruleId, [record.id]).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
  batchDelete: () => {
    $app
      .$deleteConfirm({
        title: selectAll.value ? "您确认要删除所有记录吗?" : "您确认要删除当前页所选择的记录吗?",
      })
      .then(() => {
        if (selectAll.value == true) {
          siteApi.clearRuleTestTraceList(ruleForm.value.ruleId).then((result) => {
            $app.$message.success("删除成功");
            loadList();
            selectAll.value = false;
          });
        } else {
          siteApi.deleteRuleTestTraceList(ruleForm.value.ruleId, selectedIds.value).then((result) => {
            $app.$message.success("删除成功");
            loadList();
          });
        }
      });
  },
  yes: () => {
    siteApi.testRuleFeedback(ruleForm.value.ruleId, true).then((result) => {
      $app.$message.success("已标记为通过!");
      $router.back();
    });
  },
  no: () => {
    siteApi.testRuleFeedback(ruleForm.value.ruleId, false).then((result) => {
      $app.$message.warning("已标记为未通过!");
      $router.back();
    });
  },
});
const tableData = ref([]);
//静态列配置
const staticColumns = [
  {
    prop: "pass",
    label: "结果",
    width: 120,
    custom: "status",
    customRender: {
      options: {
        1: { type: "success", name: "满足规则" },
        2: { type: "danger", name: "不满足规则" },
        3: { type: "warning", name: "未查询到" },
      },
    },
  },
  { prop: "url", label: "URL", minWidth: 200 },
  { prop: "levels", label: "精品等级", width: 200, sortable: false },
  { prop: "q_user", label: "质量等级", width: 120, sortable: false },
  { prop: "q_tc", label: "TC相似度", width: 120, sortable: false },
  { prop: "dataset", label: "入数据集", width: 200, sortable: false },
  { prop: "operation", label: "操作", width: 60, fixed: "right" },
];
const columns = ref([]);
//列表查询
const loadListData = async (data: any) => {
  const { page, size } = data;
  if (dataC.isEmpty(modelValue.metaLabelList)) {
    const list = await api.getMetaLabelList();
    modelValue.metaLabelList = list;
  }
  return new Promise((resolve: any) => {
    siteApi.getRuleTestTraceList(ruleForm.value.ruleId, data.page, data.size, data.sort).then((result) => {
      selectAll.value = false;
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    if (!dataC.isEmpty(x.data)) {
      x.data = dataC.safeObject(x.data);
      keys(x.data).forEach((key) => {
        x[key] = x.data[key];
        //判断哪些字段需要进行长度的展示
        const lenField = `${key}Len`;
        if (lenFields.has(lenField)) {
          if (typeof x.data[key] == "string") {
            x[lenField] = x.data[key].length;
          }
        }
      });
      x.levels = util.displayLevels(x.levels, modelValue.metaLabelList);
      x.q_user = util.displayQualityDataPreview(x.q_user);
      x.post_ts = timeC.format(x.post_ts * 1000, "YYYY-MM-DD hh:mm:ss");
      x.crawl_ts = timeC.format(x.crawl_ts * 1000, "YYYY-MM-DD hh:mm:ss");
      x.index_ts = timeC.format(x.index_ts * 1000, "YYYY-MM-DD hh:mm:ss");
    }
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
const modelValue = reactive({
  metaLabelList: [],
});
//初始化
onMounted(async () => {
  const rule = await siteApi.getRule(ruleForm.value.ruleId);
  const row = rule.data;
  //计算动态列
  columns.value.splice(0, columns.value.length);
  columns.value.push(...staticColumns);
  getDynamicColumn(dataC.safeObject(row.labelCondition.query));
  columns.value.push({ prop: "createdDateRender", label: "调试时间", width: 180 });
  //表格数据清空
  tableData.value.splice(0, tableData.value.length);
  //赋值表单项
  ruleForm.value = {
    ruleId: row.id,
    ruleName: row.name,
    urlList: [""],
  };
  formRef.value.resetForm();
});
// 动态列的方法
const mongoVarArgs = new Set(["EQ", "NE", "LE", "LT", "GE", "GT"]);
const existFields = new Set();
const lenFields = new Set();

const addColumn = (field, label, width = 120, sortable = false) => {
  if (!existFields.has(field)) {
    existFields.add(field);
    columns.value.push({ prop: field, label, width, sortable });
  }
};
const getDynamicColumn = (filterConfig) => {
  if (filterConfig.nodes) {
    filterConfig.nodes.forEach(getDynamicColumn);
    return;
  }
  const { field, "@type": type, op, args } = filterConfig;
  // 字符串长度类型
  if (type !== "LENGTH") {
    addColumn(field, field, field.endsWith("ts") ? 180 : 120);
  } else {
    const lenField = `${field}Len`;
    addColumn(lenField, `${field}长度`);
    lenFields.add(lenField);
  }
  // 字段之间比较类型
  if (mongoVarArgs.has(op) && /\$\{.*?\}/g.test(args[0])) {
    const comparedField = args[0].match(/\$\{(.*?)\}/)?.[1];
    if (comparedField) {
      addColumn(comparedField, comparedField, comparedField.endsWith("ts") ? 180 : 120);
    }
  }
};
</script>
<style lang="scss">
.rule-test {
  .top {
    padding: 10px 16px 0 16px;
  }

  .no-resize-textarea .el-textarea__inner {
    resize: none;
  }

  .rule-test-table {
    height: calc(100% - 200px);

    .table-page-wrapper {
      .query-wrapper {
        padding: 0 16px 12px 16px;
      }
    }
  }
}
</style>
