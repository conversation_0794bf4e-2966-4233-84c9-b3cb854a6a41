<template>
  <my-drawer class="site-version-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form 
      ref="formRef" 
      :rules="rules" 
      :ruleForm="ruleForm" 
      :formItems="formItems"
      label-width="60px">
      <template #dedupConfig>
        <dedup-config v-model="ruleForm.dedupConfig" :readOnly="isUpdate"/>
      </template>
    </my-form>
  </my-drawer>
</template>
  
<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys } from "lodash";
import DedupConfig from "@/views/common/dedup/DedupConfig.vue";
import useCtx from "@/hooks/useCtx";
import * as siteApi from "@/api/site";

const { $app } = useCtx();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}规则版本`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
     submit(ruleForm.value)
    }
  })
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  siteId: "",
  id: "",
  name: "",
  description: "",
  dedupConfig: "",
  versionRender: ""
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: { maxlength: 255 },
    separate: {
      title: "版本信息",
    },
  },
  versionRender: {
    label: "版本",
    type: "input",
    hidden: () => { return formType.value === "add" },
    attrs: {
      disabled: isUpdate,
      maxlength: 255,
      class: 'required-label'
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    } else {
      ruleForm.value = assign({}, defaultForm, row);
    }
  });
};
//提交数据
const submit = (form: any) => {
  if (isUpdate.value) {
    siteApi.modifySiteVersion(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    siteApi.insertSiteVersion(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
.site-version-edit {
}
</style>
  