<template>
  <page-wrapper route-name="site::version::">
    <div class="site-version">
      <el-tabs v-model="activeName" @tab-change="events.tabChange">
        <el-tab-pane label="站点信息" name="site-version">
          <div class="site-version-pane">
            <site-edit ref="siteEditRef" @save-data="getSite"></site-edit>
            <site-version-edit ref="siteVersionEditRef" @save-data="events.refreshVersion"></site-version-edit>
            <data-dedup ref="dataDedupRef" @dedup-data="events.dedupSiteVersion"></data-dedup>
            <el-card class="info-card" :style="{ height: activeCollapse == 1 ? '260px' : '60px' }">
              <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
                <el-collapse-item :name="1">
                  <template #title>
                    <span style="font-size: 16px; font-weight: 700">基本信息</span>&nbsp;
                    <el-button link type="primary" @click.native.stop="events.edit" :disabled="!testAuth()">
                      <el-icon size="18">
                        <Edit />
                      </el-icon>
                    </el-button>
                  </template>
                  <el-descriptions column="3">
                    <el-descriptions-item label="名称 : " label-class-name="bold">{{ modelValue.siteData.name }}</el-descriptions-item>
                    <el-descriptions-item label="Site : " label-class-name="bold">{{ modelValue.siteData.site }}</el-descriptions-item>
                    <el-descriptions-item label="domains : " label-class-name="bold">
                      <el-tag v-for="item in splitString(modelValue.siteData.domains)" type="primary">
                        {{ item }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item span="3" label="标签 : " label-class-name="bold">
                      <el-tag v-for="item in splitString(tagString, '|')" type="primary">
                        {{ item }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item span="3" label="属性 : " label-class-name="bold">
                      <el-tag v-for="item in splitString(modelValue.siteData.properties)" type="primary" class="property">
                        {{ item }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item span="3" label="描述 : " label-class-name="bold">{{ modelValue.siteData.description }}</el-descriptions-item>
                    <el-descriptions-item span="3" label="适用场景 : " label-class-name="bold">{{
                      modelValue.siteData.applicableScenarios
                    }}</el-descriptions-item>
                  </el-descriptions>
                </el-collapse-item>
              </el-collapse>
            </el-card>
            <el-card class="table-card" :style="{ height: activeCollapse == 1 ? 'calc(100vh - 440px)' : 'calc(100vh - 240px)' }">
              <el-row justify="space-between">
                <el-col :span="12">
                  <div class="el-descriptions">
                    <div class="el-descriptions__header">
                      <div class="el-descriptions__title">
                        <span>版本信息</span>&nbsp;
                        <el-button link type="primary" @click="events.refreshVersion">
                          <el-icon size="18">
                            <Refresh />
                          </el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12" style="text-align: right">
                  <el-button type="primary" @click="events.addVersion" :disabled="!testAuth()">
                    <el-icon> <CirclePlus /> </el-icon>&nbsp;新建规则版本
                  </el-button>
                </el-col>
              </el-row>
              <site-version-table
                ref="siteVersionTableRef"
                :siteId="siteId"
                :siteName="modelValue.siteData.name"
                @edit-data="events.openVersionEditWindow"
                @rule-data="events.openRulePage"
                @dedup-data="events.openDataDedupWindow"
              ></site-version-table>
            </el-card>
          </div>
        </el-tab-pane>
        <el-tab-pane label="数据预览" name="site-preview">
          <div class="site-version-pane">
            <div class="preview-table-parent">
              <preview-table ref="previewTableRef" :withExport="true"></preview-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import siteEdit from "../config/SiteEdit.vue";
import siteVersionEdit from "./VersionEdit.vue";
import dataDedup from "@/views/common/dedup/DataDedup.vue";
import siteVersionTable from "./VersionTable.vue";
import { assign, pick, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as siteApi from "@/api/site";

const { $router, $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/site/edit");
};

const siteId = $router.currentRoute.value.query.siteId;
const tab = $router.currentRoute.value.query.tab;
let metaLabel = $router.currentRoute.value.query.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
const routeName = "site::version";

const activeName = ref(tab || "site-version");

const activeCollapse = ref([1]);

const modelValue = reactive({
  siteData: {},
});
//初始化属性:来源于数据库
const getSite = () => {
  siteApi.getSite(siteId).then((result) => {
    modelValue.siteData = result;
  });
};
//初始化站点信息
onMounted(() => {
  getSite();
  if (tab == "site-preview") {
    events.dataPreview();
  }
});
//事件列表
const events = reactive({
  tabChange: (tabPaneName: string) => {
    if (tabPaneName == "site-version") {
      events.refreshVersion();
    } else if (tabPaneName == "site-preview") {
      events.dataPreview();
    }
  },
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  edit: () => {
    proxy.$refs["siteEditRef"].openWindow("edit", modelValue.siteData);
  },
  addVersion: () => {
    proxy.$refs["siteVersionEditRef"].openWindow("add", {
      siteId: modelValue.siteData.id,
    });
  },
  openVersionEditWindow: (type: string, item: any) => {
    proxy.$refs["siteVersionEditRef"].openWindow(type, item);
  },
  openDataDedupWindow: (item: any) => {
    proxy.$refs["dataDedupRef"].openWindow(item);
  },
  dedupSiteVersion: (form: any) => {
    siteApi.dedupSiteVersion(form.id, form).then((result) => {
      $app.$message.success("已添加去重任务");
      events.refreshVersion();
      proxy.$refs["dataDedupRef"].closeWindow();
    });
  },
  refreshVersion: () => {
    proxy.$refs["siteVersionTableRef"].loadList();
  },
  dataPreview: () => {
    proxy.$refs["previewTableRef"].loadList("fromSite", siteId, { siteId: siteId });
  },
  openRulePage: (siteVersionId: string, siteVersionName: string, siteVersionStatus: number) => {
    $router.push({
      name: `${routeName}::rule`,
      query: {
        siteId: siteId,
        siteVersionId: siteVersionId,
        siteVersionStatus: siteVersionStatus,
        metaLabel: [metaLabel[0], siteVersionName],
      },
    });
  },
});
const tagString = computed(() => {
  if (dataC.isEmpty(modelValue.siteData.labels)) return "";
  const list = [];
  modelValue.siteData.labels.forEach((tag) => {
    list.push(`${tag.siteKey} : ${tag.siteValue}`);
  });
  return list.join("|");
});
const splitString = (str: string, c: string = ",") => {
  return !dataC.isEmpty(str) ? str.split(c) : [];
};
</script>
<style lang="scss">
.site-version {
  .el-tabs__header {
    padding-left: 16px;
    height: 40px;
  }

  .site-version-pane {
    padding: 16px;
    height: calc(100vh - 150px);

    .preview-table-parent {
      height: calc(100vh - 160px);
    }

    .info-card {
      .bold {
        font-weight: bold;
      }

      .el-tag.property {
        margin-bottom: 5px;
      }

      .el-tag + .el-tag {
        margin-left: 10px;
      }

      .collapse {
        border: none;
        .el-collapse-item__header {
          border: none;
          height: 23px;
          margin-bottom: 12px;
        }

        .el-collapse-item__wrap {
          border: none;
        }
      }

      .el-card__body {
        height: 100%;
        .el-collapse {
          height: 100%;
          .el-collapse-item {
            height: 100%;
            .el-collapse-item__wrap {
              height: 100%;
              .el-collapse-item__content {
                height: 100%;
                .el-descriptions {
                  height: 100%;
                  .el-descriptions__body {
                    height: 100%;
                    overflow-y: auto;
                  }
                }
              }
            }
          }
        }
      }
    }

    .table-card {
      margin-top: 10px;

      .el-card__body {
        height: 100%;
      }
    }

    .query-wrapper {
      padding: 0 !important;
    }

    .table-wrapper {
      padding: 0 !important;
    }
  }
}
</style>
