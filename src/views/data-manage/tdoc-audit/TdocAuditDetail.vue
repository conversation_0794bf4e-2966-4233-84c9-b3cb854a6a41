<template>
  <page-wrapper route-name="audit-tdoc::detail::">
    <el-col class="audit-tdoc-detail">
      <preview-table ref="previewTableRef" :withSelect="true"></preview-table>
    </el-col>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted, nextTick } from "vue";
import previewTable from "@/views/common/preview/TablePreview.vue";
import useCtx from "@/hooks/useCtx";
import { dataC } from "turing-plugin";
import * as siteApi from "@/api/site";

const { $router, $app, proxy } = useCtx();

const tab = $router.currentRoute.value.query.tab;
const auditId = $router.currentRoute.value.query.auditId;
const datasetVersionId = $router.currentRoute.value.query.datasetVersionId;
const idxDbInstId = $router.currentRoute.value.query.idxDbInstId;
const site = $router.currentRoute.value.query.site;

const loadList = () => {
  proxy.$refs["previewTableRef"].loadList("fromTdocAudit", auditId, { tab, auditId, datasetVersionId, idxDbInstId, site });
};
//初始化
onMounted(() => {
  loadList();
});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.audit-tdoc-detail {
  height: 100%;
}
</style>
