<template>
  <page-wrapper route-name="tdoc-audit::">
    <div class="tdoc-audit">
      <el-tabs v-model="activeName" @tab-change="events.tabChange">
        <el-tab-pane :label="paneName0" name="pane0">
          <div class="table-pane">
            <tdoc-audit-table ref="tdocAuditTableRef0" :tab="0" :label="labelName[0]" @search-data="events.getCount"></tdoc-audit-table>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="paneName1" name="pane1">
          <div class="table-pane">
            <tdoc-audit-table ref="tdocAuditTableRef1" :tab="1" :label="labelName[1]" @search-data="events.getCount"></tdoc-audit-table>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="paneName2" name="pane2">
          <div class="table-pane">
            <tdoc-audit-table ref="tdocAuditTableRef2" :tab="2" :label="labelName[2]" @search-data="events.getCount"></tdoc-audit-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import TdocAuditTable from "./TdocAuditTable.vue";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as tdocAuditApi from "@/api/tdoc-audit";

const { $router, $app, proxy, $auth } = useCtx();

const labelName = ["待审核", "审核通过", "审核不通过"];
const total = ref([]);
const tdocAuditTableRef0 = ref(null);
const tdocAuditTableRef1 = ref(null);
const tdocAuditTableRef2 = ref(null);

const paneName0 = computed(() => {
  const item = dataC.getItemByValue(total.value, 0, "tab");
  const count = item.total || "统计中";
  return `${labelName[0]} (${count})`;
});

const paneName1 = computed(() => {
  const item = dataC.getItemByValue(total.value, 1, "tab");
  const count = item.total || "统计中";
  return `${labelName[1]} (${count})`;
});

const paneName2 = computed(() => {
  const item = dataC.getItemByValue(total.value, 2, "tab");
  const count = item.total || "统计中";
  return `${labelName[2]} (${count})`;
});

const activeName = ref("pane0");

//事件列表
const events = reactive({
  getCount: () => {
    tdocAuditApi.getCount().then((res) => {
      total.value = res.data;
    });
  },
  tabChange: (tabPaneName: string) => {
    events.getCount();
    if (tabPaneName == "pane0") {
      tdocAuditTableRef0.value.loadList();
    } else if (tabPaneName == "pane1") {
      tdocAuditTableRef1.value.loadList();
    } else if (tabPaneName == "pane2") {
      tdocAuditTableRef2.value.loadList();
    }
  },
});
//初始化
onMounted(() => {
  events.getCount();
});
</script>
<style lang="scss">
.tdoc-audit {
  .el-tabs__header {
    padding-left: 16px;
    height: 40px;
  }

  .table-pane {
    height: calc(100vh - 150px);
  }
}
</style>
