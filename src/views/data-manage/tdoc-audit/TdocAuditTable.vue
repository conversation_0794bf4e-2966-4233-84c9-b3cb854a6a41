<template>
  <div class="tdoc-audit-table">
    <MyTable
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operationAuth="authStr"
      :operations="operations"
      @operation="handleOperation"
      :withSort="withSort"
      :withSelect="true"
    >
      <template #query>
        <div class="flexBetweenEnd">
          <div>
            <el-input
              v-model="queryItems.search.modelValue"
              placeholder="请输入名称 或 Site"
              clearable
              @keydown.enter="events.search('search')"
              style="width: 220px; margin-right: 12px; margin-bottom: 12px"
            >
            </el-input>
            <el-select
              v-model="queryItems.domain.modelValue"
              placeholder="请选择领域"
              @change="events.search('domain')"
              multiple
              filterable
              clearable
              style="width: 220px; margin-right: 12px; margin-bottom: 12px"
            >
              <el-option v-for="item in modelValue.domainList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-cascader
              v-model="queryItems.dataset.modelValue"
              :options="modelValue.datasetList"
              placeholder="请选择数据集版本"
              @change="events.search('dataset')"
              filterable
              clearable
              style="width: 220px; margin-right: 12px; margin-bottom: 12px"
            ></el-cascader>
            <el-select
              v-model="queryItems.idxDbInstId.modelValue"
              placeholder="请选择索引库"
              @change="events.search('idxDbInstId')"
              filterable
              clearable
              style="width: 220px; margin-right: 12px; margin-bottom: 12px"
            >
              <el-option v-for="item in modelValue.idxDbInstList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
            <el-button :icon="Refresh" @click="events.reset" style="margin-bottom: 12px"></el-button>
          </div>
          <my-operation>
            <template #buttonGroup>
              <div class="flex" style="margin-bottom: 12px">
                <my-button type="primary" @click="events.auditOperate('accept')" v-if="waitAudit || failAudit" :operationAuth="authStr">审核通过</my-button>
                <my-button type="warning" @click="events.auditOperate('reject')" v-if="waitAudit || successAudit" :operationAuth="authStr">
                  审核不通过
                </my-button>
                <my-button type="danger" @click="events.auditOperate('purge')" v-if="failAudit" :operationAuth="authStr">彻底删除</my-button>
                <my-button type="success" @click="events.auditOperate('transfer')" v-if="successAudit" :operationAuth="authStr">入库</my-button>
                <div class="total-box" v-if="selectTotal > 0">
                  {{ $t("title.selected") }}
                  <span>{{ selectTotal }}</span>
                  {{ $t("title.item") }}
                </div>
              </div>
            </template>
          </my-operation>
        </div>
      </template>
      <template #pagination>
        <span style="margin-right: 20px">共 {{ total }} 条</span>
      </template>
    </MyTable>
  </div>
</template>
<script lang="ts" setup>
import { h, ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, cloneDeep, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import { Refresh } from "@element-plus/icons-vue";
import useCtx from "@/hooks/useCtx";
import * as tdocAuditApi from "@/api/tdoc-audit";
import * as util from "@/utils/common";
import MyTable from "@/views/common/preview/MyTable.vue";
import { ElMessageBox, ElSwitch } from "element-plus";

const { $app, proxy, $router, $auth } = useCtx();
const authStr = "/base/#/tdoc-audit/edit";
const testAuth = () => {
  return $auth.testAuth(authStr);
};
const props = defineProps(["tab", "label"]);
const myTableRef = ref(null);
const total = ref(0);
const modelValue = reactive({
  domainList: [],
  datasetList: [],
  idxDbInstList: [],
});
const waitAudit = computed(() => {
  return props.tab == 0;
});
const successAudit = computed(() => {
  return props.tab == 1;
});
const failAudit = computed(() => {
  return props.tab == 2;
});
const selectTotal = computed(() => {
  return myTableRef.value?.getSelectTotal() || 0;
});
const selectAll = computed(() => {
  return myTableRef.value?.getSelectAll() || false;
});
//列配置
const columns = computed(() => {
  const list = [
    {
      prop: "name",
      label: "名称",
      width: 160,
      custom: "link",
      blod: true,
      customRender: {
        click: (record: any) => {
          events.audit(record);
        },
        disabled: (record: any) => {
          return !testAuth() || record.isRunning;
        }
      },
    },
    { prop: "site", label: "Site", width: 160, withCopy: true },
    { prop: "description", label: "描述", minWidth: 200 },
    { prop: "totalRender", label: "数据总量", width: 120, sortable: false },
  ];
  if (waitAudit.value) {
    list.push({ prop: "earliestEntryDateRender", label: "数据最早更新时间", width: 180 });
    list.push({ prop: "latestEntryDateRender", label: "数据最新更新时间", width: 180 });
  } else if (successAudit.value || failAudit.value) {
    list.push({ prop: "lastAuditBy", label: "审核人", width: 100 });
    list.push({ prop: "lastModifiedDateRender", label: "审核时间", width: 180 });
  }
  list.push({ prop: "operation", label: "操作", width: 110, fixed: "right" });
  return list;
});
//查询面板
const query = ref<any>({
  search: "",
  labels: "",
  datasetVersionId: "",
  idxDbInstId: "",
});
const queryItems = ref<any>({
  search: {
    modelValue: "",
    defaultValue: "",
  },
  domain: {
    modelValue: [],
    defaultValue: [],
  },
  dataset: {
    modelValue: [],
    defaultValue: [],
  },
  idxDbInstId: {
    modelValue: "",
    defaultValue: "",
  },
});
const transferQueryItems = () => {
  const obj = {};
  if (!dataC.isEmpty(queryItems.value.search.modelValue)) obj.search = queryItems.value.search.modelValue;
  if (!dataC.isEmpty(queryItems.value.domain.modelValue)) obj.labels = queryItems.value.domain.modelValue.join(",");
  if (!dataC.isEmpty(queryItems.value.dataset.modelValue)) obj.datasetVersionId = queryItems.value.dataset.modelValue[1];
  if (!dataC.isEmpty(queryItems.value.idxDbInstId.modelValue)) obj.idxDbInstId = queryItems.value.idxDbInstId.modelValue;
  return obj;
};
//查询缓存
const loadHistoryQuery = (data: any) => {
  const historyQuery = dataC.safeObject(localStorage.getItem(`tdoc-audit-table-${props.tab}`));
  if (dataC.isEmpty(historyQuery)) {
    return;
  }
  keys(historyQuery).forEach((key) => {
    if (!dataC.isEmpty(queryItems.value[key])) {
      queryItems.value[key].modelValue = historyQuery[key].modelValue;
    }
  });
  data = assign(data, transferQueryItems());
};
//列表查询
const loadListData = (data: any) => {
  loadHistoryQuery(data);
  return new Promise((resolve: any) => {
    tdocAuditApi.getTdocAuditListPage(props.tab, data).then((result) => {
      total.value = result.totalElements;
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.totalRender = util.formatNumber(x.total);
    x.disabled = x.site == "default" || x.isRunning;
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.lastAuditDateRender = timeC.format(x.lastAuditDate, "YYYY-MM-DD hh:mm:ss");
    x.earliestEntryDateRender = timeC.format(x.earliestEntryDate, "YYYY-MM-DD hh:mm:ss");
    x.latestEntryDateRender = timeC.format(x.latestEntryDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  {
    type: "audit",
    label: "数据审核",
    disabled: (record: any) => record.disabled,
    disabledTips: (record: any) => {
      if (record.site == "default") {
        return "default站点不允许审核!";
      }
      if (record.isRunning) {
        return "任务正在执行中!";
      }
      return "不可操作!";
    },
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (field: String) => {
    //将非当前项置为默认值
    keys(queryItems.value).forEach((key) => {
      if (key != field) {
        queryItems.value[key].modelValue = queryItems.value[key].defaultValue;
      }
    });
    //记录查询项到缓存
    localStorage.setItem(`tdoc-audit-table-${props.tab}`, JSON.stringify(queryItems.value));
    //修改query的value
    query.value = transferQueryItems();
    //每次查询刷新tab标签上的总数
    emit("search-data");
  },
  reset: () => {
    events.search("");
  },
  audit: (record: any) => {
    $router.push({
      name: `tdoc-audit::detail`,
      query: {
        tab: props.tab,
        auditId: record.idStr,
        datasetVersionId: queryItems.value.dataset.modelValue[1],
        idxDbInstId: queryItems.value.idxDbInstId.modelValue,
        site: record.site,
        metaLabel: [`${props.label} - ${record.name}`],
      },
    });
  },
  auditOperate: (operate: String) => {
    const tableData = myTableRef.value.getTableData();
    const auditIds = tableData
      .filter((item) => {
        return item.selected == true;
      })
      .map((item) => {
        return item.idStr;
      });
    if (auditIds.length == 0) {
      $app.$message.warning("您尚未选择数据！");
      return;
    }
    const handleAuditOperate = (operate, additionalParams = {}) => {
      const obj = {
        tab: props.tab,
        selectedAll: selectAll.value,
        auditIds: auditIds,
        ...additionalParams,
      };
      const auditListOperateDto = assign({}, transferQueryItems(), obj);
      tdocAuditApi.auditListOperate(operate, auditListOperateDto).then((result) => {
        $app.$message.success("操作成功");
        //后端为了保证数据一致性，并没有直接同步设置runing状态，因此loadList()会出错，因此我们直接设置前端的状态
        tableData
          .filter((item) => item.selected)
          .forEach((item) => {
            item.selected = false;
            item.isRunning = true;
            item.disabled = true;
          });
      });
    };
    if (operate === "transfer") {
      const buildTask = ref(false);
      ElMessageBox({
        title: "确认入库?",
        message: () =>
          h(ElSwitch, {
            activeText: "增量数据自动更新",
            modelValue: buildTask.value,
            "onUpdate:modelValue": (val: boolean) => {
              buildTask.value = val;
            },
          }),
        showCancelButton: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消",
      }).then(() => {
        handleAuditOperate(operate, { buildTask: buildTask.value });
      });
    } else {
      $app.$confirm({ title: `确认操作？` }).then(() => {
        handleAuditOperate(operate);
      });
    }
  },
});
const loadList = () => {
  myTableRef.value.loadData();
};
const getCount = () => {
  if (myTableRef.value) {
    const count = myTableRef.value.getTableData().reduce((sum, item) => {
      return sum + item.total;
    }, 0);
    return util.formatNumber(count);
  } else {
    return "统计中";
  }
};
//初始化
onMounted(() => {
  loadList();
  tdocAuditApi.getCondDomainList().then((result) => {
    modelValue.domainList = result.data;
  });
  tdocAuditApi.getCondDatasetList().then((result) => {
    modelValue.datasetList = result.data;
  });
  tdocAuditApi.getCondIdxDbList().then((result) => {
    modelValue.idxDbInstList = result.data;
  });
});
//事件声明
const emit = defineEmits(["search-data"]);
//接口暴露
defineExpose({
  loadList,
  getCount,
});
</script>
<style lang="scss">
.tdoc-audit-table {
  height: 100%;

  .el-checkbox:last-of-type {
    height: 20px;
  }

  .total-box {
    font-size: 16px;
    color: $text-color-secondary;
    margin-left: 12px;
    white-space: nowrap;
    > b,
    > span {
      display: inline-block;
    }
    & > b {
      color: $text-color;
      margin-right: 16px;
    }
    & > span {
      color: $primary-color;
      margin: 0 6px;
    }
  }
}
</style>
