<template>
  <my-drawer class="analysis-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form labelWidth="130px" ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit">
      <template #file>
        <my-upload ref="uploadRef" maxSize="10M" accept=".xls,.xlsx" v-model="ruleForm.file" drag drag-style :showDownload="true" :downText="'下载模板'" @download="downloadTemplate" >
          <my-button type="primary" plain>上传文件</my-button>
        </my-upload>
      </template>
    </my-form>
  </my-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as evaluationApi from "@/api/eval-evaluation";
import * as commonApi from "@/api/common";
import * as util from "@/utils/common";

const { $app, proxy } = useCtx();
const { common } = useStore();
const props = defineProps({
  groupEnum: { type: Array, default: [] },
});
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增分析计划";
  if (formType.value == "edit") return "编辑分析计划";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  groupName: "",
  description: "",
  mode: "",
  processId: "",
  sceneProcId: "",
  
};
let ruleForm = ref<any>(assign({}, defaultForm));
const sceneProcIdValidator = (rule: any, value: any, callback: any) => {
  if (ruleForm.value.mode === 0 && dataC.isEmpty(value)) {
    return callback(new Error("场景策略版本不能为空"));
  } else {
    callback();
  }
};
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  groupName: [{ required: true, message: "分组不能为空", trigger: "change" }],
  mode: [{ required: true, message: "模式不能为空", trigger: "change" }],
  processId: [{ required: true, message: "归因策略不能为空", trigger: "change" }],
  sceneProcId: [{ validator: sceneProcIdValidator, trigger: "change" }],
  file: [{ required: true, message: "请上传文件", trigger: "blur" }],
});
//数据项
const modelValue = reactive({
  ascribeList: {
    0: [],
    1: [],
  },
  anasisList: [],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "计划名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入计划名称",
    },
  },
  groupName: {
    label: "分组",
    type: "select",
    options: computed(() => props.groupEnum),
    attrs: {
      allowCreate: true,
    },
  },
  description: {
    label: "计划描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      placeholder: "请输入计划描述",
    },
  },
  mode: {
    label: "归因模式",
    type: "select",
    options: [
      { value: 0, label: "竞品good" },
      { value: 1, label: "自研bad" },
    ],
    attrs: {
      maxlength: 255,
      placeholder: "请选择归因模式",
    },
    disabled: (record: any) => record.status !== 0 && isUpdate.value,
    events: {
      change: (val: any) => {
        ruleForm.value.processId = "";
      },
    },
    disabled: (record: any) => record.status !== 0 && isUpdate.value,
  },
  processId: {
    label: "归因策略",
    type: "select",
    options: computed(() => {
      return modelValue.ascribeList[ruleForm.value.mode];
    }),
    attrs: {
      placeholder: "请输入归因策略",
    },
    disabled: () => isUpdate.value,
  },
  sceneProcId: {
    label: "场景策略版本",
    type: "select",
    options: computed(() => {
      return modelValue.anasisList;
    }),
    attrs: {
      placeholder: "请输入场景策略版本",
      class: computed(() => {
        return ruleForm.value.mode === 0 ? "required-label" : "";
      }),
    },
    disabled: () => isUpdate.value,
  },
  file: {
    label: "上传文件",
    type: "slot",
    slotName: "file",
    hidden: () => {
      return isUpdate.value;
    },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    } else {
      ruleForm.value = assign({}, defaultForm, row);
      proxy.$refs.uploadRef.clearFiles();
    }
  });
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  //采用clear按钮会把数值置为undefined,然后提交表单时会遗漏这个参数，这里如果为空置为空串""
  form.sceneProcId = dataC.isEmpty(form.sceneProcId) ? "" : form.sceneProcId;
  if (isUpdate.value) {
    evaluationApi.modifyPlan(form.id, form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    evaluationApi.insertPlan(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//归因策略
const getAscribeList = async (type: any, name = "") => {
  const res = await evalEvaluationApi.getStrategyVersion(type, { name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
//场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
const downloadTemplate = () => {
  if(ruleForm.value.mode !== 0&&ruleForm.value.mode !== 1){
    $app.$message.warning("请先选择归因模式");
    return;
  }
  evaluationApi.exportExcel(ruleForm.value.mode).then((result) => {
    util.downloadFile(result, ruleForm.value.mode !== 0?"自研bad模板.xlsx":"竞品good模板.xlsx");
    });
};
//初始化
onMounted(async () => {
  modelValue.ascribeList[0] = await getAscribeList(0);
  modelValue.ascribeList[1] = await getAscribeList(1);
  modelValue.anasisList = await getAnasisList();
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>