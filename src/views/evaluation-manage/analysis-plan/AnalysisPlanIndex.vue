<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="analysis-plan-index">
      <analysis-edit ref="analysisPlanEditRef" @save-data="events.loadInstTableList" :groupEnum="groupEnum"></analysis-edit>
      <analysis-table ref="analysisPlanTableRef" @edit-data-inst="events.openInstEditWindow" @statistic-data="events.openStatisticPage" :groupEnum="groupEnum"></analysis-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import analysisTable from "./AnalysisTable.vue";
import analysisEdit from "./AnalysisEdit.vue";
import useCtx from "@/hooks/useCtx";
import * as evaluationApi from "@/api/eval-evaluation";
import useStore from '@/store'
import { storeToRefs } from "pinia";
const { common } = useStore()
const { $app, proxy, $router } = useCtx();
const routeName = "analysis-plan";
let groupEnum =ref([])
const events = reactive({
  loadInstTableList: () => {
    proxy.$refs["analysisPlanTableRef"].loadList();
  },
  openInstEditWindow: (type: string, item: any) => {
    proxy.$refs["analysisPlanEditRef"].openWindow(type, item);
  },
  openStatisticPage: (item: any,mode: string) => {
    $router.push({
      name: `${routeName}::details`,
      query: {
        planId: item.id,
        metaLabel: [item.name],
        mode:mode||(item.mode == 0?'good':'bad'),
        failNum: item.fail || 0,
        successNum: item.success || 0,
        totalNum: item.total || 0,

      },
    });
  }
});
const getGroups =()=>{
  evaluationApi.getPlanGroupNameData().then((res)=>{
    groupEnum.value =  res.data.map(item=>({value:item,label:item}))
  })
}
getGroups()
</script>
<style lang="scss">
</style>