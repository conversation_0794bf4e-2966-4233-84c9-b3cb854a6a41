<template>
  <page-wrapper route-name="analysis-plan::details::position::">
    <Position :loadDataApi="loadListData"/>
  </page-wrapper>
</template>

<script lang="ts" setup>
import useCtx from "@/hooks/useCtx";
import { ref, reactive, onMounted, nextTick } from "vue";
import Position from "@/views/common/position/index.vue";
import * as evaluationApi from "@/api/eval-evaluation";
const { $router, proxy, $app } = useCtx();
const routeQuery = ref($app.$route.query);
//列表查询
const loadListData = () => {
  const params = $app.$route.query.queryId;
  return evaluationApi.getTraceinfo(params);
};
</script>
<style lang="scss" scoped>
</style>