<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="idx-db-inst">
      <idx-db-inst-edit ref="attributionStrategyEditRef" @save-data="events.loadInstTableList" :groupEnum="groupEnum"></idx-db-inst-edit>
      <idx-db-inst-table ref="attributionStrategyTableRef" @edit-data-inst="events.openInstEditWindow"  @statistic-data="events.openStatisticPage"></idx-db-inst-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import idxDbInstTable from "./Table.vue";
import idxDbInstEdit from "./Edit.vue";
import useCtx from "@/hooks/useCtx";
import * as evaluationApi from "@/api/eval-evaluation";

const { $app, proxy, $router } = useCtx();
const routeName = "attribution-strategy";
let groupEnum =  ref([])
const events = reactive({
  loadInstTableList: () => {
    proxy.$refs["attributionStrategyTableRef"].loadList();
  },
  openInstEditWindow: (type: string, item: any) => {
    proxy.$refs["attributionStrategyEditRef"].openWindow(type, item);
  },
  openStatisticPage: (item: any,mode: string) => {
    $router.push({
      name: `${routeName}::details`,
      query: {
        strategyId: item.id,
        metaLabel: [item.name],
      },
    });
  }
});
const getGroups =()=>{
  evaluationApi.getPlanGroupNameData().then((res)=>{
    groupEnum.value =  res.data.map(item=>({value:item,label:item}))
  })
}
getGroups()
</script>
<style lang="scss">
.idx-db-inst {
}
</style>