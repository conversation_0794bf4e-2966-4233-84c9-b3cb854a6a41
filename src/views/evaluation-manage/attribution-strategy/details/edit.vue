<template>
  <my-drawer class="idx-db-inst-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form
      labelWidth="120px"
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      @submit="submit"
    >
    </my-form>
  </my-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import DedupConfig from "@/views/common/dedup/DedupConfig.vue";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as evaluationApi from "@/api/eval-evaluation";
import * as datasetApi from "@/api/dataset";
import * as util from "@/utils/common";
import { CODE_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";

const { $app, proxy } = useCtx();
const { api } = useStore();
const props = defineProps({
  activeName : { type: Object, default: '' }
});
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增版本信息";
  if (formType.value == "edit") return "编辑版本信息";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
//是否为发布
const isPublish = computed(() => {
  return formType.value === "publish";
});
//是否执行过发布
const hasTask = computed(() => {
  return modelValue.taskStatus != 1;
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  code:"",
  description: ""
};
const extraForm = computed(()=>({
  strategyId:$app.$route.query.strategyId,
  category: props.activeName

  
}))
let ruleForm = ref<any>(assign({}, defaultForm, extraForm.value));
const { validateCodeRule } = useValidate();
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "归因版本名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入归因版本名称",
    },
  },

  description: {
    label: "归因版本描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      placeholder: "请输入归因版本描述",
    },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm, extraForm.value)));
    } else {
      ruleForm.value = assign({}, defaultForm, extraForm.value, row);
    }
  });
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  form.name = form.name.trim();
  if (isUpdate.value) {
    evaluationApi.modifyStrategyVersion(formData.id,form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  }  else {
    evaluationApi.insertStrategyVersion(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//数据项
const modelValue = reactive({
  datasetList: [],
  datasetVersionList: [],
  taskStatus: 1,
});
//初始化
onMounted(() => {
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>