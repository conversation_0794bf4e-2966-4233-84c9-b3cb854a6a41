<template>
  <div class="annotation-component">
    <!-- 空状态提示 -->
    <div v-if="!annotationData || annotationData.length === 0" class="empty-state">
      <p>暂无标注数据</p>
    </div>

    <div v-else class="dimensions-container">
      <div v-for="dimension in annotationData" :key="dimension.code" class="dimension-column">
        <!-- 维度标题 -->
        <div class="dimension-header">
          <div class="dimension-title">
            {{ dimension.name }}
            <span v-if="dimension.required" class="required-mark">*</span>
          </div>
        </div>

        <!-- 维度选项 -->
        <div class="dimension-content">
          <!-- 单选模式 -->
          <div v-if="dimension.optType === 1" class="single-select-options">
            <div v-for="option in dimension.children" :key="option.code" class="option-item">
              <el-checkbox v-model="option.value" @change="handleLevel1Change(dimension, option)"
                class="option-checkbox">
                {{ option.name }}
              </el-checkbox>

              <!-- 递归渲染子级选项 - 默认展开所有层级 -->
              <div v-if="option.children.length > 0" class="sub-options level-indent">
                <AnnotationLevel :options="option.children" :parent-value="option.value"
                  :parent-opt-type="option.optType || 1" :parent-option="option"
                  @change="handleSubLevelChange(option, $event)" @cascade-select="handleCascadeSelect(option, dimension)" />
              </div>
            </div>
          </div>

          <!-- 多选模式 -->
          <div v-else-if="dimension.optType === 2" class="multi-select-options">
            <div v-for="option in dimension.children" :key="option.code" class="option-item">
              <el-checkbox v-model="option.value" @change="handleLevel1Change(dimension, option)"
                class="option-checkbox">
                {{ option.name }}
              </el-checkbox>

              <!-- 递归渲染子级选项 - 默认展开所有层级 -->
              <div v-if="option.children.length > 0" class="sub-options level-indent">
                <AnnotationLevel :options="option.children" :parent-value="option.value"
                  :parent-opt-type="option.optType || 1" :parent-option="option"
                  @change="handleSubLevelChange(option, $event)" @cascade-select="handleCascadeSelect(option, dimension)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue'
import { cloneDeep } from 'lodash'
import AnnotationLevel from './AnnotationLevel.vue'

const props = defineProps<{
  modelValue: any[] // 标注字典树
}>()

const emits = defineEmits(['update:modelValue'])

// 响应式数据
const annotationData = ref<any[]>([])

// 初始化数据
const initData = () => {
  console.log("初始化标注组件：", props.modelValue);

  // 处理空值情况，避免 map 方法报错
  if (!props.modelValue || !Array.isArray(props.modelValue)) {
    annotationData.value = []
    return
  }

  annotationData.value = cloneDeep(props.modelValue).map(item => {
    // 统一使用boolean值表示是否选中
    item.value = item.value === true || item.value === false ? item.value : false

    // 初始化子选项的value
    initChildrenValues(item)
    return item
  })
}

// 初始化子选项的值
const initChildrenValues = (node: any) => {
  if (node && node.children && Array.isArray(node.children) && node.children.length > 0) {
    node.children.forEach((child: any) => {
      if (child) {
        // 统一使用boolean值表示是否选中
        child.value = child.value === true || child.value === false ? child.value : false
        initChildrenValues(child)
      }
    })
  }
}

// 处理level 1选项变化
const handleLevel1Change = (dimension: any, changedOption: any) => {
  console.log(`Level 1选项变化 - 维度: ${dimension.name}, 选项: ${changedOption.name}, 状态: ${changedOption.value}`);

  // 处理单选模式的互斥逻辑
  if (dimension.optType === 1 && changedOption.value) {
    // 单选模式：如果选中了一个选项，取消其他选项的选中状态
    dimension.children.forEach((child: any) => {
      if (child.code !== changedOption.code) {
        child.value = false
        console.log(`单选模式 - 清空未选中选项 ${child.name} 的子选项`);
        clearChildrenValues(child)
      }
    })
  }

  // 如果选项被取消选中，清空其子选项
  if (!changedOption.value) {
    console.log(`清空选项 ${changedOption.name} 的子选项`);
    clearChildrenValues(changedOption)
  }

  // 如果选项被选中，触发级联选中
  if (changedOption.value) {
    // 设置维度为选中状态
    dimension.value = true
  } else {
    // 检查是否还有其他选项被选中
    const hasSelectedChild = dimension.children.some((child: any) => child.value === true)
    dimension.value = hasSelectedChild
  }

  emitUpdate()
}

// 处理子级选项变化
const handleSubLevelChange = (option: any, value: any) => {
  // 更新子级选项的值
  console.log("子级别选项变化：", option, value);

  if (value && Array.isArray(value)) {
    value.forEach((item: any) => {
      const childOption = option.children.find((child: any) => child.code === item.code)
      if (childOption) {
        childOption.value = item.value
      }
    })
  }
  emitUpdate()
}

// 处理级联选中事件
const handleCascadeSelect = (option: any, dimension: any) => {
  console.log("顶层组件处理级联选中：", option, dimension);

  // 确保level 1选项被选中
  if (option) {
    console.log("级联选中 - 选中选项:", option.name);
    option.value = true

    // 处理单选模式的互斥逻辑
    if (dimension.optType === 1) {
      // 单选模式：取消其他选项的选中状态
      dimension.children.forEach((child: any) => {
        if (child.code !== option.code) {
          child.value = false
          clearChildrenValues(child)
        }
      })
    }

    // 设置维度为选中状态
    dimension.value = true
  }

  emitUpdate()
}

// 清空子级选项的值
const clearChildrenValues = (node: any) => {
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      // 统一使用boolean值表示是否选中
      child.value = false
      clearChildrenValues(child)
    })
  }
}

// 发送更新事件
const emitUpdate = () => {
  emits('update:modelValue', cloneDeep(annotationData.value))
}

// 监听props变化
watch(() => props.modelValue, () => {
  console.log("annotationComponent watch modelValue:");
  
  // initData()
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.annotation-component {
  font-size: 13px;
  line-height: 1.4;

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
    font-size: 14px;

    p {
      margin: 0;
    }
  }

  .dimensions-container {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    flex-wrap: wrap;
    /* 允许换行 */

    .dimension-column {
      flex: 0 0 auto;
      /* 完全自适应内容宽度 */
      min-width: 100px;
      max-width: 350px;

      .dimension-header {
        margin-bottom: 8px;

        .dimension-title {
          font-weight: bold;
          font-size: 13px;
          line-height: 1.2;
          margin-bottom: 0;
          white-space: nowrap;
          /* 标题不换行 */

          .required-mark {
            color: #f56c6c;
            margin-left: 2px;
          }
        }
      }

      .dimension-content {
        .option-group {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .option-item {

            .option-radio,
            .option-checkbox {
              width: 100%;
              margin-right: 0;
              word-break: break-word;
              /* 长文本换行 */
            }

            .sub-options {
              margin-top: 4px;
            }
          }
        }

        /* 树形结构缩进样式 - 累积缩进 */
        .level-indent {
          margin-left: 1.5em;
          /* 每层缩进1.5em，会累积 */
          position: relative;
          padding-left: 0.5em;
          /* 增加内边距使内容更清晰 */

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #e4e7ed;
            /* 使用更明显的颜色和宽度 */
          }
        }
      }
    }
  }
}

::v-deep .el-radio {
  margin-right: 0;
  margin-bottom: 3px;
  width: 100%;
  height: auto;
  align-items: flex-start;

  .el-radio__input {
    margin-right: 4px;
  }

  .el-radio__label {
    padding-left: 4px;
    font-size: 12px;
    line-height: 1.3;
    word-break: break-word;
    flex: 1;
  }
}

::v-deep .el-checkbox {
  margin-right: 0;
  margin-bottom: 3px;
  width: 100%;
  height: auto;
  align-items: flex-start;

  .el-checkbox__input {
    margin-right: 4px;
  }

  .el-checkbox__label {
    padding-left: 4px;
    font-size: 12px;
    line-height: 1.3;
    word-break: break-word;
    flex: 1;
  }
}

::v-deep .el-radio-group {
  gap: 2px;
}
</style>
