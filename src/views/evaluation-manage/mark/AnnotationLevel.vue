<template>
  <div class="annotation-level">
    <!-- 单选模式 -->
    <div v-if="parentOptType === 1" class="radio-group-container">
      <div v-for="option in options" :key="option.code" class="level-option">
        <el-checkbox v-model="option.value" @change="handleOptionChange(option)" class="level-checkbox">
          {{ option.name }}
        </el-checkbox>

        <!-- 子级选项 - 默认展开所有层级 -->
        <div v-if="option.children.length > 0" class="sub-options level-indent">
          <AnnotationLevel :options="option.children" :parent-opt-type="option.optType || 1"
            :parent-option="option" @change="handleSubChange" @cascade-select="handleCascadeSelect" />
        </div>
      </div>
    </div>

    <!-- 多选模式 -->
    <div v-else class="checkbox-group-container">
      <div v-for="option in options" :key="option.code" class="level-option">
        <el-checkbox v-model="option.value" @change="handleOptionChange(option)" class="level-checkbox">
          {{ option.name }}
        </el-checkbox>

        <!-- 子级选项 - 默认展开所有层级 -->
        <div v-if="option.children.length > 0" class="sub-options level-indent">
          <AnnotationLevel :options="option.children" :parent-opt-type="option.optType || 1"
            :parent-option="option" @change="handleSubChange" @cascade-select="handleCascadeSelect" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, onMounted } from 'vue'

const props = defineProps<{
  options: any[] // 当前子级别树
  parentValue?: Boolean  //
  parentOptType?: number // 父级的optType，决定当前层级是单选还是多选
  parentOption?: any // 父级选项对象，用于级联选中
}>()

const emits = defineEmits(['change', 'cascadeSelect'])

// 初始化单选模式
const initRadioValue = () => {
  if (props.parentOptType === 1) {
    // 查找已选中的选项
    const selectedOption = props.options.find(option => option.value === true)

    // 确保单选模式下只有一个选项被选中
    if (selectedOption) {
      props.options.forEach(option => {
        if (option.code !== selectedOption.code) {
          option.value = false
        }
      })
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  initRadioValue()
})

// 处理选项变化
const handleOptionChange = (option: any) => {
  console.log(`选项 ${option.name} 状态变化为:`, option.value);

  // 处理单选模式的互斥逻辑
  if (props.parentOptType === 1 && option.value) {
    // 单选模式：如果选中了一个选项，取消其他选项的选中状态
    props.options.forEach(opt => {
      if (opt.code !== option.code) {
        opt.value = false
        clearChildrenValues(opt)
      }
    })
  }

  // 如果选中，触发级联选中父级
  if (option.value) {
    // 确保当前选项被选中
    option.value = true
    // 触发级联选中父级
    emitCascadeSelect()
  }

  // 如果取消选中，清空所有子选项
  if (!option.value) {
    console.log(`清空选项 ${option.name} 的所有子选项`);
    clearChildrenValues(option)
  }

  // 发送变化事件
  emitChange()
}

// 处理子级变化
const handleSubChange = () => {
  emitChange()
}

// 处理级联选中事件
const handleCascadeSelect = () => {
  // 先选中当前层级的父选项，然后向上传递级联选中事件
  selectCurrentParent()
  emitCascadeSelect()
}

// 选中当前层级的父选项
const selectCurrentParent = () => {
  if (props.parentOption) {
    console.log("级联选中当前层级的父选项:", props.parentOption.name);

    // 根据父级的optType来选中父选项
    if (props.parentOptType === 1) {
      // 单选模式：设置父选项为选中状态
      props.parentOption.value = true
    } else if (props.parentOptType === 2) {
      // 多选模式：设置父选项为选中状态
      props.parentOption.value = true
    }
  }
}

// 发送级联选中事件
const emitCascadeSelect = () => {
  console.log("子组件触发级联选中事件", props.parentOption);

  // 向上传递级联选中事件，让更上层的组件也能处理级联选中
  emits('cascadeSelect', props.parentOption)
}

// 清空子选项的值
const clearChildrenValues = (node: any) => {
  if (node.children && node.children.length > 0) {
    console.log(`清空节点 ${node.name} 的子选项:`, node.children.map((c: any) => c.name));
    node.children.forEach((child: any) => {
      // 统一使用boolean值表示是否选中
      child.value = false

      // 递归清空更深层的子选项
      clearChildrenValues(child)
    })
  }
}

// 发送变化事件
const emitChange = () => {
  // 收集当前层级所有选项的值
  const values = props.options.map(option => ({
    code: option.code,
    value: option.value,
    children: option.children
  }))

  emits('change', values)
}

// 监听父级值变化，如果父级取消选中，清空当前层级所有值
watch(() => props.parentValue, (newVal) => {
  if (!newVal) {
    props.options.forEach(option => {
      option.value = false
      clearChildrenValues(option)
    })
  }
})
</script>

<style lang="scss" scoped>
.annotation-level {
  .level-option {
    margin-bottom: 3px;

    .radio-group-container,
    .checkbox-group-container {

      .level-radio,
      .level-checkbox {
        width: 100%;
        margin-right: 0;
        margin-bottom: 3px;
      }

      .sub-options {
        margin-top: 3px;
      }
    }
  }
}

/* 树形结构缩进样式 - 累积缩进 */
.level-indent {
  margin-left: 1.5em;
  /* 每层缩进1.5em，会累积 */
  position: relative;
  padding-left: 0.5em;
  /* 增加内边距使内容更清晰 */

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e4e7ed;
    /* 使用更明显的颜色和宽度 */
  }
}

::v-deep .el-checkbox,
::v-deep .el-radio {
  margin-right: 0;
  margin-bottom: 2px;
  width: 100%;
  align-items: flex-start;

  .el-checkbox__label,
  .el-radio__label {
    padding-left: 6px;
    font-size: 12px;
    line-height: 1.3;
    word-break: break-word;
    flex: 1;
  }
}

::v-deep .el-radio-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 2px;
}
</style>
