# DocAnnotation 组件使用说明

## 组件介绍

`DocAnnotation` 是一个文档标注组件，参照 `MarkItem` 组件开发，采用左右布局设计：
- **左侧 (70%)**：展示文档内容
- **右侧 (30%)**：标注组件区域

## 功能特性

- **左右分栏布局**：7:3 比例，左侧展示文档，右侧进行标注
- **响应式设计**：小屏幕下自动切换为上下布局
- **文档信息展示**：序号、标题、URL、内容、时间、ID、来源等
- **内容折叠展开**：长内容支持折叠/展开功能
- **标注功能集成**：右侧集成完整的标注组件
- **交互功能**：支持复制、点击跳转等操作

## 组件结构

### 左侧文档展示区域

#### 1. 文档头部 (doc-header)
```
┌─────────┬──────────────────┬──────────────────┐
│  序号   │      title       │       url        │
├─────────┼──────────────────┼──────────────────┤
│   1     │   文档标题链接    │   文档URL链接     │
└─────────┴──────────────────┴──────────────────┘
```

#### 2. 内容区域 (doc-content-area)
```
┌─────────────────────────────────────────────────┐
│                   content                       │
├─────────────────────────────────────────────────┤
│                                                 │
│              文档内容展示区域                    │
│            (支持折叠/展开功能)                   │
│                                                 │
└─────────────────────────────────────────────────┘
```

#### 3. 文档底部 (doc-footer)
```
┌──────────┬─────────┬──────────────┬─────────────┐
│ 发布时间  │   ID    │ 来源(仅中文) │ 其他配置字段 │
├──────────┼─────────┼──────────────┼─────────────┤
│2023-12-21│doc_123  │ 医疗资讯(med)│ 策略数据展示 │
└──────────┴─────────┴──────────────┴─────────────┘
```

### 右侧标注区域

集成 `AnnotationComponent` 组件，支持多层级标注功能。

## 使用方法

### 基本用法

```vue
<template>
  <DocAnnotation
    :doc-data="docData"
    :doc-index="0"
    v-model:annotation-data="annotationData"
    @title-click="handleTitleClick"
    @url-click="handleUrlClick"
  />
</template>

<script setup>
import DocAnnotation from './DocAnnotation.vue'

const docData = ref({
  id: 'doc_123',
  title: '文档标题',
  url: 'https://example.com',
  content: '文档内容...',
  post_ts: 1703123456,
  _indexName: '来源名称',
  _indexCode: 'source_code'
})

const annotationData = ref([
  // 标注数据结构
])
</script>
```

### Props

| 属性 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `docData` | `Object` | ✅ | 文档数据对象 |
| `docIndex` | `Number` | ✅ | 文档索引（用于显示序号） |
| `annotationData` | `Array` | ✅ | 标注数据数组 |

### DocData 数据结构

```typescript
interface DocData {
  id?: string           // 文档ID
  title?: string        // 文档标题
  url?: string          // 文档URL
  content?: string      // 文档内容
  post_ts?: number      // 发布时间戳
  _indexName?: string   // 来源名称
  _indexCode?: string   // 来源代码
}
```

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:annotationData` | `newValue: Array` | 标注数据更新 |
| `titleClick` | `docData: Object` | 标题点击事件 |
| `urlClick` | `docData: Object` | URL点击事件 |

## 样式特性

### 布局比例
- **桌面端**：左侧 70%，右侧 30%
- **移动端**：上下布局，自适应高度

### 响应式断点
- `max-width: 1200px`：切换为上下布局

### 颜色主题
- 主色调：`#409eff`
- 边框色：`#e4e7ed`
- 背景色：`#f5f7fa`
- 文字色：`#303133` / `#606266` / `#909399`

## 功能详解

### 1. 内容折叠功能
- 内容超过 200 字符时显示折叠/展开按钮
- 折叠状态显示 6 行内容
- 支持手动切换展开/折叠状态

### 2. 复制功能
- URL 和 ID 字段支持一键复制
- 使用 `copyText` 工具函数

### 3. 时间格式化
- 自动格式化时间戳为 `YYYY-MM-DD HH:mm:ss`
- 无效时间显示"暂无时间"

### 4. 链接处理
- 标题和URL支持点击事件
- 可自定义点击行为（跳转、弹窗等）

## 注意事项

1. **数据校验**：组件内部会处理空数据，显示默认文本
2. **性能优化**：长内容默认折叠，减少渲染压力
3. **样式兼容**：使用 Element Plus 设计规范
4. **响应式适配**：小屏幕下自动调整布局

## 扩展建议

1. **自定义字段**：可在底部区域添加更多配置字段
2. **主题定制**：支持自定义颜色主题
3. **导出功能**：添加文档导出功能
4. **搜索高亮**：支持关键词搜索和高亮显示

## 文件结构

- `DocAnnotation.vue`：主组件
- `DocAnnotationExample.vue`：使用示例
- `DocAnnotation-README.md`：使用文档
