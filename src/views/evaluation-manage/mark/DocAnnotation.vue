<template>
  <div class="doc-annotation">
    <el-dialog title="全链路" v-model="allChainVisible" fullscreen class="position-dialog">
      <template #header> <el-button type="primary" @click="closeTraceLogs" :icon="Back">返回</el-button><Strong></Strong>
      </template>
      <MarkPosition ref="markPositionRef"></MarkPosition>
    </el-dialog>
    <el-card class="doc-card">
      <div class="doc-content-wrapper">
        <!-- 左侧：Doc展示区域 (60%) -->
        <div class="doc-display-section">
          <!-- 第一行：序号、title、url -->
          <div class="doc-header">
            <div class="doc-field sequence-field">
              <div class="field-content doc-index">{{ docIndex + 1 }}</div>
            </div>
            <div class="doc-field title-field">
              <div class="field-content">
                <el-tooltip effect="dark" content="标题" placement="top">
                  <el-button link type="primary" @click="handleTitleClick" class="title-link">
                    {{ docData.title || "暂无标题" }}
                  </el-button>
                </el-tooltip>
              </div>
            </div>
            <div class="doc-field url-field">
              <el-tooltip effect="dark" content="url" placement="top">
                <div class="field-content">
                  <el-button link type="primary" @click="handleUrlClick" class="url-link">
                    <span class="url-text">{{ docData.url || "暂无链接" }}</span>
                  </el-button>
                  <el-icon class="icon-copy" @click="copyText(docData.url)" v-if="docData.url">
                    <CopyDocument />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
          </div>

          <!-- 第二行：content内容区域 -->
          <div class="doc-content-area">
            <div class="content-body">
              <div :class="['content-text', { collapsed: isContentCollapsed, scored: displayScore }]">
                {{ docData.content || "暂无内容" }}
              </div>
            </div>
            <div class="content-toggle-wrapper">
              <el-button link type="primary" class="content-toggle" @click="toggleContent">
                {{ isContentCollapsed ? "[展开]" : "[折叠]" }}
              </el-button>
            </div>
          </div>

          <!-- 第三行：发布时间、ID、来源、其他配置字段 -->
          <div class="doc-footer">
            <div class="doc-field time-field">
              <el-tooltip effect="dark" content="发布时间" placement="top">
                <div class="field-content">
                  {{ formatTime(docData.post_ts) }}
                </div>
              </el-tooltip>
            </div>
            <div class="doc-field id-field">
              <el-tooltip effect="dark" content="doc ID" placement="top">
                <div class="field-content">
                  <span>{{ docData.id || "暂无ID" }}</span>
                  <el-icon class="icon-copy" @click="copyText(docData.id)" v-if="docData.id">
                    <CopyDocument />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
            <div class="doc-field source-field">
              <el-tooltip effect="dark" content="数据来源" placement="top">
                <div class="field-content">
                  {{ getSourceText() }}
                </div>
              </el-tooltip>
            </div>
            <div v-if="!dataC.isEmpty(props.extendFields)" class="doc-field config-field">
              <div class="field-content config-content ellipsis">
                <el-tooltip effect="dark" :content="extendFieldsText()" placement="top">
                  <span class="ellipsis-text">{{ extendFieldsText() }}</span>
                </el-tooltip>
              </div>
            </div>
          </div>

          <!-- 第四行：策略数据 -->
          <el-row v-if="displayScore" :gutter="8" class="doc-score">
            <el-col :span="24" class="flex">
              <div v-for="item in scoreRanks" class="flex">
                <router-link :to="getAllChainTo(item)">
                  <template v-if="item.code != 'ai-process'">
                    <el-badge :value="`[${item.index}]`" :offset="[10, 0]" badge-class="badge" @click="allChain">
                      <span style="cursor: pointer; color: black">{{ item.name }}<span
                          v-if="item.score === 0 || item.score">：</span></span>
                      <span style="cursor: pointer; color: green">{{ item.score === 0 || item.score ?
                        Number(item.score).toFixed(7) : "" }}</span>
                    </el-badge>
                    <el-popover v-if="item.props" placement="right" :width="250" trigger="hover">
                      <template #reference>
                        <el-icon class="icon-tip">
                          <Notification />
                        </el-icon>
                      </template>
                      <div v-for="str in getPropsStrList(item.props)">
                        {{ str }}
                      </div>
                    </el-popover>
                  </template>
                  <template v-if="item.code == 'ai-process'">
                    <span style="cursor: pointer; color: black" @click="allChain">{{ item.name }}：</span>
                    <span class="badge" @click="allChain">{{ `[${item.index}]` }}</span>
                  </template>
                </router-link>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 右侧：标注组件区域 (动态宽度，最大30%) -->
        <div class="annotation-section" ref="annotationSectionRef" v-show="disabled">
          <div class="annotation-header" style="display: flex">
            <h4>标注区域</h4>
            <el-button style="margin-left: 8px" @click="toggleDocIgnore" size="small"
              :class="{ active: docIgnoreEnabled }">弃标</el-button>
          </div>
          <div v-if="!docIgnoreEnabled" class="annotation-content">
            <AnnotationTreeComponent
              :model-value="dataC.isEmpty(annotationData) ? standardConfig.recall : annotationData"
              @update:modelValue="handleAnnotationChange" />
          </div>

          <div v-if="docIgnoreEnabled" class="annotation-content">
            <AnnotationTreeComponent
              :model-value="dataC.isEmpty(annotationData) ? standardConfig.docIgnore : annotationData"
              @update:modelValue="handleAnnotationChange" />
            <el-input v-if="docIgnoreEnabled" type="textarea" v-model="selectedElements" placeholder="请输入备注"></el-input>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from "vue";
import { keys, cloneDeep, assign } from "lodash";
import { CopyDocument } from "@element-plus/icons-vue";
import { timeC, dataC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import { useVModel } from "@vueuse/core";
import AnnotationTreeComponent from "./AnnotationTreeComponent.vue";
import MarkPosition from "./MarkPosition.vue";
import useCtx from "@/hooks/useCtx";

const { $app, $router } = useCtx();

const props = defineProps<{
  markRecordId: string; //标注记录Id
  strategyId: string; //策略Id
  targetId: string; //目标id
  standardConfig: any; //标注字典
  docData: any; // doc数据
  docIndex: number; // 文档索引
  scoreRanks: any[]; //策略数据
  annotationData: any[]; // 标注数据
  isIgnore: boolean; // 是否弃标
  remark: string; //标注信息  只在doc弃标时显示
  displayScore: boolean; //是否显示策略数据
  extendFields: string[]; //扩展字段列表
  disabled: boolean; //是否禁用
}>();

const emits = defineEmits(["update:annotationData", "update:isIgnore", "update:remark"]);

// 响应式数据
const allChainVisible = ref(false);
const isContentCollapsed = ref(true);
const docIgnoreEnabled = ref(false);
const remark = ref("");
const markPositionRef = ref(null);
const selectedElements = useVModel(props, "remark", emits);
const annotationSectionRef = ref<HTMLElement | null>(null);

// 计算属性
const shouldShowToggle = computed(() => props.docData.content?.length > 200);

// 方法
const formatTime = (timestamp: number) => (timestamp ? timeC.format(timestamp * 1000, "YYYY-MM-DD HH:mm:ss") : "暂无时间");

const getSourceText = () => {
  const { _indexName } = props.docData;
  return _indexName ? `${_indexName}` : "暂无来源信息";
};

const extendFieldsText = () => {
  if (!dataC.isEmpty(props.extendFields)) {
    return props.extendFields
      .map((item) => `${item}: ${props.docData[item] ?? ""}`)
      .join(" ; ");
  }
  return "";
};

const handleTitleClick = () => allChain();
const handleUrlClick = () => props.docData.url && window.open(props.docData.url, "_blank");
const handleAnnotationChange = (val: any[]) => emits("update:annotationData", val);
const toggleContent = () => (isContentCollapsed.value = !isContentCollapsed.value);
const toggleDocIgnore = () => {
  docIgnoreEnabled.value = !docIgnoreEnabled.value;
  //切换弃标状态时  清空选中项
  emits("update:annotationData", []);
  emits("update:remark",  "");
  emits("update:isIgnore", docIgnoreEnabled.value);
};
//全链路跳转
const allChain = async () => {
  //阻止原生路由跳转事件
  event.preventDefault();
  //修改当前路由
  const curRouteName = $router.currentRoute.value.name;
  await $router.push({ name: `${curRouteName}`, query: getAllChainToQuery() });
  //打开一个全屏dialog
  allChainVisible.value = true;
  //刷新全链路
  await nextTick();
  markPositionRef.value.getTraceinfo();
};
//获取全链路跳转的query参数
const getAllChainToQuery = () => {
  return {
    mode: "ceping",
    strategyId: props.strategyId,
    searchId: props.docData.id,
    url: props.docData.url,
    markRecordId: props.markRecordId,
    targetId: props.targetId,
  };
};
//获取全链路跳转url地址
const getAllChainTo = (item: any) => {
  const curRouteName = $router.currentRoute.value.name;
  const query = getAllChainToQuery();
  const routerQuery = new URLSearchParams();
  keys(query).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/${curRouteName}/position?${routerQuery.toString()}`;
};
//获取属性字符串显示
const getPropsStrList = (props: any) => {
  const res = keys(props).map((key) => {
    return `${key}：${JSON.stringify(props[key])}`;
  });
  return res;
};
//关闭全链路
const closeTraceLogs = () => {
  allChainVisible.value = false;
  $router.push({ name: "mark-index" });
};

// 动态调整标注区域宽度
const adjustAnnotationWidth = async () => {
  await nextTick();
  if (annotationSectionRef.value) {
    const container = annotationSectionRef.value.parentElement;
    if (container) {
      const containerWidth = container.offsetWidth;
      const maxWidth = Math.floor(containerWidth * 0.3); // 最大30%
      const minWidth = 200; // 最小宽度200px

      // 获取标注内容的实际宽度需求
      const annotationContent = annotationSectionRef.value.querySelector('.annotation-content') as HTMLElement;
      if (annotationContent) {
        // 重置样式以测量内容
        annotationSectionRef.value.style.width = 'auto';
        annotationSectionRef.value.style.maxWidth = 'none';
        annotationSectionRef.value.style.minWidth = `${minWidth}px`;

        // 测量内容的自然宽度
        const treeComponent = annotationContent.querySelector('.annotation-tree-component') as HTMLElement;
        let contentWidth = minWidth;

        if (treeComponent) {
          const dimensionsContainer = treeComponent.querySelector('.dimensions-container') as HTMLElement;
          if (dimensionsContainer) {
            // 获取所有维度的总宽度
            contentWidth = dimensionsContainer.scrollWidth + 32; // 加上padding
          }
        }

        // 确定最终宽度：如果内容宽度超过最大宽度，则使用最大宽度并启用滚动
        const finalWidth = Math.min(Math.max(contentWidth, minWidth), maxWidth);

        // 设置最终样式
        annotationSectionRef.value.style.width = `${finalWidth}px`;
        annotationSectionRef.value.style.maxWidth = `${maxWidth}px`;
        annotationSectionRef.value.style.minWidth = `${minWidth}px`;

        // 如果内容宽度超过了最大宽度，确保内部可以滚动
        if (contentWidth > maxWidth) {
          annotationContent.style.overflowX = 'auto';
          // 开发环境下的调试信息
          if (import.meta.env.DEV) {
            console.log(`标注区域达到最大宽度: ${maxWidth}px, 内容宽度: ${contentWidth}px, 启用横向滚动`);
          }
        } else {
          annotationContent.style.overflowX = 'visible';
          // 开发环境下的调试信息
          if (import.meta.env.DEV) {
            console.log(`标注区域自适应宽度: ${finalWidth}px, 内容宽度: ${contentWidth}px`);
          }
        }
      }
    }
  }
};

watch(
  () => props.isIgnore,
  (val) => {
    docIgnoreEnabled.value = val;
  }
);

// 监听标注数据变化，动态调整宽度
watch(
  () => props.annotationData,
  () => {
    adjustAnnotationWidth();
  },
  { deep: true }
);

// 监听弃标状态变化，动态调整宽度
watch(docIgnoreEnabled, () => {
  adjustAnnotationWidth();
});

// 组件挂载时的初始化
onMounted(() => {
  // 如果内容较短，默认展开
  if (props.docData.content?.length <= 200) {
    isContentCollapsed.value = false;
  }
  docIgnoreEnabled.value = props.isIgnore;

  // 初始化宽度调整
  adjustAnnotationWidth();

  // 监听窗口大小变化
  window.addEventListener('resize', adjustAnnotationWidth);
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', adjustAnnotationWidth);
});

//接口暴露
defineExpose({
  unfoldContent: () => (isContentCollapsed.value = false),
  foldContent: () => (isContentCollapsed.value = true),
});
</script>

<style lang="scss" scoped>
.ellipsis-text {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top; // 确保垂直对齐
}

.doc-annotation {
  position: relative;
  margin-bottom: 16px;

  .doc-index {
    background: #409eff;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }

  .doc-card {
    .doc-content-wrapper {
      display: flex;
      gap: 16px;

      // 左侧doc展示区域 (动态调整，最小70%)
      .doc-display-section {
        flex: 1;
        min-width: 70%;
        display: flex;
        flex-direction: column;
        min-height: 350px;
        gap: 8px;

        // 文档头部信息
        .doc-header {
          flex: 0 0 34px;
          display: flex;
          gap: 8px;
          // border: 1px solid #e4e7ed;
          // border-radius: 4px;
          overflow: hidden;

          .doc-field {
            display: flex;
            flex-direction: column;
            // border-right: 1px solid #e4e7ed;

            &:last-child {
              border-right: none;
            }

            label {
              background: #f5f7fa;
              padding: 6px 8px;
              font-size: 12px;
              font-weight: bold;
              color: #606266;
              // border-bottom: 1px solid #e4e7ed;
              text-align: center;
            }

            .field-content {
              padding: 0 8px;
              display: flex;
              align-items: center;
              gap: 4px;
              min-height: 32px;
              font-size: 13px;
            }
          }

          .sequence-field {
            flex: 0 0 30px;
          }

          .title-field {
            flex: 1;
            min-width: 0;

            .title-link {
              max-width: 100%;
              text-align: left;
              justify-content: flex-start;
            }
          }

          .url-field {
            flex: 2;
            min-width: 0;

            .url-link {
              max-width: calc(100% - 20px);
              text-align: left;
              justify-content: flex-start;

              .url-text {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .icon-copy {
              cursor: pointer;
              color: #409eff;
              flex-shrink: 0;
            }
          }
        }

        // 内容区域
        .doc-content-area {
          flex: 1;
          min-height: 0;
          position: relative;
          // border: 1px solid #e4e7ed;
          // border-radius: 4px;

          .content-body {
            height: 100%;
            padding: 12px 12px 20px 12px;

            .content-text {
              height: 100%;
              font-size: 13px;
              color: #303133;
              word-break: break-word;

              &.collapsed {
                display: -webkit-box;
                -webkit-line-clamp: 10;
                line-clamp: 10;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              &.collapsed.scored {
                -webkit-line-clamp: 7;
                line-clamp: 7;
              }
            }
          }

          .content-toggle-wrapper {
            position: absolute;
            right: 5px;
            bottom: 5px;
            background: white;
            line-height: 12px;

            .content-toggle {
              font-size: 12px;
              padding: 0;
            }
          }
        }

        // 文档底部信息
        .doc-footer {
          flex: 0 0 34px;
          display: flex;
          gap: 8px;
          // border: 1px solid #e4e7ed;
          // border-radius: 4px;
          overflow: hidden;

          .doc-field {
            display: flex;
            flex-direction: column;
            // border-right: 1px solid #e4e7ed;

            &:last-child {
              border-right: none;
            }

            label {
              background: #f5f7fa;
              padding: 6px 8px;
              font-size: 12px;
              font-weight: bold;
              color: #606266;
              border-bottom: 1px solid #e4e7ed;
              text-align: center;
            }

            .field-content {
              padding: 8px;
              display: flex;
              align-items: center;
              gap: 4px;
              min-height: 32px;
              font-size: 13px;
            }
          }

          .time-field {
            flex: 0 0 140px;
          }

          .id-field {
            flex: 0 0 120px;

            .icon-copy {
              cursor: pointer;
              color: #409eff;
              flex-shrink: 0;
            }
          }

          .source-field {
            flex: 1;
            min-width: 0;
          }

          .config-field {
            flex: 1;
            min-width: 0;
            max-width: 200px; // 限制最大宽度，防止内容过长撑开布局

            .config-content {
              width: 100%;

              .ellipsis-text {
                max-width: 100%;
                display: block; // 改为block以确保宽度限制生效
              }

              .config-placeholder {
                color: #909399;
                font-style: italic;
              }
            }
          }
        }

        // 文档策略数据
        .doc-score {
          flex: 0 0 auto;
          padding: 5px;
          background-color: #f8fafd;

          ::v-deep sup.badge,
          .badge {
            cursor: pointer;
            border: none;
            background-color: transparent;
            font-size: 13px;
            color: red;
          }

          >.flex {
            flex-wrap: wrap;

            >.flex {
              width: 300px;
              margin-top: 5px;
            }
          }

          .icon-tip {
            color: $primary-color;
            margin-left: 30px;
          }
        }
      }

      // 右侧标注区域 (动态宽度，最大30%)
      .annotation-section {
        flex: 0 0 auto;
        width: fit-content;
        min-width: 200px;
        max-width: 30%;
        // border: 1px solid #e4e7ed;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: width 0.3s ease;

        .annotation-header {
          background: #f5f7fa;
          padding: 4px 8px;
          border-bottom: 1px solid #e4e7ed;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 8px;

          h4 {
            margin: 0;
            font-size: 14px;
            color: #606266;
          }

          // 弃标按钮样式
          .el-button {
            &.active {
              background-color: #f56c6c;
              border-color: #f56c6c;
              color: white;

              &:hover {
                background-color: #f78989;
                border-color: #f78989;
              }
            }
          }
        }

        .annotation-content {
          flex: 1;
          padding: 8px;
          overflow-y: auto;
          overflow-x: auto;
          min-height: 300px;
          width: 100%;

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }

          // 为AnnotationTreeComponent提供合适的容器环境
          :deep(.annotation-tree-component) {
            width: 100%;
            overflow-x: auto;

            // 自定义滚动条样式
            &::-webkit-scrollbar {
              width: 6px;
              height: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 3px;

              &:hover {
                background: #a8a8a8;
              }
            }

            .dimensions-container {
              flex-direction: row;
              flex-wrap: nowrap;
              gap: 8px;
              overflow-x: auto;
              padding-bottom: 4px;
              width: fit-content;
              min-width: 100%;

              // 自定义内部滚动条样式
              &::-webkit-scrollbar {
                width: 6px;
                height: 6px;
              }

              &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;

                &:hover {
                  background: #a8a8a8;
                }
              }

              .dimension-wrapper {
                flex: 0 0 auto;
                width: fit-content;
                min-width: 160px;
                max-width: none;
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .doc-annotation .doc-card .doc-content-wrapper {
    flex-direction: column;

    .doc-display-section {
      flex: 1;
    }

    .annotation-section {
      flex: 0 0 auto;
      min-height: 300px;
    }
  }
}
</style>
