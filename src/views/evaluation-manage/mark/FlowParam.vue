<template>
  <div v-if="inputArgs.length > 0" class="flow-param-box">
    <template v-for="(element, index) in inputArgs">
      <div class="param-item" v-show="element.visible">
        <div class="header">
          <h4 :class="{ 'param-item-required': element.required }">{{ element.name }}</h4>
          <el-tag type="info" size="small">{{ element.type }}</el-tag>
        </div>
        <div class="content">
          <ShowDomByStyle
            ref="showDomByStyleRef"
            v-bind="{
              elementObj: element,
              inputArgs,
            }"
          />
        </div>
      </div>
   </template>
  </div>
  
</template>

<script lang="ts" setup>
import { dataC } from "turing-plugin";
import ShowDomByStyle from "@/views/common/paramComp/show-dom-by-style.vue";
defineProps({
  inputArgs: {
    type: Array as any,
    default() {
      return [];
    },
  },
});
</script>

<style lang="scss" scoped>
.flow-param-box {
  border: 1px dashed $border-color;
  padding: 12px;
  margin-top: 12px;
  border-radius: 6px;
  background-color: $body-bg;
  display: flex;
  align-items: flex-start;
  height: 91px;
  .param-item {
    height: 100%;
    .header {
      display: flex;
      align-items: center;
      height: 20px;
      h4 {
        white-space: nowrap;
      }
    }
    .content {
      height: calc(100% - 20px);
      padding-top: 3px;
      overflow-y: auto;
      //解决clearable按钮出现时引起的宽度变化问题
      ::v-deep(.el-input__wrapper) {
        position: relative;

        .el-input__inner {
          padding-right: 18px;
        }
        .el-input__suffix {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
    + .param-item {
      margin-left: 12px;
    }
  }
}

.param-item-required::before {
  color: var(--el-color-danger);
  content: "*";
  margin-right: 4px;
}
</style>
