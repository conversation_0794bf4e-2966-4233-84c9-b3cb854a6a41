<template>
  <div class="mark-chat">
    <div>
      <span v-if="dataC.isEmpty(chat.content)" class="flex">
        {{ chat.content }}
        <el-button link type="primary" @click="events.reloadChat">点此刷新</el-button>
      </span>
      <v-md-preview v-if="!dataC.isEmpty(chat.content)" :text="chat.content" />
    </div>
    <div style="margin-top: 20px">
      <span style="font-weight: bold">了解详细信息</span>
    </div>
    <div>
      <span v-for="(item, index) in recallInfo" class="flex">
        {{ index + 1 }}.
        <el-button link type="primary" @click="events.originData(item)">{{ item.doc.title }}</el-button>
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from "vue";
import { cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
//引入VMdPreview
import VMdPreview from "@kangc/v-md-editor/lib/preview";
import "@kangc/v-md-editor/lib/style/preview.css";
import vuepressTheme from "@kangc/v-md-editor/lib/theme/vuepress.js";
import "@kangc/v-md-editor/lib/theme/style/vuepress.css";
//使用VuePress 主题
VMdPreview.use(vuepressTheme);

const { $app, proxy } = useCtx();

const emits = defineEmits(["reloadChat"]);

const props = defineProps({
  chat: { type: Object },
  recallInfo: { type: Array as any },
});

const events = reactive({
  reloadChat: () => {
    emits("reloadChat");
  },
  originData: (record: any) => {
    window.open(record.doc.url, "_blank");
  },
});
</script>

<style lang="scss" scoped>
.mark-chat {
  padding-bottom: 30px;

  ::v-deep .vuepress-markdown-body {
    padding: 0;
  }
}
</style>
