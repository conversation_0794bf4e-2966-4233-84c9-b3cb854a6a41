<template>
  <page-wrapper route-name="mark-index::position::">
    <Position ref="positionRef" :loadDataApi="loadListData" :feedback="true" />
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import Position from "@/views/common/position/index.vue";
import * as ceping<PERSON>pi from "@/api/eval-manage";
import * as markApi from "@/api/eval-mark";
import useCtx from "@/hooks/useCtx";
const { $router, proxy, $app } = useCtx();
//列表查询
const loadListData = () => {
  const params = { markRecordId: $router.currentRoute.value.query.markRecordId, recall: false, trace: true, chat: false };
  return markApi.getMarkReocordById(params.markRecordId, params.recall, params.trace, params.chat);
};
const getTraceinfo = () => {
  proxy.$refs.positionRef.getTraceinfo();
};
//接口暴露
defineExpose({ getTraceinfo });
</script>
<style lang="scss" scoped></style>
