<template>
  <el-popover :visible="visible" placement="right" :width="200">
    <template #reference>
      <el-check-tag :checked="tagInfo.check" type="primary" @click="events.click" @mouseover="events.mouseover" @mouseout="events.mouseout" class="mark-tag">
        {{ tagInfo.name }}
      </el-check-tag>
    </template>
    <div style="display: flex; justify-content: space-between">
      <span>反馈细则</span>
    </div>

    <!-- <template v-if="!dataC.isEmpty(dict) && optType === '2'">
      <el-checkbox-group v-model="markValue.feedbackValue" size="small">
        <el-checkbox v-for="item in dict" :value="item.name" :disabled="tagInfo.check" style="width: 100%">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
    </template> -->

    <template v-if="!dataC.isEmpty(dict) ">
      <el-radio-group  v-model="tagInfo.value" size="small">
        <el-radio v-for="item in dict" :value="item.name" :disabled="tagInfo.check" style="width: 100%">{{ item.name }}</el-radio>
      </el-radio-group>
    </template>

    <div><el-input v-model="tagInfo.remark" type="textarea" placeholder="请输入备注" :disabled="tagInfo.check"></el-input></div>
    <div style="text-align: right; margin-top: 2px" v-if="!tagInfo.check">
      <el-button size="small" text @click="events.cancel">取消</el-button>
      <el-button size="small" type="primary" @click="events.complete"> 确定 </el-button>
    </div>
  </el-popover>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed, nextTick } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();

const emits = defineEmits(["update:modelValue"]);

const props = defineProps({
  modelValue: { type: Object },
  dict: { type: Array as any },
  optType: { type: String, default: "1" },
});

//拷贝dict 防止watch更新modelValue导致 feedback.content丢失
const dict = ref(props.dict ? [...props.dict] : []);

const visible = ref(false);

const markValue = reactive({
  feedbackValue:""
})

const tagInfo = reactive({
  name: props.modelValue.name,
  code: props.modelValue.code,
  check: props.modelValue.check,
  value: props.modelValue.value,
  remark: props.modelValue.remark,
});

// 添加标志防止循环更新
const isUpdatingFromParent = ref(false);

// 监听内部响应式数据副本的变化，更新 tagInfo 对象的属性
watch(
  () => props.modelValue,
  (newVal) => {

    console.log("markTag watch modelValue:", newVal);
    
    isUpdatingFromParent.value = true;
    tagInfo.name = newVal.name;
    tagInfo.code = newVal.code;
    tagInfo.check = newVal.check;
    tagInfo.value = newVal.value;
    tagInfo.remark = newVal.remark;
    nextTick(() => {
      isUpdatingFromParent.value = false;
    });
  },
  { deep: true }
);

// 只在确定或取消时更新父组件，而不是在每次 tagInfo 变化时都更新
function updateParent() {
  emits("update:modelValue", cloneDeep(tagInfo));
}

const events = reactive({
  click: () => {
    //如果当前为选中状态，则取消，并清空表单项
    if (tagInfo.check) {
      visible.value = false;
      setTimeout(() => {
        tagInfo.check = false;
        tagInfo.value = "";
        tagInfo.remark = "";
        // 点击取消选中时更新父组件
        updateParent();
      }, 200);
    } else {
      //当前为非选中状态，则显示页面
      visible.value = true;
    }
  },
  mouseover: () => {
    //当选中状态获取焦点时，显示页面
    if (tagInfo.check) {
      visible.value = true;
    }
  },
  mouseout: () => {
    //当选中状态获取焦点时，隐藏页面
    if (tagInfo.check) {
      visible.value = false;
    }
  },
  complete: () => {
    //设置tag选中,并隐藏页面
    console.log("complete clicked, current check:", tagInfo.check);
    tagInfo.check = true;
    visible.value = false;
    console.log("after complete, check:", tagInfo.check);
    // 只在确定时更新父组件
    updateParent();
  },
  cancel: () => {
    //取消选中，隐藏页面
    visible.value = false;
    setTimeout(() => {
      tagInfo.check = false;
      tagInfo.value = "";
      tagInfo.remark = "";
      // 只在取消时更新父组件
      updateParent();
    }, 200);
  },
});
</script>

<style lang="scss" scoped>
.mark-tag {
  font-size: 12px;
  padding: 5px 12px;
  margin: 0 5px;

  ::v-deep input[aria-hidden="true"] {
    display: none !important;
  }
  ::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    box-shadow: none !important;
  }
}
</style>
