# 标注组件使用说明

## 组件介绍

`AnnotationComponent` 是一个层次化的标注组件，支持多维度、多层级的标注数据展示和交互。所有层级的选项始终展示在页面上

## 组件版本

### AnnotationTreeComponent (推荐)
基于Element Plus的el-tree组件实现，提供更好的树形结构展示和交互体验。

**特色功能：**
- 级联选中：选中子节点时，自动选中所有父节点
- 左右排列：多维度水平排列，支持横向滚动
- 响应式布局：适配不同屏幕尺寸

### AnnotationComponent (传统版本)
基于递归组件实现的传统版本。

## 功能特性

- 支持单选和多选两种模式
- 支持多层级嵌套结构（level 0-3）
- 水平布局展示不同维度
- 树形结构展示层级关系
- 自动值绑定和状态管理
- **默认展开所有节点，显示所有层级的所有选项**
- **简化展示，不显示sample、description等描述字段**
- **树形结构缩进：每个层级比上一层级向右缩进1.5em**
- **自适应宽度：每个维度的宽度根据内容自动调整**
- **紧凑布局：减少间距和字体大小，节省空间**
- **级联选中：当子选项被选中时，自动选中上级直至level=1的所有组件**
- **智能互斥：单选模式下，选中节点时自动取消同级其他节点的选中状态**

## 数据结构

### 基本字段说明

- `code`: 唯一标识符
- `level`: 层级（0=维度, 1=选项, 2=反馈, 3=详细内容）
- `name`: 显示名称
- `optType`: 选择类型（1=单选, 2=多选）
- `required`: 是否必填（仅对level=0生效）
- `value`: 存储选中的值
- `children`: 子选项数组

### 值绑定规则

**统一规则**：所有层级（Level 0-3）都使用 boolean 值表示是否选中

1. **Level 0（维度）**:
   - `value` 存储 boolean 值，表示是否有子选项被选中

2. **Level 1（选项）**:
   - `value` 存储 boolean 值，表示是否选中

3. **Level 2（反馈）**:
   - `value` 存储 boolean 值，表示是否选中

4. **Level 3（详细内容）**:
   - `value` 存储 boolean 值，表示是否选中

### optType 层级逻辑

**重要**: 上一层的 `optType` 决定下一层的单选/多选模式：

- **Level 0 的 optType** → 决定 **Level 1** 是单选还是多选
- **Level 1 的 optType** → 决定 **Level 2** 是单选还是多选
- **Level 2 的 optType** → 决定 **Level 3** 是单选还是多选

例如：
- 如果 Level 0 的 `optType = 1`，则 Level 1 的选项使用单选按钮
- 如果 Level 0 的 `optType = 2`，则 Level 1 的选项使用多选框

## 使用方法

### 基本用法

#### 树组件版本 (推荐)
```vue
<template>
  <AnnotationTreeComponent
    v-model="annotationData"
    @update:modelValue="handleAnnotationChange"
  />
</template>
```

#### 传统版本
```vue
<template>
  <AnnotationComponent
    v-model="annotationData"
    @update:modelValue="handleChange"
  />
</template>

<script setup>
import AnnotationComponent from './AnnotationComponent.vue'
import { ref } from 'vue'

const annotationData = ref([
  // 数据结构见下方示例
])

const handleChange = (newValue) => {
  console.log('标注数据变化:', newValue)
}
</script>
```

### 数据示例

```javascript
const exampleData = [
  {
    "code": "dimension1",
    "level": 0,
    "name": "内容质量",
    "optType": 1, // 单选
    "required": true,
    "value": null,
    "children": [
      {
        "code": "good",
        "level": 1,
        "name": "好",
        "optType": 1,
        "required": false,
        "value": false,
        "children": []
      },
      {
        "code": "bad",
        "level": 1,
        "name": "不好",
        "optType": 1,
        "required": false,
        "value": false,
        "children": [
          {
            "code": "title_issue",
            "level": 2,
            "name": "标题问题",
            "optType": 1,
            "required": false,
            "value": false,
            "children": [
              {
                "code": "title_problem1",
                "level": 3,
                "name": "标题问题1",
                "optType": 1,
                "required": false,
                "value": false,
                "children": []
              }
            ]
          }
        ]
      }
    ]
  }
]
```

## 布局说明

- **水平布局**: 不同维度（level 0）从左到右排列，支持换行
- **垂直布局**: 同一维度内的选项从上到下排列
- **自适应宽度**: 每个维度根据内容自动调整宽度（最小100px，最大350px）
- **树形缩进**: 每个层级比上一层级向右缩进1.5em
- **视觉指示**: 使用垂直连接线显示层级关系
- **紧凑设计**: 减少间距和字体大小，提高空间利用率

## 注意事项

1. 组件会自动处理值的联动关系
2. 取消选中父级选项时，会自动清空所有子级选项
3. 必填字段仅对 level 0 的维度生效
4. 组件支持深度响应式更新
5. **所有节点默认展开显示，显示所有层级的所有选项**
6. **简化展示，不显示sample、description等描述性字段**
7. **树形结构：每个层级向右缩进1.5em，使用连接线显示层级关系**
8. **自适应宽度：维度宽度根据内容自动调整，支持换行布局**
9. **紧凑设计：优化间距和字体大小，减少组件占用空间**
10. **级联选中：当子选项被选中时，自动选中上级直至level=1的所有组件**

## 文件结构

- `AnnotationComponent.vue`: 主组件
- `AnnotationLevel.vue`: 递归子组件
- `AnnotationExample.vue`: 完整使用示例
- `AnnotationExampleSimple.vue`: 简化使用示例
- `README.md`: 使用说明
