<template>
  <div class="my-mission">
    <div class="my-mission-query">
      <el-input v-model="query.keywords" placeholder="请输入关键字进行搜索" @keydown.enter="events.search"
        style="width: 240px"></el-input>
      <el-button :icon="Refresh" @click="events.reset" class="refresh-btn"></el-button>
    </div>
    <el-row :gutter="12" class="my-mission-list">
      <my-empty v-if="modelValue.missionList.length == 0" description="暂无任务" :size="120" style="width: 100%" />
      <el-col v-else :xl="8" :lg="12" :md="12" :sm="24" :xs="24" v-for="mission in modelValue.missionList">
        <el-card :class="{ 'my-mission-card': true, 'my-mission-card-finished':  mission.assignQueryCount - mission.completeQueryCount === 0 }">
          <template #header>
            <div class="flexBetween">
              <Strong>{{ mission.name }}</Strong>
              <div class="start-btn">
                <router-link :to="markTo(mission)">
                  <el-button text :icon="Pointer"
                    v-if="mission.assignQueryCount - mission.completeQueryCount">开始处理</el-button>
                  <el-button text :icon="Pointer" v-else>查看</el-button>
                </router-link>
              </div>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="完成情况" :span="2">剩&nbsp;<strong>{{ mission.assignQueryCount - mission.completeQueryCount
            }}</strong>&nbsp;条 / 共&nbsp;<strong>{{
                mission.assignQueryCount }}</strong>&nbsp;条</el-descriptions-item>
            <el-descriptions-item label="开始时间">{{ timeC.format(mission.beginTime, "YYYY-MM-DD hh:mm:ss")
            }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ timeC.format(mission.endTime, "YYYY-MM-DD hh:mm:ss")
            }}</el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="2">{{ mission.description }}</el-descriptions-item>
          </el-descriptions>

        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
import { keys, cloneDeep, assign } from "lodash";
import { dataC, timeC } from "turing-plugin";
import { Refresh, Pointer } from "@element-plus/icons-vue";
import useCtx from "@/hooks/useCtx";
import * as missionApi from "@/api/eval-task";

const { $app, $router } = useCtx();

const modelValue = reactive({
  myMission: [],
  missionList: [],
});
//标注跳转url地址
const markTo = (mission: any) => {
  const query = {
    missionId: mission.id,
  };
  const routerQuery = new URLSearchParams();
  keys(query).forEach((key) => {
    routerQuery.append(key, String(query[key]));
  });
  return `/mark-index?${routerQuery.toString()}`;
};
//查询面板
const query = ref<any>({
  keywords: "",
});
//筛选后的列表
const getMissionList = () => {
  const keywords = query.value.keywords;
  if (dataC.isEmpty(keywords)) return modelValue.myMission;
  else return modelValue.myMission.filter((mission) => mission.name.includes(keywords) || mission.description.includes(keywords));
};
//事件列表
const events = reactive({
  search: async (obj: any) => {
    const result = await missionApi.myMission();
    modelValue.myMission = result.data;
    modelValue.missionList = getMissionList();
  },
  reset: (obj: any) => {
    query.value.keywords = "";
    events.search();
  },
});
onMounted(() => {
  events.search();
});
</script>
<style lang="scss" scoped>
.my-mission-query {
  height: 40px;
  padding: 8px;

  .refresh-btn {
    padding: 8px;
    margin-left: 10px;
  }
}

.my-mission-list {
  max-height: calc(100% - 40px);
  width: calc(100% - 2px);
  overflow-y: auto;
  padding: 15px 8px;
  transition: transform 0.2s;

  .my-mission-card {
    margin-bottom: 15px;
    position: relative;

    &:hover {
      transform: translateY(-3px);
    }

    ::v-deep .el-card__header {
      padding: 2px 12px;
      background-color: rgb(64, 142, 213);
      color: #fff;
    }

    .start-btn {
      .el-button {
        color: #fff;
      }

      .el-button:hover {
        color: var(--el-color-primary);
      }
    }

    ::v-deep .el-card__body {
      padding: 10px 12px;
    }

    ::v-deep .el-card__footer {
      padding: 10px 12px;
    }
  }

  .my-mission-card-finished {
    ::v-deep .el-card__header {
      padding: 2px 12px;
      background-color: rgb(242, 243, 244);
      color: var(--el-color-success);
      .start-btn {
      .el-button {
        color: var(--el-color-success);
      }

        .el-button:hover {
          color:#fff;
          background-color: var(--el-color-success);
        }
    }
    }
  }
}
</style>
