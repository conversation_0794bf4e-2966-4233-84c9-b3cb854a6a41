<template>
  <my-drawer class="dataset-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800px">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit" label-width="138px">
      <template #badUrl>
        <el-row :gutter="20">
          <el-col :span="8" v-for="key in ruleForm.dims" class="margin-bottom-10">
            <div v-if="!dataC.isEmpty(modelValue.classifyR)" v-for="group in modelValue.classifyR.find((item) => item.name === 'bad').ruleGroup">
              <el-form-item :prop="'urlConfig.bad.' + key">
                <el-select v-model="group.dimOptions.find((dim) => dim.dimName === key).optionNames" multiple :disabled="true">
                  <!-- <el-option v-for="sub in getUrlOption(key)" :key="sub.value" :label="sub.label" :value="sub.value" :disabled="(ruleForm.urlConfig.good[key] || []).includes(sub.value)"> </el-option> -->
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #goodUrl>
        <el-row :gutter="20">
          <el-col :span="8" v-for="key in ruleForm.dims" class="margin-bottom-10">
            <div v-if="!dataC.isEmpty(modelValue.classifyR)" v-for="group in modelValue.classifyR.find((item) => item.name === 'good').ruleGroup">
              <el-form-item :prop="'urlConfig.good.' + key">
                <el-select v-model="group.dimOptions.find((dim) => dim.dimName === key).optionNames" multiple :disabled="true">
                  <!-- <el-option v-for="sub in getUrlOption(key)" :key="sub.value" :label="sub.label" :value="sub.value" :disabled="(ruleForm.urlConfig.bad?.[key] || []).includes(sub.value)"> </el-option> -->
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </template>
      <template #strategyConfig>
        <div class="strategy-config-container">
          <div class="button-add">
            <my-button type="primary" text="primary" @click="handleAddStr(false)" v-if="!isUpdate">添加策略</my-button>
            <my-button type="primary" text="primary" @click="handleAddStr(true)" v-if="!isUpdate">添加离线数据</my-button>
          </div>
          <div v-for="(item, index) in ruleForm.strategyConfig" class="strategy-config-item">
            <div :style="{ color: item.offline ? '#177BF8' : '#373C40' }"><span v-if="item.offline">离线</span>策略{{ index + 1 }}</div>
            <el-row>
              <el-col :span="22">
                <my-form ref="strategyFormRef" :rules="rules" :ruleForm="item" :formItems="strategyformItems[index]" @submit="submit" label-width="250px">
                  <template #queryGroupId>
                    <div style="display: flex; align-items: center">
                      <my-button type="primary" text="primary" v-if="item.offline" @click="offlineImportShow(index)">导入query集离线数据</my-button>
                      <div v-if="item.queryGroupId"><span>query集id：</span>{{ item.queryGroupId }}</div>
                    </div>
                  </template>
                </my-form>
              </el-col>
              <el-col :span="2">
                <my-button type="danger" text="danger" @click="handleDeleteStr(index)" v-if="ruleForm.strategyConfig.length > 1 && !isUpdate"
                  ><el-icon :size="20"> <Remove /> </el-icon></my-button
              ></el-col>
            </el-row>
          </div>
        </div>
      </template>
    </my-form>
    <ImportOfflineModal ref="importOfflineModalRef" @import="handleImport" />
  </my-drawer>
</template>

<script setup lang="ts">
interface Dimension {
  children: Dimension[];
  code: string;
  defaultValue: string;
  definition: string;
  description: string;
  level: number;
  name: string;
  optEvalFields: string;
  optType: number;
  required?: boolean;
  sample: string;
}

import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as metaWordApi from "@/api/eval-manage";
import * as markGroupApi from "@/api/eval-mark-group";
import * as commonApi from "@/api/common";
import * as evaluationApi from "@/api/eval-evaluation";
import * as util from "@/utils/common";
import ImportOfflineModal from "./importOfflineModal.vue";
const props = defineProps({
  treeData: { type: Array },
  treeNode: { type: Object },
  type: { type: String },
  anasisList: { type: Array },
});

const modelValue = reactive({
  dimGroup: [],
  classifyR: [],
});

const customClassifyR = computed(() => {
  return modelValue.classifyR.filter((rule) => rule.name !== "good" && rule.name !== "bad");
});

const { $app, proxy, $router } = useCtx();
const { common } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}`;
});
const dialogVisible = ref<boolean>(false);
// 表单相关
const formType = ref<string>("add");
const importOfflineModalRef = ref<any>(null);
const formRef = ref<any>(null);
const strategyFormRef = ref<any>(null);
const handleClose = () => {
  formRef.value.resetForm();
  if (strategyFormRef.value) {
    strategyFormRef.value.map((item) => {
      item.resetForm();
    });
  }
  dialogVisible.value = false;
};
const handleImport = (data: any, index: any) => {
  ruleForm.value.strategyConfig[index].queryGroupId = data;
};
const handleConfirm = async () => {
  const valid1 = await formRef.value.getValidateResult();
  if (valid1) {
    if (strategyFormRef.value) {
      const valid2 = await Promise.all(strategyFormRef.value.map((form) => form.getValidateResult()));
      if (valid2) {
        submit(ruleForm.value);
      }
    } else {
      submit(ruleForm.value);
    }
  }
};
// 增加策略配置
const handleAddStr = (offline: boolean) => {
  const obj: any = { offline };
  if (offline) {
    obj.ascribeMode = "0";
  }
  if (ruleForm.value.strategyConfig) {
    ruleForm.value.strategyConfig.push(obj);
  } else {
    ruleForm.value.strategyConfig = [obj];
  }
};
const offlineImportShow = (index) => {
  importOfflineModalRef.value.openDialog(index);
};
// 删除策略配置
const handleDeleteStr = (index) => {
  ruleForm.value.strategyConfig.splice(index, 1);
  strategyformItems.value.splice(index, 1);
};

const handleCustomeDeleteStr = (index) => {
  ruleForm.value.customizeConfig.splice(index, 1);
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});

const defaultForm = {
  id: "",
  parentId: 0,
  name: "",
  standardId: "",
  dims: [],
  recallDims: [],
  chatDims: [],
  urlConfig: {
    good: {},
    bad: {},
  },
  level: 0,
  strategyConfig: [],
  customizeConfig: [],
};
let ruleForm = ref<any>(assign({}, defaultForm));
const validatePass1 = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error("请输入"));
  } else {
    callback();
  }
};
const validatePass2 = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error("请至少选择一个选项"));
  } else {
    callback();
  }
};
const validatePass = (rule: any, value: any, callback: any) => {
  callback();
};
const rules = computed(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "change" }],
  standardId: [{ required: true, message: "标注标准不能为空", trigger: "change" }],
  dims: [
    {
      required: ruleForm.value.level == 1,
      message: "标注维度不能为空",
      trigger: "change",
    },
  ],
  goodUrl: [
    {
      required: ruleForm.value.level == 1,
      validator: validatePass,
      trigger: "change",
    },
  ],
  badUrl: [
    {
      required: ruleForm.value.level == 1,
      validator: validatePass,
      trigger: "change",
    },
  ],
  strategyConfig: [{ required: true, validator: validatePass, trigger: "change" }],
  regionCode: [{ required: true, message: "运行环境不能为空", trigger: "change" }],
  processId: [{ required: true, message: "场景策略版本不能为空", trigger: "change" }],
  ascribeMode: [{ required: true, message: "归因模式不能为空", trigger: "change" }],
  ascribeProcessId: [{ required: true, message: "归因策略不能为空", trigger: "change" }],
  id4Ascribe: [{ required: true, message: "竞对场景策略版本不能为空", trigger: "change" }],
  queryGroupId: [{ required: true, message: "query集离线数据不能为空", trigger: "change" }],
}));

const getUrlOption = (key) => {
  return formItems.value.dims.options.find((item) => item.value == key)?.options || [];
};
// 表单项
const formItems = ref<any>({
  parentId: {
    label: "父级菜单",
    type: "select",
    options: [],
    hidden: () => {
      return ruleForm.value.parentId == "0";
    },
    attrs: {
      disabled: true,
      maxlength: 255,
      placeholder: "请输入父级菜单",
    },
  },
  standardId: {
    label: "标注标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: false },
    disabled: () => isUpdate.value || ruleForm.value.level == 2,
    events: {
      change: (val) => {
        updateStandard(val);
      },
    },
    hidden: () => {
      return ruleForm.value.parentId == "0";
    },
  },
  dims: {
    label: "标注维度",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: true },
    disabled: () => true,
    events: {
      change: (val) => {
        // ruleForm.value.urlConfig.good[val] = [];
      },
      placeholder: "请输入标注标准",
    },
    hidden: () => {
      return !ruleForm.value.standardId;
    },
  },
  recallDims: {
    label: "recall维度标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: true },
    disabled: () => true,
    events: {
      change: (val) => {
        // ruleForm.value.urlConfig.good[val] = [];
      },
      placeholder: "请输入维度标准",
    },
    hidden: () => {
      return !ruleForm.value.standardId;
    },
  },
  chatDims: {
    label: "chat维度标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: true },
    disabled: () => true,
    events: {
      change: (val) => {
        // ruleForm.value.urlConfig.good[val] = [];
      },
      placeholder: "请输入维度标准",
    },
    hidden: () => {
      return !ruleForm.value.standardId;
    },
  },
  goodUrl: {
    label: "good定义",
    hidden: () => {
      return ruleForm.value.parentId == "0" || !ruleForm.value.dims || !ruleForm.value.dims.length;
    },
    type: "slot",
    slotName: "goodUrl",
    disabled: () => isUpdate.value,
  },
  badUrl: {
    label: "bad定义",
    hidden: () => {
      return ruleForm.value.parentId == "0" || ruleForm.value.level == 0 || !ruleForm.value.dims || !ruleForm.value.dims.length;
    },
    type: "slot",
    slotName: "badUrl",
    disabled: () => isUpdate.value,
  },
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入名称",
    },
  },
  strategyConfig: {
    label: "策略配置",
    hidden: () => {
      return ruleForm.value.parentId == "0" || ruleForm.value.level != 2;
    },
    type: "slot",
    slotName: "strategyConfig",
    disabled: () => isUpdate.value,
  },
});
const generateFormItems = (index) => {
  return {
    compName: {
      label: "竞品名称",
      type: "input",
      attrs: {
        disabled: isUpdate.value,
      },
      hidden: () => {
        return !ruleForm.value.strategyConfig[index].offline;
      },
    },
    regionCode: {
      label: "运行环境",
      type: "select",
      options: areaList,
      attrs: {
        maxlength: 255,
        disabled: isUpdate.value,
        placeholder: "请选择运行环境",
      },
    },
    processId: {
      label: "场景策略版本",
      type: "select",
      options: isUpdate.value ? props.anasisList : [],
      attrs: {
        maxlength: 255,
        disabled: isUpdate.value,
        placeholder: "请输入场景策略版本",
        remote: true,
        remoteMethod: function (search: string) {
          if (this.remote && search) {
            getAnasisList(search).then((res) => {
              strategyformItems.value[index].processId.options = res;
            });
          }
        },
      },
      hidden: () => {
        return ruleForm.value.strategyConfig[index].offline;
      },
      events: {
        change: (val) => {
          // ruleForm.value.strategyConfig[index].id4Ascribe = val;
        },
      },
    },
    ascribeMode: {
      label: "归因模式",
      type: "select",
      options: [
        { value: "0", label: "竞品good" },
        { value: "1", label: "自研bad" },
      ],
      events: {},
      attrs: {
        maxlength: 255,
        placeholder: "请输入归因模式",
        disabled: isUpdate.value,
      },
    },
    ascribeProcessId: {
      label: "归因策略",
      type: "select",
      options: ascribeList.value[ruleForm.value.strategyConfig[index].ascribeMode],
      attrs: {
        maxlength: 255,
        placeholder: "请输入归因策略",
        disabled: isUpdate.value,
        remote: true,
        remoteMethod: function (search: string) {
          if (this.remote) {
            getAscribeList(ruleForm.value.strategyConfig[index].ascribeMode, search).then((res) => {
              strategyformItems.value[index].ascribeProcessId.options = res;
            });
          }
        },
      },

      hidden: (val) => {
        return val.ascribeMode != 0 && val.ascribeMode != 1;
      },
    },
    id4Ascribe: {
      label: "竞对场景策略版本",
      type: "select",
      options: isUpdate.value ? props.anasisList : [],
      attrs: {
        maxlength: 255,
        placeholder: "请输入竞对场景策略版本",
        disabled: isUpdate.value,
        remote: true,
        remoteMethod: function (search: string) {
          if (this.remote && search) {
            getAnasisList(search).then((res) => {
              strategyformItems.value[index].id4Ascribe.options = res;
            });
          }
        },
      },
      hidden: (val) => {
        return val.ascribeMode == 1 || (val.ascribeMode != 0 && val.ascribeMode != 1);
      },
    },
    queryGroupId: {
      label: "query集离线数据",
      type: "slot",
      slotName: "queryGroupId",
      hidden: () => {
        return !ruleForm.value.strategyConfig[index].offline;
      },
    },
  };
};
const areaList = ref([]);
const ascribeList = ref({
  0: [],
  1: [],
});
// 场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};

const getAscribeList = async (type: any, name = "") => {
  const res = await evaluationApi.getStrategyVersion(type, { name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
const getAscribeListGood = async (name = "") => {
  const res = await evaluationApi.getStrategyVersion(0, { name: "" });
  ascribeList.value[0] = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
const getAscribeListBad = async (name = "") => {
  const res = await evaluationApi.getStrategyVersion(1, { name: "" });
  ascribeList.value[1] = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
getAscribeListGood();
getAscribeListBad();

const strategyformItems = ref<any>([]);
const getAreaList = async () => {
  const res = await common.getAreaList();
  areaList.value = res.map((item: any) => ({
    ...item,
    label: item.name,
    value: item.code,
  }));
};
getAreaList();
const revertData = (data: any) => {
  const original = cloneDeep(data);
  original.standardId = data?.standardId || "";
  // 将 ascribeMode 转换为字符串类型
  if (original.strategyConfig && original.strategyConfig.length > 0) {
    original.strategyConfig.forEach((item) => {
      if (typeof item.ascribeMode === "number") {
        item.ascribeMode = String(item.ascribeMode); // 转换为字符串类型
      }
    });
  }

  // original.dims = data?.standardConfig?.dimensionList?.map((item: any) => item.name) || [];
  // original.urlConfig = {
  //   good: {},
  //   bad: {},
  // };
  // original.urlConfig.good = (data?.classifyRules || [])
  //   .find((item: any) => item.name === "good")
  //   ?.rule?.reduce((sum: any, item: any) => {
  //     sum[item.dimName] = item.optionNames;
  //     return sum;
  //   }, {});
  // original.urlConfig.bad = (data?.classifyRules || [])
  //   .find((item: any) => item.name === "bad")
  //   ?.rule?.reduce((sum: any, item: any) => {
  //     sum[item.dimName] = item.optionNames;
  //     return sum;
  //   }, {});
  // original.customizeConfig = (data?.classifyRules || [])
  //   .filter((item: any) => item.name !== "good" && item.name !== "bad")
  //   .map((item: any) => {
  //     item.name = item.name;
  //     Object.assign(
  //       item,
  //       item.rule.reduce((sum: any, item: any) => {
  //         sum[item.dimName] = item.optionNames;
  //         return sum;
  //       }, {})
  //     );
  //     return item;
  //   });
  console.log("original:", original);

  return original;
};
//打开窗口

//type edit  add   row ：目录信息
const openDialog = async (type: string, row: any) => {
  console.log("type  row:", type, row);
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      if (!dataC.isEmpty(row.standardId)) {
        updateStandard(row.standardId);
      }
      ruleForm.value = pick({ ...defaultForm, ...revertData(row) }, keys(assign({}, defaultForm)));
    } else {
      const row1 = revertData(row);
      if (!dataC.isEmpty(row.standardId)) {
        updateStandard(row.standardId);
      }

      const keysEnum = [
        ["level", "parentId"],
        ["standardId", "dims", "level", "urlConfig", "parentId", "customizeConfig"],
        ["standardId", "dims", "level", "urlConfig", "parentId", "standardConfig"],
      ];

      ruleForm.value = pick({ ...defaultForm, ...row1 }, keysEnum[row.level]);
      if (row.level == 1) {
        ruleForm.value.urlConfig = { good: {}, bad: {} };
        ruleForm.value.customizeConfig = [];
      }
      // if (row.level == 2) {
      //   handleAddStr();
      // }
    }
  });
};
const transformData = (data) => {
  const transformed = cloneDeep(data);
  if (transformed.dims) {
    transformed.dims = transformed.dims.join(",");
  }

  // 将 ascribeMode 转换为数值类型
  if (transformed.strategyConfig && transformed.strategyConfig.length > 0) {
    transformed.strategyConfig.forEach((item) => {
      if (typeof item.ascribeMode === "string") {
        item.ascribeMode = Number(item.ascribeMode); // 转换为数值类型
      }
    });
  }

  // 转换 bad 对象（拼接字符串）
  // if (data.urlConfig?.good) {
  //   transformed.urlConfig.good = Object.entries(data.urlConfig.good).reduce((acc, [key, arr]) => {
  //     if (key !== "name") {
  //       acc[key] = (arr || []).join(",");
  //     } else {
  //       acc[key] = arr;
  //     }
  //     return acc;
  //   }, {});
  // }

  // if (data.urlConfig?.bad) {
  //   // 转换 bad 对象（拼接字符串）
  //   transformed.urlConfig.bad = Object.entries(data.urlConfig.bad).reduce((acc, [key, arr]) => {
  //     acc[key] = arr.join(",");
  //     return acc;
  //   }, {});
  // }
  // if (data?.customizeConfig) {
  //   // 转换 bad 对象（拼接字符串）
  //   transformed.customizeConfig = data?.customizeConfig.map((item) => {
  //     item = Object.entries(item).reduce((acc, [key, arr]) => {
  //       acc[key] = Array.isArray(arr) ? arr.join(",") : arr;
  //       return acc;
  //     }, {});
  //     return item;
  //   });
  // }
  if (data.level != 2) {
    delete transformed.strategyConfig;
  }
  return transformed;
};
// 转换函数
const transformDataRule = (data) => {
  let transformed: any[] = [];
  // 转换 bad 对象（拼接字符串）
  if (data.urlConfig?.good) {
    transformed.push({
      name: "good",
      rule: [],
    });
    transformed[0].rule = Object.entries(data.urlConfig.good).reduce((sum, [key, value]) => {
      sum.push({
        dimName: key,
        optionNames: value,
      });
      return sum;
    }, []);
  }
  if (data.urlConfig?.bad) {
    transformed.push({
      name: "bad",
      rule: [],
    });
    // 转换 bad 对象（拼接字符串）
    transformed[1].rule = Object.entries(data.urlConfig.bad).reduce((sum, [key, value]) => {
      sum.push({
        dimName: key,
        optionNames: value,
      });
      return sum;
    }, []);
  }
  if (data?.customizeConfig) {
    transformed = transformed.concat(
      data.customizeConfig.map((item) => ({
        name: item.name,
        rule: Object.keys(item)
          .filter((key) => key !== "name")
          .map((dimKey) => ({
            dimName: dimKey,
            optionNames: item[dimKey],
          })),
      }))
    );
  }
  if (data.level != 2) {
    delete transformed.strategyConfig;
  }
  return transformed;
};
function hasDuplicatesValue(arr) {
  const seen = new Set(); // 使用Set记录已存在的键值组合
  for (const obj of arr) {
    // 提取除name外的所有字段值，按字段名排序后拼接成唯一键
    const keys = Object.keys(obj)
      .filter((k) => k !== "name")
      .sort();
    const key = keys.map((k) => `${k}:${obj[k]}`).join("|"); // 例如："age:25|city:New York"

    if (seen.has(key)) {
      return true; // 发现重复
    }
    seen.add(key);
  }
  return false; // 无重复
}
function hasDuplicateName(arr) {
  const names = arr.map((item) => item.name);
  return new Set(names).size !== names.length;
}
const hasRepeat = (data) => {
  const form = transformData(data);
  let arr: any[] = [form.urlConfig.good, form.urlConfig.bad];
  if (form.customizeConfig) {
    arr = [...arr, ...form.customizeConfig];
  }
  return hasDuplicatesValue(arr);
};
//提交数据
const submit = (form: any) => {
  console.log(form);
  const params = transformData(form);
  // 选项值有重复
  // if (params.level == 1 && hasRepeat(form)) {
  //   $app.$message.warning("自定义选项的值有重复");
  //   return;
  // }
  // if (params.level == 1 && hasDuplicateName(form.customizeConfig)) {
  //   $app.$message.warning("自定义选项的名称有重复");
  //   return;
  // }
  let apiParams = {
    name: form.name,
    parentId: form.parentId,
    level: form.level,
    standardId: form.standardId,
    classifyRule: transformDataRule(form),
    id: "",
  };
  if (form.level == 2) {
    apiParams.strategyConfig = form.strategyConfig;
    apiParams.standardConfig = form.standardConfig;
  }
  if (isUpdate.value) {
    apiParams.id = form.id;
    metaWordApi.editTree(apiParams).then(() => {
      $app.$message.success("修改成功");
      emit("reload", props.treeNode);
      handleClose();
    });
  } else {
    metaWordApi[`createTree${params.level}`](apiParams).then(() => {
      $app.$message.success("新增成功");
      emit("reload");
      handleClose();
    });
  }
};

function updateStandard(id: string) {
  metaWordApi.getStandardById(id).then((res) => {
    //获取标注维度
    updateDims(res.recallDimsGroupId);
    updateRecallDims(res.recallDimsGroupId);
    updateChatDims(res.chatDimsGroupId);
    modelValue.classifyR = res.classifyRules;
  });
}

function updateDims(recallDimsGroupId: string) {
  const recallDims = modelValue.dimGroup.find((item) => item.id === recallDimsGroupId);
  // 使用示例
  if (recallDims?.dimsTree) {
    const levelZeroDims = getLevelZeroDimensions(recallDims.dimsTree);
    formItems.value.dims.options = levelZeroDims.map((dim) => ({
      label: dim.name,
      value: dim.code,
    }));
    ruleForm.value.dims = levelZeroDims.map((dim) => dim.name);
  }
}

function updateRecallDims(recallDimsGroupId: string) {
  const recallDims = modelValue.dimGroup.find((item) => item.id === recallDimsGroupId);
  if (recallDims) {
    // 更新dims的选项
    formItems.value.recallDims.options = [
      {
        label: recallDims.name,
        value: recallDims.id,
      },
    ];
    // 更新选中的值
    ruleForm.value.recallDims = [recallDims.id];
  }
}

function updateChatDims(chatDimsGroupId: string) {
  const chatDimGroup = modelValue.dimGroup.find((item) => item.id === chatDimsGroupId);
  if (chatDimGroup) {
    // 更新dims的选项
    formItems.value.chatDims.options = [
      {
        label: chatDimGroup.name,
        value: chatDimGroup.id,
      },
    ];
    // 更新选中的值
    ruleForm.value.chatDims = [chatDimGroup.id];
  }
}

const flattenTree = (tree: any[]) => {
  let result: any[] = [];

  function flattenNode(node: any) {
    result.push({ label: node.name, value: node.id }); // 将当前节点添加到结果数组中
    if (node.children && node.children.length > 0) {
      // 如果节点有子节点，递归扁平化子节点
      node.children.forEach(flattenNode);
    }
  }

  tree.forEach(flattenNode); // 对树中的每个节点应用扁平化
  return result;
};
// watch监听
watch(
  () => props.treeData,
  (val) => {
    formItems.value.parentId.options = flattenTree(val as any[]);
  },
  { deep: true }
);
// 初始化表单配置
watch(
  () => ruleForm.value.strategyConfig,
  (newVal) => {
    strategyformItems.value = (newVal || []).map((_, index) => generateFormItems(index));
  },
  { immediate: true, deep: true }
);
//初始化
onMounted(() => {
  // 获取标准列表
  metaWordApi.getStandardList().then((res: { data: StandardItem[] }) => {
    formItems.value.standardId.options = res.data.map((item: StandardItem) => ({
      value: item.id,
      label: item.name,
    }));
  });

  markGroupApi.getList().then((res) => {
    modelValue.dimGroup = res.data;
  });
});
//事件声明
const emit = defineEmits(["reload"]);
//接口暴露
defineExpose({
  openDialog,
});

// 获取第一层级（level = 0）的维度列表
const getLevelZeroDimensions = (dimensions: Dimension[]): Dimension[] => {
  return dimensions.filter((dim) => dim.level === 0);
};
</script>
<style lang="scss" scoped>
.button-add {
  height: 15px;

  ::v-deep {
    .el-button {
      float: right;
    }
  }
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

::v-deep {
  .el-row {
    width: 100%;
  }

  .el-form-item__content {
    .strategy-config-container {
      width: 100%;

      .strategy-config-item {
        .el-form-item {
          margin-bottom: 18px;
        }
      }
    }
  }
}
</style>