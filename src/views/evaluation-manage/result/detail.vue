<template>
  <page-wrapper route-name="evaluation-reslut::details::">
    <div class="baike-entry-intervence">
      <table-page ref="myTableRef" :columns="columns" :loadDataApi="loadListData" :transformListData="transformListData" :withPagination="false"
        :loadImmediately="false" :query="query">
        <template #query>
          <div class="flexBetweenStart">
            <my-query :queryItems="queryItems" @search="events.search" />
            <my-operation>
              <template #buttonGroup>
                <my-button type="export" @click="events.export">导出</my-button>
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import * as metaWordDbApi from "@/api/eval-manage";
import { getTemplateList } from "@/api/mock";
import { computed } from "vue";

const { $app, proxy, $router } = useCtx();
const routeQuery = $app.$route.query;
const routeName = "baike-entry-intervence";
//列配置
const defaultColumns = ref([
  {
    prop: "sceneProcessName",
    label: "策略名称",
    width: 225,
  },
  {
    prop: "regionName",
    label: "环境",
    minWidth: 180,
  },
  {
    prop: "queryCount",
    label: "query条数",
    width: 120,
  },
  {
    prop: "createdBy",
    label: "标注人",
    width: 100,
  },

  { prop: "markMinTimeRender", label: "开始时间", width: 180 },
  { prop: "markMaxTimeRender", label: "结束时间", width: 180 },
  {
    prop: "oneGoodDocQuery",
    label: "含≥1条goodUrl结果的query数",
    width: 280,
  },
  { prop: "hasResultAbsorbPercentageRender", label: "有结果吸收率", width: 180 },
  { prop: "docCount", label: "Doc数", width: 120 },

  { prop: "goodDocCount", label: "good doc", width: 120 },
  { prop: "goodDocPercentageRender", label: "good率", width: 120 },
  { prop: "badDocCount", label: "bad doc", width: 120 },
  { prop: "badDocPercentageRender", label: "bad率", width: 120 },
  { prop: "topKGoodQuery", label: "TopK完全good的query数", width: 230 },
  {
    prop: "topKGoodQueryPercentageRender",
    label: "TopK完全good率",
    width: 200,
  },
]);
const columns = ref([]);
const createSequence = (max) =>
  Array.from({ length: max }, (_, index) => index + 1).reverse();
//查询面板
const query = ref<any>({});

const queryItems = ref<any>({
  topK: {
    modelValue: "",
    type: "select",
    label: "topK",
    width: "180px",
    options: [],
    attrs: {
      placeholder: "topK",
      clearable: false,
    },
  },
});

//列表查询
const loadListData = (data: any) => {
  const params = {
    ...data,
    ...JSON.parse(routeQuery.query),
    categoryId: routeQuery.categoryId,
  };
  return new Promise((resolve: any) => {
    metaWordDbApi
      .getAnalysisList( params)
      .then((result) => {
        const arr = util.generateTableColumns(result.data);
        columns.value = defaultColumns.value.concat([...arr]);
        result.data = result.data.map((item) => ({
          ...item,
          ...item.extendMap,
        }));
        resolve(result);
      });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.markMinTimeRender = !dataC.isEmpty(x.markMinTime)
      ? timeC.format(x.markMinTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.markMaxTimeRender = !dataC.isEmpty(x.markMaxTime)
      ? timeC.format(x.markMaxTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.hasResultAbsorbPercentageRender = !dataC.isEmpty(x.hasResultAbsorbPercentage)
      ? x.hasResultAbsorbPercentage + '%'
      : "";
    x.goodDocPercentageRender = !dataC.isEmpty(x.goodDocPercentage)
      ? x.goodDocPercentage + '%'
      : "";
    x.badDocPercentageRender = !dataC.isEmpty(x.badDocPercentage)
      ? x.badDocPercentage + '%'
      : "";
    x.topKGoodQueryPercentageRender = !dataC.isEmpty(x.topKGoodQueryPercentage)
      ? x.topKGoodQueryPercentage + '%'
      : "";
    return x;
  });
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  export: () => {
    const params = {
      ...JSON.parse(routeQuery.query),
      topK: query.value.topK,
    };
    metaWordDbApi.exportTopK(routeQuery.categoryId, params).then((result) => {
      util.downloadFile(result, `${routeQuery.nodeName}数据分析.xlsx`);
    });
  },
});

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
const getTableData = () => {
  metaWordDbApi.getTopK(routeQuery.categoryId).then((res) => {
    queryItems.value.topK.options = createSequence(res.data || 5).map(
      (item: any) => ({
        label: "top" + item,
        value: item,
      })
    );
    queryItems.value.topK.modelValue = createSequence(res.data || 5)[0];
    query.value.topK = queryItems.value.topK.modelValue;
  });
};
getTableData();
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>