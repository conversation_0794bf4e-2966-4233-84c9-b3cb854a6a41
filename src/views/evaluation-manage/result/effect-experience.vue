<template>
    <div class="effect-experience-container">
      <table-page ref="myTableRef" :columns="columns" :loadDataApi="loadListData" :transformListData="transformListData" :withPagination="false"
        :query="query">
        <template #query>
          <div class="flexBetweenStart">
            <my-query :queryItems="queryItems" @search="events.search" />
            <my-operation>
              <template #buttonGroup>
                <my-button type="export" @click="events.export">导出</my-button>
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import * as metaWordDbApi from "@/api/eval-manage";;

const { $app, proxy, $router } = useCtx();
const routeQuery = $app.$route.query;
//列配置
const defaultColumns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "sceneProcessName",
    label: "策略名称",
    minWidth: 240,
  },
  // 文本可复制
  {
    prop: "regionName",
    label: "环境",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "query",
    minWidth: 190,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        const routeInfo = {
          name: `mark-index`,
          query: {
            markMode: "history",
            categoryId: record.categoryId,
            taskId: record.taskId,
          },
        };
        const resolvedRoute = $router.resolve(routeInfo);
        window.open(resolvedRoute.href, "_blank");
      },
    },
  },
  {
    prop: "account",
    label: "标注人",
    minWidth: 120,
  },
  {
    prop: "createdDateRender",
    label: "标注时间",
    minWidth: 180,
  },
  {
    prop: "type",
    label: "来源",
    minWidth: 120,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        0: { type: "info", name: "策略结果" },
        1: { type: "warning", name: "全链路结果" },
        2: { type: "success", name: "大模型结果" },
      },
    },
  },
  {
    prop: "docIdxRender",
    label: "结果",
    minWidth: 100,
  },
  {
    prop: "url",
    label: "URL",
    minWidth: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        window.open(record.url, "_blank")
      },
    },
  },
  {
    prop: "title",
    label: "标题",
    minWidth: 200,
  },
]);
const columns = ref([]);
const createSequence = (max) =>
  Array.from({ length: max }, (_, index) => index + 1).reverse();
//查询面板
const query = ref<any>({});

const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "标注人 或 Query",
    },
  },
  sceneProcessId: {
    type: "select",
    label: "",
    width: "220px",
    modelValue: "",
    options:[],
    attrs: {
      placeholder: "场景策略",
      filterable:true,
    }
  },
  type: {
    type: "select",
    label: "",
    modelValue: "",
    options: [
      { label: "策略结果", value: 0 },
      { label: "全链路结果", value: 1 },
      { label: "大模型结果", value: 2 },
    ],
    width: "180px",
    attrs: {
      placeholder: "来源",
    },
  },

  time: {
    type: "datetimerange",
    label: "",
    modelValue: "",
    width: "250px",
    collapsed: true,
    attrs: {
      placeholder: "请输入备注",
    },
  },
});

//列表查询
const loadListData = (data: any) => {
  const params = {
    ...data,
  };
  return new Promise((resolve: any) => {
    metaWordDbApi
      .getAnalysisList( params)
      .then((result) => {
        const arr = util.generateTableColumns(result.data);
        columns.value = defaultColumns.value.concat([...arr]);
        result.data = result.data.map((item) => ({
          ...item,
          ...item.extendMap,
        }));
        resolve(result);
      });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.markMinTimeRender = !dataC.isEmpty(x.markMinTime)
      ? timeC.format(x.markMinTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.markMaxTimeRender = !dataC.isEmpty(x.markMaxTime)
      ? timeC.format(x.markMaxTime, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.hasResultAbsorbPercentageRender = !dataC.isEmpty(x.markMaxTime)
      ? x.hasResultAbsorbPercentage + '%'
      : "";
    x.goodDocPercentageRender = !dataC.isEmpty(x.markMaxTime)
      ? x.goodDocPercentage + '%'
      : "";
    x.badDocPercentageRender = !dataC.isEmpty(x.markMaxTime)
      ? x.badDocPercentage + '%'
      : "";
    x.topkGoodQueryPercentageRender = !dataC.isEmpty(x.markMaxTime)
      ? x.topkGoodQueryPercentage + '%'
      : "";
    return x;
  });
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  export: () => {
    const params = {
      ...JSON.parse(routeQuery.query),
      topK: query.value.topK,
    };
    metaWordDbApi.exportTopK(routeQuery.categoryId, params).then((result) => {
      util.downloadFile(result, `${routeQuery.nodeName}数据分析.xlsx`);
    });
  },
});

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style scoped lang="scss">
.effect-experience-container {
  height: 100%;
}
</style>