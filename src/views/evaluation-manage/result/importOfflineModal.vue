<template>
  <my-drawer
    class="mock-add"
    v-model="dialogVisible"
    title="导入query集离线数据"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems">
      <template #file>
        <my-upload
          ref="uploadRef"
          maxSize="10M"
          accept=".xls,.xlsx"
          v-model="ruleForm.file"
          drag
          drag-style
          showDownload
          @download="downloadTemp">
          <template #tips>
              <div class="upload-tips">
              <p>3、仅支持扩展名为：.xlsx/xls文件；大小不超过10M</p>
              </div>
          </template>
        </my-upload>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE } from '@/utils/validate';
import useValidate from '@/hooks/validate'
import useStore from "@/store";
import * as util from "@/utils/common";
import { importOfflineData, downloadOfflineTemplate } from "@/api/eval-query-set";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { proxy } = useCtx()
const { t } = useI18n();
const { api } = useStore();
let index = 0;

// 弹窗相关
const dialogVisible = ref<boolean>(false)
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      importOfflineData({...ruleForm.value,name: 'query集离线数据'}).then((res: any) => {
        emits('import',res.data,index)
      })
      dialogVisible.value = false
    }
  })
};

/* 校验 */
const { validateNameRule } = useValidate()
// 表单相关
const formType = ref<string>('add')
const formRef = ref<any>(null)
const defaultForm = {
  file: null
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  file: [{ required: true, message: "请上传文件", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  file: { label: '上传文件', type: 'slot', slotName: 'file'}
});


const openDialog = async ( i: any) => {
  index = i || 0;
  ruleForm.value = assign({}, defaultForm);
  dialogVisible.value = true;
  nextTick(() => {
    proxy.$refs.uploadRef.clearFiles()
  })
};
const emits = defineEmits(["import"]);

const downloadTemp = () => {
  downloadOfflineTemplate({offline:true}).then((res: any) => {
    util.downloadFile(res, 'query集离线数据模版.xlsx')
  })
}

defineExpose({ openDialog });
</script>

<style lang="scss">
</style>