<template>
  <page-wrapper route-name="evaluation-reslut::">
    <div class="result">
      <div class="meta-dictionary-main">
            <MetaWordTable
              ref="wordTableRef"
              :treeNode="treeNode"
              :treeData="treeData"
              @updateTree="getTreeData"
              :operationAuth="operationAuth"
            />
          </div>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import MetaWordTable from "./table.vue";
import MetaDictionaryTree from "./tree.vue";
import * as metaWordDbApi from "@/api/eval-manage";
import * as commonApi from "@/api/common";
import { computed } from "vue";
import effectExperience from './effect-experience.vue'
import * as util from "@/utils/common";
const { word } = useStore();
const operationAuth = "/base/#/evaluation-reslut/edit";
const treeRef = ref();
const { $app, proxy, $router } = useCtx();
const activeName = ref("query-mark");
const routeQuery = computed(() => $app.$route.query);
const wordTableRef = ref();
const treeNode = computed(() => treeRef.value?.getCurrentNode());
let treeData = ref<any>([]);
function findNodeById(nodes, targetId) {
  for (const node of nodes) {
    if (node.id === targetId) return node;
    if (node.children?.length) {
      const found = findNodeById(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
}
const getTreeData = async (key?: any) => {
  const filterText = treeRef.value.filterText;
  const params = { name: filterText };
  const treeRes: any = await metaWordDbApi.getTree(params);
  treeData.value = treeRes.data;
  const categoryId = findNodeById(treeRes.data, routeQuery.value.categoryId)
    ? routeQuery.value.categoryId
    : treeRes.data?.[0]?.id;
  treeRef.value.setCurrentKey(key || categoryId || treeRes.data?.[0]?.id);
};
const tabChange = () => {};
const getTableData = () => {
  wordTableRef.value && wordTableRef.value.loadList();
};
const anasisList = ref([]);
// 场景策略
const getAnasisList = async (name = "") => {
  const res = await commonApi.getSceneVersion({ name });
  anasisList.value = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};
getAnasisList();
</script>
<style scoped lang="scss">
.result {
  ::v-deep {
    .el-tabs__header {
      padding-left: 16px;
      height: 40px;
    }
  }
  .meta-dictionary-main {
    padding: 16px;
    height: calc(100vh - 103px);
    display: flex;
    padding: 20px;

    ::v-deep {
      .left-card {
        overflow-y: auto;
        overflow-x: hidden;
        .el-card__body {
          padding: 10px;
        }
      }
      .meta-dict-table {
        padding-left: 10px;
        width: calc(100% - 375px);
        .info-card {
          height: 100%;
          .el-card__body {
            padding: 0;
            height: 100%;
          }
        }
      }
    }
  }
}
</style>