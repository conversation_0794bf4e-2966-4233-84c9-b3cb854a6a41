<template>
  <div class="meta-dict-table" v-show="treeNode?.id">
    <el-card class="info-card">
      <table-page
        ref="myWordTableRef"
        :columns="columns"
        :query="query"
        :loadDataApi="loadListData"
        :transformListData="transformListData"
        :loadImmediately="false"
        :operationAuth="operationAuth"
      >
        <template #query>
          <div class="flexBetweenStart">
            <my-query
              :queryItems="queryItems"
              :refresh-btn="{ show: true }"
              @search="events.search"
              @reset="events.reset"
              supportFold
              @toggle="events.toggleQuery"
            />
            <my-operation>
              <template #buttonGroup>
                <my-button type="primary" @click="events.redirct"
                  >策略结果分析</my-button
                >
                <my-button
                  type="export"
                  @click="events.export"
                  >导出</my-button
                >
              </template>
            </my-operation>
          </div>
        </template>
        <template #header>
          <div class="header">
            <span>{{ treeNode?.name }}</span>
          </div>
        </template>
      </table-page>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, watch } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaWordDbApi from "@/api/eval-manage";
import useStore from "@/store";
import { assign, pick, keys } from "lodash";
import * as util from "@/utils/common";
import * as commonApi from "@/api/common";
const { $app, proxy, $router } = useCtx();
const { common } = useStore();
const props = defineProps({
  treeNode: { type: Object, default: {} },
  treeData: { type: Array },
  operationAuth: { type: String },
});

//列配置
const defaultColumns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  {
    prop: "sceneProcessName",
    label: "策略名称",
    minWidth: 240,
  },
  // 文本可复制
  {
    prop: "regionName",
    label: "环境",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "query",
    minWidth: 190,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        const routeInfo = {
          name: `mark-index`,
          query: {
            markMode: "history",
            categoryId: record.categoryId,
            markRecordId: record.markRecordId,
          },
        }; 
        const resolvedRoute = $router.resolve(routeInfo);
        window.open(resolvedRoute.href, "_blank");
      },
    },
  },
  {
    prop: "createdBy",
    label: "标注人",
    minWidth: 120,
  },
  {
    prop: "createdDateRender",
    label: "标注时间",
    minWidth: 180,
  },
  {
    prop: "type",
    label: "来源",
    minWidth: 120,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        0: { type: "info", name: "策略结果" },
        1: { type: "warning", name: "全链路结果" },
        2: { type: "success", name: "大模型结果" },
      },
    },
  },
  {
    prop: "docIdxRender",
    label: "结果",
    minWidth: 100,
  },
  {
    prop: "url",
    label: "URL",
    minWidth: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        window.open(record.url,"_blank")
      },
    },
  },
  {
    prop: "title",
    label: "标题",
    minWidth: 200,
  },
]);
const columns = ref([]);
const sortTableData = ref<any>([]);
//查询面板
const query = ref<any>({});
const value1 = ref(false);

const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    width: "220px",
    modelValue: "",
    attrs: {
      placeholder: "标注人 或 Query",
    },
  },
  sceneProcessId: {
    type: "select",
    label: "",
    width: "220px",
    modelValue: "",
    options:[],
    attrs: {
      placeholder: "场景策略",
      filterable:true,
    }
  },
  type: {
    type: "select",
    label: "",
    modelValue: "",
    options: [
      { label: "策略结果", value: 0 },
      { label: "全链路结果", value: 1 },
      { label: "大模型结果", value: 2 },
    ],
    width: "180px",
    attrs: {
      placeholder: "来源",
    },
  },

  time: {
    type: "datetimerange",
    label: "",
    modelValue: "",
    width: "250px",
    collapsed: true,
    attrs: {
      placeholder: "请输入备注",
    },
  },
});
const getSceneList = (id:any)=>{
  if(id){
    common.getSceneVersionList(id).then((res) => {
      queryItems.value.sceneProcessId.options = res
    })
  }
}
const handleParams = (params)=>{
  if(params.time){
      params.startTime = params.time[0]
      params.endTime = params.time[1]
      delete  params.time
    }
    return params
}
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    let params: any = {
      ...data,
      categoryId: props.treeNode.id,
    };
    if (!props.treeNode.id) {
      return;
    }
    metaWordDbApi.getTablePage( handleParams(params)).then((result) => {
      const arr = util.generateTableColumns(result.content);
      columns.value = defaultColumns.value.concat([...arr]);
      result.content = result.content.map((item: any) => ({
        ...item,
        ...item.extendFieldMap,
      }));
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.docIdxRender = (x.docIdx != undefined && x.type===0) ? `Top${x.docIdx+1}` : "-";
    return x;
  });
};

//事件列表
const events = reactive({
  toggleQuery: () => {
    nextTick(() => {
      // proxy.$refs.myWordTableRef?.mediaHeight();
      window.dispatchEvent(new Event("resize"));
    });
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  redirct: () => {
    $router.push({
      name: `evaluation-reslut::details`,
      query: {
        metaLabel: "策略结果分析",
        query: JSON.stringify(handleParams(query.value)),
        categoryId: props.treeNode.id,
        nodeName: props.treeNode.name,
      },
    });
  },
  export: () => {
    metaWordDbApi.getTableExport(props.treeNode.id).then((result) => {
      util.downloadFile(result, `${props.treeNode.name}.xlsx`);
    });
  },
});
const loadList = () => {
  proxy.$refs.myWordTableRef?.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "updateTree"]);
//接口暴露
defineExpose({
  loadList,
});
// watch监听
watch(
  () => props.treeNode,
  (val: any) => {
    if (!val) {
      return;
    }
    value1.value = val.enabled;
    events.toggleQuery();
    loadList();
    getSceneList(val.id)
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>
<style lang="scss" scoped>
.meta-dict-table {
  height: 100%;
  .header {
    span {
      margin-right: 20px;
    }
    span:first-child {
      font-weight: 550;
    }
    ::v-deep {
      .el-switch {
        height: 0px;
        margin-left: 10px;
      }
    }
  }
}
::v-deep {
  .t-query {
    width: 80%;
    flex: 1;
  }
}
</style>