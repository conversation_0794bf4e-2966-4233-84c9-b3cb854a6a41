<template>
  <el-card class="left-card">
    <div>
      <el-input v-model="filterText" placeholder="请输入名称" @keyup.enter="emits('updateTree')">
        <template #append>
          <el-button :icon="Search" @click="emits('updateTree')" size="small" />
        </template>
      </el-input>
    </div>
    <el-tree
      ref="treeRef"
      style="max-width: 600px"
      :allow-drop="allowDrop"
      :expand-on-click-node="false"
      :data="treeData"
      node-key="id"
      :current-node-key="currentKey"
      :default-expanded-keys="expandArr"
      :props="defaultProps"
      @node-expand="handleNodeExpand"
      :highlight-current="true"
      v-if="treeData && treeData.length"
    >
      <template #default="{ node, data }">
        <span :class="{ 'custom-tree-node': true }" @click="setCurrentKey(data.id)">
          <span class="text" :title="node.label">
            {{ node.label }}
          </span>
          <el-popover placement="bottom" title="" :width="200" trigger="click" popper-class="operate-popover">
            <template #reference>
              <el-icon class="icon-more"><MoreFilled /></el-icon>
            </template>
            <template #default>
              <div class="items">
                <my-button link v-if="data.id != '0'" @click="events.add('current', data.parentId || 0, data.level || 0)" :operationAuth="operationAuth"
                  >新增同级菜单</my-button
                >
                <my-button v-if="data.level < 2" @click="events.add('next', data.id, (data.level || 0) + 1, data)" link :operationAuth="operationAuth"
                  >新增子菜单</my-button
                >
                <my-button link :operationAuth="operationAuth" @click="events.edit(data)">编辑</my-button>
                <my-button link :operationAuth="operationAuth" @click="events.delete(data)">删除</my-button>
              </div>
            </template>
          </el-popover>
        </span>
      </template>
    </el-tree>
    <el-button type="primary" @click="events.add('current', 0, 0)" v-else>新增</el-button>
    <AddTreeDialog ref="addRef" @reload="loadList" :treeData="treeData" :treeNode="getCurrentNode()" :anasisList="anasisList" :type="type" />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, defineEmits, onMounted } from "vue";
import { assign, pick, keys } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import { ElTree } from "element-plus";
import AddTreeDialog from "./addTree.vue";
import { Search } from "@element-plus/icons-vue";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import { downloadFile, findNodeById } from "@/utils/common.ts";
import * as metaWordApi from "@/api/eval-manage";
import type { AllowDropType, NodeDropType } from "element-plus/es/components/tree/src/tree.type";
import { computed } from "vue";
const { $app, proxy, $router } = useCtx();
const props = defineProps({
  treeData: { type: Array, default: [] },
  title: { type: String },
  type: { type: String },
  operationAuth: { type: String },
  anasisList: { type: Array, default: [] },
});
const emits = defineEmits(["updateTree", "updateTable"]);
const currentKey = ref("");
const dialogVisible = ref(false);
const areaOptions = ref<any>([]);
const area = ref("");
const filterText = ref("");
const expandArr = ref(["all"]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const data: any[] = [];

const defaultProps = {
  children: "children",
  label: "name",
  disabled: "disabled", // 指定哪个字段作为禁用标志
};
const setCurrentKey = (key: any) => {
  if (!key) {
    return;
  }
  nextTick(() => {
    treeRef.value.setCurrentKey(key);
    $router.push({
      name: `evaluation-reslut`,
      query: {
        ...$app.$route.query,
        nodeName: getCurrentNode().name,
        categoryId: key,
      },
    });
  });
};

const getCurrentNode = () => {
  return treeRef.value?.getCurrentNode();
};
const handleNodeExpand = (data: any, node: any, el: any) => {
  const expand: boolean = node.expand;
  if (expand) {
    expandArr.value.push(data.id);
  } else {
    expandArr.value.splice(expandArr.value.indexOf(node.id), 1);
  }
};
function findNodeById(tree, targetId) {
  for (const node of tree) {
    if (node.id === targetId) return node;
    if (node.children?.length) {
      const found = findNodeById(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
}
//事件列表
const events = reactive({
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: (type: string, parentId: any, level: number, data?: any) => {
    const params = { ...data, level, parentId };
    if (type == "current") {
      const parent = findNodeById(props.treeData, parentId || 0);
      if (parent) {
        params.standardId = parent.standardId;
      }
    }
    proxy.$refs.addRef?.openDialog("add", params);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `删除此文档会一并清除所有标注信息，确定继续？`,
      })
      .then(() => {
        metaWordApi.deleteTreeNode(record.id).then(() => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
});
const allowDrop = (draggingNode: any, dropNode: any, type: AllowDropType) => {
  if (draggingNode.level === dropNode?.level) {
    if (draggingNode.data.parentId === dropNode.data.parentId) {
      return type === "prev" || type === "next";
    }
  } else {
    return false;
  }
};
const loadList = (node?: any) => {
  emits("updateTree", node?.id);
};
onMounted(async () => {
  nextTick(() => {
    emits("updateTree");
  });
});
defineExpose({
  filterText,
  setCurrentKey,
  getCurrentNode,
});
</script>

<style lang="scss" scoped>
.icon-copy {
  color: $primary-color;
  margin-left: 12px;
  cursor: pointer;
  vertical-align: middle;
}
.icon-more {
  height: 100%;
}
.left-card {
  width: 375px;
  flex-shrink: 0;
  .title {
    font-weight: 550;
    display: flex;
    justify-content: space-between;
    i {
      margin-left: 5px;
    }
  }
  .layout-tab-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    .sub-title {
      margin-right: 8px;
    }
  }
  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 3px;
    .status {
      display: inline-block;
      height: 8px;
      width: 8px;
      border-radius: 8px;
      vertical-align: middle;
      margin-right: 2px;
      &.active {
        background-color: var(--el-color-primary);
      }
      &.error {
        background-color: var(--el-color-danger);
      }
    }
    .text {
      display: inline-block;
      align-content: center;
      @include no-wrap();
      width: 270px;
      word-break: break-all;
    }
  }
  .expire-node {
    background-color: var(--el-fill-color-light);
  }
  ::v-deep {
    .el-input {
      margin: 12px 0 10px 0;
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep(.el-button + .el-button) {
  margin-left: 0;
}
.disabled-overlay {
  cursor: not-allowed;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  background: rgba(255, 255, 255, 0.5);
}
.operate-popover {
  .items {
    ::v-deep(.el-button) {
      display: block;
      padding: 5px;
      width: 100%;
      text-align: left;
      cursor: pointer;
      &:hover {
        background: var(--el-color-primary-light-9);
      }
    }
  }
}
</style>