<template>
  <page-wrapper route-name="search-request::">
    <div class="search-request-table">
      <table-page ref="myTableRef" name="search-request-table" :columns="columns" :query="query"
        :loadDataApi="loadListData" :transformListData="transformListData" operationAuth="/base/#/search-request/edit"
        :loadImmediately="true" @operation="handleOperation" :withStoreColumn="true">
        <template #query>
          <my-query ref="queryRef" :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search"
            @reset="events.reset"></my-query>
          <div class="custom-operation">
            <my-operation>
              <template #buttonGroup>
                <my-button type="primary" @click="events.searchBtn">搜索</my-button>
                <el-popover placement="bottom" :width="200" trigger="hover">
                  <div>
                    <el-input-number v-model="exportNum" placeholder="请输入数量" />
                  </div>
                  <template #reference>
                    <my-button type="export" @click="events.export">导出</my-button>
                  </template>
                </el-popover>
                <my-button type="primary" @click="events.openTaskList">导出列表</my-button>
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
      <my-drawer class="mock-add" width="900px" v-model="historyVisible" title="导出列表" :showConfirm="false"
        @close="historyVisible = false">
        <TaskList ref="TaskListRef" :queryDisplay="false" :queryItemsIndex="queryItems"> </TaskList>
      </my-drawer>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as searchResquestApi from "@/api/search-request";
import TaskList from "@/views/evaluation-manage/search-request/taskList.vue";
import { getEnableList } from "@/api/app";
const { $app, proxy, $auth, $router } = useCtx();
import moment from "moment";
const historyVisible = ref(false);
const { word, common } = useStore();

const exportNum = ref(100);
//列配置
const columns = ref([
  {
    prop: "appIdRender",
    label: "appid",
    width: 200,
  },
  {
    prop: "appType",
    label: "appid类型",
    width: 120,
  },
  { prop: "prodCode", label: "关联产品", minWidth: 120 },
  { prop: "productVersion", label: "方案版本", minWidth: 160 },
  {
    prop: "flowName",
    label: "策略名称",
    minWidth: 120,
  },
  {
    prop: "query",
    label: "query",
    width: 180,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        $router.push({
          name: "search-request::position",
          query: {
            metaLabel: ["全链路定位"],
            traceId: record.traceId,
            region: query.value.region,
          },
        });
      },
    },
  },
  {
    prop: "fromType",
    label: "query来源",
    minWidth: 120,
  },
  {
    prop: "topK",
    label: "topK",
    minWidth: 85,
  },
  {
    prop: "scene",
    label: "场景标签",
    minWidth: 200,
  },
  {
    prop: "intent",
    label: "搜索意图",
    width: 120,
  },

  { prop: "docsCount", label: "结果数量", minWidth: 110 },
  { prop: "plugin", label: "插件分类", minWidth: 110 },
  { prop: "traceId", label: "traceId", width: 200,withCopy:true },

  { prop: "createdDateRender", label: "创建时间", width: 170 },
]);
//查询面板
const query = ref<any>({
});
const queryItems = ref<any>({
  region: {
    label: "环境",
    type: "select",
    options: [],
    defaultValue: '',
    attrs: { clearable: false },
  },

  appIdList: {
    label: "appid",
    type: "select",
    options: [],
    defaultValue: [],
    attrs: { multiple: true },
    events: { change: () => { } }
  },
  appTypeList: {
    label: "appid类型",
    type: "select",
    options: [],
    defaultValue: [],
    attrs: { multiple: true },
  },
  flowName: {
    label: "策略名称",
    type: "input",
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  query: {
    label: "query",
    type: "input",
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  traceId: {
    label: "traceId",
    type: "input",
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  fromType: {
    label: "query来源",
    type: "input",
    defaultValue: '',
    attrs: { clearable: false },
    events: { change: () => { } }
  },
  topK: {
    label: "TopK",
    type: "input",
    options: [],
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  scene: {
    label: "场景标签",
    type: "input",
    options: [],
    defaultValue: '',
    attrs: { clearable: false },
    events: { change: () => { } }
  },
  intent: {
    label: "搜索意图",
    type: "select",
    options: [],
    defaultValue: [],
    attrs: { clearable: false, multiple: true },
    events: { change: () => { } }
  },
  docCount: {
    label: "结果数量",
    type: "input",
    options: [],
    defaultValue: '',
    attrs: { clearable: false },
    events: { change: () => { } }
  },
  plugin: {
    label: "插件分类",
    type: "input",
    options: [],
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  filterFiledName: {
    label: "时间过滤字段",
    type: "input",
    options: [],
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  filterStartTime: {
    label: "时间过滤开始时间",
    type: "input",
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  filterEndTime: {
    label: "时间过滤结束时间",
    type: "input",
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  filterDomain: {
    label: "过滤站点域名",
    type: "input",
    defaultValue: '',
    attrs: {},
    events: { change: () => { } }
  },
  prodCode: {
    label: "关联产品",
    type: "select",
    options: [],
    defaultValue: [],
    attrs: { multiple: true },
  },
  pvidList: {
    label: "方案版本",
    type: "select",
    options: [],
    defaultValue: [],
    attrs: { multiple: true },
  },
  createdDate: {
    label: "创建时间",
    type: "daterange",
    options: [],
    defaultValue: [],
    attrs: { clearable: false },
    events: { change: () => { } }
  },
});
const handleParams = (data: any) => {
  const { createdDate, ...rest } = data
  rest.beginTime = data.createdDate[0]
  rest.endTime = data.createdDate[1]
  rest.filterStartTime = data?.filterStartTime?.[0]
  rest.filterEndTime = data?.filterStartTime?.[1]
  return rest
}
const getProductListApi = () => {
  common.getOpenedProductList().then((res) => {
    queryItems.value.prodCode.options = res?.map((x: any) => ({
      label: x.name,
      value: x.code,
      code: x.code,
    }));
  });
};
const getProductVersionListApi = () => {
  common.getOpenedProductVersionList().then((res) => {
    queryItems.value.pvidList.options = res?.map((x: any) => ({
      label: x.name,
      value: x.id,
      code: x.code,
    }));
  });
};
getProductListApi();
getProductVersionListApi();
//查询缓存
const loadHistoryQuery = (data: any) => {
  const historyQuery = dataC.safeObject(localStorage.getItem("search-request-table"));
  if (dataC.isEmpty(historyQuery)) {
    return;
  }
  keys(historyQuery).forEach((key) => {
    if (dataC.isEmpty(historyQuery[key])) {
      historyQuery[key] = queryItems.value[key].defaultValue
    }
    queryItems.value[key].modelValue = historyQuery[key];
    data[key] = historyQuery[key];
  });
};
//列表查询
const loadListData = async (data: any) => {
  loadHistoryQuery(data)
  return new Promise((resolve: any) => {
    if (!data.region) {
      return
    }
    searchResquestApi.getSearchResults(handleParams(data)).then((result) => {
      result.content = result.content.map((item: any) => ({
        ...item,
      }));
      //返回数据
      resolve(result);
    })
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createTime, "YYYY-MM-DD hh:mm:ss");
    x.appIdRender = queryItems.value.appIdList.options.find((item: any) => item.value === x.appId)?.label;
    return x;
  });
};
const updateEnvirenment = () => {
  word.getAreaList().then((values: any) => {
    queryItems.value.region.options = values.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
    }));
    const regionCode = values[0].code;
    query.value.region = regionCode;
    queryItems.value.region = { ...queryItems.value.region, modelValue: regionCode, defaultValue: regionCode };
  });
};
updateEnvirenment()
const getApplication = () => {
  getEnableList().then((res) => {
    queryItems.value.appIdList.options = res.data.map((item: any) => {
      return {
        ...item,
        value: item.appId,
        label: `${item.name}(${item.appId})`,
      };
    });
    queryItems.value.appTypeList.options = Array.from(new Set(res.data.map((item: any) => item.type)))
      .filter(item => item !== "" && item != null) // 过滤掉空字符串
      .map((item: any) => {
        return {
          ...item,
          value: item,
          label: item,
        };
      });
  });
};
getApplication()
const getIntent = () => {
  searchResquestApi.getSearchIntent().then((res) => {
    queryItems.value.intent.options = res.data.map((item: any) => ({
      ...item,
      value: item.dictCode,
      label: item.dictName,
    }));
  });
};
getIntent()

const updateDate = () => {
  // 获取当天的开始时间和结束时间
  const start = moment().startOf('day').format("YYYY-MM-DD");
  const end = moment().endOf('day').format("YYYY-MM-DD");
  query.value.createdDate = [start, end];
  queryItems.value.createdDate.modelValue = [start, end];
  queryItems.value.createdDate.defaultValue = [start, end];
};

updateDate()
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  searchBtn: () => {
    proxy.$refs.queryRef.searchImmediatelyFun()
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
    localStorage.setItem("search-request-table", JSON.stringify(query.value));
  },
  reset: (obj: any) => {},
  openTaskList: () => {
    historyVisible.value = true;
    nextTick(() => {
      proxy.$refs.TaskListRef.loadList();
    })
  },
  export: () => {
    searchResquestApi.exportSearchResults({ ...handleParams(query.value), count: exportNum.value }).then((res) => {
      $app.$message.success(`导出成功`);
    })
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//初始化
onMounted(() => {
});

//事件声明
const emit = defineEmits(["edit-data-inst", "statistic-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.search-request-table {
  height: 100%;

  ::v-deep {
    .t-query {
      .el-form-item__label {
        width: 125px;
      }

      .el-select {
        width: 180px !important;
        .el-select__selection{
          flex-wrap: nowrap;
        }
      }

      .el-input {
        width: 180px !important;
      }
    }
  }
::v-deep{
  .custom-operation{
   padding:0 8px 8px;
   float: right;
  }
}
}
</style>
