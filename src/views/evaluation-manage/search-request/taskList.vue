<template>
  <div class="task-history-table">
    <table-page
      ref="myTableRef"
      :name="'task-history' + dataType"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
      :withStoreColumn="false"
      :withSelection="true"
      :withOrder="false"
      @selection-change="events.handleSelectionChange"
    >
      <template #query>
        <div class="flexBetweenStart">
          <div>
            <my-query
              :queryItems="queryItems"
              :refresh-btn="{ show: true }"
              @search="events.search"
              @reset="events.reset"
            />
          </div>
          <my-operation>
            <template #buttonGroup>
              <my-button
                type="primary"
                @click="events.batchDelete"
                :disabled="selectedIds.length === 0"
                >批量删除</my-button
              >
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
    <my-drawer
      class="mock-add"
      width="500px"
      v-model="detailVisible"
      title="详情"
      :showConfirm="false"
      @close="detailVisible = false"
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item label="appid">
          {{ transformOptionToLabel(record.appIdList, props.queryItemsIndex.appIdList?.options) }}
        </el-descriptions-item>
        <el-descriptions-item label="appid类型">
          {{ transformOptionToLabel(record.appTypeList, props.queryItemsIndex.appTypeList?.options) }}
        </el-descriptions-item>
        <el-descriptions-item label="query">
          {{ record.query || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="query来源">
          {{ record.fromType || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="策略名称">
          {{ record.flowName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="场景标签">
          {{ record.scene || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="搜索意图">
          {{ transformOptionToLabel(record.intent, props.queryItemsIndex.intent?.options) }}
        </el-descriptions-item>
        <el-descriptions-item label="结果数量">
          {{ record.docCount || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="TopK">
          {{ record.topK || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="环境">
          {{ transformOptionToLabel(record.region, props.queryItemsIndex.region?.options) }}
        </el-descriptions-item>
        <el-descriptions-item label="选择时间">
          {{ record.beginTime || '-' }}~{{ record.endTime || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </my-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import { keys, assign } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import { dataC, timeC } from "turing-plugin";
import useStore from "@/store";
import useCtx from "@/hooks/useCtx";
import * as searchResquestApi from "@/api/search-request";
import * as util from "@/utils/common";

interface Option {
  value: string | number;
  label: string;
}

interface QueryItem {
  options?: Option[];
}

interface QueryItemsIndex {
  appIdList?: QueryItem;
  appTypeList?: QueryItem;
  intent?: QueryItem;
  region?: QueryItem;
}

const { $app, proxy } = useCtx();
const { api } = useStore();
const detailVisible = ref(false);

const props = defineProps({
  dataType: { type: Number, default: null },
  queryDisplay: { type: Boolean, default: true },
  queryItemsIndex: { type: Object as () => QueryItemsIndex, default: () => ({}) },
});

const record = ref<any>({});
const selectedIds = ref<any[]>([]);
const queryItems = ref<any[]>([]);

// 转换选项值为标签的通用函数
const transformOptionToLabel = (value: string | number | (string | number)[], options?: Option[]): string => {
  if (!value) return '-';
  if (!options?.length) return String(value);
  
  if (Array.isArray(value)) {
    return value.map(v => options.find(opt => opt.value === v)?.label || v).join(',') || '-';
  }
  
  return options.find(opt => opt.value === value)?.label || String(value) || '-';
};

//列配置
const columns = ref([
  {
    prop: "status",
    label: "状态",
    width: 100,
    custom: "status",
    customRender: {
      options: {
        2: { type: "danger", name: "失败" },
        1: { type: "primary", name: "成功" },
        0: { type: "warning", name: "导出中" },
      },
    },
  },
  {
    prop: "createdDateRender",
    label: "申请时间",
    width: 170,
  },
  {
    prop: "lastModifiedDateRender",
    label: "完成时间",
    width: 170,
  },
  {
    prop: "count",
    label: "导出数量",
    width: 120,
  },
  {
    prop: "createdBy",
    label: "导出人",
    sortable: false,
  },
  // 操作
  {
    prop: "operation",
    label: "操作",
    width: 150,
    fixed: "right",
  },
]);
//查询面板
const query = ref<any>({
  dataType: props.dataType,
});
//列表查询
const loadListData = async (data: any) => {
  return new Promise((resolve: any) => {
    searchResquestApi.getSearchTasks(data).then((result) => {
      result.content = result.content.map((x: any) => ({
        ...x,
        ...x.condition,
      }));
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.taskType = x.type;
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作 查看和下载
const operations = [
  {
    type: "view",
    label: "查看",
  },
  {
    type: "download",
    label: "下载",
    disabled: (obj: any) => {
      return obj.status != 1;
    },
  },
  {
    type: "delete",
    label: "删除",
    btnType: "danger",
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    proxy.$refs.myTableRef.loadData();
  },
  reset: (obj: any) => {
    proxy.$refs.myTableRef.loadData();
  },
  view: (obj: any) => {
    detailVisible.value = true;
    record.value = obj.condition;
  },
  download: (obj: any) => {
    searchResquestApi.downloadSearchResult(obj.fileId).then((res) => {
      util.downloadFile(res, obj.fileName);
    });
  },
  delete: (obj: any) => {
    $app
      .$confirm({
        title: `您确认要删除${obj.id}吗?`,
      })
      .then(() => {
        searchResquestApi.deleteSearchTask([obj.id]).then((res) => {
          proxy.$message.success("删除成功");
          loadList();
        });
      });
  },
  batchDelete: () => {
    $app
      .$confirm({
        title: `您确认要删除吗?`,
      })
      .then(() => {
        searchResquestApi.deleteSearchTask(selectedIds.value).then((res) => {
          proxy.$message.success("删除成功");
          loadList();
        });
      });
  },
  handleSelectionChange: (val: any) => {
    selectedIds.value = val.map((x: any) => x.id);
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
onMounted(() => {});
//事件声明
const emit = defineEmits([]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.task-history-table {
  height: 100%;

  .icon-copy {
    color: $primary-color;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
