<template>
  <my-drawer class="concern-node-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form labelWidth="130px" ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit">
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { assign, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import * as evalSettingApi from "@/api/eval-setting";

const { $app } = useCtx();

const emit = defineEmits(["save-data"]);

// 弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增策略字段";
  if (formType.value == "edit") return "编辑策略字段";
});
const dialogVisible = ref<boolean>(false);

const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};

const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

// 是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  code: "",
  inputFiled: "",
  outputField: "",
  scoreField: "",
  indexField: "",
  showInRecall: false,
  showInTrace: false,
  defaultValue: "",
  description: "",
};

let ruleForm = ref<any>(assign({}, defaultForm));

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "流程节点名称不能为空", trigger: "blur" },
    { min: 1, max: 100, message: "流程节点名称长度在 1 到 100 个字符", trigger: "blur" },
  ],
  code: [
    { required: true, message: "Code不能为空", trigger: "blur" },
    { min: 1, max: 50, message: "Code长度在 1 到 50 个字符", trigger: "blur" },
  ],
  inputFiled: [
    { required: true, message: "入参字段不能为空", trigger: "blur" },
    { min: 1, max: 50, message: "入参字段长度在 1 到 50 个字符", trigger: "blur" },
  ],
  outputField: [
    { required: true, message: "出参字段不能为空", trigger: "blur" },
    { min: 1, max: 50, message: "出参字段长度在 1 到 50 个字符", trigger: "blur" },
  ],
});

// 表单项
const formItems = ref<any>({
  name: {
    label: "流程节点",
    type: "input",
    attrs: {
      maxlength: 100,
      placeholder: "请输入流程节点名称",
    },
  },
  code: {
    label: "Code",
    type: "input",
    attrs: {
      maxlength: 50,
      placeholder: "请输入Code",
    }
  },
  inputFiled: {
    label: "入参",
    type: "input",
    attrs: {
      placeholder: "请输入入参字段",
    },
  },
  outputField: {
    label: "出参",
    type: "input",
    attrs: {
      placeholder: "请输入出参字段",
    },
  },
  scoreField: {
    label: "得分字段",
    type: "input",
    attrs: {
      placeholder: "请输入得分字段",
    },
  },
  indexField: {
    label: "位次字段",
    type: "input",
    attrs: {
      placeholder: "请输入位次字段",
    },
  },
  showInRecall: {
    label: "支持策略数据展示",
    type: "switch",
  },
  showInTrace: {
    label: "支持全链路展示",
    type: "switch",
  },
  defaultValue: {
    label: "默认值",
    type: "input",
    attrs: {
      placeholder: "请输入默认值",
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 500,
      placeholder: "请输入描述",
      rows: 3,
    },
  },
});

// 提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);

  if (isUpdate.value) {
    evalSettingApi.saveConcernNode(form).then(() => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    evalSettingApi.saveConcernNode(form).then(() => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};

// 打开窗口
const openWindow = (type: string, item: any) => {
  formType.value = type;
  if (type === "edit") {
    ruleForm.value = assign({}, defaultForm, item);
  } else {
    ruleForm.value = assign({}, defaultForm);
  }
  dialogVisible.value = true;
};

// 暴露方法
defineExpose({
  openWindow,
});
</script>

<style lang="scss" scoped>
</style>
