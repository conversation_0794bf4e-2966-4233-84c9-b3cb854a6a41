<template>
  <page-wrapper route-name="concern-node-setting::">
    <div class="concern-node-setting">
      <concernNodeEdit ref="concernNodeEditRef" @save-data="events.loadTableList" :categoryEnum="categoryEnum"></concernNodeEdit>
      <concernNodeTable ref="concernNodeTableRef" @edit-data-inst="events.openEditWindow" :categoryEnum="categoryEnum"></concernNodeTable>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import concernNodeTable from "./ConcernNodeTable.vue";
import concernNodeEdit from "./ConcernNodeEdit.vue";
import useCtx from "@/hooks/useCtx";
import * as evalSettingApi from "@/api/eval-setting";

const { proxy } = useCtx();
let categoryEnum = ref([]);

const events = reactive({
  loadTableList: () => {
    proxy.$refs["concernNodeTableRef"].loadList();
  },
  openEditWindow: (type: string, item: any) => {
    proxy.$refs["concernNodeEditRef"].openWindow(type, item);
  }
});

// 获取字段分类列表
const getCategories = () => {
  evalSettingApi.findAllCategories().then((res) => {
    categoryEnum.value = res.data.map((item: string) => ({ value: item, label: item }));
  });
};

getCategories();
</script>

<style lang="scss" scoped>
</style>