<template>
  <my-drawer class="exp-prod-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <my-form labelWidth="130px" ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit">
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { assign, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import * as evalSettingApi from "@/api/eval-setting";

const { $app } = useCtx();

const emit = defineEmits(["save-data"]);

// 弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增体验产品配置";
  if (formType.value == "edit") return "编辑体验产品配置";
});
const dialogVisible = ref<boolean>(false);

const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};

const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

// 是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  code: "",
  enabled: true,
  description: "",
};

let ruleForm = ref<any>(assign({}, defaultForm));

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: "名称不能为空", trigger: "blur" },
    { min: 1, max: 100, message: "名称长度在 1 到 100 个字符", trigger: "blur" },
  ],
  code: [
    { required: true, message: "流程ID不能为空", trigger: "blur" },
    { min: 1, max: 50, message: "流程ID长度在 1 到 50 个字符", trigger: "blur" },
  ],
});

// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 100,
      placeholder: "请输入名称",
    },
  },
  code: {
    label: "流程ID",
    type: "input",
    attrs: {
      maxlength: 50,
      placeholder: "请输入流程ID",
    },
  },
  enabled: {
    label: "是否启用",
    type: "switch",
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 500,
      placeholder: "请输入描述",
      rows: 3,
    },
  },
});

// 提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);

  if (isUpdate.value) {
    evalSettingApi.saveExpProd(form).then(() => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    evalSettingApi.saveExpProd(form).then(() => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};

// 打开窗口
const openWindow = (type: string, item: any) => {
  formType.value = type;
  if (type === "edit") {
    ruleForm.value = assign({}, defaultForm, item);
  } else {
    ruleForm.value = assign({}, defaultForm);
  }
  dialogVisible.value = true;
};

// 暴露方法
defineExpose({
  openWindow,
});
</script>

<style lang="scss" scoped>
</style>
