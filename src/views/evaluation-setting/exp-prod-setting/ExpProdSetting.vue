<template>
  <page-wrapper route-name="exp-prod-setting::">
    <div class="exp-prod-setting">
      <exp-prod-edit ref="expProdEditRef" @save-data="events.loadTableList" />
      <exp-prod-table ref="expProdTableRef" @edit-data-inst="events.openEditWindow" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import ExpProdTable from "./ExpProdTable.vue";
import ExpProdEdit from "./ExpProdEdit.vue";
import useCtx from "@/hooks/useCtx";

const { proxy } = useCtx();

const events = reactive({
  loadTableList: () => {
    proxy.$refs["expProdTableRef"].loadList();
  },
  openEditWindow: (type: string, item: any) => {
    proxy.$refs["expProdEditRef"].openWindow(type, item);
  }
});
</script>

<style lang="scss" scoped>
.exp-prod-setting {
  height: 100%;
}
</style>
