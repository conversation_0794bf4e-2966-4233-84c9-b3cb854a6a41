<template>
    <div class="expProdTableRef">
        <table-page ref="expProdTableRef" name="expProdTableRef" :columns="columns" :query="query"
            :loadDataApi="loadListData" :transformListData="transformListData" :operations="operations"
            @operation="handleOperation" :withSort="true" :defaultSort="{ prop: 'lastModifiedDate', order: 'desc' }"
            :dragRow="isDragging" @row-change="handleTableRowChange">
            <template #query>
                <div class="flexBetweenStart">
                    <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search"
                        @reset="events.reset" />
                    <my-operation>
                        <template #buttonGroup>
                            <my-button type="add" @click="events.add" v-if="!isDragging">新建</my-button>
                            <my-button type="danger" @click="isDragging = true" v-if="!isDragging">排序</my-button>
                            <my-button type="danger" @click="events.saveDrag" v-if="isDragging">保存</my-button>
                            <my-button type="info" @click="events.cancelSort" v-if="isDragging">取消</my-button>

                        </template>
                    </my-operation>
                </div>
            </template>
            <template #enabled="{ record }">
                <el-switch v-model="record.enabled" @change="handleEnabledChange(record)"
                    :loading="record.enabledLoading || false" v-if="record" />
            </template>
        </table-page>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as evalSettingApi from "@/api/eval-setting.ts";
const { $app, proxy, $auth } = useCtx();

const { api } = useStore();

const isDragging = ref(false);

const sortTableData = ref<any>([]);

//列配置
const columns = ref([
    { prop: "name", label: "名称", minWidth: 120, sortable: true },
    { prop: "code", label: "流程ID", width: 240, withCopy: true, sortable: true },
    {
        prop: "enabled",
        label: "是否启用",
        width: 120,
        custom: "switch",
        sortable: true,
        customRender: {
            attrs: {
                "active-value": true,
                "inactive-value": false,
            },
            beforeChange: (record: any) => {
                console.log("enabled change record", record);

                $app.$confirm({ title: `您确认要${record.enabled ? "停用" : "启用"}吗？` }).then((res) => {
                    evalSettingApi.updateExpProdEnabled(record.id, !record.enabled).then((res) => {
                        loadList();
                        $app.$message.success(record.enabled ? "停用成功" : "启用成功");
                    });
                });
            },
        },
    },
    { prop: "description", label: "描述", minWidth: 300 },
    { prop: "createdBy", label: "创建人", width: 100 },
    { prop: "createdDateRender", label: "创建时间", width: 170, sortable: "createdDate" },
    { prop: "lastModifiedBy", label: "更新人", width: 100, sortable: true },
    { prop: "lastModifiedDateRender", label: "更新时间", width: 170, sortable: "lastModifiedDate" },
    { prop: "operation", label: "操作", width: 120, fixed: "right" },
]);
//查询面板
const query = ref<any>({
});
const queryItems = ref<any>({
    search: {
        type: "input",
        label: "",
        modelValue: "",
        defaultValue: "",
        attrs: {
            placeholder: "名称 或 流程ID",
        },
    },
});

//列表查询
const loadListData = async (data: any) => {
    return new Promise((resolve: any) => {
        evalSettingApi.findExpProdByPage(data).then((result) => {
            console.log('体验产品配置数据result:', result);
            //返回数据
            resolve(result);
        }).catch((error) => {
            console.error('加载体验产品配置失败:', error);
            resolve(error);
        });
    });
};
//转换接口返回的数据
const transformListData = (data: any) => {
    return data.map((x: any) => {
        console.log('体验产品配置数据:', x);

        x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
        x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
        x.enabledLoading = false; // 添加启用状态切换的loading状态
        // 确保 enabled 字段存在，如果不存在则设置默认值
        if (x.enabled === undefined || x.enabled === null) {
            x.enabled = false;
        }
        return x;
    });
};

// 处理启用状态切换
const handleEnabledChange = (record: any) => {
    if (!record || !record.id) {
        $app.$message.error('记录数据异常');
        return;
    }

    record.enabledLoading = true;
    evalSettingApi.updateExpProdEnabled(record.id, record.enabled).then(() => {
        $app.$message.success(`${record.enabled ? '启用' : '禁用'}成功`);
        record.enabledLoading = false;
    }).catch((error) => {
        console.error('更新启用状态失败:', error);
        // 如果失败，恢复原状态
        record.enabled = !record.enabled;
        record.enabledLoading = false;
        $app.$message.error('操作失败，请重试');
    });
};

//操作
const operations = [
    {
        type: "edit",
        label: "编辑",
    },
    { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
    const { type, record } = data;
    typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
    search: (obj: any) => {
        query.value = assign({}, query.value, obj);
    },
    reset: (obj: any) => {
    },
    add: () => {
        emit("edit-data-inst", "add", {});
    },
    edit: (record: any) => {
        emit("edit-data-inst", "edit", record);
    },
    delete: (record: any) => {
        $app
            .$deleteConfirm({
                title: `您确认要删除 ${record.name}?`,
            })
            .then(() => {
                evalSettingApi.deleteExpProdById(record.id).then((result) => {
                    loadList();
                    $app.$message.success(`删除 ${record.name} 成功`);
                });
            });
    },
    saveDrag: () => {
        isDragging.value = true;
        const ids = sortTableData.value.map((item:any) => item.id);

        const params = {
            ids: ids,
        };

        evalSettingApi.sortExpProd(params).then((result) => {
            loadList();
            $app.$message.success(`排序成功`);
        });
    },
    cancelSort: () => {
        isDragging.value = false;
        sortTableData.value = [];
        loadList();
    },
});

function handleTableRowChange(data:any[]){
    console.log('排序后的数据:', data);
    sortTableData.value = data;
}

const loadList = () => {
    proxy.$refs.expProdTableRef.loadData();
};
//初始化
onMounted(() => { });
//销毁
onUnmounted(() => {
});
//事件声明
const emit = defineEmits(["edit-data-inst"]);
//接口暴露
defineExpose({
    loadList,
});
</script>
<style lang="scss" scoped>
.expProdTableRef {
    height: 100%;
}
</style>
