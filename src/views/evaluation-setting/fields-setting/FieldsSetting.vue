<template>
  <page-wrapper route-name="fields-setting::">
    <div class="fields-setting">
      <fields-edit ref="fieldsEditRef" @save-data="events.loadTableList" :categoryEnum="categoryEnum"></fields-edit>
      <fields-table ref="fieldsTableRef" @edit-data-inst="events.openEditWindow" :categoryEnum="categoryEnum"></fields-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import fieldsTable from "./FieldsTable.vue";
import fieldsEdit from "./FieldsEdit.vue";
import useCtx from "@/hooks/useCtx";
import * as evalSettingApi from "@/api/eval-setting";

const { proxy } = useCtx();
let categoryEnum = ref([]);

const events = reactive({
  loadTableList: () => {
    proxy.$refs["fieldsTableRef"].loadList();
  },
  openEditWindow: (type: string, item: any) => {
    proxy.$refs["fieldsEditRef"].openWindow(type, item);
  }
});

// 获取字段分类列表
const getCategories = () => {
  evalSettingApi.findAllCategories().then((res) => {
    categoryEnum.value = res.data.map((item: string) => ({ value: item, label: item }));
  });
};

getCategories();
</script>

<style lang="scss" scoped>
</style>