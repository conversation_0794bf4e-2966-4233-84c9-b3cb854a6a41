<template>
  <div class="fieldsTableRef">
    <table-page
      ref="fieldsTableRef"
      name="fieldsTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add">新建测评字段</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as evalSetting<PERSON>pi from "@/api/eval-setting.ts";
const { $app, proxy, $auth } = useCtx();


const { api } = useStore();
//列配置
const columns = ref([
  {
    prop: "category",
    label: "字段分类",
    width: 180
  },
  { prop: "field", label: "字段", width: 140, withCopy: true },
  { prop: "name", label: "名称", minWidth: 120 },
  { prop: "type", label: "字段类型", minWidth: 120 },
  { prop: "defaultValue", label: "默认值", minWidth: 120 },
  { prop: "sample", label: "示例", minWidth: 120 },
  { prop: "path", label: "路径", minWidth: 120 },
  { prop: "showInTrace", label: "全链路展示", minWidth: 120 },
  {
    prop: "description",
    label: "描述",
    minWidth: 300,
  },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "operation", label: "操作", width: 120, fixed: "right" },
]);
//查询面板
const query = ref<any>({
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    attrs: {
      placeholder: "名称 或 字段",
    },
  },
});

//列表查询
const loadListData = async (data: any) => {
  return new Promise((resolve: any) => {
    evalSettingApi.findByPage(data).then((result) => {
      //返回数据
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
  },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {
  },
  add: () => {
    emit("edit-data-inst", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data-inst", "edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        evalSettingApi.deleteById(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  }
});
const loadList = () => {
  proxy.$refs.fieldsTableRef.loadData();
};
//初始化
onMounted(() => {});
//销毁
onUnmounted(() => {
});
//事件声明
const emit = defineEmits(["edit-data-inst"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.fieldsTableRef {
  height: 100%;
}
</style>