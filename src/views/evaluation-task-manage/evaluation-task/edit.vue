<template>
  <my-drawer class="analysis-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm"
    @close="handleClose" size="1000">
    <my-form labelWidth="130px" ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"
      @submit="submit">
      <template #groupUserAssignments>
        <el-icon @click="addgroupUserAssignments(Object.keys(ruleForm.groupUserAssignments).length)"
          style="cursor: pointer;padding:20px 0;" :size="24" color="#177BF8">
          <CirclePlus />
        </el-icon>
        <div v-for="(key, index) in Object.keys(ruleForm.groupUserAssignments)" style="width: 100%;">
          <div style="width: 100%;display: flex;align-items: space-between;margin-bottom: 10px;">
            <span style="margin-right: 10px;">{{ key }}</span>
            <TransferSelect v-model="ruleForm.groupUserAssignments[key]" :transferData="handlePersonData(key)"
              style="width: 88%; margin-right: 10px;" />
            <el-icon v-if="index == Object.keys(ruleForm.groupUserAssignments).length - 1 && index != 0"
              @click="deletegroupUserAssignments(key)" style="cursor: pointer;" :size="24" color="#177BF8">
              <Remove />
            </el-icon>
          </div>
        </div>
      </template>

      <template #extendFieldsRef>
        <div style="width: 100%;">
          <el-row>
            <el-col :span="10"></el-col>
            <el-col :span="6"><my-button @click="clearSelectedField()" type="priamry"
                primary>清空已选字段</my-button></el-col>
            <el-col :span="8">
              <el-dropdown @command="changeFieldCategory">
                <el-button>
                  快速选择类别
                  <el-icon size="18px">
                    <ArrowDown />
                  </el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="item in fieldCategoryOptions" :command="item">{{ item.label
                      }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-col>
          </el-row>

          <el-row>
            <el-checkbox-group v-model="ruleForm.extendFields" style="width: 100%;display: flex;flex-wrap: wrap;">
              <el-checkbox v-for="item in fieldList" :label="item.field" :key="item.field" :value="item.field"
                style="margin-right: 10px;margin-bottom: 10px;">
                {{ item.name }}
              </el-checkbox>
            </el-checkbox-group>
          </el-row>
        </div>
      </template>

      <template #strategyConfigRef>
        <div class="strategy-config-container">
          <div>
            <div class="button-add">
              <my-button type="primary" text="primary" @click="handleAddStr(false)" v-if="!isReleased">添加策略</my-button>
              <my-button type="primary" text="primary" @click="handleAddStr(true)"
                v-if="!isReleased && ruleForm.type == 2">添加离线数据</my-button>
            </div>
          </div>

          <div v-for="(item, index) in ruleForm.strategyConfig" class="strategy-config-item">
            <div :style="{ color: item.offline ? '#177BF8' : '#373C40' }"><span v-if="item.offline">离线</span>策略{{ index
              + 1 }}</div>
            <el-row>
              <el-col :span="22">
                <my-form ref="strategyFormRef" :rules="rules" :ruleForm="item" :formItems="strategyformItems[index]"
                  @submit="submit" label-width="150px">
                  <template #queryGroupId>
                    <div style="display: flex; align-items: center">
                      <my-button type="primary" text="primary" v-if="item.offline"
                        @click="offlineImportShow(index)">导入query集离线数据</my-button>
                      <div v-if="item.queryGroupId"><span>query集id：</span>{{ item.queryGroupId }}</div>
                    </div>
                  </template>

                  <template #strategyConfig-processParams>
                    <FlowParam v-if="item.inputArgs && item.inputArgs.length > 0" :key="paramUseKey[index] || 0"
                      :inputArgs="item.inputArgs" style="width: 100%"></FlowParam>
                    <div v-else style="color: #999; padding: 10px;">
                      无可调整参数
                    </div>
                  </template>
                </my-form>
              </el-col>
              <el-col :span="2">
                <my-button type="danger" text="danger" @click="handleDeleteStr(index)"
                  v-if="ruleForm.strategyConfig.length > 1 && !isReleased"><el-icon :size="20">
                    <Remove />
                  </el-icon></my-button></el-col>
            </el-row>
          </div>
        </div>
      </template>


    </my-form>
    <ImportOfflineModal ref="importOfflineModalRef" @import="handleImport" />
  </my-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import TransferSelect from "@/components/TransferSelect.vue";
import * as evaluationApi from "@/api/eval-task";
import * as markModuleApi from "@/api/eval-mark-module";
import * as evalSettingApi from "@/api/eval-setting";
import * as commonApi from "@/api/common";
import * as evaluationManageApi from "@/api/eval-evaluation";
import * as util from "@/utils/common";
import FlowParam from "@/views/evaluation-manage/mark/FlowParam.vue";
import ImportOfflineModal from "@/views/evaluation-manage/result/importOfflineModal.vue";

const { $app, proxy } = useCtx();
const { common } = useStore();
const props = defineProps({
  treeNode: { type: String, default: "" },
  groupEnum: { type: Array, default: [] },
  publishList: { type: Array, default: [] },
  standardList: { type: Array, default: [] },
  missiontypeEnum: { type: Array, default: [] },
  modeltypeEnum: { type: Array, default: [] },
});
// 字段列表
const fieldList = ref<any[]>([]);

function changeFieldCategory(newValue: any) {
  if (!dataC.isEmpty(newValue)) {

    const list = fieldList.value.filter(item => {
      if (item.category == newValue.label) {
        return item;
      }
    }).map(item => item.field);

    list.forEach((item) => {
      if (!ruleForm.value.extendFields.includes(item)) {
        ruleForm.value.extendFields.push(item);
      }
    });
  }
}

const paramUseKey = ref<any>({});

const personData = ref<any[]>([]);
// 字段类型
const fieldCategoryValue = ref<string>("");
// 字段类型options
const fieldCategoryOptions = ref<any[]>([]);
// 场景策略列表
const sceneList = ref<any[]>([]);

const getSceneList = async () => {
  const res = await commonApi.getSceneVersion({});
  sceneList.value = res.data;
};

getSceneList();


//获取流程入参
const getProcessInputArgs = async (processId: string, index: number, processParams?: Record<string, any>) => {
  try {
    const result = await commonApi.getProcessInputArgs(processId);
    if (!result?.inputArgs) {
      $app.$message.warning("未获取到流程参数，接口返回格式异常");
      return [];
    }
    const resp = result.inputArgs.filter((item: { key: string }) => item.key !== "query");
    ruleForm.value.strategyConfig[index].inputArgs = resp;

    // 如果有processParams，则回显值到inputArgs中
    if (processParams && Object.keys(processParams).length > 0) {
      ruleForm.value.strategyConfig[index].inputArgs = fillBack(processParams, resp);
    }

    // 初始化processParams对象
    if (!ruleForm.value.strategyConfig[index].processParams) {
      ruleForm.value.strategyConfig[index].processParams = {};
    }
    paramUseKey.value[index] = Math.random();
  } catch (error) {
    console.error("Failed to get input args:", error);
    $app.$message.warning("获取流程参数失败");
    return [];
  }
  console.log("获取流程入参");
};

// 策略配置表单值
const strategyformItems = ref<any>([]);
const importOfflineModalRef = ref<any>(null);
const areaList = ref([]);
const ascribeList = ref<any>({
  0: [],
  1: [],
});

//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增测评任务";
  if (formType.value == "edit") return "编辑测评任务";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});

//是否为发布状态
const isReleased = computed(() => {
  return recordData.value && recordData.value.status == 2;
})

// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  catalogCode: "",
  name: "",
  type: 1,
  description: "",
  queryGroupId: "",
  groupUserAssignments: { '分组1': [] },
  standardId: '',
  extendFields: [],
  strategyConfig: [],
};
let ruleForm = ref<any>(assign({}, defaultForm));
const recordData = ref<any>(null);
const moduleData = ref<any>({ catalogTree: null });
const validatePass = (rule: any, value: any, callback: any) => {
  callback();
};


const addgroupUserAssignments = (key: number) => {
  ruleForm.value.groupUserAssignments[`分组${key + 1}`] = []
}
const handlePersonData = (key: string) => {
  const currentPerson = ruleForm.value.groupUserAssignments[key]
  const otherPerson = Object.keys(ruleForm.value.groupUserAssignments).filter((item: any) => item !== key).flatMap((item: any) => ruleForm.value.groupUserAssignments[item])

  return personData.value.map((item: any) => ({
    key: item.key,
    label: item.label,
    disabled: otherPerson.includes(item.key)
  }))
}
const deletegroupUserAssignments = (key: string) => {
  delete ruleForm.value.groupUserAssignments[key];
}

// 获取树的所有节点的code name对象列表
const getAllTreeNodes = (tree: any): Array<{ code: string, name: string }> => {
  if (!tree) return [];

  const result: Array<{ code: string, name: string }> = [];

  const traverse = (node: any) => {
    if (node.code && node.name) {
      result.push({
        code: node.code,
        name: node.name
      });
    }

    if (node.children && Array.isArray(node.children)) {
      node.children.forEach(traverse);
    }
  };

  traverse(tree);
  return result;
};

// 获取树结构数据
const getCatalogTree = async () => {
  try {
    const result = await markModuleApi.find("CPRW");
    moduleData.value = result;
  } catch (error) {
    console.error('获取目录树失败:', error);
  }
};

// 获取所有的字段列表
const getFieldsList = async () => {
  try {
    const result = await evalSettingApi.list();
    fieldList.value = result.data;
    fieldList.value.map(item => {
      item.label = item.name;
      item.value = item.field;
    })
  } catch (error) {
    console.error('获取字段列表失败:', error);
  }
}
getFieldsList();

// 获取字段类型列表
const getFieldCategoryList = async () => {
  try {
    const result = await evalSettingApi.findAllCategories();
    console.log("getFieldCategoryList:", result);

    if (dataC.isEmpty(result.data)) {
      fieldCategoryOptions.value = [];
      return;
    }

    fieldCategoryOptions.value = result.data.map((category: String) => {
      return {
        label: category,
        value: category
      }
    });
  } catch (error) {
    console.error('获取字段类型列表:', error);
  }
}
getFieldCategoryList();


function clearSelectedField() {
  ruleForm.value.extendFields = [];
}

// 增加策略配置
const handleAddStr = (offline: boolean) => {
  const obj: any = {
    offline,
    processParams: {}, // 初始化流程参数对象
    inputArgs: [] // 初始化输入参数数组
  };
  if (offline) {
    obj.ascribeMode = 0;
  }
  if (ruleForm.value.strategyConfig) {
    ruleForm.value.strategyConfig.push(obj);
  } else {
    ruleForm.value.strategyConfig = [obj];
  }
};

// 删除策略配置
const handleDeleteStr = (index: number) => {
  ruleForm.value.strategyConfig.splice(index, 1);
  strategyformItems.value.splice(index, 1);
};

// 显示离线数据导入弹窗
const offlineImportShow = (index: number) => {
  importOfflineModalRef.value.openDialog(index);
};

// 处理离线数据导入
const handleImport = (data: any, index: any) => {
  ruleForm.value.strategyConfig[index].queryGroupId = data;
};



// 不再需要getAnasisList函数，直接使用sceneList

const getAscribeList = async (type: any, name = "") => {
  const res = await evaluationManageApi.getStrategyVersion(type, { name });
  return res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};

const getAscribeListGood = async (name = "") => {
  const res = await evaluationManageApi.getStrategyVersion(0, { name: "" });
  ascribeList.value[0] = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};

const getAscribeListBad = async (name = "") => {
  const res = await evaluationManageApi.getStrategyVersion(1, { name: "" });
  ascribeList.value[1] = res.data.map((item: any) => ({
    ...item,
    label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
    value: item.processId,
  }));
};

const getAreaList = async () => {
  const res: any = await common.getAreaList();
  areaList.value = res.map((item: any) => ({
    ...item,
    label: item.name,
    value: item.code,
  }));
};


// 生成策略表单项
const generateFormItems = (index: number) => {
  return {
    compName: {
      label: "竞品名称",
      type: "input",
      attrs: {
        disabled: isReleased,
      },
      hidden: () => {
        return !ruleForm.value.strategyConfig[index].offline;
      },
    },
    regionCode: {
      label: "运行环境",
      type: "select",
      options: areaList,
      attrs: {
        maxlength: 255,
        disabled: isReleased,
        placeholder: "请选择运行环境",
      },
    },
    processId: {
      label: "场景策略版本",
      type: "select",
      options: computed(() => {
        // 使用sceneList进行回显
        return sceneList.value.map((item: any) => ({
          ...item,
          label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
          value: item.processId,
        }));
      }),
      attrs: {
        maxlength: 255,
        disabled: isReleased,
        placeholder: "请选择场景策略版本",
        filterable: true,
      },
      hidden: () => {
        return ruleForm.value.strategyConfig[index].offline;
      },
      events: {
        change: (val: any) => {
          if (val) {
            // 当选择场景策略版本时，获取流程入参
            console.log("processId change", val, index);
            // 保存当前的processParams，以便在获取新的inputArgs后合并
            const currentParams = ruleForm.value.strategyConfig[index].processParams || {};
            getProcessInputArgs(val, index, currentParams);
          } else {
            // 清空流程入参
            ruleForm.value.strategyConfig[index].processParams = {};
            ruleForm.value.strategyConfig[index].inputArgs = [];
          }
        },
      },
    },
    processParams: {
      label: "场景策略参数",
      type: "slot",
      slotName: "strategyConfig-processParams",
      attrs: {
        maxlength: 255,
        placeholder: "请输入场景策略参数",
        disabled: isReleased,
      },
      hidden: () => {
        return dataC.isEmpty(ruleForm.value.strategyConfig[index].processId);
      },
    },
    ascribeMode: {
      label: "归因模式",
      type: "select",
      options: [
        { value: 0, label: "竞品good" },
        { value: 1, label: "自研bad" },
      ],
      events: {},
      attrs: {
        maxlength: 255,
        placeholder: "请输入归因模式",
        disabled: isReleased,
      },
    },
    ascribeProcessId: {
      label: "归因策略",
      type: "select",
      options: ascribeList.value[ruleForm.value.strategyConfig[index].ascribeMode],
      attrs: {
        maxlength: 255,
        placeholder: "请输入归因策略",
        disabled: isReleased,
        remote: true,
        remoteMethod: function (search: string) {
          if (this.remote) {
            getAscribeList(ruleForm.value.strategyConfig[index].ascribeMode, search).then((res) => {
              strategyformItems.value[index].ascribeProcessId.options = res;
            });
          }
        },
      },
      hidden: (val: any) => {
        return val.ascribeMode != 0 && val.ascribeMode != 1;
      },
    },
    id4Ascribe: {
      label: "竞对场景策略版本",
      type: "select",
      options: computed(() => {
        // 使用sceneList进行回显
        return sceneList.value.map((item: any) => ({
          ...item,
          label: `${item.name}(v${util.padNumberToDigits(item.version, 3)})`,
          value: item.processId,
        }));
      }),
      attrs: {
        maxlength: 255,
        placeholder: "请选择竞对场景策略版本",
        disabled: isReleased,
        filterable: true,
      },
      hidden: (val: any) => {
        return val.ascribeMode == 1 || (val.ascribeMode != 0 && val.ascribeMode != 1);
      },
    },
    queryGroupId: {
      label: "query集离线数据",
      type: "slot",
      slotName: "queryGroupId",
      hidden: () => {
        return !ruleForm.value.strategyConfig[index].offline;
      },
    },
  };
};

// 表单项
const formItems = ref<any>({
  catalogCode: {
    label: "所属目录",
    type: "select",
    disabled: () => true,
    options: computed(() => {
      const allNodes = getAllTreeNodes(moduleData.value.catalogTree);
      return allNodes.map(node => ({
        value: node.code,
        label: node.name
      }));
    }),
    attrs: {
      placeholder: "请选择所属目录",
    },
  },
  name: {
    label: "任务名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入任务名称",
    },
  },
  description: {
    label: "任务描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      placeholder: "请输入任务描述",
    },
  },
  type: {
    label: "测评方式",
    type: "radio",
    options: props.missiontypeEnum,
    attrs: {
      allowCreate: true,
    },
    disabled: () => isReleased,
    events: {
      change: () => {
        ruleForm.value.categoryId = ''
      }
    }
  },
  groupUserAssignments: {
    label: "参与人员",
    type: "slot",
    slotName: "groupUserAssignments",
  },
  queryGroupId: {
    label: "测评数据",
    type: 'select',
    options: computed(() => props.publishList),
    attrs: {
      placeholder: "请选择测评配置",
    },
    disabled: () => recordData.value && recordData.value.status == 2,
    hidden: () => {
      return ruleForm.value.type == 2
    }
  },
  time: {
    type: "datetimerange",
    label: "活动时间安排",
    modelValue: [],
    disabled: () => {
      return recordData.value && recordData.value.status == 2
    },
    attrs: {
      placeholder: "请输入备注",
      disabledDate: (time: any) => {
        return time.getTime() < Date.now() - 60 * 60 * 24 * 1000
      }
    },
  },
  standardId: {
    label: "测评标准",
    type: 'select',
    required: true,
    disabled: () => recordData.value && recordData.value.status == 2,
    options: computed(() => props.standardList),
    attrs: {
      placeholder: "请选择测评配置",
    },
  },
  extendFields: {
    label: "测评字段配置",
    type: 'slot',
    slotName: "extendFieldsRef",
    attrs: {
    },
  },
  strategyConfig: {
    label: "测评内容配置",
    type: 'slot',
    slotName: "strategyConfigRef",
    attrs: {
    },
  },
});

const rules = reactive<FormRules>({
  name: [
    { required: true, message: "任务名称不能为空", trigger: "blur" },
    { min: 1, max: 100, message: "任务名称长度在 1 到 100 个字符", trigger: "blur" },
  ],
  type: [{ required: true, message: "测评方式不能为空", trigger: "change" }],
  queryGroupId: [{ required: true, message: "测评配置不能为空", trigger: "change" }],
  groupUserAssignments: [{ required: true, message: "参与人员不能为空", trigger: "change" }],
  time: [{ required: true, message: "活动时间安排不能为空", trigger: "change" }],
  standardId: [{ required: true, message: "测评标准不能为空", trigger: "change" }],
  extendFields: [{ required: false, message: "测评字段配置不能为空", trigger: "change" }],
  // 策略配置相关验证规则
  strategyConfig: [{ required: true, validator: validatePass, trigger: "change" }],
  regionCode: [{ required: true, message: "运行环境不能为空", trigger: "change" }],
  processId: [{ required: true, message: "场景策略版本不能为空", trigger: "change" }],
  ascribeMode: [{ required: true, message: "归因模式不能为空", trigger: "change" }],
  ascribeProcessId: [{ required: true, message: "归因策略不能为空", trigger: "change" }],
  id4Ascribe: [{ required: true, message: "竞对场景策略版本不能为空", trigger: "change" }],
});

//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      recordData.value = row;
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
      ruleForm.value.groupUserAssignments = row.groupUserAssignments || {}
      ruleForm.value.time = [timeC.format(row.beginTime, "YYYY-MM-DD hh:mm:ss"), timeC.format(row.endTime, "YYYY-MM-DD hh:mm:ss")]

      // 调试信息：检查standardId回显
      console.log("编辑数据回显:", {
        rowStandardId: row.standardId,
        formStandardId: ruleForm.value.standardId,
        standardList: props.standardList
      });
      // 确保策略配置数据包含必要字段
      if (ruleForm.value.strategyConfig && ruleForm.value.strategyConfig.length > 0) {
        ruleForm.value.strategyConfig.forEach(async (item: any, index: number) => {
          if (!item.processParams) {
            item.processParams = {};
          }
          if (!item.inputArgs) {
            item.inputArgs = [];
          }
          // 如果有processId，获取流程入参并回显processParams
          if (item.processId) {
            await getProcessInputArgs(item.processId, index, item.processParams);
          }
          item.ascribeMode += 1;

        });
      }
    } else {
      recordData.value = null;
      ruleForm.value = assign({}, cloneDeep(defaultForm), row);
      // 新建时设置当前选中的目录
      if (props.treeNode) {
        ruleForm.value.catalogCode = props.treeNode;
      }
    }
  });
};


function fillBack(from: Record<string, any>, to: any[]): any[] {
  if (!from || !to || to.length === 0) {
    return to;
  }

  // 创建一个新的数组，避免直接修改原数组
  const result = to.map(item => ({
    ...item,
    value: from[item.key] !== undefined ? from[item.key] : item.value
  }));

  console.log("processParams回显", { from, to, result });

  return result;
}


const buildPayload = (inputArgs: Array<{ key: string; value: any }>) => {
  if (!inputArgs || !Array.isArray(inputArgs)) {
    return {};
  }

  const payload: Record<string, any> = {};
  inputArgs.forEach((item) => {
    if (item && item.key && item.value !== undefined) {
      payload[item.key] = item.value;
    }
  });

  console.log("构建参数payload:", payload);
  return payload;
};

//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);

  const params = {
    ...form,
    beginTime: form.time[0],
    endTime: form.time[1],

    dataAssign: form.groupUserAssignments

  }
  //离线测评 queryGroupId从场景策略中获取
  if (form.type == 2) {

    let offlineStrategyConfig = form.strategyConfig.find(
      (item: any) => item.offline
    );

    if (dataC.isEmpty(offlineStrategyConfig)) {
      $app.$message.warning("没有离线策略配置");
      return;
    }
    params.queryGroupId = offlineStrategyConfig.queryGroupId
  }

  if (dataC.isEmpty(params.queryGroupId)) {
    $app.$message.warning("没有query集信息");
    return;
  }

  // 确保每个策略配置都有正确的processParams
  if (params.strategyConfig && Array.isArray(params.strategyConfig)) {
    params.strategyConfig.forEach((item: any) => {
      // 如果inputArgs存在，从中构建processParams
      if (item.inputArgs && Array.isArray(item.inputArgs)) {
        item.processParams = buildPayload(item.inputArgs);
      } else if (!item.processParams) {
        // 如果没有inputArgs也没有processParams，初始化为空对象
        item.processParams = {};
      }
      // 删除inputArgs，不需要提交到后端
      delete item.inputArgs;
    });
  }

  for (let key in form.groupUserAssignments) {
    if (form.groupUserAssignments[key].length == 0) {
      $app.$message.warning("请选择参与人员");
      return;
    }
  }

  console.log("params:", params);

  if (isUpdate.value) {
    evaluationApi.updateMission(form.id, params).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    evaluationApi.createMission(params).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
const getPersons = () => {
  evaluationApi.queryLabelers().then((result) => {
    personData.value = result.data.map((item: any) => ({
      key: item.account,
      label: `${item.account}（${item.name}）`,
      disabled: item.disabled,
    }));
  });
}
// 初始化表单配置
watch(
  () => ruleForm.value.strategyConfig,
  (newVal: any) => {
    strategyformItems.value = (newVal || []).map((_: any, index: number) => generateFormItems(index));
  },
  { immediate: true, deep: true }
);

function updateAscribeMode(){
  console.log("updateAscribeMode",ruleForm.value.strategyConfig);
}

//初始化
onMounted(async () => {
  getPersons();
  getCatalogTree();
  getAreaList();
  getAscribeListGood();
  getAscribeListBad();
  updateAscribeMode();
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss" scoped>
::v-deep(transfer__buttons) {
  padding: 0;
}

.button-add {
  height: 15px;

  ::v-deep {
    .el-button {
      float: right;
    }
  }
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

::v-deep {
  .el-row {
    width: 100%;
  }

  .el-form-item__content {
    .strategy-config-container {
      width: 100%;

      .strategy-config-item {
        .el-form-item {
          margin-bottom: 18px;
        }
      }
    }
  }
}
</style>
