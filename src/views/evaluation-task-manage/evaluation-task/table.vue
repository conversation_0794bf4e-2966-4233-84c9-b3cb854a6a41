<template>
  <div class="evaluation-task-table">
    <description-edit
      ref="descriptionEditRef"
      @save-data="events.modifyDescription"
    ></description-edit>
    <table-page
      ref="myTableRef"
      name="evaluation-task-table"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/evaluation-task/edit"
      :operations="operations"
      @operation="handleOperation"
      :withSort="false"
    >
      <template #description="scope">
        <el-button
          link
          type="primary"
          @click="events.openDescriptionEditWindow(scope.row)"
          :disabled="!testAuth() || scope.row.status == 3"
        >
          <el-icon>
            <Edit />
          </el-icon>
        </el-button>
        {{ scope.row.description }}
      </template>
      <template #ascribeProcess="scope">
        <el-popover placement="right" :width="480" :disabled="scope.row.ascribeStatus == 0" trigger="hover" @show="updateAscribeProgress(scope.row.id)">
          <el-row :gutter="10">
            <el-col :span="12">
              <span>全部数量：{{ scope.row.ascribeTotal }}</span>
            </el-col>
            <el-col :span="12">
              <span>成功数量：{{ scope.row.ascribeSuccess }}</span>
            </el-col>
            <el-col :span="12">
              <span>失败数量：{{ scope.row.ascribeFail }}</span>
            </el-col>
            <el-col :span="12">
              <span>剩余数量：{{ scope.row.ascribeTotal - (scope.row.ascribeSuccess || 0) - (scope.row.ascribeFail || 0) }}</span>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <span>开始时间：{{ scope.row.ascribeStartTime }}</span>
            </el-col>
            <el-col :span="12">
              <span>结束时间：{{ scope.row.ascribeEndTime }}</span>
            </el-col>
          </el-row>
          <el-progress
            :text-inside="true"
            :stroke-width="16"
            :percentage="getAscribePercent(scope.row)"
            :status="ascribeStatusEnum.find((item1) => item1.value == scope.row?.ascribeStatus)?.type as any"
          />
          <template #reference>
            <status-dot
              :type="ascribeStatusEnum.find((item1) => item1.value == scope.row?.ascribeStatus)?.type"
              :name="ascribeStatusEnum.find((item1) => item1.value == scope.row?.ascribeStatus)?.label"
            />
          </template>
        </el-popover>
      </template>
      <template #query>
        <div class="flexBetweenStart">
          <my-query
            :queryItems="queryItems"
            :refreshBtn="{ show: true }"
            @search="events.search"
            @reset="events.reset"
          />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" :disabled="!testAuth()"
                >创建测评任务</my-button
              >
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from "vue";
import { keys, assign } from "lodash";
import useStore from "@/store";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as evaluationApi from "@/api/eval-task";
import * as markModuleApi from "@/api/eval-mark-module";
import IntervalClient from "@/utils/interval-client";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";
const { $app, proxy, $auth, $router  } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/evaluation-task/edit");
};

const treeNode = ref("");

const { api, common } = useStore();
const props = defineProps({
  groupEnum: { type: Array, default: [] },
  missiontypeEnum: { type: Array, default: [] },
  standardList: { type: Array, default: [] },
});
const statusEnum = [
  { value: 1, label: "草稿", type: "info" },
  { value: 2, label: "已发布", type: "success" },
  { value: 3, label: "已归档", type: "warning" },
];

const ascribeStatusEnum = [
  { value: 0, label: "待归因", type: "info" },
  { value: 1, label: "正在归因", type: "warning" },
  { value: 2, label: "已完成", type: "success" },
  { value: 3, label: "已完成", type: "exception" },
];

const divideAndKeepTwoDecimals = (numerator: number, denominator: number) => {
  const result = numerator / denominator;
  return Math.floor(result * 10000) / 10000;
};

//获取自动归因百分比
const getAscribePercent = (item: any) => {
  if (dataC.isEmpty(item)) {
    //item都为null返回0
    return 0;
  } else if ((item.ascribeSuccess == 0 && item.ascribeTotal == 0) || item.ascribeSuccess >= item.ascribeTotal) {
    //已结束状态下, 已处理和总量都为0 或者已处理大于等于总量
    return 100;
  } else {
    //total刚开始会没有值，直接给一亿
    const done = Number(!dataC.isEmpty(item.ascribeSuccess) ? item.ascribeSuccess + (item?.ascribeFail || 0) : 0);
    const total = Number(dataC.isEmpty(item.ascribeTotal) || item.ascribeTotal == 0 ? 100_000_000 : item.ascribeTotal);
    const res = divideAndKeepTwoDecimals(done, total);
    return Number((res * 100).toFixed(2));
  }
};

const standardOptions = computed(() => {
  return props.standardList.map((item: any) => ({
    ...item,
    label: item.name,
    value: item.id,
  }));
});

//列配置
const columns = ref([
  {
    prop: "name",
    label: "任务名称",
    width: 180,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.statistic(record, "plan");
      },
    },
  },
  {
    prop: "id",
    label: "任务ID",
    width: 110,
  },
  {
    prop: "description",
    label: "任务描述",
    width: 180,
    slotName: "description",
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 110,
    custom: "status",
    customRender: {
      options: statusEnum,
    },
  },
  {
    prop: "enabled",
    label: "是否启用",
    minWidth: 110,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      disabled: (record: any) => record.status == 3,
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          // 禁用需要确认提示，启用直接执行
          $app
            .$confirm({
              title: `确定${record.enabled ? "停用" : "启用"}测试任务“${
                record.name
              }”吗？`,
            })
            .then(() => {
              evaluationApi
                .enableMission(record.id, !record.enabled)
                .then((res) => {
                  loadList(treeNode.value);
                  $app.$message.success(
                    record.enabled ? "测试任务停用成功" : "测试任务启用成功"
                  );
                  resolve(true);
                });
            })
            .catch(() => {
              reject();
            });
        });
      },
    },
  },
  {
    prop: "standardIdRender",
    label: "测评标准",
    minWidth: 200,
    customRender: {
      options: standardOptions,
    },
  },
  {
    prop: "queryCount",
    label: "query条数",
    width: 90,
  },
  {
    prop: "markProcess",
    label: "标注进度",
    width: 90,
  },
  {
    prop: "ascribeProcess",
    label: "自动归因进度",
    width: 110,
    slotName: "ascribeProcess",
    showOverflowTooltip: false,
    sortable: false,
  },
  {
    prop: "userCount",
    label: "参与人数",
    width: 90,
  },
  {
    prop: "type",
    label: "测评方式",
    minWidth: 110,
    custom: "tagStatus",
    customRender: {
      options: props.missiontypeEnum,
    },
  },
  { prop: "timeRender", label: "活动时间", minWidth: 230 },
  { prop: "catalogCodeRender", label: "所属目录", minWidth: 200 },

  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 170 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 170 },
  { prop: "operation", label: "操作", width: 320, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  search: "",
});
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    defaultValue: "",
    attrs: {
      placeholder: "名称",
    },
  },
});

const allTreeNode = ref({});

// 获取树结构数据
const getCatalogTree = async () => {
  try {
    const result = await markModuleApi.find("CPRW");
    allTreeNode.value = getAllTreeNodes(result.catalogTree);
  } catch (error) {
    console.error('获取目录树失败:', error);
  }
};

// 获取树的所有节点的code name对象列表
const getAllTreeNodes = (tree: any): Array<{ code: string, name: string }> => {
  if (!tree) return [];

  const result: Array<{ code: string, name: string }> = [];

  const traverse = (node: any) => {
    if (node.code && node.name) {
      result.push({
        code: node.code,
        name: node.name
      });
    }

    if (node.children && Array.isArray(node.children)) {
      node.children.forEach(traverse);
    }
  };

  traverse(tree);
  return result;
};

//定时任务监控
const intervalClinet = ref<any>(null);
const updateAscribeProgress = (id: string) => {
  evaluationApi.ascribeProcess(id).then((res) => {

    if(!res.data) return;
    let data = res.data;

    let tableData = proxy.$refs["myTableRef"].getTableData();
    const list = tableData.map((x: any) => {
      if (x.id == id) {
        x.ascribeStatus = data.status;
        x.ascribeSuccess = data.success;
        x.ascribeFail = data.fail;
        x.ascribeStartTime = data.startTime ? timeC.format(data.startTime, "YYYY-MM-DD hh:mm:ss") : '';
        x.ascribeEndTime = data.endTime ? timeC.format(data.endTime, "YYYY-MM-DD hh:mm:ss") : '';
        x.ascribeTotal = data.total;
      }
      return x;
    });
    proxy.$refs["myTableRef"].setTableData(list);
  }).catch((error) => {
    console.error('获取归因进度失败:', error);
  });
};

const getIntervalClinet = (data: any) => {
  let tableData = data || proxy.$refs["myTableRef"].getTableData();
  //如果已有定时任务对象，则停止并创建新的
  intervalClinet.value?.disconnect();
  //如果列表为空 则不创建新的
  if (dataC.isEmpty(tableData)) return;
  // 获取定时任务对象并启动,以持续刷新任务信息
  intervalClinet.value = new IntervalClient(3000, true);
  intervalClinet.value.onHandler(getTaskProgressListByTask, tableData).connect();
};

const getTaskProgressListByTask = (tableData: Array<any>) => {
  const taskIdList = tableData.filter((x) => {
    return !dataC.isEmpty(x.id) && x.ascribeStatus == 1; // 正在归因状态
  });
  if (dataC.isEmpty(taskIdList)) return;
  taskIdList.map((item) => {
    updateAscribeProgress(item.id);
  });
};

//列表查询
const loadListData = async (data: any) => {

  if(dataC.isEmpty(treeNode.value)){
    treeNode.value = $router.currentRoute.value.query.catalog as string || "";
  }

  const params = {
    ...data,
    catalogCode: treeNode.value,
  };

  console.log("params:", params);

  return new Promise((resolve: any) => {
    evaluationApi
      .getMissionPage({ ...params, sort: data.sort || "createdDate,desc" })
      .then((result) => {
        result.content = result.content.map((item) => ({
          ...item,
        }));
        //返回数据
        resolve(result);
      });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = timeC.format(
      x.lastModifiedDate,
      "YYYY-MM-DD hh:mm:ss"
    );
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.typeRender = computed(() => {
      return props.missiontypeEnum.find((item) => item.value == x.type)?.label;
    });
    x.statusRender = statusEnum.find((item) => item.value == x.status)?.label;
    x.timeRender =
      timeC.format(x.beginTime, "YYYY-MM-DD hh:mm:ss") +
      " ~ " +
      timeC.format(x.endTime, "YYYY-MM-DD hh:mm:ss");

    // 计算标注进度：completeQueryCount / assignQueryCount * 100%
    if (x.assignQueryCount && x.assignQueryCount > 0) {
      const percentage = Math.round((x.completeQueryCount || 0) / x.assignQueryCount * 100);
      x.markProcess = percentage + '%';
    } else {
      x.markProcess = '0%';
    }

    // 初始化归因相关字段（如果后端没有返回）
    if (!x.ascribeStatus && x.ascribeStatus !== 0) x.ascribeStatus = 0; // 默认待归因
    if (!x.ascribeSuccess) x.ascribeSuccess = 0;
    if (!x.ascribeFail) x.ascribeFail = 0;
    if (!x.ascribeTotal) x.ascribeTotal = 0;

    x.catalogCodeRender = allTreeNode.value.find((item)=>item.code == x.catalogCode)?.name;

    x.standardIdRender = props.standardList.find(item => item.id == x.standardId)?.name;

    return x;
  });
};
//操作
const operations = [
  {
    type: "edit",
    label: "编辑",
    disabled: (record: any) => record.enabled || record.status === 3,
    disabledTips: (record: any) => {
      if (record.enabled) {
        return "只有禁用后才能操作编辑";
      }
      if (record.status === 3) {
        return "当前版本已经归档，不可再编辑";
      }
    },
  },
  {
    type: "delete",
    label: "删除",
    btnType: "danger",
    disabled: (record: any) => record.enabled,
    disabledTips: (record: any) => {
      if (record.enabled) {
        return "只有禁用后才能操作删除";
      }
    },
  },
  {
    type: "publish",
    label: "发布",
    disabled: (record: any) => record.status === 2 || record.status === 3,
    disabledTips: (record: any) => {
      if (record.status === 2) {
        return "当前版本已经发布，不可再发布";
      }
      if (record.status === 3) {
        return "当前版本已经归档，不可再发布";
      }
    },
  },
  {
    type: "archive",
    label: "归档",
    disabled: (record: any) =>
      record.enabled || record.status == 3 || record.status === 1,
    disabledTips: (record) => {
      if (record.enabled) {
        return "只有禁用后才能操作归档";
      }
      if (record.status == 1) {
        return "当前版本为草稿，不可归档";
      }
      if (record.status == 3) {
        return "当前版本已经归档，不可再归档";
      }
    },
  },
  {
    type: "copy",
    label: "复制",
  },
  {
    type: "ascribe",
    label: "自动归因",
    disabled: (record: any) =>
      !record.enabled || !record.markFinished,
    disabledTips: (record) => {
      if (!record.enabled) {
        return "启用状态才可自动归因";
      }
      if (!record.markFinished) {
        return "标注未完成，不可自动归因";
      }
      return "";
    },
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(data: any, record: any) {
    evaluationApi
      .updateMission(record.id, {
        ...record,
        description: data.description,
        name: data.name,
      })
      .then((result) => {
        $app.$message.success("修改成功");
        proxy.$refs["descriptionEditRef"].closeWindow();
        loadList(treeNode.value);
      });
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  statistic: (record: any, mode: String) => {
    emit("statistic-data", record, mode);
  },
  result: (record: any, mode: String) => {
    emit("statistic-data", record);
  },
  add: () => {
    emit("edit-data-inst", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data-inst", "edit", record);
  },
  publish: (record: any) => {
    $app.$confirm({ title: `确定发布吗` }).then(() => {
      evaluationApi.publishMission(record.id).then((res) => {
        $app.$message.success(`发布${record.name}成功`);
        loadList(treeNode.value);
      });
    });
  },
  copy: (record: any) => {
    $app.$confirm({ title: `确定复制吗` }).then(() => {
      evaluationApi.copyMission(record.id).then((res) => {
        $app.$message.success(`复制${record.name}成功`);
        loadList(treeNode.value);
      });
    });
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        evaluationApi.deleteMission(record.id).then((result) => {
          loadList(treeNode.value);
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
  archive: (record: any) => {
    $app.$confirm({ title: `确定归档吗` }).then(() => {
      evaluationApi.archiveMission(record.id).then((result) => {
        loadList(treeNode.value);
        $app.$message.success(`归档 ${record.name} 成功`);
      });
    });
  },
  ascribe: (record: any) => {
    $app.$confirm({ title: `确定开始自动归因吗？` }).then(() => {
      evaluationApi.ascribeMission(record.id).then((result) => {
        $app.$message.success(`开始自动归因 ${record.name}`);
        updateAscribeProgress(record.id);
        // 启动定时刷新
        getIntervalClinet(null);
      });
    });
  },
});
const modelValue = reactive({
  metaRegionList: [],
});
const loadList = (currentNode: string) => {

  treeNode.value = currentNode;

  proxy.$refs.myTableRef.loadData();
  // 延迟启动定时任务，确保数据加载完成
  nextTick(() => {
    getIntervalClinet(null);
  });
};
//初始化
onMounted(() => {
  getCatalogTree();
});
//销毁
onUnmounted(() => {
  intervalClinet.value?.disconnect();
});
//事件声明
const emit = defineEmits(["edit-data-inst", "statistic-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.evaluation-task-table {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
