<template>
  <page-wrapper route-name="query-set::details::">
    <div class="query-set-container">
      <el-card
        class="info-card"
        :style="{ height: activeCollapse == 1 ? '170px' : '60px' }"
      >
        <el-collapse
          v-model="activeCollapse"
          @change="events.collapseChange"
          class="collapse"
        >
          <el-collapse-item :name="1">
            <template #title>
              <span style="font-size: 16px; font-weight: 700">基本信息</span>
            </template>
            <el-descriptions column="3">
              <el-descriptions-item
                label="query集名称: "
                label-class-name="bold"
                >{{ `${routeQuery.name}` }}</el-descriptions-item
              >
              <el-descriptions-item label="分组 : " label-class-name="bold">{{
                routeQuery.label
              }}</el-descriptions-item>
              <el-descriptions-item
                label="query条数 : "
                label-class-name="bold"
                >{{ routeQuery.size }}</el-descriptions-item
              >
              <el-descriptions-item
                :span="3"
                label="query集描述 : "
                label-class-name="bold"
                >{{ routeQuery.description||"无" }}</el-descriptions-item
              >
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <el-card
        class="table-card"
        :style="{
          height:
            activeCollapse == 1 ? 'calc(100vh - 320px)' : 'calc(100vh - 210px)' }"
        
      >

      <div class="el-descriptions">
          <div class="el-descriptions__header">
            <div class="el-descriptions__title">
              <span>{{ "结果详情" }}</span>
              &nbsp;
              <el-button link type="primary" @click="events.refreshSite">
                <el-icon size="18">
                  <Refresh />
                </el-icon>
              </el-button>
            </div>
            <my-button type="export" @click="events.exportExcel" style="margin-left: 20px">导出</my-button>
          </div>
        </div>
        <div class="query-set-table">
          <table-page
            ref="myTableRef"
            :loadDataApi="loadListData"
            :columns="columns"
            :transformListData="transformListData"
            :defaultPageSizes="[100,200]"
            :withSort="false"
          >
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as querySetApi from "@/api/eval-query-set";
import * as util from "@/utils/common";
import { computed } from "vue";
import {getTextLength} from '@/utils/common.ts'
import TextButton from "@/components/button/TextButton.vue";
const { $router, proxy, $app } = useCtx();
const { api } = useStore();
const props = defineProps(["transformListData"]);
import * as commonApi from "@/api/common";
const routeQuery = $router.currentRoute.value.query;
let metaLabel = routeQuery.metaLabel;
if (!(metaLabel instanceof Array)) {
  metaLabel = [metaLabel];
}
let typeActive = ref("1");
const tableData = ref([]);
const baseInfo = ref({});
const routeName = "query-set";
//事件声明
const emit = defineEmits(["preview-data"]);
const activeCollapse = ref([1]);
const withOrder = ref<Boolean>(false);
const columns = ref<any[]>([{
  label: "query",
  prop: "query",
  minWidth: 180,
},
]);

//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    querySetApi.getQueryGroupDetail(routeQuery.querySetId,{...data,sort:'idx,asc'}).then((result) => {
      //返回数据
      resolve(result);
    });
  });
};

//事件列表
const events = reactive({
  collapseChange: (val: string[]) => {
    window.dispatchEvent(new Event("resize"));
  },
  refreshSite: () => {
    loadList();
  },
  exportExcel: () => {
    querySetApi
        .exportQueryGroup(routeQuery.querySetId)
        .then((res) =>
          util.downloadFile(
            res,
            `${routeQuery.name}结果详情.xlsx`
          )
        );
  },
});
const loadList = () => {
  proxy.$refs["myTableRef"]?.loadData();
};
//接口暴露
defineExpose({
  loadList,
  loadListData,
});
</script>
<style lang="scss">
.query-set-container {
  padding: 10px;

  .info-card {
    .bold {
      font-weight: bold;
    }

    .collapse {
      border: none;
      .el-collapse-item__header {
        border: none;
        height: 23px;
        margin-bottom: 12px;
      }

      .el-collapse-item__wrap {
        border: none;
      }
    }

    .el-card__body {
      height: 100%;
      .el-collapse {
        height: 100%;
        .el-collapse-item {
          height: 100%;
          .el-collapse-item__wrap {
            height: 100%;
            .el-collapse-item__content {
              height: 100%;
              .el-descriptions {
                height: 100%;
                .el-descriptions__body {
                  height: 100%;
                  overflow-y: auto;
                }
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    margin-top: 10px;

    .el-card__body {
      height: 100%;
    }
  }
  .query-set-table {
    height: calc(100% - 40px);
  }

}
</style>
<style lang="scss" scoped>
  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0 !important;
  }
</style>