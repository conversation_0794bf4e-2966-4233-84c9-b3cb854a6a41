<template>
  <my-drawer class="query-set-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm"
    @close="handleClose" size="800">
    <my-form labelWidth="130px" ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"
      @submit="submit">
      <template #file>
        <my-upload ref="uploadRef" maxSize="10M" accept=".xls,.xlsx" v-model="ruleForm.file" drag drag-style
          :showDownload="true" @download="events.download">
          <my-button type="primary" plain>上传文件</my-button>
        </my-upload>
      </template>
    </my-form>
  </my-drawer>
</template>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as querySetApi from "@/api/eval-query-set";
import * as util from "@/utils/common";

const { $app, proxy } = useCtx();
const { common } = useStore();
const props = defineProps({
  groupEnum: { type: Array, default: [] },
});
//弹窗相关
const dialogTitle = computed(() => {
  if (formType.value == "add") return "新增query集";
  if (formType.value == "edit") return "编辑query集";
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  label: "",
  description: "",
  status: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = computed<FormRules>(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  label: [{ required: true, message: "分组不能为空", trigger: "change" }],
  file: [{ required: !isUpdate.value, message: "请上传文件", trigger: "blur" }],
}));
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入计划名称",
    },
  },
  label: {
    label: "分组",
    type: "select",
    options: computed(() => props.groupEnum),
    attrs: {
      allowCreate: true,
    
    },
    disabled: (record: any) => record.status == 2,
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      maxlength: 255,
      placeholder: "请输入描述",
     
    },
    disabled: (record: any) => record.status == 2,
  },
  file: {
    label: "录入数据",
    type: "slot",
    slotName: "file",
    hidden: (record: any) => record.status == 2,
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    } else {
      ruleForm.value = assign({}, defaultForm, row);
      proxy.$refs.uploadRef.clearFiles();
    }
  });
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  if (isUpdate.value) {
    querySetApi.updateQueryGroup(form.id, form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    querySetApi.createQueryGroup(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
const events = {
  download: () => {
    querySetApi.downloadTemplate({offline:false}).then((result) => {
      util.downloadFile(result, "query-set-template.xlsx");
    });
  },
};
//初始化
onMounted(async () => {
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>