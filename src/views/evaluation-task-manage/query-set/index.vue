<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="query-set-index">
      <analysis-edit ref="analysisPlanEditRef" @save-data="events.loadInstTableList" :groupEnum="groupEnum"></analysis-edit>
      <analysis-table ref="analysisPlanTableRef" @edit-data-inst="events.openInstEditWindow" @statistic-data="events.openStatisticPage" :groupEnum="groupEnum"></analysis-table>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import analysisTable from "./table.vue";
import analysisEdit from "./edit.vue";
import useCtx from "@/hooks/useCtx";
import * as querySetApi from "@/api/eval-query-set";
import useStore from '@/store'
const { common } = useStore()
const { $app, proxy, $router } = useCtx();
const routeName = "query-set";
let groupEnum =ref([])
const events = reactive({
  loadInstTableList: () => {
    proxy.$refs["analysisPlanTableRef"].loadList();
  },
  openInstEditWindow: (type: string, item: any) => {
    proxy.$refs["analysisPlanEditRef"].openWindow(type, item);
  },
  openStatisticPage: (item: any) => {
    $router.push({
      name: `${routeName}::details`,
      query: {
        querySetId: item.id,
        metaLabel: [item.name],
        label: item.label,
        description: item.description,
        status: item.status,
        name: item.name,
        size: item.size,
      },
    });
  }
});
const getGroups =()=>{
  querySetApi.getLabelList().then((res)=>{
    groupEnum.value =  res.data.map(item=>({value:item,label:item}))
  })
}
getGroups()
</script>
<style lang="scss">
</style>