<template>
  <my-drawer
      class="component-add"
      v-model="dialogVisible"
      :title="dialogTitle"
      :width="700"
      @confirm="handleConfirm"
      @close="handleClose">
    <my-form
        ref="formRef"
        :rules="rules"
        :ruleForm="ruleForm"
        :formItems="formItems"
        :disabled="disabled">
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import {ref, reactive, computed, nextTick, onMounted} from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE, CODE_RULE } from '@/utils/validate';
import useValidate from '@/hooks/validate'
import { addProduct, editProduct } from '@/api/product.ts'
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import {getRegionListApi} from '@/views/common/common.ts'

const { t } = useI18n();
const { $app } = useCtx()

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  return (formType.value === 'add' ? t('btn.new') : t('btn.edit')) + '产品'
})
const isUpdate = computed(() => {
  return formType.value === 'edit'
})

// 弹窗相关
const dialogVisible = ref<boolean>(false)
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editProduct : addProduct;
      func(ruleForm.value).then(res => {
        $app.$message.success(isUpdate.value ? '产品编辑成功' : '产品新建成功')
        dialogVisible.value = false;
        emits('reload')
      })
    }
  })
};

/* 校验 */
const { validateNameRule, validateCodeRule } = useValidate()
// 表单相关
const formType = ref<string>('add')
const formRef = ref<any>(null)
const disabled = ref<boolean>(false)
const defaultForm = {
  id: undefined,
  name: "",
  code: "",
  description: '',
  region: ''
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, '请输入产品名称') }],
  code: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, '请输入产品编码') }],
  // description: [{ required: true, message: "请输入产品描述", trigger: "blur" }],
  region: [{ required: true, message: "请选择测试环境", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "产品名称",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: NAME_RULE
    }
  },
  code: {
    label: "产品编码",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: CODE_RULE,
      disabled: computed(() => isUpdate.value)
    }
  },
  description: {
    label: "产品描述",
    type: "textarea",
    attrs: { maxlength: 255  }
  },
  region: {
    label: "验证环境",
    type: "radio",
    options: computed(() => Object.entries(regionList.value).map(([value, label]) => {
      return { label, value }
    })),
    attrs: {
      maxlength: 30
    }
  },
});

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
      disabled.value = row.opened;
    } else {
      ruleForm.value = assign({}, defaultForm);
      disabled.value = false;
    }
  })
};

defineExpose({ openDialog });

const regionList = ref([])
onMounted(async () => {
  regionList.value = await getRegionListApi(1)
})
</script>

<style lang="scss">
</style>
