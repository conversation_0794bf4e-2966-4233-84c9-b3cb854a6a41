<template>
  <page-wrapper :route-name="`${routeName}::`">
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <div class="component-list">
      <table-page
        ref="myTableRef"
        :query="query"
        :columns="columns"
        :operations="operations"
        :loadDataApi="getProductList"
        operationAuth="/base/#/product/edit"
        :transformListData="transformListData"
        @operation="handleOperation"
      >
        <!-- 查询 + 操作插槽内容 -->
        <template #query>
          <div class="flexBetween">
            <my-query :refresh-btn="{ show: true }" :queryItems="queryItems" @search="events.searchQuery" />
            <my-operation style="margin-bottom: 12px; display: flex; justify-content: flex-end">
              <template #buttonGroup>
                <my-button type="add" @click="events.add" operationAuth="/base/#/product/edit">新建产品</my-button>
              </template>
            </my-operation>
          </div>
        </template>
        <template #description="scope">
          <el-button link type="primary" @click="events.openDescriptionEditWindow(scope.row)" :disabled="!testAuth()">
            <el-icon>
              <Edit />
            </el-icon>
          </el-button>
          {{ scope.row.description }}
        </template>
      </table-page>
    </div>
    <AddDialog ref="addRef" @reload="loadList" />
    <RelateApp ref="relateAppRef" />
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import AddDialog from "./add.vue";
import RelateApp from "./relateApp.vue";
import { getProductList, deleteProduct, enableOrDisableProduct, editProduct } from "@/api/product.ts";
import { getRegionListApi } from "@/views/common/common.ts";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";

const { t } = useI18n();
const { $app, proxy, $router, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/product/edit");
};
const routeName = "product";

/* 查询 */
const query = ref<any>({});
const queryItems = ref<any>({
  name: {
    type: "input",
    width: "240px",
    modelValue: "",
    attrs: {
      // 'suffix-icon': Search
      placeholder: "名称 或 编码",
    },
  },
  // type: {
  //   type: 'input',
  //   label: '分类',
  //   width: '180px',
  //   attrs: {
  //     'suffix-icon': Search,
  //     placeholder: '请输入分类名称'
  //   }
  // }
});

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "nameRender",
    label: "产品名称",
    width: 200,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        events.toVersion(record);
      },
    },
  },
  // 文本可复制
  { prop: "code", label: "产品编码", withCopy: true, width: 180, showOverflowTooltip: false },
  { prop: "regionRender", label: "验证环境", width: 150 },
  { prop: "description", label: "描述", slotName: "description", minWidth: 200 },
  {
    prop: "opened",
    label: "是否启用",
    width: 112,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app.$confirm({ title: `确定${record.opened ? "禁用" : "启用"}产品方案“${record.name}”吗？` }).then(() => {
          enableOrDisableProduct({ id: record.id, opened: !record.opened }).then(() => {
            loadList();
          });
        });
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDate", label: "更新时间", width: 160 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDate", label: "创建时间", width: 160 },
  { prop: "operation", label: "操作", width: 210, fixed: "right" },
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  {
    type: "delete",
    label: t("btn.delete"),
    btnType: "danger",
    disabled: (record: any) => record.opened == true,
    disabledTips: (record: any) => {
      if (record.opened) {
        return "产品已经启用，不可删除";
      }
    },
  },
  { type: "relateApp", label: "关联应用列表" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    proxy.$refs.addRef?.openDialog("edit", record);
  } else if (type === "delete") {
    events.delete(record);
  } else if (type === "relateApp") {
    events.relateApp(record);
  }
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}`;
    x.regionRender = computed(() => regionList.value[x.region] ?? x.region);
    if (x.versionNo) x.nameRender += `(${x.versionNo})`;
    x.createdDate = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDate = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

/* events */
const events = reactive({
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  resetQuery: (obj: any) => {
    for (let key in obj) {
      queryItems.value[key].modelValue = obj[key].modelValue;
    }
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "产品方案", n: record.name }),
      })
      .then(() => {
        deleteProduct(record.id).then(() => {
          loadList();
        });
      });
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  toVersion: (record: any) => {
    $router.push({
      name: `${routeName}::version`,
      params: {
        id: record.id,
      },
      query: { metaLabel: record.name },
    });
  },
  relateApp: (record: any) => {
    proxy.$refs.relateAppRef?.openDialog(record.id);
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(record: any) {
    editProduct(record).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};

const regionList = ref([]);
onMounted(async () => {
  regionList.value = await getRegionListApi(1);
});
</script>

<style lang="scss" scoped></style>
