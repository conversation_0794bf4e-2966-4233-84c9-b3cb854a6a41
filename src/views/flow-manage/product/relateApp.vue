<template>
  <my-drawer v-model="dialogVisible" :title="dialogTitle" :width="700" @close="handleClose" :showConfirm="false">
    <div class="relate-app-list">
      <table-page ref="myTableRef" :query="query" :columns="columns" :loadDataApi="getRelateAppList" :loadImmediately="false" :withPagination="false" :withSort="false"> </table-page>
    </div>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import useCtx from "@/hooks/useCtx";
import { getRelateAppList } from "@/api/app";

const { $app, proxy } = useCtx();

const dialogTitle = computed(() => {
  return "关联应用列表";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  dialogVisible.value = false;
};

const query = ref<any>({
  productId: "",
});
const columns = ref([
  { prop: "appId", label: "应用id", withCopy: true, width: 140 },
  { prop: "name", label: "名称", width: 150 },
  { prop: "description", label: "描述", minWidth: 180 },
]);
const openDialog = (productId: string) => {
  dialogVisible.value = true;
  query.value.productId = productId;
  nextTick(() => {
    proxy.$refs.myTableRef.loadData();
  });
};

defineExpose({ openDialog });
</script>

<style lang="scss">
.relate-app-list {
  height: 100%;
}
</style>
