<template>
  <my-drawer class="component-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" />
    <template #extraOperate>
      <el-button type="primary" @click="handleConfirm(true)">确认并进入画布</el-button>
    </template>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import { addProductVersion, editProductVersion } from "@/api/product.ts";
import { getTemplateListByType } from "@/api/template.ts";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload", "canvas"]);
const dialogTitle = computed(() => {
  return (formType.value === "add" ? t("btn.new") : t("btn.edit")) + "产品版本";
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = (canvas: boolean = false) => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editProductVersion : addProductVersion;
      // 若为空白模板，去除templateId字段
      const params = cloneDeep(ruleForm.value);
      params.templateId == "0" && delete params.templateId;
      func(params).then((res) => {
        if (!canvas) {
          $app.$message.success(isUpdate.value ? "产品版本编辑成功" : "产品版本新建成功");
        } else {
          if (!isUpdate.value) ruleForm.value.id = res.data;
          emits("canvas", ruleForm.value);
        }
        dialogVisible.value = false;
        emits("reload");
      });
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  flowId: "",
  name: "",
  description: "",
  templateId: "0",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, "请输入策略名称") }],
  // description: [{ required: true, message: "请输入策略描述", trigger: "blur" }],
  templateId: [{ required: true, message: "请选择创建方式", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "版本名称",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: NAME_RULE,
    },
  },
  description: {
    label: "版本描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  templateId: {
    label: "创建方式",
    type: "select",
    options: computed(() => templateList.value),
    hidden: () => {
      return isUpdate.value;
    },
  },
});

// 获取编排模板列表
const templateList = ref([]);
const getTemplateListApi = async () => {
  getTemplateListByType(-2).then((res) => {
    if (res?.data?.length) {
      templateList.value = res?.data?.map((x: any) => ({
        label: x.name,
        value: x.id,
      }));
    }
    templateList.value.unshift({
      label: "空白模板",
      value: "0",
    });
  });
};
getTemplateListApi();

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
      !ruleForm.value.templateId && (ruleForm.value.templateId = "0");
    } else {
      ruleForm.value = assign({}, defaultForm, { flowId: row });
    }
  });
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
