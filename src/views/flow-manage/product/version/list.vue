<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="compVersion-list height-adaptive" style="padding: 10px">
      <el-card class="info-card">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span>基本信息</span>&nbsp;
              <!-- <el-button link type="primary" @click.native.stop="events.editComp(productDetail)">
              <el-icon size="18"><Edit /></el-icon>
            </el-button> -->
              <my-button link type="primary" @click="events.editComp(productDetail)" :stopPropagation="true" operationAuth="/base/#/product/edit"
                ><el-icon size="18"><Edit /></el-icon
              ></my-button>
            </template>
            <el-descriptions :column="2">
              <el-descriptions-item label-class-name="bold" label="产品名称：">{{ getText(productDetail.name) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="产品编码：">{{ getText(productDetail.code) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="产品描述：">{{ getText(productDetail.description) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="验证环境：">{{
                getText(regionList[productDetail.region] ?? productDetail.region)
              }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <el-card class="table-card" style="flex: 1">
        <div class="flex">
          <el-descriptions>
            <template #title>
              <div class="flex">
                <span style="width: 75px">版本信息</span>
                <el-button link type="primary" @click="events.searchQuery">
                  <el-icon size="18"><Refresh /></el-icon>
                </el-button>
                <el-select v-model="query.opened" style="width: 100px; margin-left: 20px" placeholder="是否启用" clearable>
                  <el-option label="已启用" :value="true" />
                  <el-option label="已禁用" :value="false" />
                </el-select>
                <el-input v-model="query.id" placeholder="请输入id" clearable style="width: 240px; margin-left: 10px"></el-input>
              </div>
            </template>
          </el-descriptions>
          <el-button type="primary" @click="events.add" :icon="CirclePlus" style="margin-left: auto" :disabled="!testAuth()">创建产品版本</el-button>
        </div>
        <div style="height: calc(100% - 40px)">
          <table-page
            ref="myTableRef"
            :query="query"
            :columns="columns"
            operationAuth="/base/#/product/edit"
            :operations="operations"
            :loadDataApi="loadListData"
            :transformQuery="transformQuery"
            :transformListData="transformListData"
            @operation="handleOperation"
          >
            <template #convertId="{ row }">
              <div>
                <span>
                  {{ events.maskProcessId(row.id) }}
                  <el-link class="icon-copy" type="primary" :underline="false" @click="copyText(row.id)" :icon="CopyDocument" />
                </span>
              </div>
            </template>
            <template #convertProcessId="{ row }">
              <div>
                <span>
                  {{ events.maskProcessId(row.processId) }}
                  <el-link class="icon-copy" type="primary" :underline="false" @click="copyText(row.processId)" :icon="CopyDocument" />
                </span>
              </div>
            </template>
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
  <AddDialog ref="addRef" @reload="getDetail" />
  <AddVersionDialog ref="addVersionRef" @reload="loadList" @canvas="events.toFlow" />
  <AddTemplateDialog ref="templateRef" :saveFun="saveAsTemplate" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { useI18n } from "vue-i18n";
import { getText } from "@/utils/helpers";
import {
  getProductDetail,
  getProductVersionList,
  deleteProductVersion,
  copyProductVersion,
  publishProductVersion,
  generateTemplate,
  changeVersionOpened,
} from "@/api/product.ts";
import { saveAsTemplate } from "@/api/template";
import useCtx from "@/hooks/useCtx";
import { CirclePlus, Edit } from "@element-plus/icons-vue";
import { getRegionListApi } from "@/views/common/common.ts";
import AddVersionDialog from "./add.vue";
import AddDialog from "@/views/flow-manage/product/add.vue";
import AddTemplateDialog from "@/views/flow-manage/template/add.vue";
import { dataC, timeC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import { CopyDocument } from "@element-plus/icons-vue";

const { t } = useI18n();
const { $app, proxy, $router, $route, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/template/edit");
};
const $id = $route.params.id as string;
const routeName = "product::version";

const productDetail = ref<any>({});
const getDetail = () => {
  getProductDetail($id).then((res: any) => {
    productDetail.value = res || {};
  });
};
const activeCollapse = ref([1]);
/* 查询 */
const query = ref<any>({
  opened: undefined,
  id: undefined,
});
/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "nameRender",
    label: "名称",
    width: 220,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        events.toFlow(record);
      },
    },
  },
  { prop: "description", label: "描述", minWidth: 150 },
  { prop: "id", label: "id", width: 140, slotName: "convertId" },
  { prop: "processId", label: "流程", width: 140, slotName: "convertProcessId" },
  {
    prop: "status",
    label: "状态",
    width: 150,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        1: { type: "warning", name: "草稿" },
        2: { type: "success", name: "已发布" },
      },
      reason: (record: any) => record.rejectReason, // 驳回原因
    },
  },
  {
    prop: "opened",
    label: "是否启用",
    width: 112,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
      },
      beforeChange: (record: any) => {
        $app.$confirm({ title: `确定${record.opened ? "禁用" : "启用"}“${record.nameRender}”吗？` }).then(() => {
          changeVersionOpened(record.id, !record.opened).then(() => {
            $app.$message.success(record.opened ? "禁用成功" : "启用成功");
            loadList();
          });
        });
      },
      disabled: (record: any) => {
        return record.status != 2;
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDate", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDate", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 120, fixed: "right" },
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  { type: "publish", label: "发布版本", disabled: (record: any) => record.status == 2, collapsed: true, disabledTips: "版本已发布，不可发布版本" },
  { type: "copy", label: "复制版本", collapsed: true },
  {
    type: "delete",
    label: `${t("btn.delete")}版本`,
    btnType: "danger",
    collapsed: true,
  },
  { type: "saveAs", label: "另存为模板", collapsed: true },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};

// 转换传参
const transformQuery = ({ ...rest }) => {
  return {
    flowId: $id,
    ...rest,
  };
};
//列表查询
const validateNumber = (numberStr, numberLabel) => {
  const maxNumberStr = "9223372036854775807";
  if (isNaN(Number(numberStr))) {
    $app.$message.warning(`${numberLabel}输入的值不合法`);
    return false;
  } else if (Number(numberStr) < 0 || numberStr.length > maxNumberStr.length || (numberStr.length == maxNumberStr.length && numberStr > maxNumberStr)) {
    $app.$message.warning(`${numberLabel}输入的值不在有效范围内`);
    return false;
  }
  return true;
};
const loadListData = (data: any) => {
  if (!dataC.isEmpty(data.id) && !validateNumber(data.id, "id")) {
    return new Promise((resolve: any, reject: any) => {
      reject();
    });
  } else {
    return new Promise((resolve: any) => {
      getProductVersionList(data).then((result) => {
        //返回数据
        resolve(result);
      });
    });
  }
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}(v${String(x.version).padStart(3, "0")})`;
    x.createdDate = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDate = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

/* events */
const events = reactive({
  collapseChange: (val: string[]) => {
    setTimeout(() => {
      proxy.$refs.myTableRef.mediaHeight();
    }, 500);
  },
  searchQuery: () => {
    query.value = assign({}, query.value);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "版本", n: record.name }),
      })
      .then(() => {
        deleteProductVersion(record.id).then((res: any) => {
          $app.$message.success("版本删除成功！");
          loadList();
        });
      });
  },
  add: () => {
    proxy.$refs.addVersionRef?.openDialog("add", $id);
  },
  edit: (record: any) => {
    proxy.$refs.addVersionRef?.openDialog("edit", record);
  },
  copy: (record: any) => {
    $app.$confirm({ title: `您确认要复制版本“${record.name}”吗？` }).then(() => {
      copyProductVersion(record.id).then((res: any) => {
        $app.$message.success("版本复制成功！");
        loadList();
      });
    });
  },
  publish: (record: any) => {
    $app.$confirm({ title: `您确认要发布版本“${record.name}”吗？` }).then(() => {
      publishProductVersion(record.id).then((res: any) => {
        $app.$message.success("版本发布成功！");
        loadList();
      });
    });
  },
  // view: (record: any) => {
  // },
  saveAs: (record: any) => {
    proxy.$refs.templateRef?.openDialog("saveAs", { type: -2, flowVersionId: record.id });
  },
  editComp: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  // 拓扑图
  toFlow: (record: any) => {
    if ((record.status == 1 || !record.status) && $auth.testAuth("/base/#/product/edit")) {
      window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/detail?id=${$id}&vid=${record.id}&t=product`);
    } else {
      window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/view?id=${$id}&vid=${record.id}&t=product`);
    }
  },
  maskProcessId: (appSecret: any) => {
    // 中间6位字符
    if (!appSecret) {
      return "";
    }
    const start = appSecret.substring(0, 3); // 显示前3位
    const end = appSecret.substring(appSecret.length - 3); // 显示后3位
    const masked = start + "******" + end;
    return masked;
  },
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};

const regionList = ref([]);
onMounted(async () => {
  regionList.value = await getRegionListApi(1);
  getDetail();
});
</script>

<style lang="scss" scoped>
.compVersion-list {
  padding: 10px;

  .el-card {
    :deep(.el-card__body) {
      height: 100%;
    }
  }

  .info-card {
    height: auto;
    margin-bottom: 10px;

    :deep(.el-descriptions__label.bold) {
      font-weight: bold;
      background: #fff !important;
    }
  }

  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0;
  }
}
</style>
