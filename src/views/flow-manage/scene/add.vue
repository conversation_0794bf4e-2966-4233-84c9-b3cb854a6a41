<template>
  <my-drawer
      class="component-add"
      v-model="dialogVisible"
      :title="dialogTitle"
      :width="700"
      @confirm="handleConfirm"
      @close="handleClose">
    <my-form
        ref="formRef"
        :rules="rules"
        :ruleForm="ruleForm"
        :formItems="formItems">
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import {ref, reactive, computed, nextTick, onMounted} from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE, CODE_RULE } from '@/utils/validate';
import useValidate from '@/hooks/validate'
import { addScene, editScene, getSceneGroupList } from '@/api/scene.ts'
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import {getRegionListApi} from '@/views/common/common.ts'

const { t } = useI18n();
const { $app } = useCtx()

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  return (formType.value === 'add' ? t('btn.new') : t('btn.edit')) + '场景策略'
})
const isUpdate = computed(() => {
  return formType.value === 'edit'
})

// 弹窗相关
const dialogVisible = ref<boolean>(false)
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
  getGroupList();
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editScene : addScene;
      func(ruleForm.value).then(res => {
        $app.$message.success(isUpdate.value ? '场景策略编辑成功' : '场景策略新建成功')
        handleClose();
        emits('reload')
      })
    }
  })
};

/* 校验 */
const { validateNameRule, validateCodeRule } = useValidate()
// 表单相关
const formType = ref<string>('add')
const formRef = ref<any>(null)
const defaultForm = {
  id: undefined,
  name: "",
  code: "",
  type: -1,
  groupName: '',
  description: '',
  region: ''
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, '请输入策略名称') }],
  code: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, '请输入策略编码') }],
  groupName: [{ required: true, message: "请输入分组", trigger: "change" }],
    region: [{ required: true, message: "请选择区域环境", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "策略名称",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: NAME_RULE
    }
  },
  code: {
    label: "策略编码",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: CODE_RULE,
      disabled: computed(() => isUpdate.value)
    }
  },
  // type: {
  //   label: "类型",
  //   type: "radio",
  //   options: [
  //     { value: -1, label: "场景策略" },
  //     { value: -2, label: "产品方案" }
  //   ],
  //   attrs: {
  //     disabled: computed(() => isUpdate.value)
  //   }
  // },
  groupName: {
    label: "分组",
    type: "select",
    options: [],
    attrs: {
      allowCreate: true,
    }
  },
  description: {
    label: "策略描述",
    type: "textarea",
    attrs: { maxlength: 255  }
  },
  region: {
    label: "验证环境",
    type: "radio",
    options: computed(() => Object.entries(regionList.value).map(([value, label]) => {
      return { label, value }
    }))
  },
});

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  })
};
const regionList = ref([])
//获取分组列表
const getGroupList = async () => {
  const groups = await getSceneGroupList();
  formItems.value.groupName.options = groups.data.map((item) => {
    return {
      label: item,
      value: item
    }
  });
}
//初始化
onMounted(async () => {
  regionList.value = await getRegionListApi(1);
  getGroupList();
})
//接口暴露
defineExpose({ openDialog });
</script>

<style lang="scss">
</style>
