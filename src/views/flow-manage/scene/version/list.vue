<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="compVersion-list height-adaptive" style="padding: 10px">
      <el-card class="info-card">
        <el-collapse v-model="activeCollapse"  @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
            <span>基本信息</span>&nbsp;
            <!-- <el-button link type="primary" @click.native.stop="events.editComp(sceneDetail)">
              <el-icon size="18"><Edit /></el-icon>
            </el-button> -->
            <my-button link  type="primary" @click="events.editComp(sceneDetail)" :stopPropagation="true" operationAuth="/base/#/scene/edit"><el-icon size="18"><Edit /></el-icon></my-button>
            </template>
            <el-descriptions :column="2">
              <el-descriptions-item label-class-name="bold" label="策略名称：">{{ getText(sceneDetail.name) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="策略编码：">{{ getText(sceneDetail.code) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="类型：">{{ systemMap[sceneDetail.type] }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="分组：">{{ getText(sceneDetail.groupName) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="策略描述：">{{ getText(sceneDetail.description) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="验证环境：">{{
                getText(regionList[sceneDetail.region] ?? sceneDetail.region)
              }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <el-card class="table-card" style="flex: 1">
        <div style="display: flex">
          <el-descriptions>
            <template #title>
              <span>版本信息</span>&nbsp;
              <el-button link type="primary" @click="events.searchQuery">
                <el-icon size="18"><Refresh /></el-icon>
              </el-button>
            </template>
          </el-descriptions>
          <el-button type="primary" @click="events.add" :icon="CirclePlus" style="margin-left: auto" :disabled="!testAuth()">创建策略版本</el-button>
        </div>
        <div style="height: calc(100% - 40px)">
          <table-page
            ref="myTableRef"
            :query="query"
            operationAuth="/base/#/scene/edit"
            :columns="columns"
            :operations="operations"
            :loadDataApi="getSceneVersionList"
            :transformQuery="transformQuery"
            :transformListData="transformListData"
            @operation="handleOperation"
          >
            <template #convertProcessId="{ row }">
              <div>
                <span>
                  {{ events.maskProcessId(row.processId) }}
                  <el-link class="icon-copy" type="primary" :underline="false" @click="copyText(row.processId)" :icon="CopyDocument" />
                </span>
              </div>
            </template>
          </table-page>
        </div>
      </el-card>
    </div>
  </page-wrapper>
  <AddDialog ref="addRef" @reload="getDetail" />
  <AddVersionDialog ref="addVersionRef" @reload="loadList" @canvas="events.toFlow" />
  <AddTemplateDialog ref="templateRef" :saveFun="saveAsTemplate" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { useI18n } from "vue-i18n";
import { getText } from "@/utils/helpers";
import { deleteSceneVersion, getsceneDetail, getSceneVersionList, copySceneVersion, publishSceneVersion } from "@/api/scene.ts";
import { saveAsTemplate } from "@/api/template";
import useCtx from "@/hooks/useCtx";
import { CirclePlus, Edit } from "@element-plus/icons-vue";
import { systemMap, getRegionListApi } from "@/views/common/common.ts";
import AddVersionDialog from "./add.vue";
import AddDialog from "../add.vue";
import AddTemplateDialog from "@/views/flow-manage/template/add.vue";
import { timeC } from "turing-plugin";
import { copyText } from "@/utils/helpers";
import { CopyDocument } from "@element-plus/icons-vue";

const { t } = useI18n();
const { $app, proxy, $router, $route, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/scene/edit");
}
const $id = $route.params.id as string;
const routeName = "scene::version";
const activeCollapse = ref([1]);
const sceneDetail = ref<any>({});
const getDetail = () => {
  getsceneDetail($id).then((res: any) => {
    sceneDetail.value = res || {};
  });
};

/* 查询 */
const query = ref<any>({});

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "nameRender",
    label: "名称",
    width: 220,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        events.toFlow(record);
      },
    },
  },
  { prop: "description", label: "描述" },
  {
        prop: 'processId',
        label: '流程',
        width: 140,
        slotName: 'convertProcessId'
  },
  {
    prop: "status",
    label: "状态",
    width: 150,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        1: { type: "warning", name: "草稿" },
        2: { type: "success", name: "已发布" },
      },
    },
  },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDate", label: "更新时间", width: 160 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDate", label: "创建时间", width: 160 },
  { prop: "operation", label: "操作", width: 130, fixed: "right" },
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  { type: "publish", label: "发布版本", disabled: (record: any) => record.status == 2, collapsed: true, disabledTips:  "版本已发布，不可发布版本"},
  { type: "copy", label: "复制版本", collapsed: true },
  {
    type: "delete",
    label: `${t("btn.delete")}版本`,
    btnType: "danger",
    collapsed: true,
  },
  { type: "saveAs", label: "另存为模板", collapsed: true },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};

// 转换传参
const transformQuery = ({ ...rest }) => {
  const query = {
    id: $id,
    ...rest,
  };
  return query;
};

// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}(v${String(x.version).padStart(3, "0")})`;
    x.createdDate = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDate = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

/* events */
const events = reactive({
  collapseChange: (val: string[]) => {
    setTimeout(()=>{
      proxy.$refs.myTableRef.mediaHeight()
    },500)
  },
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "版本", n: record.name }),
      })
      .then(() => {
        deleteSceneVersion(record.id).then((res: any) => {
          $app.$message.success("版本删除成功！");
          loadList();
        });
      });
  },
  add: () => {
    proxy.$refs.addVersionRef?.openDialog("add", $id);
  },
  edit: (record: any) => {
    proxy.$refs.addVersionRef?.openDialog("edit", record);
  },
  copy: (record: any) => {
    $app.$confirm({ title: `您确认要复制版本“${record.name}”吗？` }).then(() => {
      copySceneVersion(record.id).then((res: any) => {
        $app.$message.success("版本复制成功！");
        loadList();
      });
    });
  },
  publish: (record: any) => {
    $app.$confirm({ title: `您确认要发布版本“${record.name}”吗？` }).then(() => {
      publishSceneVersion(record.id).then((res: any) => {
        $app.$message.success("版本发布成功！");
        loadList();
      });
    });
  },
  // view: (record: any) => {
  // },
  saveAs: (record: any) => {
    proxy.$refs.templateRef?.openDialog("saveAs", { type: -1, flowVersionId: record.id });
  },
  // 编辑策略
  editComp: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  // 流程拓扑
  toFlow: (record: any) => {
    if ((record.status == 1||!record.status) && $auth.testAuth('/base/#/scene/edit')) {
      window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/detail?id=${$id}&vid=${record.id}&t=scene`);
    } else {
      window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/view?id=${$id}&vid=${record.id}&t=scene`);
    }
  },
  maskProcessId: (appSecret: any) => {
    // 中间6位字符
    if(!appSecret) {
      return ''
    }
    const start = appSecret.substring(0, 3); // 显示前3位
    const end = appSecret.substring(appSecret.length - 3); // 显示后3位
    const masked = start + '******' + end; 
    return masked;
  }
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
const regionList = ref([]);
onMounted(async () => {
  getDetail();
  regionList.value = await getRegionListApi(1);
});
</script>

<style lang="scss" scoped>
.compVersion-list {
  padding: 10px;

  .el-card {
    :deep(.el-card__body) {
      height: 100%;
    }
  }

  .info-card {
    height: auto;
    margin-bottom: 10px;

    :deep(.el-descriptions__label.bold) {
      font-weight: bold;
      background: #fff !important;
    }
  }

  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0;
  }
}
</style>
