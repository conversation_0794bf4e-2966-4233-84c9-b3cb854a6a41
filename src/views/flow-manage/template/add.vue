<template>
  <my-drawer class="component-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE, CODE_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import { addTemplate, editTemplate, saveAsTemplate, getTemplateGroupList } from "@/api/template.ts";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  if (formType.value === "add") {
    return "新建编排模板";
  } else if (formType.value === "edit") {
    return "编辑编排模板";
  } else if (formType.value == "saveAs") {
    return "另存为模板";
  } else {
    return "编排模板";
  }
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});
const isSaveAs = computed(() => {
  return formType.value === "saveAs";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
  getGroupList();
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editTemplate : isSaveAs.value ? saveAsTemplate : addTemplate;
      func(ruleForm.value).then((res) => {
        $app.$message.success(isUpdate.value ? "编排模板编辑成功" : isSaveAs.value ? "另存为模板成功" : "编排模板新建成功");
        handleClose();
        emits("reload");
      });
    }
  });
};

/* 校验 */
const { validateNameRule, validateCodeRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "",
  code: "",
  type: -1,
  groupName: "",
  description: "",
  flowVersionId: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        return callback(new Error("请输入模板名称"));
      }
      if (value.length > 20) {
        return callback(new Error("模板名称不能超过20个字符"));
      }
      return callback();
    } }],
  code: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        return callback(new Error("请输入模板编码"));
      }
      if (value.length > 20) {
        return callback(new Error("模板编码不能超过20个字符"));
      }
      return callback();
    } }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  groupName: [{ required: true, message: "请输入分组", trigger: "change" }],
  // description: [{ required: true, message: "请输入模板描述", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "模板名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: NAME_RULE,
    },
  },
  code: {
    label: "模板编码",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: CODE_RULE,
      disabled: computed(() => isUpdate.value),
    },
  },
  type: {
    label: "类型",
    type: "radio",
    options: [
      { value: -1, label: "场景策略" },
      { value: -2, label: "产品方案" },
    ],
    attrs: {
      disabled: computed(() => formType.value === "saveAs" || formType.value === "edit"),
    },
  },
  groupName: {
    label: "分组",
    type: "select",
    options: computed(() => (ruleForm.value.type == -1 ? modelValue.sceneGroupList : modelValue.productGroupList)),
    attrs: {
      allowCreate: true,
    },
  },
  description: {
    label: "模板描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});
//打开弹窗
const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else if (type === "saveAs") {
      ruleForm.value = assign({}, defaultForm, row);
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  });
};
//数据项
const modelValue = reactive({
  sceneGroupList: [],
  productGroupList: [],
});
//获取分组列表
const getGroupList = async () => {
  const sceneGroups = await getTemplateGroupList(-1);
  modelValue.sceneGroupList = sceneGroups.data.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
  const productGroups = await getTemplateGroupList(-2);
  modelValue.productGroupList = productGroups.data.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
};
//初始化
onMounted(() => {
  getGroupList();
});
//接口暴露
defineExpose({ openDialog });
</script>

<style lang="scss"></style>
