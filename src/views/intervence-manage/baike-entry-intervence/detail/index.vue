<template>
  <div class="baike-entry-intervence">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :withPagination="false"
      :dragRow="dragable"
      :withSort="false"
      @row-change="handleTableRowChange"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query
            :queryItems="queryItems"
            :refreshBtn="{ show: true }"
            @search="events.search"
            @reset="events.reset"
          />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" operationAuth="/base/#/baike-entry-intervence/edit">新建</my-button>
              <my-button type="danger" @click="dragable = true" v-if="!dragable" operationAuth="/base/#/baike-entry-intervence/edit"
                >排序</my-button
              >
              <my-button type="info" @click="events.cancelSort" v-if="dragable" operationAuth="/base/#/baike-entry-intervence/edit"
                >取消</my-button
              >
              <my-button type="primary" @click="events.sort" v-if="dragable" operationAuth="/base/#/baike-entry-intervence/edit"
                >保存</my-button
              >
            </template>
          </my-operation>
        </div>
      </template>
      <template #header>
        <el-tabs v-model="query.type" @tab-click="handleTabClick">
          <el-tab-pane label="名称匹配" name="nameMatch"></el-tab-pane>
          <el-tab-pane label="同义匹配" name="synonymMatch"></el-tab-pane>
          <el-tab-pane label="别名匹配" name="aliasMatch"></el-tab-pane>
        </el-tabs>
      </template>
      <template #operate="{ row }">
        <div class="flex">
          <my-button
            link
            type="primary"
            @click="events.edit(row)"
            operationAuth="/base/#/baike-entry-intervence/edit"
          >
            编辑
          </my-button>
          <my-button
            v-if="row.status === 0 || row.status === -1"
            link
            :type="row.status == '-1' ? 'primary' : 'danger'"
            @click="events.line(row)"
            operationAuth="/base/#/baike-entry-intervence/edit"
          >
            {{ row.status == "0" ? "下线" : row.status == "-1" ? "上线" : "" }}
          </my-button>
          <my-button
            link
            type="danger"
            :disabled="row.status == '1'"
            @click="events.lock(row)"
            operationAuth="/base/#/baike-entry-intervence/edit"
          >
            锁定
          </my-button>
        </div>
      </template>
    </table-page>
    <AddDialog
      ref="addRef"
      @save-data="loadList"
      :columns="columns"
      :currentTab="query.type"
      :statusList="statusList"
      :mustRecallList="mustRecallList"
      :matchList="matchList"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as entryIntervenceApi from "@/api/baike-entry-intervence";
import { getTemplateList } from "@/api/mock";
import AddDialog from "./add.vue";

const { $app, proxy, $router } = useCtx();
const routeName = "baike-entry-intervence";

const statusList = [
  { value: 0, label: "上线", type: "primary" },
  { value: -1, label: "下线", type: "danger" },
  { value: 1, label: "锁定", type: "info" },
];
const mustRecallList = [
  { value: 0, label: "非必出", type: "primary" },
  { value: 1, label: "召回必出", type: "danger" },
  { value: 2, label: "结果必出", type: "info" },
];
const matchList = [
  { value: "nameMatch", label: "名称匹配" },
  { value: "aliasMatch", label: "别名匹配" },
  { value: "synonymMatch", label: "同义词匹配" },
];
const dragable = ref(false);
const sortTableData = ref<any>([]);
const dragloading = ref(false);

//列配置
const columns = ref([
  {
    label: "词条ID",
    prop: "idStr",
    width: 210,
    withCopy: true,
  },
  {
    label: "词条名称",
    prop: "title",
    minWidth: 150,
  },
  {
    label: "词条类型",
    prop: "type",
    width: 200,
  },
  {
    label: "链接",
    prop: "url",
    width: 250,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
       window.open(record.url,'_blank')
      },
    },
    sortable: false,
  },
  {
    label: "rank值",
    prop: "rank",
    width: 100,
  },
  {
    label: "是否必出",
    prop: "mustRecall",
    width: 100,
    custom: "tagStatus",
    customRender: {
      options: mustRecallList,
    },
  },
  {
    label: "状态",
    prop: "status",
    custom: "status",
    customRender: {
      options: statusList,
    },
    width: 80,
  },
  {
    prop: "operation",
    label: "操作",
    width: 155,
    fixed: "right",
    slotName: "operate",
  },
]);

//查询面板
const query = ref<any>({
  type: "nameMatch",
});
const queryItems = ref<any>({
  detailId: {
    type: "input",
    label: "词条ID",
    modelValue: "",
    attrs: {
      placeholder: "词条ID",
    },
  },
  detailUrl: {
    type: "input",
    label: "词条Url",
    modelValue: "",
    attrs: {
      placeholder: "词条Url",
    },
  },
  statusList: {
    modelValue: [],
    type: "select",
    label: "状态",
    width: "180px",
    options: statusList,
    attrs: {
      placeholder: "状态",
      multiple: true,
    },
  },
  mustRecallList: {
    modelValue: [],
    type: "select",
    label: "是否必出",
    width: "180px",
    options: mustRecallList,
    attrs: {
      placeholder: "是否必出",
      multiple: true,
    },
  },
  detailType: {
    type: "input",
    label: "词条类型",
    modelValue: "",
    attrs: {
      placeholder: "词条类型",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size, sort } = data;
  const params = {
    ...data,
    id: $app.$route.query.id,
  };
  return new Promise((resolve: any) => {
    entryIntervenceApi.getEntryDetail(params).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = !dataC.isEmpty(x.createdDate)
      ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate)
      ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    return x;
  });
};
//操作
const handleTableRowChange = (data: any[]) => {
  console.log(data, "data");
  sortTableData.value = data;
};
const handleTabClick = (val) => {
  query.value.type = val.props.name;
  console.log("handleTabClick", query.value.type);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  line: (record: any) => {
    $app
      .$confirm({
        title: `您确认要${record.status == "0" ? "下线" : "上线"}${
          record.title
        }吗?`,
      })
      .then(() => {
        let statusParams;
        if (record.status == "-1") {
          statusParams = "0";
        } else if (record.status == 0) {
          statusParams = "-1";
        } else if (status == "1") {
          statusParams = "1";
        }
        entryIntervenceApi
          .updateDetail({
            ...record,
            status: statusParams,
            type: query.value.type,
            id: record.idStr,
            masterId: $app.$route.query.id,
          })
          .then(() => {
            $app.$message.success(
              `${record.status == "0" ? "下线" : "上线"}成功`
            );
            loadList();
          });
      });
  },
  lock: (record: any) => {
    $app
      .$confirm({
        title: `您确认要锁定${record.title}吗?`,
      })
      .then(() => {
        entryIntervenceApi
          .updateDetail({
            ...record,
            status: "1",
            type: query.value.type,
            id: record.idStr,
            masterId: $app.$route.query.id,
          })
          .then((result) => {
            $app.$message.success("锁定成功");
            loadList();
          });
      });
  },
  sort: () => {
    dragloading.value = true;
    const params = {
      id: $app.$route.query.id,
      idList: sortTableData.value.map((item) => item.idStr),
      type: query.value.type,
    };
    entryIntervenceApi.updateDetailSort(params).then(() => {
      dragable.value = false;
      dragloading.value = false;
      loadList();
      $app.$message.success(`排序成功`);
    });
  },
  cancelSort: () => {
    dragable.value = false;
    loadList();
  },
});

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>
