<template>
  <page-wrapper route-name="manual-intervention::">
    <div class="baike-entry-intervence">
      <el-tabs v-model="currentTab">
        <el-tab-pane label="domain黑名单" name="nameMatch" :lazy="true">
          <domain-black
            ref="domainBlackRef"
            :statusList="statusList"
          />
        </el-tab-pane>
        <el-tab-pane label="url黑名单" name="synonymMatch" :lazy="true">
          <URLBlack ref="URLBlackRef" />
        </el-tab-pane>
      </el-tabs></div
  ></page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import DomainBlack from "./domainBlack/DomainBlackIndex.vue";
import URLBlack from "./urlBlack/URLBlackIndex.vue";
const currentTab = ref("nameMatch");
const { word, api } = useStore();
const { $app, proxy, $router } = useCtx();
const statusList = [
  { value: false, label: "失效", type: "danger" },
  { value: true, label: "生效", type: "primary" },
];
</script>

<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
  ::v-deep {
    .el-tabs {
      height: 100%;
      .el-tabs__header {
        padding: 0 15px;
      }
      .el-tabs__content {
        padding: 10px 15px 0 15px;
        .el-tab-pane {
          height: 100%;
        }
      }
    }
  }
}
</style>
