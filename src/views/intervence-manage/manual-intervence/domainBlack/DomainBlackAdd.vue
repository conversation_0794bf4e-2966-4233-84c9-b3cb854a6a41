<template>
  <my-drawer
    class="mock-add"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      @submit="submit"
    >
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import * as manualInterventionApi from "@/api/manual-intervention";
import { getIdxDbInstListPage } from "@/api/idx-db";

import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const { t } = useI18n();
const { word } = useStore();
const props = defineProps({
  statusList: { type: Array },
  areaList: { type: Array },
  idxList: { type: Array },
});
const dialogTitle = computed(() => {
  return formType.value === "add" ? t("btn.new") : t("btn.edit");
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  domain: "",
  enabled: false,
  idxCode: "",
  idx: "",
  reason: "",
  site: "",
  regionCodes: [],
};
let ruleForm = ref<any>(assign({ }, defaultForm));
const rules = reactive<FormRules>({
  domain: [
    {
      required: true,
      trigger: "blur",
      message: "请输入domain",
    },
  ],
});
// 表单项
const formItems = ref<any>();

const getOptions = (type: any, row: any) => {
  let idxCodeOptions =
    type == "edit" ? [{ label: row.idx, value: row.idxCode }] : [];
  formItems.value = {
    domain: {
      label: "domain",
      type: "input",
      attrs: {
        maxlength: 50,
        placeholder: "domain",
      },
    },
    site: {
      label: "对应site",
      type: "input",
      attrs: {
        maxlength: 50,
        placeholder: "对应site",
      },
    },
    idxCode: {
      label: "所属索引库",
      type: "select",
      options: idxCodeOptions,
      attrs: {
        clearable: true,
        remote: true,
        "remote-method": (query: any) => {
          if (query == undefined) {
            return;
          }
          getIdxDbInstListPage(query, 1, 200, "").then((res) => {
            formItems.value.idxCode.options = res.content.map((item: any) => ({
              ...item,
              label: item.name,
              value: item.code,
            }));
          });
        },
      },
      events: { change: () => {} },
    },
    regionCodes: {
      label: "所属环境",
      type: "select",
      options: props.areaList,
      disabled: () => isUpdate.value,
      attrs: { clearable: false, multiple: true },
      events: { change: () => {} },
    },
    enabled: {
      label: "规则状态",
      type: "radio",
      options: props.statusList,
    },
    reason: {
      label: "下线原因",
      type: "textarea",
      attrs: {
        maxlength: 50,
        placeholder: "下线原因",
      },
    },
  };
};

const openDialog = async (type: string, row: any) => {
  console.log(type, row);
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 2. 加载相关的下拉列表数据
  getOptions(type, row);
  // 3. 回显相关的操作
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = assign(pick(row, keys(defaultForm)), {
        regionCodes: [row.regionCode],
      }); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign(defaultForm);
    }
  });
};
const emits = defineEmits(["save-data"]);
const submit = (form: any) => {
  // 接口相关业务代码，执行完成后关闭弹窗
  if (isUpdate.value) {
    manualInterventionApi
      .editDomainBlack(form.id, {
        ...form,
        idx: formItems.value.idxCode.options.find(
          (item: any) => item.value == form.idxCode
        )?.label,
      }, ruleForm.value.regionCodes[0])
      .then(() => {
        $app.$message.success("修改成功");
        emits("save-data");
        handleClose();
      });
  } else {
    manualInterventionApi
      .addDomainBlack({
        ...form,
        idx: formItems.value.idxCode.options.find(
          (item: any) => item.value == form.idxCode
        )?.label,
      })
      .then(() => {
        $app.$message.success("新增成功");
        emits("save-data");
        handleClose();
      });
  }
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
