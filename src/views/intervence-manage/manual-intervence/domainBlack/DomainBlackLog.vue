<template>
  <div class="baike-entry-intervence">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :withSort="false"
      :withPagination="false"
    >
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as manualIntervention from "@/api/manual-intervention";
import { getTemplateList } from "@/api/mock";
import { computed } from "vue";
const { $app, proxy, $router } = useCtx();
const routeName = "manual-intervention";
const props = defineProps({
  statusList: { type: Array },
});
//列配置
const columns = ref([
  {
    prop: "id",
    label: "id",
    width: 150,
  },
  {
    prop: "domain",
    label: "doamin",
    width: 180,

  },
  {
    prop: "site",
    label: "对应site",
    minWidth: 120,

  },
  {
    prop: "idx",
    label: "所属索引库",
    
    minWidth: 120,
  },
  // {
  //   prop: "status",
  //   label: "所属环境",
  //   minWidth: 120,
  // },
  {
    prop: "reason",
    label: "下线原因",
    minWidth: 150,
  },
  {
    prop: "enabled",
    label: "规则状态",
    minWidth: 100,
    custom: "status",
    customRender: {
      options: [
        { value: false, label: "停用", type: "danger" },
        { value: true, label: "启用", type: "primary" },
      ],
    },
  },
  {
    prop: "optDesc",
    label: "操作",
    minWidth: 150,
  },
  {
    prop: "failReason",
    label: "失败原因",
    minWidth: 150,
  },


  {
    prop: "lastModifiedBy",
    label: "操作用户",
    minWidth: 100,
  },
  { prop: "lastModifiedDateRender", label: "操作时间", width: 180, },
  
]);
//查询面板
const query = ref<any>({});
const queryItems = ref<any>({
  name: {
    type: "input",
    label: "domain",
    modelValue: "",
    attrs: {
      placeholder: "实体词",
    },
  },

  user: {
    type: "input",
    label: "对应site",
    modelValue: "",
    attrs: {
      placeholder: "实体词",
    },
  },
  user1: {
    type: "input",
    label: "索引库",
    modelValue: "",
    attrs: {
      placeholder: "实体词",
    },
  },
  status: {
    modelValue: [],
    type: "select",
    label: "环境",
    width: "180px",
    options: props.statusList,
    attrs: {
      placeholder: "环境",
      multiple: true,
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size, sort } = data;
  const params = {
    id:$app.$route.query.id,
    ...data,
  };
  return new Promise((resolve: any) => {
    manualIntervention.getDomainLog(params, $app.$route.query.regionCode).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = !dataC.isEmpty(x.createdDate)
      ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate)
      ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    return x;
  });
};

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>
