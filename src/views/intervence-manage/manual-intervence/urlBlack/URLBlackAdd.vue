<template>
  <div class="url-black">
    <div class="flexBetweenStart">
      <my-query
        :queryItems="queryItems"
        :refreshBtn="{ show: true }"
        @search="events.search"
        @reset="events.reset"
      />
      <my-button type="add" @click="events.addAll()" :disabled="cardInfo.content.findIndex((item: any) => !item.isInBlackList)=='-1'">全部加入黑名单</my-button>
    </div>
    <div class="content">
      <Card
        v-for="(item, index) in cardInfo.content"
        :key="item.url"
        :info="item"
        :index="index"
        @save-data="loadData"
      />
      <my-empty :size="120" text="请输入查询条件" v-if="!hasSearch" />
      <my-empty
        :size="120"
        text="暂无数据"
        v-if="hasSearch && !cardInfo.content.length"
      />
    </div>

    <!-- <div
      class="pagination-wrapper"
      ref="custom-pagination"
      v-show="cardInfo.totalElements > 10"
    >
      <my-pagination
        v-bind="page"
        @current-change="events.currentChange"
        @size-change="events.sizeChange"
      />
    </div> -->
    <URLBlackReason ref="reasonRef" @save-data="loadData" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { assign, debounce } from "lodash";
import useStore from "@/store";
const { word, api } = useStore();
import useCtx from "@/hooks/useCtx";
import * as manualIntervention from "@/api/manual-intervention";
import { getTemplateList } from "@/api/mock";
import { getIdxDbInstListPage } from "@/api/idx-db";
import URLBlackReason from "./URLBlackReason.vue";
import Card from "./Card.vue";
import { computed } from "vue";
const { $app, proxy, $router } = useCtx();
const routeName = "manual-intervention";
const props = defineProps({
  statusList: { type: Array },
  areaList: { type: Array },
  idxList: { type: Array },
});
/* 分页 */
const page = reactive({
  pageNum: 1,
  pageSize: 20 as number,
  pageSizes: [20] as Array<number>,
  total: 0,
});
let contentHeight = ref(0);
let hasSearch = ref(false);
//列配置
const cardInfo = ref<any>({
  content:[]
});
//查询面板
const query = ref<any>({});
const queryItems = ref<any>({
  url: {
    type: "input",
    label: "url",
    modelValue: "",
    attrs: {
      placeholder: "url",
    },
  },
  docId: {
    type: "input",
    label: "docId",
    modelValue: "",
    attrs: {
      placeholder: "docId",
    },
  },
  title: {
    type: "input",
    label: "title",
    modelValue: "",
    attrs: {
      placeholder: "title",
    },
  },
  regionCodes: {
    type: "select",
    label: "环境",
    width: "210px",
    modelValue: [],
    options: [],
    attrs: {
      placeholder: "环境",
      multiple: true,
    },
  },
});
const updateEnvirenment = () => {
  // columns.value.
  word.getAreaList().then((values: any) => {
    queryItems.value.regionCodes.options = values.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
    }));
  });
};
updateEnvirenment();
//列表查询
const loadData = () => {
  hasSearch.value = true;
  if (
    JSON.stringify(query.value) ==
    JSON.stringify({
      url: "",
      docId: "",
      title: "",
      regionCodes: [],
    })
  ) {
    hasSearch.value = false;
    cardInfo.value.content= []
    return;
  }
  const params = {
    // ...query.value,
    ...query.value,
    // page:page.pageNum,
    // size:page.pageSize,
  };
  manualIntervention.geturlBlackInfo(params).then((res: any) => {
    cardInfo.value = {
      content: res.data.map((item: any) => ({
        ...item,
        short:
          item?.content?.length > 100
            ? item.content.slice(0, 100)
            : item.content,
        hasMore: item?.content?.length > 100,
        isShort: true,
      })),
      totalElements: 1,
    };
  });
};
// 计算表格高度
const mediaHeight = () => {
  const contextHeight = proxy.$refs["table-page-wrapper"]?.offsetHeight || 0;
  const queryHeight = proxy.$refs["custom-query"]?.offsetHeight || 0;
  const headerHeight = proxy.$refs["custom-header"]?.offsetHeight || 0;
  const paginationHeight = proxy.$refs["custom-pagination"]?.offsetHeight || 0;
  contentHeight.value =
    contextHeight - (queryHeight + paginationHeight + headerHeight + 13);
};
const debounceFun = debounce(mediaHeight, 200);
onMounted(() => {
  const debounceFun = debounce(mediaHeight, 200);
  window.addEventListener("resize", debounceFun);
});
onUnmounted(() => {
  window.removeEventListener("resize", debounceFun);
});

const events = reactive({
  addAll() {
    proxy.$refs.reasonRef?.openDialog(
      cardInfo.value.content.filter((item: any) => !item.isInBlackList)
    );
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
    loadData();
  },
  reset: (obj: any) => {},
  currentChange: (val: number) => {
    page.pageNum = val;
    loadData();
  },
  sizeChange: (val: number) => {
    page.pageSize = val;
    loadData();
  },
});
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
</script>
<style scoped lang="scss">
.url-black {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px 16px;
  .content {
    height: 100%;
  }
}
</style>
