<template>
  <div class="baike-entry-intervence">
    <table-page
      ref="myTableRef"
      name="urlBlack"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :show-overflow-tooltip="false"
      operationAuth="/base/#/manual-intervention/edit"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query
            :queryItems="queryItems"
            :refreshBtn="{ show: true }"
            @search="events.search"
            @reset="events.reset"
          />
          <my-operation>
            <template #buttonGroup>
              <my-button type="primary" plain @click="events.refresh"
                >刷新</my-button
              >
              <my-button type="add" @click="events.add" operationAuth="/base/#/manual-intervention/edit">新建</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #operate="{ row }">
        <my-button
          v-if="row.status !== '1'"
          link
          type="primary"
          @click="events.log(row)"
        >
          日志
        </my-button>
        <my-button
          link
          :type="row.status == '1' ? 'danger' : 'primary'"
          @click="events.line(row)"
          operationAuth="/base/#/manual-intervention/edit"
        >
          {{ row.status == "1" ? "失效" : "生效" }}
        </my-button>
        <my-button link type="danger" @click="events.delete(row)" operationAuth="/base/#/manual-intervention/edit">
          删除
        </my-button>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as manualIntervention from "@/api/manual-intervention";
import { getTemplateList } from "@/api/mock";
import { computed } from "vue";
import useStore from "@/store";
const { word, api } = useStore();
const { $app, proxy, $router } = useCtx();
const routeName = "manual-intervention";
const props = defineProps({
  statusList: { type: Array },
});
//列配置
const columns = ref([
  {
    prop: "docId",
    label: "docId",
    width: 210,
    withCopy: true,
  },
  {
    prop: "title",
    label: "title",
    width: 200,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        record.url && window.open(record.url, "_blank");
      },
    },
  },
  {
    prop: "short_content",
    label: "content",
    minWidth: 200,
  },
  {
    prop: "url",
    label: "url",
    minWidth: 200,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        record.url && window.open(record.url, "_blank");
      },
    },
  },
  {
    prop: "idxName",
    label: "所属索引库",
    minWidth: 120,
  },
  {
    prop: "regionName",
    label: "所属环境",
    minWidth: 120,
  },

  {
    prop: "reason",
    label: "下线原因",
    minWidth: 150,
  },
  {
    prop: "status",
    label: "规则状态",
    minWidth: 120,
    custom: "status",
    customRender: {
      options: [
        { value: 0, label: "失效", type: "danger" },
        { value: 1, label: "生效", type: "primary" },
      ],
    },
  },
  {
    prop: "lastModifiedBy",
    label: "更新人",
    minWidth: 100,
  },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 120 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  {
    prop: "operation",
    label: "操作",
    width: 160,
    fixed: "right",
    slotName: "operate",
  },
]);
//查询面板
const query = ref<any>({
  regionCodes: [],
});
const queryItems = ref<any>({
  url: {
    type: "input",
    label: "url",
    width: 120,
    modelValue: "",
    attrs: {
      placeholder: "url",
    },
  },

  docId: {
    type: "input",
    label: "docId",
    width: 140,
    modelValue: "",
    attrs: {
      placeholder: "docId",
    },
  },
  title: {
    type: "input",
    label: "title",
    width: 140,
    modelValue: "",
    attrs: {
      placeholder: "title",
    },
  },
  regionCodes: {
    modelValue: [],
    type: "select",
    label: "环境",
    width: "180px",
    options: [],
    attrs: {
      placeholder: "环境",
      multiple: true,
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size, sort } = data;
  const params = {
    ...data,
  };
  return new Promise((resolve: any) => {
    manualIntervention.geturlBlackPage(params).then((result) => {
      result.content = result.content.map((item: any) => ({
        ...item,
        short_content:
          item?.content?.length > 100
            ? item?.content.slice(0, 100) + "..."
            : item?.content,
        hasMore: item?.content?.length > 100,
        isShort: true,
      }));
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = !dataC.isEmpty(x.createdDate)
      ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate)
      ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    return x;
  });
};
//事件列表
const events = reactive({
  log: (record: any) => {
    $router.push({
      name: `manual-intervention::url-black-log`,
      query: {
        id: record.id,
        metaLabel: [record.docId],
      },
    });
  },
  add: () => {
    $router.push({
      name: `manual-intervention::url-black-add`,
      query: {
        metaLabel: ["新建url黑名单"],
      },
    });
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.title}?`,
      })
      .then(() => {
        manualIntervention.deleteUrlBlackPage(record).then(() => {
          loadList();
          $app.$message.success(`删除 ${record.title} 成功`);
        });
      });
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  line: (record: any) => {
    $app
      .$confirm({
        title: `您确认要${record.status == "1" ? "失效" : "生效"}“${
          record.title
        }”吗?`,
      })
      .then(() => {
        manualIntervention
          .updateURLBlackStatus(record.id, {
            status: record.status == "0" ? "1" : "0",
            regionCode: record.regionCode,
          })
          .then(() => {
            $app.$message.success(
              `${record.status == "1" ? "失效" : "生效"}成功`
            );
            loadList();
          });
      });
  },
  refresh: () => {
    manualIntervention.getUrlRefresh().then(() => {
      $app.$message.success("刷新成功");
    });
  },
});
const updateEnvirenment = () => {
  // columns.value.
  word.getAreaList().then((values: any) => {
    queryItems.value.regionCodes.options = values.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
    }));
  });
};
updateEnvirenment();
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
  columns,
  queryItems,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>
