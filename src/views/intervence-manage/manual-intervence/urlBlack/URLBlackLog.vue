<template>
  <div class="baike-entry-intervence">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :show-overflow-tooltip="false"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      :withSort="false"
      :withPagination="false"
    >
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as manualIntervention from "@/api/manual-intervention";
import { getTemplateList } from "@/api/mock";
import { computed } from "vue";
const { $app, proxy, $router } = useCtx();
const routeName = "manual-intervention";
const props = defineProps({
  statusList: { type: Array },
});
//列配置
const columns = ref([
  {
    prop: "docId",
    label: "docId",
    width: 200,
  },
  {
    prop: "title",
    label: "title",
    width: 200,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        record.url&&window.open(record.url, "_blank");
      },
    },
  },
  {
    prop: "short_content",
    label: "content",
    minWidth: 200,
  },
  {
    prop: "url",
    label: "url",
    minWidth: 200,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        record.url&&window.open(record.url, "_blank");
      },
    },
  },
  {
    prop: "idxName",
    label: "所属索引库",
    minWidth: 120,
  },
  {
    prop: "regionName",
    label: "所属环境",
    minWidth: 120,
  },
  {
    prop: "reason",
    label: "下线原因",
    minWidth: 150,
  },
  {
    prop: "optDesc",
    label: "操作",
    minWidth: 100,
  },
  {
    prop: "lastModifiedBy",
    label: "操作用户",
    minWidth: 100,
  },
  { prop: "lastModifiedDateRender", label: "操作时间", width: 180 },
]);

//列表查询
const loadListData = (data: any) => {
  const { page, size, sort } = data;
  const params = {
    id: $app.$route.query.id,
    ...data,
  };
  return new Promise((resolve: any) => {
    manualIntervention
      .getUrlBlackLog(
        $app.$route.query.id,
        params,
      )
      .then((result) => {
        result.data = result.data.map((item: any) => ({
          ...item,
          short_content:
            item?.content?.length > 100
              ? item?.content.slice(0, 100) + "..."
              : item?.content,
          hasMore: item?.content?.length > 100,
          isShort: true,
        }));
        resolve(result);
      });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = !dataC.isEmpty(x.createdDate)
      ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate)
      ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss")
      : "";
    return x;
  });
};

const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data", "version-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style scoped lang="scss">
.baike-entry-intervence {
  height: 100%;
}
</style>
