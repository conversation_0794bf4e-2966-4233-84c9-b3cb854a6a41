<template>
  <my-drawer
    class="mock-add"
    v-model="dialogVisible"
    title="请填写原因，确认是否下线该数据"
    @confirm="handleConfirm"
    @close="handleClose"
  >
    <my-form
      ref="formRef"
      :rules="rules"
      :ruleForm="ruleForm"
      :formItems="formItems"
      label-width="60"
    >
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import * as manualInterventionApi from "@/api/manual-intervention";
import { getIdxDbInstListPage } from "@/api/idx-db";

import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const { t } = useI18n();
const props = defineProps({
  statusList: { type: Array },
  areaList: { type: Array },
  idxList: { type: Array },
});
const form = reactive({
  reason: [],
  remark: "",
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  remark: "",
  reson: "",
  docList: [],
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  domain: [
    {
      required: true,
      trigger: "blur",
      message: "请输入domain",
    },
  ],
});
const resonList = [
  { label: "回答不是最新信息", value: "回答不是最新信息" },
  { label: "来源不可靠", value: "来源不可靠" },
  { label: "包含成人或者低俗信息", value: "包含成人或者低俗信息" },
  { label: "网页打不开", value: "网页打不开" },
  { label: "网页乱码", value: "网页乱码" },
  { label: "内容质量低", value: "内容质量低" },
  { label: "内容有错误", value: "内容有错误" },
  { label: "内容敏感", value: "内容敏感" },
  { label: "其他", value: "其他" },
];
// 表单项
const formItems = ref<any>({
  reason: {
    label: "",
    type: "checkbox",
    options: resonList,
  },
  remark: {
    label: "",
    type: "textarea",
    attrs: {
      maxlength: 255,
      placeholder:'下线原因补充描述'
    },
  },
});

const dialogTitle = computed(() => {
  return formType.value === "add" ? t("btn.new") : t("btn.edit");
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

const openDialog = async (row: any) => {
  dialogVisible.value = true;
  // 3. 回显相关的操作
  nextTick(() => {
    ruleForm.value = assign(pick({ docList: row }, keys(defaultForm))); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
  });
};
const emits = defineEmits(["save-data"]);
const submit = (form: any) => {
  const params = {
    ...form,
    reason: (form.reason || []).join(","),
  };

  // 接口相关业务代码，执行完成后关闭弹窗
  manualInterventionApi.addUrlBlack(params).then(() => {
    $app.$message.success("加入黑名单成功");
    emits("save-data");
    handleClose();
  });
};

defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
::v-deep {
  .el-checkbox-group {
    .el-checkbox {
      display: block;
    }
  }
}
</style>
