<template>
  <my-drawer
    class="idx-db-tem-edit"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
    size="800"
    :showConfirm="!isRead"
  >
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" :disabled="isRead" label-width="110">
      <template #vCfgFields>
        <el-col>
          <el-row :gutter="10" class="item-row">
            <template v-for="(item, index) in ruleForm.vCfgFields">
              <el-col v-if="index > 0" :span="5" style="margin-bottom: 10px">
                <el-form-item
                  label=""
                  label-width="0"
                  :prop="`vCfgFields.${index}.concatString`"
                  :rules="{ required: true, message: '必选', trigger: 'change' }"
                >
                  <el-select v-model="item.concatString" placeholder="请选择拼接符">
                    <el-option v-for="(item1, index1) in modelValue.concatStringList" :label="item1.label" :value="item1.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="7" style="margin-bottom: 10px">
                <el-form-item label="" label-width="0" :prop="`vCfgFields.${index}.field`" :rules="{ required: true, message: '必选', trigger: 'change' }">
                  <el-select v-model="item.field" placeholder="请选择向量字段">
                    <el-option
                      v-for="(item1, index1) in vCfgFieldList"
                      :label="item1.label"
                      :value="item1.value"
                      :disabled="getVCfgFieldDisabled(item1, item.field)"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="3">
              <el-button v-if="ruleForm.vCfgFields.length > 1" link type="danger" @click="events.removeVCfgField()" style="margin-left: 10px">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button v-if="ruleForm.vCfgFields.length < vCfgFieldList.length" link type="primary" @click="events.insertVCfgField" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
      <template #tCfgFields>
        <el-button link type="primary" @click="events.insertTCfgField" v-if="dataC.isEmpty(ruleForm.tCfgFields)">
          <el-icon size="18px">
            <CirclePlus />
          </el-icon>
        </el-button>
        <el-col>
          <el-row :gutter="10" v-for="(item, index) in ruleForm.tCfgFields" class="item-row">
            <el-col :span="9">
              <el-form-item label="" label-width="0" :prop="`tCfgFields.${index}.field`" :rules="{ required: true, message: '必选', trigger: 'change' }">
                <el-select v-model="item.field" placeholder="请选择分词字段">
                  <el-option
                    v-for="(item1, index1) in tCfgFieldList"
                    :label="item1.label"
                    :value="item1.value"
                    :disabled="getTCfgFieldDisabled(item1, item.field)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="" label-width="0" :prop="`tCfgFields.${index}.maxLen`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-input v-model="item.maxLen" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" placeholder="请输入截断长度"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="" label-width="0" :prop="`tCfgFields.${index}.defaultWeight`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-input
                  v-model="item.defaultWeight"
                  oninput="value=value.replace(/[^\d.]|^0+(?=\d)|\.(?=.*\.)/g, '').replace(/^\./g, '0.')"
                  placeholder="请输入权重"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button link type="danger" @click="events.removeTCfgField(index)" style="margin-left: 10px">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button link type="primary" @click="events.insertTCfgField" v-if="ruleForm.tCfgFields.length == index + 1" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
      <template #filterFields>
        <el-col>
          <el-row :gutter="10" v-for="(item, index) in ruleForm.filterFields" class="item-row">
            <el-col :span="14">
              <el-form-item label="" label-width="0" :prop="`filterFields.${index}.field`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-select v-model="item.field" placeholder="请选择" filterable :disabled="item.preExist" @change="events.handleFilterFieldChange(item)">
                  <el-option
                    v-for="(item1, index1) in modelValue.filterFieldList"
                    :label="`${item1.label}(${item1.value})`"
                    :value="item1.value"
                    :disabled="getFilterFieldDisabled(item1, item.field)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item label="" label-width="0" :prop="`filterFields.${index}.type`" :rules="{ required: true, message: '必选', trigger: 'change' }">
                <el-input v-model="item.type" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-button link type="danger" @click="events.removeFilterField(index)" style="margin-left: 10px" v-show="!item.preExist">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button link type="primary" @click="events.insertFilterField" v-if="ruleForm.filterFields.length == index + 1" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import { dataC, timeC } from "turing-plugin";
import DedupConfig from "@/views/common/dedup/DedupConfig.vue";
import useCtx from "@/hooks/useCtx";
import useValidate from "@/hooks/validate";
import useStore from "@/store";
import * as idxDbApi from "@/api/idx-db";
import * as metaSiteFieldApi from "@/api/meta-site-field";

const { $app, proxy } = useCtx();
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  if (isAdd.value) {
    return "新建特征库模板";
  } else if (isUpdate.value) {
    return "编辑特征库模板";
  } else if (isRead.value) {
    return formType.value === "read" ? "查看特征库模板" : "特征库配置";
  } else {
    return "特征库模板";
  }
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  dialogVisible.value = false;
  setTimeout(() => {
    formRef.value.resetForm();
  }, 500);
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为新增
const isAdd = computed(() => {
  return formType.value === "add";
});
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
//是否为查看
const isRead = computed(() => {
  return formType.value === "read" || formType.value === "readConfig";
});
//是否为查看配置
const isReadConfig = computed(() => {
  return formType.value === "readConfig";
});
//支持分词属性列表
const tCfgFieldList = computed(() => {
  return modelValue.metaSiteFieldList
    .filter((item) => item.tokenization)
    .map((item) => {
      return { value: item.fieldName, label: `${item.name}(${item.fieldName})` };
    });
});
//支持向量属性列表
const vCfgFieldList = computed(() => {
  return modelValue.metaSiteFieldList
    .filter((item) => item.vector)
    .map((item) => {
      return { value: item.fieldName, label: `${item.name}(${item.fieldName})` };
    });
});
//向量配置是否启用
const vCfgConfigEnabled = computed({
  get() {
    return formItems.value.vCfgM.separate.enabled;
  },
  set(value) {
    formItems.value.vCfgM.separate.enabled = value;
  },
});
//分词配置是否启用
const tCfgConfigEnabled = computed({
  get() {
    return formItems.value.tCfgFields.separate.enabled;
  },
  set(value) {
    formItems.value.tCfgFields.separate.enabled = value;
  },
});
//数据项
const modelValue = reactive({
  metaSiteFieldList: [],
  filterFieldList: [],
  concatStringList: [],
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  code: "",
  description: "",
  vCfgM: 32,
  vCfgEfConstruction: 256,
  vCfgDim: 768,
  vCfgSimilarity: "l2_norm",
  vCfgQuantization: "bbq",
  vCfgFields: [],
  vCfgService: "",
  tCfgFields: [],
  tCfgService: "",
  additionalProperties: [],
  enabled: false,
};
let ruleForm = ref<any>(assign({}, defaultForm));
const { validateNameRule, validateCodeRule } = useValidate();
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, "请输入名称") }],
  code: [{ required: true, message: "编码不能为空", trigger: "blur" }],
  vCfgM: [{ required: true, message: "最近邻连接数不能为空", trigger: "blur" }],
  vCfgEfConstruction: [{ required: true, message: "候选列表大小不能为空", trigger: "blur" }],
  vCfgDim: [{ required: true, message: "向量索引维度不能为空", trigger: "blur" }],
  vCfgSimilarity: [{ required: true, message: "相似度指标不能为空", trigger: "change" }],
  vCfgQuantization: [{ required: true, message: "向量量化不能为空", trigger: "change" }],
  vCfgService: [{ required: true, message: "向量服务版本不能为空", trigger: "change" }],
  tCfgService: [{ required: true, message: "分词服务版本不能为空", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      labelWidth: 60,
      maxlength: 255,
    },
    separate: {
      title: "基础信息",
    },
    hidden: () => {
      return isReadConfig.value;
    },
    hiddenSeparate: () => {
      return isReadConfig.value;
    },
  },
  code: {
    label: "编码",
    type: "input",
    attrs: {
      disabled: isUpdate,
      labelWidth: 60,
      maxlength: 255,
    },
    hidden: () => {
      return isAdd.value || isReadConfig.value;
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: {
      labelWidth: 60,
      maxlength: 255,
    },
    hidden: () => {
      return isReadConfig.value;
    },
  },
  vCfgM: {
    label: "最近邻连接数",
    type: "inputNumber",
    attrs: {
      clearable: false,
      precision: 0,
      min: 0,
      max: 10000,
    },
    separate: {
      title: "向量配置",
      enabled: true,
    },
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  vCfgEfConstruction: {
    label: "候选列表大小",
    type: "inputNumber",
    attrs: {
      clearable: false,
      precision: 0,
      min: 0,
      max: 10000,
    },
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  vCfgDim: {
    label: "向量索引维度",
    type: "inputNumber",
    attrs: {
      clearable: false,
      precision: 0,
      min: 0,
      max: 10000,
    },
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  vCfgSimilarity: {
    label: "相似度指标",
    type: "radio",
    options: [
      { value: "l2_norm", label: "l2_norm" },
      { value: "dot_product", label: "dot_product" },
    ],
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  vCfgQuantization: {
    label: "向量量化",
    type: "switch",
    attrs: {
      activeValue: "bbq",
      inactiveValue: "false",
    },
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  vCfgFields: {
    label: "向量字段",
    type: "slot",
    slotName: "vCfgFields",
    attrs: {
      class: "required-label",
    },
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  vCfgService: {
    label: "向量服务版本",
    type: "select",
    options: [],
    hidden: () => {
      return !vCfgConfigEnabled.value;
    },
  },
  tCfgFields: {
    label: "分词字段",
    type: "slot",
    slotName: "tCfgFields",
    attrs: {
      class: "required-label",
    },
    separate: {
      title: "分词配置",
      enabled: true,
    },
    hidden: () => {
      return !tCfgConfigEnabled.value;
    },
  },
  tCfgService: {
    label: "分词服务版本",
    type: "select",
    options: [],
    hidden: () => {
      return !tCfgConfigEnabled.value;
    },
  },
  filterFields: {
    type: "slot",
    slotName: "filterFields",
    attrs: {
      class: "no-label ",
    },
    separate: {
      title: "字段配置",
    },
  },
  additionalProperties: {
    type: "select",
    options: [{ label: "原始正文", value: "readableContent" }],
    attrs: {
      multiple: true,
      class: "no-label",
      placeholder: "请选择附加字段",
    },
    separate: {
      title: "附加字段",
    },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  //刷新select数据源
  refreshPageData();
  //设置页面表单
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (isUpdate.value || isRead.value) {
      let vectorConfigItem = undefined;
      let vectorConfigFields = undefined;
      let tokenizationConfigArr = undefined;
      let tokenizationConfigService = undefined;
      //因为索引库实例查询时不会返还vectorConfig属性，当enabled为false时
      if ("vectorConfig" in row.idxConfig) {
        vectorConfigItem = row.idxConfig.vectorConfig.fields[0];
        if (typeof vectorConfigItem.calcFields[0] === "string") {
          //需要兼容以前calcField选择多个,concatString只选择一次的情况
          vectorConfigFields = vectorConfigItem.calcFields.map((item, index) => {
            return { field: item, concatString: index == 0 ? "" : vectorConfigItem.concatString };
          });
        } else {
          //最新的为calcField和concatString成对出现
          vectorConfigFields = vectorConfigItem.calcFields.map((item) => {
            return { field: item.name, concatString: item.concatString };
          });
        }
      } else {
        vectorConfigItem = {};
        vectorConfigFields = [];
      }
      //因为索引库实例查询时不会返还tokenizationConfig属性，当enabled为false时
      if ("tokenizationConfig" in row.idxConfig) {
        tokenizationConfigArr = row.idxConfig.tokenizationConfig.fields.map((item) => {
          return { field: item.calcField, maxLen: item.maxLen, defaultWeight: item.defaultWeight };
        });
        tokenizationConfigService = row.idxConfig.tokenizationConfig.fields[0]?.service || "";
      } else {
        tokenizationConfigArr = [];
        tokenizationConfigService = "";
      }
      const realForm = {
        id: row.id,
        name: row.name,
        code: row.code,
        description: row.description,
        vCfgFields: vectorConfigFields,
        vCfgM: vectorConfigItem.m,
        vCfgEfConstruction: vectorConfigItem.efConstruction,
        vCfgDim: vectorConfigItem.dim,
        vCfgSimilarity: vectorConfigItem.similarity,
        vCfgQuantization: vectorConfigItem.quantization || "false",
        vCfgService: vectorConfigItem.service,
        tCfgFields: tokenizationConfigArr,
        tCfgService: tokenizationConfigService,
        additionalProperties: row.idxConfig.additionalProperties,
        enabled: row.enabled,
      };
      ruleForm.value = assign({}, realForm);
      vCfgConfigEnabled.value = row.idxConfig.vectorConfigEnabled;
      tCfgConfigEnabled.value = row.idxConfig.tokenizationConfigEnabled;
      events.fillFilterFields(row.idxConfig.fields);
    } else {
      ruleForm.value = assign({}, defaultForm);
      vCfgConfigEnabled.value = true;
      tCfgConfigEnabled.value = true;
      events.fillVCfgFields();
      events.fillTCfgFields();
      events.fillFilterFields([]);
    }
  });
};
//向量字段selecet项不可重复选择
const getVCfgFieldDisabled = (data, current) => {
  return ruleForm.value.vCfgFields.findIndex((item) => item.field == data.value && current !== item.field) !== -1;
};
//分词字段selecet项不可重复选择
const getTCfgFieldDisabled = (data, current) => {
  return ruleForm.value.tCfgFields.findIndex((item) => item.field == data.value && current !== item.field) !== -1;
};
//过滤字段selecet项不可重复选择
const getFilterFieldDisabled = (data, current) => {
  return ruleForm.value.filterFields.findIndex((item) => item.field == data.value && current !== item.field) !== -1;
};
//提交数据
const submit = (form: any) => {
  //向量配置
  if (vCfgConfigEnabled.value && form.vCfgFields.length == 0) {
    $app.$message.warning("向量配置不可为空！");
    return;
  }
  const vectorConfigArr = [];
  const vectorConfigItem = {
    calcFields: form.vCfgFields.map((item) => {
      return { name: item.field, concatString: item.concatString };
    }),
    m: form.vCfgM,
    efConstruction: form.vCfgEfConstruction,
    dim: form.vCfgDim,
    similarity: form.vCfgSimilarity,
    concatString: form.vCfgConcatString,
    service: form.vCfgService,
  };
  //向量量化如果没有千万别传到后端(虽然我觉得应该让后端去做兼容处理)
  if (form.vCfgQuantization != "false") {
    vectorConfigItem.quantization = form.vCfgQuantization;
  }
  //分词配置
  vectorConfigArr.push(vectorConfigItem);
  if (tCfgConfigEnabled.value && form.tCfgFields.length == 0) {
    $app.$message.warning("分词配置不可为空！");
    return;
  }
  const tokenizationConfigArr = form.tCfgFields.map((item) => {
    return { calcField: item.field, maxLen: item.maxLen, defaultWeight: item.defaultWeight, service: form.tCfgService };
  });
  //传到后端的表单
  const realForm = {
    id: form.id,
    name: form.name.trim(),
    code: form.code,
    description: form.description,
    idxConfig: {
      fields: form.filterFields,
      vectorConfig: { fields: vectorConfigArr },
      vectorConfigEnabled: vCfgConfigEnabled.value,
      tokenizationConfig: { fields: tokenizationConfigArr },
      tokenizationConfigEnabled: tCfgConfigEnabled.value,
      additionalProperties: form.additionalProperties,
    },
    enabled: form.enabled,
  };
  if (isUpdate.value) {
    idxDbApi.modifyIdxDbTem(realForm).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    idxDbApi.insertIdxDbTem(realForm).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//事件列表
const events = reactive({
  insertVCfgField: () => {
    ruleForm.value.vCfgFields.push({
      field: "",
      concatString: "",
    });
  },
  removeVCfgField: () => {
    ruleForm.value.vCfgFields.splice(ruleForm.value.vCfgFields.length - 1, 1);
  },
  fillVCfgFields: () => {
    ruleForm.value.vCfgFields.splice(0, ruleForm.value.vCfgFields.length);
    events.insertVCfgField();
    events.insertVCfgField();
  },
  insertTCfgField: () => {
    ruleForm.value.tCfgFields.push({
      field: "",
      maxLen: "",
      defaultWeight: "",
    });
  },
  removeTCfgField: (index) => {
    ruleForm.value.tCfgFields.splice(index, 1);
  },
  fillTCfgFields: () => {
    ruleForm.value.tCfgFields.splice(0, ruleForm.value.tCfgFields.length);
    events.insertTCfgField();
  },
  insertFilterField: () => {
    ruleForm.value.filterFields.push({
      field: "",
      type: "",
    });
  },
  removeFilterField: (index) => {
    ruleForm.value.filterFields.splice(index, 1);
  },
  fillFilterFields: (curFields: Array) => {
    //模板写死的几个属性
    ruleForm.value.filterFields = [
      { field: "_id", type: "long", preExist: true },
      { field: "domain", type: "string", preExist: true },
      { field: "s", type: "int", preExist: true },
    ];
    const list = ruleForm.value.filterFields.map((item) => {
      return item.field;
    });
    if (curFields.length > 0) {
      curFields.map((item) => {
        if (!list.includes(item.field)) {
          ruleForm.value.filterFields.push(item);
        }
      });
    } else {
      modelValue.filterFieldList.map((item) => {
        if (!list.includes(item.value)) {
          if (item.value.startsWith("L")) {
            ruleForm.value.filterFields.push({
              field: item.value,
              type: "string",
            });
          } else {
            ruleForm.value.filterFields.push({
              field: item.value,
              type: item.type,
            });
          }
        }
      });
    }
  },
  handleFilterFieldChange: (item) => {
    item.type = dataC.getItemByValue(modelValue.filterFieldList, item.field, "value")["type"];
  },
});
//初始化属性元数据信息
const getMetaSiteFieldList = () => {
  api.getMetaSiteFieldList().then((result) => {
    modelValue.metaSiteFieldList = result;
  });
};
//初始化向量服务版本
const getMetaDictEmbeddingList = () => {
  api.getMetaDictList("ai-embedding").then((result) => {
    formItems.value.vCfgService.options = [
      ...result.map((item) => ({
        label: item.name,
        value: item.code,
      })),
    ];
  });
};
//初始化向量拼接符
const getMetaDictConcatStringList = () => {
  api.getMetaDictList("embedding-concat-string").then((result) => {
    modelValue.concatStringList = [
      ...result.map((item) => ({
        label: item.name,
        value: item.code,
      })),
    ];
  });
};
//初始化分词服务版本
const getMetaDictSegList = () => {
  api.getMetaDictList("ai-seg").then((result) => {
    formItems.value.tCfgService.options = [
      ...result.map((item) => ({
        label: item.name,
        value: item.code,
      })),
    ];
  });
};
//初始化过滤属性
const getFilterFieldList = () => {
  metaSiteFieldApi.getFilterFieldList().then((result) => {
    modelValue.filterFieldList = result.data;
  });
};
//初始化
onMounted(() => {
  refreshPageData();
});
const refreshPageData = () => {
  getMetaSiteFieldList();
  getMetaDictEmbeddingList();
  getMetaDictConcatStringList();
  getMetaDictSegList();
  getFilterFieldList();
};
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
.idx-db-tem-edit {
}
</style>
