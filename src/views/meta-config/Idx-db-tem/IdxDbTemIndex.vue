<template>
  <div class="idx-db-tem">
    <idx-db-tem-edit ref="idxDbTemEditRef" @save-data="events.loadList"></idx-db-tem-edit>
    <idx-db-tem-table ref="idxDbTemTableRef" @edit-data="events.openWindow"></idx-db-tem-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from "vue";
import idxDbTemTable from "./IdxDbTemTable.vue";
import idxDbTemEdit from "./IdxDbTemEdit.vue";
import useCtx from "@/hooks/useCtx";

const { $app, proxy, $router } = useCtx();

const events = reactive({
  loadList: () => {
    proxy.$refs["idxDbTemTableRef"].loadList();
  },
  openWindow: (type: string, item: any) => {
    proxy.$refs["idxDbTemEditRef"].openWindow(type, item);
  },
});
</script>
<style lang="scss">
.idx-db-tem {
}
</style>
