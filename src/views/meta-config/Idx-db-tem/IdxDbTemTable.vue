<template>
  <div class="idx-db-tem-table">
    <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/idx-db-tem/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">新建特征库模板</my-button>
            </template>
          </my-operation>
        </div>
      </template>
      <template #name="scope">
        <div class="flex">
          <el-button link type="primary" @click="events.read(scope.row)" class="btn-title fold">{{ scope.row.name }}</el-button>
          <el-button link type="primary" @click="events.openDescriptionEditWindow(scope.row)" :disabled="!testAuth()">
            <el-icon>
              <Edit />
            </el-icon>
          </el-button>
        </div>
      </template>
      <template #description="scope">
        <div class="flex">
          <div class="btn-title">
            <span>{{ scope.row.description }}</span>
          </div>
          <el-button link type="primary" @click="events.openDescriptionEditWindow(scope.row)" :disabled="!testAuth()">
            <el-icon>
              <Edit />
            </el-icon>
          </el-button>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";
import * as idxDbApi from "@/api/idx-db";

const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/idx-db-tem/edit");
};
//列配置
const columns = ref([
  { prop: "name", label: "名称", slotName: "name", width: 180 },
  { prop: "code", label: "编码", width: 150, withCopy: true },
  {
    prop: "enabled",
    label: "启用状态",
    width: 120,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: true,
        inactiveValue: false,
        size: "small",
      },
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.enabled === true) {
            $app
              .$confirm({ title: `确定禁用 ${record.name}?` })
              .then(() => {
                idxDbApi.disabledIdxDbTem(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`禁用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: `确定启用 ${record.name}?` })
              .then(() => {
                idxDbApi.enabledIdxDbTem(record.id).then((result) => {
                  loadList();
                  resolve(true);
                  $app.$message.success(`启用 ${record.name} 成功`);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },
  { prop: "description", label: "描述", slotName: "description", minWidth: 400 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 180, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "名称 或 编码",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    idxDbApi.getIdxDbTemListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.splitConfig = x.idxConfig.splitConfig;
    x.tokenizeConfig = x.idxConfig.tokenizeConfig;
    x.vectorConfigFields = x.idxConfig.vectorConfig.fields;
    x.vectorConfigVersion = x.idxConfig.vectorConfig.version;
    x.vectorDbId = x.idxConfig.vectorDbId;
    x.filterConfig = x.idxConfig.filterConfig;
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [
  { type: "copy", label: "复制模板" },
  {
    type: "edit",
    label: "编辑",
    disabled: (data: any) => data.enabled == true,
    disabledTips: (data: any) => {
      if (data.enabled == true) {
        return "已启用，不可编辑";
      }
      return "不可删除";
    },
  },
  {
    type: "delete",
    label: "删除",
    btnType: "danger",
    disabled: (data: any) => data.enabled == true,
    disabledTips: (data: any) => {
      if (data.enabled == true) {
        return "已启用，不可删除";
      }
      return "不可删除";
    },
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  add: () => {
    emit("edit-data", "add", {});
  },
  copy: (record: any) => {
    $app.$confirm({ title: `确定复制 ${record.name}?` }).then(() => {
      idxDbApi.copyIdxDbTem(record.id).then((result) => {
        loadList();
        $app.$message.success(`复制 ${record.name} 成功`);
      });
    });
  },
  read: (record: any) => {
    if (testAuth() && !record.enabled) {
      emit("edit-data", "edit", record);
    } else {
      emit("edit-data", "read", record);
    }
  },
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除 ${record.name}?`,
      })
      .then(() => {
        idxDbApi.removeIdxDbTem(record.id).then((result) => {
          loadList();
          $app.$message.success(`删除 ${record.name} 成功`);
        });
      });
  },
  openDescriptionEditWindow(record: any) {
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(record: any) {
    idxDbApi.modifyIdxDbTemBase(record).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss">
.idx-db-tem-table {
  height: 100%;

  .btn-title {
    width: calc(100% - 30px);
    text-align: left;
    margin: 0 5px;

    > span {
      width: 100%;
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .btn-title.fold {
    font-weight: bold;
  }

  .btn-title.el-button + .el-button {
    margin-left: 0;
  }
}
</style>
