<template>
  <my-drawer class="component-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE, CODE_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import { saveCopm, editCopm, getComponentCategoryList } from "@/api/comp";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";

const { t } = useI18n();
const { $app } = useCtx();

const emits = defineEmits(["reload"]);
const dialogTitle = computed(() => {
  return (formType.value === "add" ? t("btn.new") : t("btn.edit")) + "组件";
});
const isUpdate = computed(() => formType.value === "edit");

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
  getCategoryList();
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const func = isUpdate.value ? editCopm : saveCopm;
      const formData = cloneDeep(ruleForm.value);
      formData.system = formData.system?.length > 1 ? -3 : formData.system[0];
      func(formData).then((res) => {
        $app.$message.success(isUpdate.value ? "组件编辑成功" : "组件新建成功");
        handleClose();
        emits("reload");
      });
    }
  });
};

/* 校验 */
const { validateNameRule, validateCodeRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "",
  code: "",
  system: [-1],
  labelName: "",
  desc: "",
  components: []
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, "请输入名称") },{ min: 2, max: 100, message: '长度需要在 2 到 100之间', trigger: 'blur' }],
  code: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, "请输入编码") },{ min: 2, max: 100, message: '长度需要在 2 到 100之间', trigger: 'blur' }],
  system: [{ required: true, message: "请选择类型", trigger: "change" }],
  labelName: [{ required: true, message: "请输入分类", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "组件名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: NAME_RULE,
    },
  },
  code: {
    label: "组件编码",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: CODE_RULE,
      disabled: computed(() => isUpdate.value),
    },
  },
  system: {
    label: "类型",
    type: "checkbox",
    options: [
      { value: -1, label: "场景策略" },
      { value: -2, label: "产品方案" },
    ],
  },
  labelName: {
    label: "分类",
    type: "select",
    options: [],
    attrs: {
      allowCreate: true,
    }
  },
  desc: {
    label: "组件描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});

const openDialog = async (type: string, row: any) => {
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
      ruleForm.value.system = row.system == -3 ? [-1, -2] : [row.system];
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  });
};
//获取分类列表
const getCategoryList = async () => {
  const labels = await getComponentCategoryList();
  formItems.value.labelName.options = labels.data.map((item) => {
    return {
      label: item,
      value: item
    }
  });
}
//初始化
onMounted(async () => {
  getCategoryList();
});
//暴露接口
defineExpose({ openDialog });
</script>

<style lang="scss"></style>
