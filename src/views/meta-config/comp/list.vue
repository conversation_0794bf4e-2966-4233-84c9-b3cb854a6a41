<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="component-list">  
      <table-page
        ref="myTableRef"
        :query="query"
        :columns="columns"
        :operations="operations"
        :loadDataApi="getCompList"
        operationAuth="/base/#/component/edit"
        :transformListData="transformListData"
        @operation="handleOperation">
        <!-- 查询 + 操作插槽内容 -->
        <template #query>
          <div class="flexBetween">
            <my-query 
              :refresh-btn="{ show: true }"
              :queryItems="queryItems"
              @search="events.searchQuery"/>
            <my-operation style="margin-bottom: 12px; display: flex; justify-content: flex-end;">
              <template #buttonGroup>
                <my-button type="add" @click="events.add" operationAuth="/base/#/component/edit">新建组件</my-button>
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
    </div>
    <AddDialog ref="addRef" @reload="loadList" />
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { assign } from 'lodash'
import { timeC } from 'turing-plugin'
import { useI18n } from "vue-i18n"
import useCtx from "@/hooks/useCtx";
import AddDialog from "./add.vue";
import { getCompList, deleteComp } from '@/api/comp'
import {systemMap} from "@/views/common/common.ts";

const { t } = useI18n();
const { $app, proxy, $router } = useCtx();
const routeName = 'component'

/* 查询 */
const query = ref<any>({})
const queryItems = ref<any>({
  name: {
    type: 'input',
    width: '240px',
    modelValue: '',
    attrs: {
      placeholder: '名称'
    }
  },
  code: {
    type: 'input',
    width: '240px',
    modelValue: '',
    attrs: {
      placeholder: '编码'
    }
  }
})

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "nameRender", 
    label: "组件名称", 
    width: 180,
    blod: true,
    custom: 'link',
    customRender: {
      click: (record: any) => {
        events.toVersion(record)
      }
    }
  },
  // 文本可复制
  { prop: "code", label: "编码", withCopy: true, width: 180, showOverflowTooltip: false},
  { prop: "systemRender", label: "类型", width: 150 },
  { prop: "labelName", label: "分类", width: 150 },
  { prop: "desc", label: "描述", minWidth: 280},
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDate", label: "更新时间", width: 160 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDate", label: "创建时间", width: 160 },
  { prop: "operation", label: '操作', width: 110, fixed: 'right' }
]);
const operations = [
  { type: "edit", label: t("btn.edit") },
  { type: "delete", label: t("btn.delete"), btnType: "danger" }
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    proxy.$refs.addRef?.openDialog("edit", record);
  } else if (type === "delete") {
    events.delete(record)
  } else if (type === "version") {
    events.toVersion(record)
  }
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}`
    x.systemRender = x.system === -3 ? "场景策略、产品方案" : systemMap[x.system]
    if (x.versionNo) x.nameRender += `(${x.versionNo})`
    x.createdDate = timeC.format(x.createdDate, 'YYYY-MM-DD hh:mm:ss')
    x.lastModifiedDate = timeC.format(x.lastModifiedDate, 'YYYY-MM-DD hh:mm:ss')
    return x;
  });
};

/* events */
const events = reactive({
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj)
  },
  resetQuery: (obj: any) => {
    for (let key in obj) {
      queryItems.value[key].modelValue = obj[key].modelValue
    } 
  },
  delete: (record: any) => {
    $app.$deleteConfirm({
      title: t('tip.deleteConfirmTitle', {t: '组件', n: record.name})
    }).then(() => {
      deleteComp(record.id).then(() => {
        $app.$message.success("删除组件成功！");
        loadList()
      })
    })
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  toVersion: (record: any) => {
    $router.push({
      name: `${routeName}::version`,
      params: { id: record.id },
      query: { metaLabel: record.name }
    })
  }
})

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
</script>

<style lang="scss" scoped>
</style>