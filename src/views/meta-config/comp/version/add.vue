<template>
  <FullscreenPage :title="!isUpdate ? '创建组件版本' : $query.copy ? '复制组件版本' : $query.disabled ? '查看组件版本' : '编辑组件版本'">
    <div class="component-version-add">
      <div class="form-wrapper">
        <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
          <!-- 自定义插槽内容 -->
          <template #inputArgs>
            <param-table v-model="ruleForm.inputArgs" :disabled="readOnly" @saveComp="handleConfirm(false)" />
          </template>
          <template #outputArgs>
            <param-table v-model="ruleForm.outputArgs" :isOutput="true" :disabled="readOnly" />
          </template>
        </my-form>
      </div>
      <div class="btns-footer">
        <my-button @click="handleCancel">{{ $t("btn.cancel") }}</my-button>
        <my-button v-if="!readOnly && testAuth()" type="primary" @click="handleConfirm">{{ $t("btn.confirm") }}</my-button>
      </div>
    </div>
  </FullscreenPage>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import paramTable from "./paramTable.vue";
import { getCompDetail, saveCopmVerison } from "@/api/comp";
import useCtx from "@/hooks/useCtx";
import FullscreenPage from "@/components/layout/FullscreenPage2.vue";
const { $app, $router, $route, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/component/edit");
}

const $id = $route.params.id as string;
const $query = $route.query;
const isUpdate = computed(() => !!$query.vid);
const readOnly = ref($query.disabled === "false");

const compDetail = ref<any>({});
const getDetail = () => {
  getCompDetail($id).then((res: any) => {
    compDetail.value = res || {};
    isUpdate.value && initForm($query.copy);
  });
};

// 表单相关
const formWidth = "600px";
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "", // 版本名称
  desc: "", // 版本描述
  serviceName: "", // 服务坐标
  protocol: "", // 接口协议
  url: "", // 接口地址
  inputArgs: [], // 入参
  outputArgs: [], // 出参
  disabled: true, // 草稿
};

let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", message: "请输入版本名称" }],
  // desc: [{ required: true, trigger: "blur", message: '请输入版本描述' }],
  serviceName: [{ required: true, trigger: "blur", message: "请输入服务坐标" }],
  protocol: [{ required: true, trigger: "blur", message: "请输入接口协议" }],
  url: [{ required: true, trigger: "blur", message: "请输入接口地址" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "版本名称",
    type: "input",
    width: formWidth,
    disabled: () => readOnly.value,
    separate: {
      title: "基础信息",
    },
    attrs: {
      placeholder: "请输入版本名称，示例：领域名称",
      maxlength: 255,
    },
  },
  desc: {
    label: "描述",
    type: "textarea",
    width: formWidth,
    disabled: () => readOnly.value,
    attrs: {
      maxlength: 255,
    },
  },
  serviceName: {
    label: "服务坐标",
    type: "input",
    width: formWidth,
    disabled: () => readOnly.value,
    separate: { title: "接口参数" },
    attrs: {
      placeholder: "请输入服务坐标，示例：tlb:ai-domain-v2",
      maxlength: 255,
    },
  },
  protocol: {
    label: "接口协议",
    type: "select",
    disabled: () => readOnly.value,
    options: [
      { value: "https", label: "https" },
      { value: "http", label: "http" },
      { value: "wss", label: "wss" },
      { value: "grpc", label: "grpc" },
      { value: "mq", label: "mq" },
    ],
    width: formWidth,
  },
  url: {
    label: "接口地址",
    type: "input",
    disabled: () => readOnly.value,
    width: formWidth,
    attrs: {
      placeholder: "请输入接口地址，示例：/ai-domain/api/v2",
      maxlength: 255,
    },
  },
  inputArgs: {
    type: "slot",
    slotName: "inputArgs",
    attrs: { class: "no-label" },
    separate: { title: "入参" },
  },
  outputArgs: {
    type: "slot",
    slotName: "outputArgs",
    attrs: { class: "no-label" },
    separate: { title: "出参" },
  },
});

// 回显表单
const initForm = (isCopy: boolean = false) => {
  const components = compDetail.value?.components || [];
  ruleForm.value = assign(
    {},
    defaultForm,
    components.find((item: any) => item.id == $route.query.vid)
  );
  // 复制时 去除id && 状态置为草稿
  if (isCopy) {
    delete ruleForm.value.id;
    ruleForm.value.disabled = true;
  }
};

const handleConfirm = (isShowTip = true) => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      const params = {
        code: compDetail.value.code,
        ...ruleForm.value,
      };
      console.log(ruleForm.value.inputArgs,'11');
      
      if(!(ruleForm.value.inputArgs&&ruleForm.value.inputArgs.length)){
        $app.$message.warning('请输入入参')
        return
      }
      if(!(ruleForm.value.outputArgs&&ruleForm.value.outputArgs.length)){
        $app.$message.warning('请输入出参')
        return
      }
      saveCopmVerison($id, params).then(() => {
        isShowTip && $app.$message.success(isUpdate.value ? ($query.copy ? "版本复制成功" : "版本编辑成功") : "版本新建成功");
        isShowTip && $router.back();
      });
    }
  });
};

const handleCancel = () => {
  formRef.value.resetForm();
  $router.back();
};

onMounted(() => {
  getDetail();
  initForm();
});
</script>

<style lang="scss">
.component-version-add {
  height: 100%;
  .form-wrapper {
    @include calc-height(52px);
    overflow-y: auto;
    .common-part-title {
      background: #eff5ff;
      padding: 10px 24px;
      border-radius: 4px;
      &::before {
        left: 12px !important;
      }
    }
  }
  .btns-footer {
    padding: 16px 24px 4px 24px;
    text-align: right;
    border-top: 1px solid $border-color;
  }
}
</style>
