<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="compVersion-list height-adaptive">
      <el-card class="info-card">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span>基本信息</span>&nbsp;
              <!-- <el-button link type="primary" @click.native.stop="events.editComp(compDetail)">
              <el-icon size="18"><Edit /></el-icon>
            </el-button> -->
              <my-button link type="primary" @click="events.editComp(compDetail)" :stopPropagation="true" operationAuth="/base/#/component/edit"
                ><el-icon size="18"><Edit /></el-icon
              ></my-button>
            </template>
            <el-descriptions :column="2">
              <el-descriptions-item label-class-name="bold" label="组件名称：">{{ getText(compDetail.name) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="组件编码：">{{ getText(compDetail.code) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="类型：">{{ systemMap[compDetail.system] }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="所属类别：">{{ getText(compDetail.labelName) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="组件描述：">{{ getText(compDetail.desc) }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>
      <description-edit ref="descriptionEditRef" @save-data="events.modifyDescription"></description-edit>
      <el-card class="table-card" style="flex: 1">
        <div style="display: flex">
          <el-descriptions>
            <template #title>
              <span>版本信息</span>&nbsp;
              <el-button link type="primary" @click="events.searchQuery">
                <el-icon size="18"><Refresh /></el-icon>
              </el-button>
            </template>
          </el-descriptions>
          <my-button type="primary" @click="events.add" :icon="CirclePlus" style="margin-left: auto" operationAuth="/base/#/component/edit"
            >创建组件版本</my-button
          >
        </div>
        <div style="height: calc(100% - 45px)">
          <myTable
            ref="myTableRef"
            operationAuth="/base/#/component/edit"
            :tableData="tableData"
            :columns="columns"
            :operations="operations"
            :transformQuery="transformQuery"
            :transformListData="transformListData"
            @operation="handleOperation"
          >
            <template #description="scope">
              <el-button link type="primary" @click="events.openDescriptionEditWindow(scope.row)" :disabled="!testAuth()">
                <el-icon>
                  <Edit />
                </el-icon>
              </el-button>
              {{ scope.row.desc }}
            </template>
          </myTable>
        </div>
      </el-card>
    </div>
  </page-wrapper>
  <AddDialog ref="addRef" @reload="loadList" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { getText } from "@/utils/helpers";
import { getCompDetail, getCompVersionList, deleteCompVersion, saveCopmVerison } from "@/api/comp";
import useCtx from "@/hooks/useCtx";
import { Edit, CirclePlus } from "@element-plus/icons-vue";
import { systemMap } from "@/views/common/common.ts";
import AddDialog from "../add.vue";
import { timeC } from "turing-plugin";
import * as util from "@/utils/common";
import { assign, cloneDeep, debounce } from "lodash";
import { nextTick } from "vue";
import descriptionEdit from "@/views/common/DescriptionEdit.vue";

const { t } = useI18n();
const { $app, proxy, $router, $route, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/component/edit");
};
const $id = $route.params.id as string;
const routeName = "component::version";
const tableData = ref([]);
const myTableRef = ref();
const compDetail = ref<any>({});
const activeCollapse = ref([1]);

//路由返回页面刷新逻辑
watch($route, (to) => {
  if (to.name === "component::version") {
    loadList();
  }
});

/* 查询 */
const query = ref<any>({});

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 可点击项
  {
    prop: "nameRender",
    label: "版本名称",
    width: 180,
    blod: true,
    custom: "link",
    customRender: {
      click: (record: any) => {
        events.edit(record);
      },
    },
  },
  { prop: "desc", label: "描述", slotName: "description", minWidth: 200 },
  {
    prop: "disabledRender",
    label: "版本状态",
    width: 150,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        true: { type: "warning", name: "草稿" },
        false: { type: "success", name: "已发布" },
      },
    },
  },
  // { prop: "debugResult", label: "调试结论", width: 150 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 160 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 160 },
  { prop: "operation", label: "操作", width: 130, fixed: "right" },
]);
const operations = [
  { type: "edit", label: t("btn.edit"), disabled: (record: any) => !record.disabled, disabledTips: "版本已经发布，不可编辑" },
  // { type: "debug", label: '调试'},
  { type: "publish", label: "发布版本", disabled: (record: any) => !record.disabled, collapsed: true, disabledTips: "版本已经发布，不可发布版本" },
  { type: "copy", label: "复制版本", collapsed: true },
  {
    type: "delete",
    label: `${t("btn.delete")}版本`,
    btnType: "danger",
    collapsed: true,
  },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "debug") {
    window.history.pushState({}, "", `${(window as any).SYSTEM_CONFIG_BASEURL}/astrolink-ui/#/flow/detail?id=${$id}&n=${record.name}&vid=${record.id}`);
  } else {
    typeof events[type] == "function" && events[type](record);
  }
};

// 转换传参
const transformQuery = ({ ...rest }) => {
  return {
    id: $id,
    ...rest,
  };
};

// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.nameRender = `${x.name}(v${util.padNumberToDigits(x.index, 3)})`;
    x.disabledRender = `${x.disabled}`;
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

/* events */
const events = reactive<any>({
  collapseChange: (val: string[]) => {
    setTimeout(() => {
      myTableRef.value.mediaHeight();
    }, 500);
  },
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj);
    loadList();
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { t: "版本", n: record.name }),
      })
      .then(() => {
        deleteCompVersion($id, record.id).then((res: any) => {
          console.log("删除", record);
          $app.$message.success("版本删除成功！");
          loadList();
        });
      });
  },
  add: () => {
    $router.push({ name: `${routeName}::add` });
  },
  edit: (record: any) => {
    if (!$auth.testAuth("/base/#/component/edit")) {
      $router.push({
        name: `${routeName}::add`,
        query: {
          vid: record.id,
          disabled: "false",
        },
      });
    } else {
      $router.push({
        name: `${routeName}::add`,
        query: {
          vid: record.id,
          disabled: record.disabled,
        },
      });
    }
  },
  editComp: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  publish: (record: any) => {
    $app.$confirm({ title: `确定要发布版本“${record.name}”吗？` }).then(() => {
      saveCopmVerison($id, { ...record, disabled: false }).then((res) => {
        $app.$message.success("发布成功");
        loadList();
      });
    });
  },
  copy: (record: any) => {
    $router.push({
      name: `${routeName}::add`,
      query: {
        vid: record.id,
        copy: true,
      },
    });
  },
  openDescriptionEditWindow(record: any) {
    record.description = record.desc;
    proxy.$refs["descriptionEditRef"].openWindow(record);
  },
  modifyDescription(data: any, record: any) {
    record.desc = record.description;
    delete record.description;
    saveCopmVerison($id, { ...data, ...record,name: data.name, desc: data.description }).then((result) => {
      $app.$message.success("修改成功");
      proxy.$refs["descriptionEditRef"].closeWindow();
      loadList();
    });
  },
});

/* 列表刷新 */
const loadList = () => {
  getCompDetail($id).then((res: any) => {
    compDetail.value = res || {};
    tableData.value = res.components;
    nextTick(() => {
      myTableRef.value.loadData();
    });
  });
};
onMounted(() => {
  loadList();
})
</script>

<style lang="scss" scoped>
.compVersion-list {
  padding: 10px;
  .el-card {
    :deep(.el-card__body) {
      height: 100%;
    }
  }

  .info-card {
    height: auto;
    margin-bottom: 10px;

    :deep(.el-descriptions__label.bold) {
      font-weight: bold;
      background: #fff !important;
    }
  }

  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0;
  }
}
.basic-wrapper {
  background: $detail-bg;
  @include area-padding();
  border-radius: $border-radius-box;
}
.version-wrapper {
  background: $detail-bg;
}
</style>
