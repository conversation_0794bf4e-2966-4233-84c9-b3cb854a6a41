<template>
  <div class="paramTable">
    <div class="query-wrapper">
      <my-button type="primary" :disabled="disabled" plain @click="events.add"  operationAuth="/base/#/component/edit">新增参数</my-button>
    </div>
    <!-- <my-table
      :columns="columns"
      :tableData="tableData"
      :operations="operations"
      @operation="handleOperation">
      <template #typeName="scope">
        {{ argsTypeList.find(e => e.value === scope.row.type)?.label }}
      </template>
    </my-table> -->
    <!-- <div>{{ tableData }}</div> -->
    <ul class="table-header flexBox">
      <li style="width: 50px"></li>
      <li style="width: 60px; padding-left: 10px">序号</li>
      <li v-for="item in columns" :key="item.prop" class="column-item">
        {{ item.label }}
      </li>
      <li style="width: 100px">操作</li>
    </ul>
    <draggable handle=".draggable-move" :list="tableData" ghost-class="ghost" chosen-class="chosenClass" animation="300" @end="draggableEnd" :sort="!disabled">
      <template #item="{ element, index }">
        <div>
          <ul class="table-body flexBox">
            <li>
              <el-icon style="width: 50px" class="move-icon draggable-move"><Rank /></el-icon>
            </li>
            <li style="width: 60px; padding-left: 10px">{{ index + 1 }}</li>
            <li v-for="item in columns" :key="item.prop" class="column-item">
              {{ element[item.prop] }}
            </li>
            <li style="width: 100px">
              <el-button v-if="disabled||!$auth.testAuth('/base/#/component/edit')" type="primary" link @click="handleOperation({ type: 'edit', record: element, index: index })">{{ t("btn.detail") }}</el-button>
              <template v-else>
                <el-button type="primary" link @click="handleOperation({ type: 'edit', record: element, index: index })">{{ t("btn.edit") }}</el-button>
                <el-button type="danger" link @click="handleOperation({ type: 'delete', record: element, index: index })">{{ t("btn.delete") }}</el-button>
              </template>
            </li>
          </ul>
        </div>
      </template>
    </draggable>
    <paramAdd ref="paramAddRef" :readOnly="disabled" @update="events.updateTableData" :isOutput="isOutput" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import paramAdd from "@/views/common/paramComp/paramAdd.vue";
import { argsTypeList } from "@/views/common/paramComp/utils/constants";
import draggable from "vuedraggable";
import { Rank } from "@element-plus/icons-vue";

// 实现v-model
const emits = defineEmits(["update:modelValue", "saveComp"]);
const props = defineProps({
  modelValue: { type: Array as any }, // 父组件v-model绑定的值
  isOutput: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
});

const tableData = ref<Array<any>>(props.modelValue || []);
watch(
  () => props.modelValue,
  (nu) => {
    if (nu) {
      tableData.value = [...nu];
    } else {
      tableData.value = [];
    }
  }
);

const updateValue = () => {
  emits("update:modelValue", tableData.value);
};

const draggableEnd = () => {
  emits("update:modelValue", tableData.value);
  //emits("saveComp");
}

const { proxy, $app ,$auth } = useCtx();
const { t } = useI18n();
const columns = ref([
  { prop: "key", label: "参数标识" },
  { prop: "name", label: "参数名称" },
  { prop: "type", label: "参数类型", slotName: "typeName" },
  { prop: "desc", label: "参数描述" },
  { prop: "defaultValue", label: "默认值" },
  { prop: "norefer", label: "是否无引用" },
  {
    prop: "required",
    label: "是否必填",
    width: 150,
    custom: "switch",
    customRender: {
      attrs: {
        "active-value": true,
        "inactive-value": false,
        disabled: props.disabled,
      },
      beforeChange: () => {
        return new Promise((resolve: any) => {
          resolve(true);
        });
      },
    },
  },
  // { prop: "operation", label: "操作", width: 120 },
]);
const operations = [
  { type: "edit", label: props.disabled ? t("btn.detail") : t("btn.edit") },
  { type: "delete", label: t("btn.delete"), btnType: "danger", disabled: () => props.disabled },
];

const curIndex = ref<number>(0);
const handleOperation = (data: any) => {
  const { type, record, index } = data;
  if (type === "edit") {
    curIndex.value = index;
    proxy.$refs.paramAddRef.openDrawer({ ...record, norefer: record.norefer ?? false });
  } else if (type === "delete") {
    tableData.value.splice(index, 1);
    emits("update:modelValue", tableData.value);
  }
};

const events = reactive({
  add: () => {
    curIndex.value = -1;
    proxy.$refs.paramAddRef.openDrawer(null);
  },
  updateTableData: (data: any) => {
    if (Boolean(tableData.value.find((e, i) => curIndex.value !== i && e.key === data.key))) {
      return $app.$message.warning("参数标识已存在！");
    }
    if (curIndex.value === -1) {
      tableData.value.push(data);
    } else {
      tableData.value[curIndex.value] = data;
    }
    updateValue();
    proxy.$refs.paramAddRef.closeDrawer();
  },
});
</script>

<style lang="scss" scoped>
.paramTable {
  width: 100%;
  .query-wrapper {
    @include flexBetween();
    padding-bottom: 12px;
  }
}

.table-header {
  background-color: #f5f7fa;
  height: 44px;

  .column-item {
    flex: 1;
  }

  li {
    font-weight: 600;
  }
}

.table-body {
  li {
    height: 53px;
    line-height: 53px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-bottom: 1px solid #ebeef5;
  }

  .column-item {
    flex: 1;
  }

  &:hover {
    background-color: #fafafa;
  }
}

.move-icon {
  cursor: move;
}
</style>
