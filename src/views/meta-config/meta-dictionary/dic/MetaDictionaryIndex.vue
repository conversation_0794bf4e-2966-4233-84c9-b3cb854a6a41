<template>
  <div class="meta-dictionary-main">
    <MetaDictionaryTree
      ref="treeRef"
      :treeData="treeData"
      title="词典类型"
      :type="type"
      :areaInfo="areaInfo"
      @updateTree="getTreeData"
      @updateTable="getTableData"
      operationAuth="/base/#/meta-dict/edit"
    />
    <MetaDictionaryTable
      ref="tableRef"
      :treeNode="treeNode"
      :treeData="treeData"
      @updateTree="getTreeData"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
import useStore from "@/store";
import MetaDictionaryTable from "./MetaDictionaryTable.vue";
import useCtx from "@/hooks/useCtx";
import MetaDictionaryTree from "../common/MetaDictionaryTree.vue";
import * as metaWordDbApi from "@/api/meta-word";
import { computed } from "vue";
const { word } = useStore();
const treeRef = ref();
const tableRef = ref();

const treeNode = computed(()=>treeRef.value?.getCurrentNode());
const { $route } = useCtx();
const type = computed(() => {
  if ($route.path == "/meta-dict") {
    return "word";
  } else {
    return "dict";
  }
});
let treeData = ref<any>([]);
  const areaInfo = computed(()=>{
  return (treeRef.value?.areaOptions||[]).find(item=>item.code==word.area)||{}
})
const getTreeData = async (key?: any) => {
  const filterText = treeRef.value.filterText;
  const params = { name: filterText };
  const treeRes: any = await metaWordDbApi.getTree(
    type.value,
    params,
    word.area + "/"
  );
  treeData.value = treeRes?.data || [];
  treeRef.value.setCurrentKey(key||treeData.value[0].id);
};

const getTableData = () => {
  tableRef.value && tableRef.value.loadList();
};
</script>
<style scoped lang="scss">
.meta-dictionary-main {
  display: flex;
  height: 100%;
  padding: 20px;
  ::v-deep {
    .left-card {
      overflow-y: auto;
      overflow-x: hidden;
      .el-card__body{
        padding: 10px;
      }
    }
    .meta-dict-table {
      padding-left: 10px;
      width: calc(100% - 375px);
      .info-card {
        height: 100%;
        .el-card__body {
          padding: 0;
          height: 100%;
        }
      }
    }
  }
}
</style>
