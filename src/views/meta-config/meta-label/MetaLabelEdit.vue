<template>
  <my-drawer class="meta-label-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import * as metaLabelApi from "@/api/meta-label";

const { $app, proxy } = useCtx();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}等级字典`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  code: "",
  name: "",
  description: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  code: [{ required: true, message: "编码不能为空", trigger: "blur" }],
  // name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  code: {
    label: "编码",
    type: "input",
    attrs: {
      disabled: true,
      maxlength: 255,
      placeholder: "请输入编码",
    },
  },
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入名称",
    },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm));
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  });
};
//提交数据
const submit = (form: any) => {
  if (isUpdate.value) {
    if (dataC.isEmpty(form.name)) {
      form.name = undefined;
    }
    metaLabelApi.modifyMetaLabel(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  }
};
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>
