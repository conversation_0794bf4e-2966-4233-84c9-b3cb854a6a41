<template>
  <div class="meta-label">
    <meta-label-edit ref="metaLabelEditRef" @save-data="loadList"></meta-label-edit>
    <meta-label-table ref="metaLabelTableRef" @edit-data="openWindow"></meta-label-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import metaLabelTable from "./MetaLabelTable.vue";
import metaLabelEdit from "./MetaLabelEdit.vue";

const metaLabelEditRef = ref(null);
const metaLabelTableRef = ref(null);

//打开编辑窗口
const openWindow = (type: string, item: any) => {
  metaLabelEditRef.value.openWindow(type, item);
};
//查询列表数据
const loadList = () => {
  metaLabelTableRef.value.loadList();
};
</script>
<style scoped lang="scss"></style>
