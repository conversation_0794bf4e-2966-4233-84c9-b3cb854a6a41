<template>
  <div class="meta-label-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/meta-label/edit"
      :operations="operations"
      @operation="handleOperation"
      :defaultSort="{ prop: 'code', order: 'asc' }"
      :defaultPageSizes="[100, 200]"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaLabel<PERSON>pi from "@/api/meta-label";

const { $app, proxy } = useCtx();
//列配置
const columns = ref([
  { prop: "code", label: "编码", width: 80 },
  { prop: "name", label: "名称", width: 100 },
  { prop: "description", label: "描述", minWidth: 1000 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "operation", label: "操作", width: 60, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "编码 或 名称",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  return new Promise((resolve: any) => {
    metaLabelApi.getMetaLabelListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [{ type: "edit", label: "编辑" }];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    events.edit(record);
  }
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.meta-label-table {
  height: 100%;
}
</style>
