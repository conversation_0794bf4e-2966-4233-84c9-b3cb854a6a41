<template>
  <my-drawer class="meta-region-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems">
      <template #apiGateway>
        <el-button link type="primary" @click="events.insertApiGateway" v-if="ruleForm.apiGatewayList.length == 0">
          <el-icon size="18px">
            <CirclePlus />
          </el-icon>
        </el-button>
        <el-col>
          <el-row v-for="(item, index) in ruleForm.apiGatewayList" class="item-row">
            <el-col :span="20">
              <el-form-item label="" label-width="0" :prop="`apiGatewayList[${index}]`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-input v-model="ruleForm.apiGatewayList[index]"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button link type="danger" @click="events.removeApiGateway(index)" style="margin-left: 10px">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button link type="primary" @click="events.insertApiGateway" v-if="ruleForm.apiGatewayList.length == index + 1" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as metaRegionApi from "@/api/meta-region";

const { $app, proxy } = useCtx();
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}区域环境`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      if (dataC.isEmpty(ruleForm.value.apiGatewayList)) {
        $app.$message.warning("请至少增加一个网关地址!");
        return;
      }
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  code: "",
  envType: 1,
  apiGateway: "",
};
const extraForm = {
  apiGatewayList: [],
};
let ruleForm = ref<any>(assign({}, defaultForm, extraForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  code: [{ required: true, message: "编码不能为空", trigger: "blur" }],
  envType: [{ required: true, message: "验证环境不能为空", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  code: {
    label: "编码",
    type: "input",
    attrs: {
      disabled: isUpdate,
      maxlength: 255,
      placeholder: "编码",
    },
  },
  name: {
    label: "名称",
    type: "input",
    attrs: {
      disabled: isUpdate,
      maxlength: 255,
      placeholder: "请输入名称",
    },
  },
  envType: {
    label: "环境类型",
    type: "radio",
    options: [
      { value: 1, label: "验证环境" },
      { value: 2, label: "生产环境" },
    ],
  },
  apiGatewayList: {
    label: "网关地址",
    type: "slot",
    slotName: "apiGateway",
    attrs: {
      class: "required-label",
    },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(assign({}, defaultForm, extraForm)));
      ruleForm.value.apiGatewayList = !dataC.isEmpty(row.apiGateway) ? row.apiGateway.split(",") : [];
    } else {
      ruleForm.value = assign({}, defaultForm, extraForm);
      ruleForm.value.apiGatewayList = [];
      events.insertApiGateway();
    }
  });
};
//提交数据
const submit = (form: any) => {
  form.apiGateway = form.apiGatewayList.join(",");
  if (isUpdate.value) {
    metaRegionApi.modifyMetaRegion(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    metaRegionApi.insertMetaRegion(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//事件列表
const events = reactive({
  insertApiGateway: () => {
    ruleForm.value.apiGatewayList.push("");
  },
  removeApiGateway: (index) => {
    ruleForm.value.apiGatewayList.splice(index, 1);
  },
});
//初始化
onMounted(() => {});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>
