<template>
  <div class="meta-region">
    <meta-region-edit ref="metaRegionEditRef" @save-data="loadList"></meta-region-edit>
    <meta-region-table ref="metaRegionTableRef" @edit-data="openWindow"></meta-region-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import metaRegionTable from "./MetaRegionTable.vue";
import metaRegionEdit from "./MetaRegionEdit.vue";

const metaRegionEditRef = ref(null);
const metaRegionTableRef = ref(null);

//打开编辑窗口
const openWindow = (type: string, item: any) => {
  metaRegionEditRef.value.openWindow(type, item);
};
//查询列表数据
const loadList = () => {
  metaRegionTableRef.value.loadList();
};
</script>
<style scoped lang="scss"></style>
