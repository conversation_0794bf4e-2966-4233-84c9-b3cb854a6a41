<template>
  <div class="meta-region-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/meta-region/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">新建区域环境</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaRegionApi from "@/api/meta-region";

const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/meta-region/edit");
};
//列配置
const columns = ref([
  { prop: "code", label: "编码", width: 120, withCopy: true },
  { prop: "name", label: "名称", width: 120 },
  { prop: "envTypeRender", label: "环境类型", width: 120 },
  { prop: "apiGateway", label: "网关地址", minWidth: 200 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "编码 或 名称",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size } = data;
  return new Promise((resolve: any) => {
    metaRegionApi.getMetaRegionListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.envTypeRender = x.envType == 1 ? "验证环境" : x.envType == 2 ? "生产环境" : "未知类型";
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    events.edit(record);
  } else if (type == "delete") {
    events.delete(record);
  }
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  add: () => {
    emit("edit-data", "add", {});
  },
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除${record.name}吗?`,
      })
      .then(() => {
        metaRegionApi.removeMetaRegion(record.id).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.meta-region-table {
  height: 100%;
}
</style>
