<template>
  <my-drawer class="meta-label-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems"> </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { assign, pick, keys } from "lodash";
import type { FormRules } from "element-plus";
import useCtx from "@/hooks/useCtx";
import * as metaSiteFieldApi from "@/api/meta-site-field";

const { $app, proxy } = useCtx();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}站点属性`;
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
  getMetaSiteFieldCategoryList();
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  fieldName: "",
  //abbr: "",
  type: "",
  category: "",
  name: "",
  universal: false,
  vector: false,
  tokenization: false,
  filter: false,
  defaultValue: "",
  description: "",
  sample: "",
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  fieldName: [{ required: true, message: "字段不能为空", trigger: "blur" }],
  type: [{ required: true, message: "类型不能为空", trigger: "change" }],
  category: [{ required: true, message: "类别不能为空", trigger: "change" }],
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
});
// 表单项
const formItems = ref<any>({
  fieldName: {
    label: "字段",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入编码",
    },
    disabled: () => isUpdate.value,
  },
  type: {
    label: "类型",
    type: "select",
    options: [
      { value: "int", label: "int"},
      { value: "long", label: "long" },
      { value: "float32", label: "float32" },
      { value: "double", label: "double" },
      { value: "string", label: "string" },
    ],
    attrs: {
      placeholder: "请选择类型,可支持自定义类型",
      filterable: true,
      allowCreate: true,
      clearable: false,
    },
    disabled: () => isUpdate.value,
  },
  category: {
    label: "类别",
    type: "select",
    options: [],
    attrs: {
      placeholder: "请选择类别,可支持自定义类别",
      filterable: true,
      allowCreate: true,
      clearable: false,
    },
  },
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入名称",
    },
  },
  vector: {
    label: "支持向量",
    type: "switch",
  },
  tokenization: {
    label: "支持分词",
    type: "switch",
  },
  filter: {
    label: "支持过滤",
    type: "switch",
  },
  defaultValue: {
    label: "默认值",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
  sample: {
    label: "样例",
    type: "textarea",
    attrs: { maxlength: 255 },
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (type === "edit") {
      ruleForm.value = pick(row, keys(defaultForm));
    } else {
      ruleForm.value = assign({}, defaultForm);
    }
  });
};
//提交数据
const submit = (form: any) => {
  if (isUpdate.value) {
    metaSiteFieldApi.modifyMetaSiteField(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    const params = { ...form, abbr: form.fieldName };
    metaSiteFieldApi.addMetaSiteField(params).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//获取类别列表
const getMetaSiteFieldCategoryList = () => {
  metaSiteFieldApi.getMetaSiteFieldCategoryList().then((result) => {
    formItems.value.category.options = result.data.map((item) => ({
      label: item,
      value: item,
    }));
  });
};
//初始化
onMounted(() => {
  getMetaSiteFieldCategoryList();
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss"></style>
