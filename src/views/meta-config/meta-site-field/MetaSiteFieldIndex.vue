<template>
  <div class="meta-site-field">
    <meta-site-field-edit ref="metaSiteFieldEditRef" @save-data="loadList" :categoryList="categoryList"></meta-site-field-edit>
    <meta-site-field-table ref="metaSiteFieldTableRef" @edit-data="openWindow" :categoryList="categoryList"></meta-site-field-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import metaSiteFieldTable from "./MetaSiteFieldTable.vue";
import metaSiteFieldEdit from "./MetaSiteFieldEdit.vue";
import * as metaSiteFieldApi from "@/api/meta-site-field";
const metaSiteFieldEditRef = ref(null);
const metaSiteFieldTableRef = ref(null);
const categoryList = ref([])
//获取类别列表
const getMetaSiteFieldCategoryList = () => {
  metaSiteFieldApi.getMetaSiteFieldCategoryList().then((result) => {
    categoryList.value = result.data.map((item) => ({
      label: item,
      value: item,
    }));
  });
};
getMetaSiteFieldCategoryList()
//打开编辑窗口
const openWindow = (type: string, item: any) => {
  metaSiteFieldEditRef.value.openWindow(type, item);
};
//查询列表数据
const loadList = () => {
  metaSiteFieldTableRef.value.loadList();
};
</script>
<style scoped lang="scss"></style>
