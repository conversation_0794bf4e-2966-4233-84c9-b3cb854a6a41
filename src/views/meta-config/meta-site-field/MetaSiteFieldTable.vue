<template>
  <div class="meta-site-field-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/meta-site-field/edit"
      :operations="operations"
      @operation="handleOperation"
      :defaultPageSizes="[100, 200]"
      :defaultSort="{ prop: 'idx', order: 'asc' }"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" operationAuth="/base/#/meta-site-field/edit">新建</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaSiteFieldApi from "@/api/meta-site-field";
import { computed } from "vue";
import { useI18n } from "vue-i18n";
import { watch } from "vue";
const { t } = useI18n();
const { $app, proxy } = useCtx();
//列配置
const columns = ref([
  { prop: "category", label: "类别", width: 110 },
  { prop: "fieldName", label: "字段", width: 140 },
  // { prop: "abbr", label: "缩写", width: 80 },
  { prop: "type", label: "类型", width: 100 },
  { prop: "name", label: "名称", width: 120 },
  // { prop: "universalRender", label: "是否通用", width: 80 },
  { prop: "vectorRender", label: "支持向量", width: 110 },
  { prop: "tokenizationRender", label: "支持分词", width: 110 },
  { prop: "filterRender", label: "支持过滤", width: 110 },
  { prop: "defaultValue", label: "默认值", width: 120 },
  { prop: "description", label: "描述", minWidth: 200 },
  { prop: "sample", label: "样例", minWidth: 200 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
//显示的列
// const originColumns = ref([
//   { prop: "category", label: "类别", width: 110 },
//   { prop: "fieldName", label: "字段", width: 140 },
//   // { prop: "abbr", label: "缩写", width: 80 },
//   { prop: "type", label: "类型", width: 100 },
//   { prop: "name", label: "名称", width: 120 },
//   // { prop: "universalRender", label: "是否通用", width: 80 },
//   { prop: "vectorRender", label: "支持向量", width: 110 },
//   { prop: "tokenizationRender", label: "支持分词", width: 110 },
//   { prop: "filterRender", label: "支持过滤", width: 110 },
//   { prop: "defaultValue", label: "默认值", width: 120 },
//   { prop: "description", label: "描述", minWidth: 200 },
//   { prop: "sample", label: "样例", minWidth: 200 },
//   { prop: "lastModifiedBy", label: "更新人", width: 100 },
//   { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
//   { prop: "operation", label: "操作", width: 60, fixed: "right" },
// ]);
//查询面板
const query = ref<any>({});
const props = defineProps({
  categoryList: { type: Array, default: [] }, // 按钮类型
});
const isOrNot = [
  { value: true, label: "是" },
  { value: false, label: "否" },
];
const queryItems = ref<any>({
  search: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "名称 或 字段",
    },
  },
  categoryList: {
    type: "select",
    label: "",
    modelValue: [],
    options: computed(() => props.categoryList),
    attrs: {
      placeholder: "类别",
      multiple: true,
    },
  },
  vector: {
    type: "select",
    label: "",
    modelValue: [],
    options: isOrNot,
    attrs: {
      placeholder: "支持向量",
    },
  },
  tokenization: {
    type: "select",
    label: "",
    modelValue: [],
    options: isOrNot,
    attrs: {
      placeholder: "支持分词",
    },
  },
  filter: {
    type: "select",
    label: "",
    modelValue: [],
    options: isOrNot,
    attrs: {
      placeholder: "支持过滤",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size } = data;
  const params = { ...data };
  if (!dataC.isEmpty(params?.categoryList)) {
    params.categoryList = (params?.categoryList || []).join(",");
  }
  return new Promise((resolve: any) => {
    metaSiteFieldApi.getMetaSiteFieldListPage(params).then((result) => {
      const list = cloneDeep(result.data);
      const totalElements = list.length;
      const content = list.slice((page - 1) * size, page * size);
      resolve({
        content,
        totalElements,
      });
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.universalRender = x.universal ? "是" : "-";
    x.vectorRender = x.vector ? "是" : "-";
    x.tokenizationRender = x.tokenization ? "是" : "-";
    x.filterRender = x.filter ? "是" : "-";
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: t("btn.delete"), btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "edit") {
    events.edit(record);
  } else if (type == "delete") {
    events.delete(record);
  }
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  add: (record: any) => {
    emit("edit-data", "add", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要同时删除“${record.name}”属性吗？`,
      })
      .then(() => {
        metaSiteFieldApi.deleteMetaSiteField(record.id).then(() => {
          loadList();
        });
      });
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.meta-site-field-table {
  height: 100%;
}
</style>
