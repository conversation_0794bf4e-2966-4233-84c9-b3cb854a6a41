<template>
  <my-drawer
    class="meta-vector-db-edit"
    v-model="dialogVisible"
    :title="dialogTitle"
    @confirm="handleConfirm"
    @close="handleClose"
    size="800"
    :showConfirm="!isRead"
  >
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" label-width="120" :disabled="isRead">
      <template #fields>
        <el-button link type="primary" @click="events.insertFieldAll" v-if="ruleForm.fields.length == 0">
          <el-icon size="18px">
            <CirclePlus />
          </el-icon>
        </el-button>
        <el-col>
          <el-row :gutter="10" v-for="(item, index) in ruleForm.fields" class="item-row">
            <el-col :span="6">
              <el-form-item label="" label-width="0" :prop="`fields.${index}.name`" :rules="{ required: true, message: '必填', trigger: 'blur' }">
                <el-select v-model="item.name" placeholder="请选择" filterable>
                  <el-option
                    v-for="(item1, index1) in modelValue.vectorDbList"
                    :label="`${item1.label}(${item1.value})`"
                    :value="item1.value"
                    :disabled="getDisabled(item1, item.name)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="" label-width="0" :prop="`fields.${index}.type`" :rules="{ required: true, message: '必选', trigger: 'change' }">
                <el-select v-model="item.type" placeholder="请选择">
                  <el-option v-for="(item1, index1) in modelValue.fieldTypeList" :label="item1.label" :value="item1.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item
                label="主键"
                label-width="60"
                :prop="`fields.${index}.isPrimaryKey`"
                :rules="{ required: true, message: '必选', trigger: 'change' }"
              >
                <el-switch v-model="item.isPrimaryKey" @change="(val) => handleChange(val, index)" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="索引" label-width="60" :prop="`fields.${index}.needIndex`" :rules="{ required: true, message: '必选', trigger: 'change' }">
                <el-switch v-model="item.needIndex" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button link type="danger" @click="events.removeField(index)" style="margin-left: 10px">
                <el-icon size="18px">
                  <Delete />
                </el-icon>
              </el-button>
              <el-button link type="primary" @click="events.insertField" v-if="ruleForm.fields.length == index + 1" style="margin-left: 10px">
                <el-icon size="18px">
                  <CirclePlus />
                </el-icon>
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import type { FormRules } from "element-plus";
import { assign, pick, keys, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as metaVectorDbApi from "@/api/meta-vector-db";
import * as metaSiteFieldApi from "@/api/meta-site-field";

const { $app, proxy } = useCtx();
const { api } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  if (isAdd.value) {
    return "新增向量库模板";
  } else if (isUpdate.value) {
    return "编辑向量库模板";
  } else if (isRead.value) {
    return "查看向量库模板";
  } else {
    return "向量库模板";
  }
});
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      if (dataC.isEmpty(ruleForm.value.fields)) {
        $app.$message.warning("请至少增加一个字段!");
        return;
      }
      if (ruleForm.value.fields.findIndex((item) => item.isPrimaryKey == true) == -1) {
        $app.$message.warning("请至少增加一个字段主键!");
        return;
      }
      submit(ruleForm.value);
    }
  });
};
//是否为新增
const isAdd = computed(() => {
  return formType.value === "add";
});
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});
//是否为查看
const isRead = computed(() => {
  return formType.value === "read";
});
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: "",
  name: "",
  indexType: "HNSW",
  metricType: "IP",
  level: "Strong",
  M: 32,
  efConstruction: 256,
  dim: 768,
  fields: [],
  enabled: false,
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  indexType: [{ required: true, message: "向量库索引类不能为空", trigger: "change" }],
  metricType: [{ required: true, message: "度量类型不能为空", trigger: "change" }],
  level: [{ required: true, message: "一致性级别不能为空", trigger: "change" }],
  M: [
    { required: true, message: "最近邻连接数不能为空", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== undefined && value !== null && value < 0) {
          callback(new Error("最近邻连接数不能为负数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  efConstruction: [
    { required: true, message: "候选列表大小不能为空", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== undefined && value !== null && value < 0) {
          callback(new Error("候选列表大小不能为负数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  dim: [
    { required: true, message: "向量索引维度不能为空", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== undefined && value !== null && value < 0) {
          callback(new Error("向量索引维度不能为负数"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  enabled: [{ required: true, message: "是否启用不能为空", trigger: "change" }],
});
// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
    },
  },
  indexType: {
    label: "向量库索引类",
    type: "radio",
    options: [
      { value: "HNSW", label: "HNSW" },
      { value: "GPU_IVF_PQ", label: "GPU_IVF_PQ" },
      { value: "DISKANN", label: "DISKANN" },
    ],
  },
  metricType: {
    label: "度量类型",
    type: "radio",
    options: [
      { value: "L2", label: "L2" },
      { value: "COS", label: "COS" },
      { value: "IP", label: "IP" },
    ],
  },
  level: {
    label: "一致性级别",
    type: "radio",
    options: [
      { value: "Strong", label: "Strong" },
      { value: "Bound", label: "Bound" },
      { value: "Session", label: "Session" },
    ],
  },
  M: {
    label: "最近邻连接数",
    type: "inputNumber",
    attrs: {
      clearable: false,
      precision: 0,
      min: 0,
    },
  },
  efConstruction: {
    label: "候选列表大小",
    type: "inputNumber",
    attrs: {
      clearable: false,
      precision: 0,
    },
  },
  dim: {
    label: "向量索引维度",
    type: "inputNumber",
    attrs: {
      clearable: false,
      precision: 0,
    },
  },
  fields: {
    label: "字段",
    type: "slot",
    slotName: "fields",
    attrs: {
      class: "required-label",
    },
  },
  enabled: {
    label: "是否启用",
    type: "switch",
  },
});
//打开窗口
const openWindow = async (type: string, row: any) => {
  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    formRef.value.resetForm();
    if (isUpdate.value || isRead.value) {
      ruleForm.value = pick(row, keys(assign({}, defaultForm)));
    } else {
      ruleForm.value = assign({}, defaultForm);
      ruleForm.value.fields = [];
      // events.insertField();
    }
  });
};
const getDisabled = (data, current) => {
  return ruleForm.value.fields.findIndex((item) => item.name == data.value && current !== item.name) !== -1;
};
//提交数据
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  form.name = form.name.trim();
  if (isUpdate.value) {
    metaVectorDbApi.modifyMetaVectorDb(form).then((result) => {
      $app.$message.success("修改成功");
      emit("save-data");
      handleClose();
    });
  } else {
    metaVectorDbApi.insertMetaVectorDb(form).then((result) => {
      $app.$message.success("新增成功");
      emit("save-data");
      handleClose();
    });
  }
};
//事件列表
const events = reactive({
  insertField: () => {
    ruleForm.value.fields.push({
      name: "",
      type: "",
      isPrimaryKey: false,
      needIndex: false,
    });
  },
  insertFieldAll: () => {
    ruleForm.value.fields = modelValue.vectorDbList.map((item) => {
      if (item.value.startsWith("L")) {
        return {
          name: item.value,
          type: "string",
          isPrimaryKey: false,
          needIndex: false,
        };
      } else {
        return {
          name: item.value,
          type: item.type,
          isPrimaryKey: false,
          needIndex: false,
        };
      }
    });
  },
  removeField: (index) => {
    ruleForm.value.fields.splice(index, 1);
  },
});
const modelValue = reactive({
  vectorDbList: [],
  fieldTypeList: [
    { label: "int64", value: "int64" },
    { label: "int32", value: "int32" },
    { label: "int16", value: "int16" },
    { label: "int8", value: "int8" },
    { label: "vector", value: "vector" },
    { label: "string", value: "string" },
    { label: "float", value: "float" },
  ],
});
const handleChange = (val, index) => {
  if (val) {
    const fields = ruleForm.value.fields.map((item) => ({ ...item, isPrimaryKey: false }));
    ruleForm.value = { ...ruleForm.value, fields };
    ruleForm.value.fields[index].isPrimaryKey = true;
  }
};
//初始化站点属性
const getVectorDbFieldList = () => {
  metaSiteFieldApi.getVectorDbFieldList().then((result) => {
    modelValue.vectorDbList.push(...result.data);
  });
};
//初始化
onMounted(() => {
  getVectorDbFieldList();
});
//事件声明
const emit = defineEmits(["save-data"]);
//接口暴露
defineExpose({
  openWindow,
});
</script>
<style lang="scss">
.meta-vector-db-edit {
}
</style>
