<template>
  <div class="meta-vector-db">
    <meta-vector-db-edit ref="metaVectorDbEditRef" @save-data="loadList"></meta-vector-db-edit>
    <meta-vector-db-table ref="metaVectorDbTableRef" @edit-data="openWindow"></meta-vector-db-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import metaVectorDbTable from "./MetaVectorDbTable.vue";
import metaVectorDbEdit from "./MetaVectorDbEdit.vue";

const metaVectorDbEditRef = ref(null);
const metaVectorDbTableRef = ref(null);

//打开编辑窗口
const openWindow = (type: string, item: any) => {
  metaVectorDbEditRef.value.openWindow(type, item);
};
//查询列表数据
const loadList = () => {
  metaVectorDbTableRef.value.loadList();
};
</script>
<style scoped lang="scss"></style>
