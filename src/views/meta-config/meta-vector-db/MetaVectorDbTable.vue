<template>
  <div class="meta-vector-db-table">
    <table-page
      ref="myTableRef"
      :columns="columns"
      :query="query"
      :loadDataApi="loadListData"
      :transformListData="transformListData"
      operationAuth="/base/#/meta-vector-db/edit"
      :operations="operations"
      @operation="handleOperation"
    >
      <template #query>
        <div class="flexBetweenStart">
          <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
          <my-operation>
            <template #buttonGroup>
              <my-button type="add" @click="events.add" :disabled="!testAuth()">新建向量库模板</my-button>
            </template>
          </my-operation>
        </div>
      </template>
    </table-page>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { dataC, timeC } from "turing-plugin";
import useCtx from "@/hooks/useCtx";
import * as metaVectorDbApi from "@/api/meta-vector-db";

const { $app, proxy, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/meta-vector-db/edit");
};
//列配置
const columns = ref([
  {
    prop: "name",
    label: "名称",
    width: 160,
    custom: "link",
    blod: true,
    customRender: {
      click: (record: any) => {
        events.read(record);
      },
    },
  },
  {
    prop: "enabled",
    label: "启用状态",
    width: 120,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: true,
        inactiveValue: false,
        size: "small",
      },
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.enabled === true) {
            $app
              .$confirm({ title: "确定禁用？" })
              .then(() => {
                const form = cloneDeep(record);
                form.enabled = false;
                metaVectorDbApi.modifyMetaVectorDb(form).then((result) => {
                  $app.$message.success("禁用成功");
                  loadList();
                  resolve(true);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: "确定启用？" })
              .then(() => {
                const form = cloneDeep(record);
                form.enabled = true;
                metaVectorDbApi.modifyMetaVectorDb(form).then((result) => {
                  $app.$message.success("启用成功");
                  loadList();
                  resolve(true);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },
  { prop: "indexType", label: "向量库索引类", width: 140 },
  { prop: "metricType", label: "度量类型", width: 120 },
  { prop: "level", label: "一致性级别", width: 120 },
  { prop: "M", label: "最近邻连接数", width: 140 },
  { prop: "efConstruction", label: "候选列表大小", width: 140 },
  { prop: "dim", label: "向量索引维度", width: 140 },
  { prop: "fields", label: "字段", minWidth: 400, sortable: false },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 110, fixed: "right" },
]);
//查询面板
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    attrs: {
      placeholder: "名称",
    },
  },
});
//列表查询
const loadListData = (data: any) => {
  const { page, size } = data;
  return new Promise((resolve: any) => {
    metaVectorDbApi.getMetaVectorDbListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
//转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = !dataC.isEmpty(x.createdDate) ? timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss") : "";
    x.lastModifiedDateRender = !dataC.isEmpty(x.lastModifiedDate) ? timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss") : "";
    return x;
  });
};
//操作
const operations = [
  { type: "edit", label: "编辑" },
  { type: "delete", label: "删除", btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//事件列表
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  add: () => {
    emit("edit-data", "add", {});
  },
  read: (record: any) => {
    if(testAuth()) {
      emit("edit-data", "edit", record);
    } else {
      emit("edit-data", "read", record);
    }
  },
  edit: (record: any) => {
    emit("edit-data", "edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: `您确认要删除${record.name}吗?`,
      })
      .then(() => {
        metaVectorDbApi.removeMetaVectorDb(record.id).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
});
const loadList = () => {
  proxy.$refs.myTableRef.loadData();
};
//事件声明
const emit = defineEmits(["edit-data"]);
//接口暴露
defineExpose({
  loadList,
});
</script>
<style lang="scss" scoped>
.meta-vector-db-table {
  height: 100%;
}
</style>
