<template>
  <my-drawer class="mock-add" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" :showConfirm="!isRead">
    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" label-width="80px">
      <!-- 自定义插槽内容 -->
      <template #paramTable>
        <paramTable v-model="ruleForm.inputArgs" :readonly="isRead" />
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import type { FormRules } from "element-plus";
import { NAME_RULE } from "@/utils/validate";
import useValidate from "@/hooks/validate";
import paramTable from "./components/paramTable.vue";
import useCtx from "@/hooks/useCtx";
import { useI18n } from "vue-i18n";
import * as metaDedupApi from "@/api/meta-dedup";

const { $app, proxy } = useCtx();
const { t } = useI18n();
const dialogTitle = computed(() => {
  if (formType.value === "add") return "新建去重规则";
  else if (formType.value === "edit") return "编辑去重规则";
  else if (formType.value === "read") return "查看去重规则";
  else return "去重规则";
});
const isUpdate = computed(() => {
  return formType.value === "edit";
});
const isRead = computed(() => {
  return formType.value === "read";
});

// 弹窗相关
const dialogVisible = ref<boolean>(false);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};
const handleConfirm = () => {
  formRef.value.submitForm((valid: any) => {
    if (valid) {
      submit(ruleForm.value);
    }
  });
};

/* 校验 */
const { validateNameRule } = useValidate();
// 表单相关
const formType = ref<string>("add");
const formRef = ref<any>(null);
const defaultForm = {
  id: undefined,
  name: "",
  description: "",
  inputArgs: [],
  dedupScript: null,
  status: 2,
};
let ruleForm = ref<any>(assign({}, defaultForm));
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: "名称不能为空",
      trigger: "change",
    },
  ],
  status: [{ required: true, message: "是否启用不能为空", trigger: "change" }],
});

// 表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
    },
    disabled: () => isRead.value,
    separate: {
      title: "基础信息",
    },
  },
  status: {
    label: "是否启用",
    type: "switch",
    attrs: {
      activeValue: 1,
      inactiveValue: 2,
    },
    disabled: () => isRead.value,
  },
  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
    disabled: () => isRead.value,
  },
  inputArgs: {
    type: "slot",
    slotName: "paramTable",
    attrs: { class: "no-label" },
  },
  dedupScript: {
    label: "",
    type: "textarea",
    attrs: {
      class: "no-label",
      rows: 8,
    },
    separate: {
      title: "去重脚本",
    },
  },
  // file: {
  //   type: "slot",
  //   slotName: "file",
  //   attrs: { class: "no-label" }, // 设置no-label  内容可以布满100%
  //   separate: {
  //     // 设置separate可以用标题分隔表单项
  //     title: "脚本文件",
  //   },
  // },
});

const openDialog = async (type: string, row: any) => {
  // console.log(type, row)
  // 1.打开弹窗
  formType.value = type;
  dialogVisible.value = true;
  /* 业务代码 */
  // 3. 回显相关的操作
  nextTick(() => {
    if (isUpdate.value || isRead.value) {
      ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
    } else {
      ruleForm.value = assign({}, defaultForm);
      //proxy.$refs.uploadRef.clearFiles();
    }
  });
};
const emits = defineEmits(["reload"]);
const submit = (formData: any) => {
  const form = cloneDeep(formData);
  form.name = form.name.trim();
  // 接口相关业务代码，执行完成后关闭弹窗
  if (isUpdate.value) {
    metaDedupApi.modifyMetaDedup(form).then((result) => {
      $app.$message.success("修改成功");
      emits("reload");
      handleClose();
    });
  } else {
    metaDedupApi.insertMetaDedup(form).then((result) => {
      $app.$message.success("新增成功");
      emits("reload");
      handleClose();
    });
  }
};

const downloadTemp = () => {
  console.log("下载模版");
};

defineExpose({ openDialog });
</script>

<style lang="scss"></style>
