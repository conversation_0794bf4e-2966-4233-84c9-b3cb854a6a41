<!-- 单纯的表格组件 -->
<template>
  <el-table
    ref="myTableRef"
    class="custom-table"
    :data="tableData"
    :height="tableHeight"
    v-bind="{
      border: false,
      'show-overflow-tooltip': {
        // 'effect': 'light',
        placement: 'left',
      },
      ...$attrs,
    }"
    @selection-change="handleSelectionChange"
  >
    <!-- 多选框 -->
    <el-table-column type="selection" width="60" fixed="left" v-if="withSelection" :selectable="(row: any) => selectable(row)" />
    <!-- 序号 -->
    <el-table-column type="index" width="70" label="序号" fixed="left" v-if="withOrder">
      <template #default="{ $index }">
        <span v-if="page">{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
        <span v-else>{{ $index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column
      v-for="(item, index) of columns"
      :key="index"
      v-bind="{
        ...item,
      }"
    >
      <!-- 默认项 -->
      <template #default="scope" v-if="!item.custom">
        {{ getText(scope.row[item.prop]) }}
        <!-- 支持复制的项 -->
        <el-icon class="icon-copy" v-if="item.withCopy" @click="copyText(scope.row[item.prop], item.copySuccessText || '已复制！')"><CopyDocument /></el-icon>
      </template>
      <!-- 可点击的项 -->
      <template #default="scope" v-if="item.custom === 'link'">
        <text-button
          v-if="!isEmpty(scope.row[item.prop])"
          @click="item.customRender && item.customRender.click(scope.row)"
          :style="{ fontWeight: item.blod ? 550 : 400 }"
        >
          {{ scope.row[item.prop] }}
        </text-button>
        <span v-else>{{ NO_TEXT }}</span>
      </template>
      <!-- 状态项 -->
      <template #default="scope" v-if="['status', 'tagStatus'].includes(item.custom) && item.customRender">
        <template v-if="!isEmpty(scope.row[item.prop])" v-for="(obj, key) in item.customRender.options" :key="key">
          <component
            :is="item.custom === 'status' ? 'status-dot' : 'status-tag'"
            :type="obj.type"
            :name="obj.name"
            :reason="item.customRender.reason ? item.customRender.reason(scope.row) : null"
            v-if="scope.row[item.prop] == key"
          />
        </template>
        <span v-else>{{ NO_TEXT }}</span>
      </template>
      <!-- 鼠标移入支持编辑文本 -->
      <template #default="scope" v-if="item.custom === 'editInput' && item.customRender">
        <div class="edit-input-text" v-if="!scope.row.isEditing">
          {{ getText(scope.row[item.prop]) }}
          <el-icon class="icon" @click="editInputFocus(item, scope)"><EditPen /></el-icon>
        </div>
        <el-input
          id="custom-edit-input"
          v-else
          v-model="scope.row[item.prop]"
          :placeholder="'请输入' + item.label"
          clearable
          v-bind="item.customRender.attrs"
          @blur="editInputBlur(item, scope)"
          @keydown.enter="editInputEnter(item, scope)"
        >
        </el-input>
      </template>
      <!-- 开关 -->
      <template #default="scope" v-if="item.custom === 'switch'">
        <el-switch
          v-model="scope.row[item.prop]"
          :active-value="1"
          :inactive-value="0"
          :before-change="item.customRender && item.customRender.beforeChange.bind({}, scope.row)"
          v-bind="{
            'active-value': 1,
            'inactive-value': 0,
            ...item.customRender.attrs,
          }"
        >
        </el-switch>
      </template>
      <!-- 其他自定义组件 按需添加 -->
      <!-- 插槽 -->
      <template #default="scope" v-if="item.slotName">
        <!-- <slot :row="scope.row"></slot> -->
        <slot :name="item.slotName" :row="scope.row"></slot>
      </template>
      <!-- 操作项 -->
      <template #default="scope" v-if="item.prop === 'operation'">
        <div class="flex">
          <!-- 在外面的操作按钮 -->
          <template v-for="(operation, i) of noCollapsedOperations">
            <!-- 禁用的按钮需要给出禁用提示 -->
            <el-tooltip placement="top" effect="dark" v-if="isDisabled(scope.row, operation) && operation.disabledTips">
              <template #content>{{ operation.disabledTips }} </template>
              <my-button
                link
                :type="operation.btnType || 'primary'"
                :key="'operation' + i"
                v-if="isExit(scope.row, operation)"
                :disabled="isDisabled(scope.row, operation)"
                @click="handleOperation(scope.row, operation, scope.$index)"
              >
                {{ operation.label }}
              </my-button>
            </el-tooltip>
            <my-button
              v-else
              link
              :type="operation.btnType || 'primary'"
              :key="'operation' + i"
              v-if="isExit(scope.row, operation)"
              :disabled="isDisabled(scope.row, operation)"
              @click="handleOperation(scope.row, operation, scope.$index)"
            >
              {{ operation.label }}
            </my-button>
          </template>
          <!-- 在更多里面的操作按钮 -->
          <el-dropdown v-if="collapsedOperations.length > 0">
            <my-button link type="primary" style="margin-left: 12px">
              更多<el-icon style="margin-left: 5px"><arrow-down /></el-icon>
            </my-button>
            <template #dropdown>
              <template v-for="(operation, i) in collapsedOperations">
                <el-dropdown-item :key="'operation2' + i" v-if="isExit(scope.row, operation)">
                  <el-tooltip placement="top" effect="dark" v-if="isDisabled(scope.row, operation) && operation.disabledTips">
                    <template #content>{{ operation.disabledTips }} </template>
                    <my-button
                      link
                      :type="operation.btnType || 'primary'"
                      :disabled="isDisabled(scope.row, operation)"
                      @click="handleOperation(scope.row, operation, scope.$index)"
                    >
                      {{ operation.label }}
                    </my-button>
                  </el-tooltip>
                  <my-button
                    v-else
                    link
                    :type="operation.btnType || 'primary'"
                    :disabled="isDisabled(scope.row, operation)"
                    @click="handleOperation(scope.row, operation, scope.$index)"
                  >
                    {{ operation.label }}
                  </my-button>
                </el-dropdown-item>
              </template>
            </template>
          </el-dropdown>
        </div>
      </template>
    </el-table-column>
    <template #empty>
      <slot name="empty" v-if="$slots.empty"></slot>
      <t-empty :size="120" v-else />
    </template>
  </el-table>
</template>

<script setup lang="ts">
import { ref, nextTick, computed } from "vue";
import { assign } from "lodash";
import { copyText, getText } from "@/utils/helpers";
import { NO_TEXT } from "@/utils/constants";
import { textC, dataC } from "turing-plugin";
import TextButton from "@/components/button/TextButton.vue";
import { CopyDocument } from "@element-plus/icons-vue";
const { isEmpty } = dataC;
const props = defineProps({
  columns: { type: Array<any>, default: () => [] },
  tableData: { type: Array, default: () => [] },
  operations: {
    type: Array<any>,
    default() {
      return [];
    },
  },
  tableHeight: { type: Number },
  withSelection: { type: Boolean, default: false },
  withOrder: { type: Boolean, default: true },
  page: { type: Object }, // 设置当前表格的页数和每页查询的条数（如果表格有分页，需要设置page属性，否则翻页时不能计算出叠加的序号。如果表格没有分页需求，无需设置）
  // 多选框是否禁用【根据行数据的某个字段去区分，如状态为失败的数据禁选】
  selectable: {
    type: Function,
    default() {
      return true;
    },
  },
});
const emits = defineEmits(["operation", "selection-change"]);
const handleOperation = (record: any, operation: any, index: number) => {
  emits(
    "operation",
    assign(
      {},
      { record },
      {
        type: operation.type,
        index,
      }
    )
  );
};
const handleSelectionChange = (arr: any) => {
  emits("selection-change", arr);
};

// 操作项“更多”
const collapsedOperations = computed(() => {
  return props.operations.filter((x) => x.collapsed);
});
const noCollapsedOperations = computed(() => {
  return props.operations.filter((x) => !x.collapsed);
});

const isDisabled = (record: any, operation: any) => {
  return typeof operation.disabled === "function" ? !!operation.disabled(record) : !!operation.disabled;
};
const isExit = (record: any, operation: any) => {
  return typeof operation.exist === "function" ? !!operation.exist(record) : true;
};

// 自定义项的操作
const editInputBlur = (item: any, scope: any) => {
  item.customRender && item.customRender.blur(scope.row);
};
const editInputEnter = (item: any, scope: any) => {
  item.customRender && item.customRender.enter(scope.row);
};

const editInputFocus = (item: any, scope: any) => {
  scope.row.isEditing = true;
  nextTick(() => {
    const El = document.getElementById("custom-edit-input");
    const textLength = scope.row[item.prop].length;
    textC.setSelectionRange(El, textLength);
  });
};

/* 抛出所有的原生方法 */
const myTableRef = ref<any>(null);
const defaultExposes = [
  "clearSelection",
  "getSelectionRows",
  "toggleRowSelection",
  "toggleAllSelection",
  "toggleRowExpansion",
  "setCurrentRow",
  "clearSort",
  "clearFilter",
  "doLayout",
  "sort",
  "scrollTo",
  "setScrollTop",
  "setScrollLeft",
];
const setExposes = () => {
  const exposes: any = {};
  defaultExposes.forEach((key: string) => {
    exposes[key] = (...args: any) => {
      myTableRef.value[key](...args);
    };
  });
  return exposes;
};
defineExpose({
  ...setExposes(),
});
</script>

<style lang="scss" scoped>
.icon-copy {
  color: $primary-color;
  margin-left: 5px;
  cursor: pointer;
}
.edit-input-text {
  cursor: pointer;
  > .icon {
    display: none;
    margin-left: 4px;
    color: $primary-color;
  }
  &:hover {
    > .icon {
      display: inline-block;
    }
  }
}
</style>

<style lang="scss"></style>
