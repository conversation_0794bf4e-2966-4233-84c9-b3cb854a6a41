<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="remove-duplicates">
      <table-page
        ref="tablePageRef"
        :query="query"
        :columns="columns"
        operationAuth="/base/#/remove-duplicates/edit"
        :operations="operations"
        :loadDataApi="loadListData"
        :transformListData="transformListData"
        @operation="handleOperation"
      >
        <!-- 查询 + 操作插槽内容 -->
        <template #query>
          <div class="flexBetweenStart">
            <my-query :queryItems="queryItems" :refreshBtn="{ show: true }" @search="events.search" @reset="events.reset" />
            <my-operation>
              <template #buttonGroup>
                <!-- <my-button type="add" @click="events.add" operationAuth="/base/#/remove-duplicates/edit">新建去重规则</my-button> -->
              </template>
            </my-operation>
          </div>
        </template>
      </table-page>
      <AddDialog ref="addRef" @reload="loadList" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { assign, cloneDeep } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import AddDialog from "./add.vue";
import * as metaDedupApi from "@/api/meta-dedup";

const { t } = useI18n();
const { $app, proxy, $router, $auth } = useCtx();
const testAuth = () => {
  return $auth.testAuth("/base/#/remove-duplicates/edit");
};
const routeName = "removeDuplicates";
/* 查询 */
const query = ref<any>({
  keywords: "",
});
const queryItems = ref<any>({
  keywords: {
    type: "input",
    label: "",
    modelValue: "",
    width: "240px",
    attrs: {
      placeholder: "名称",
    },
  },
});

/* 表格 */
const columns = ref([
  { prop: "name", label: "名称", width: 180 },
  {
    prop: "status",
    label: "启用状态",
    width: 120,
    custom: "switch",
    customRender: {
      attrs: {
        activeValue: 1,
        inactiveValue: 2,
        size: "small",
        disabled: true,
      },
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          if (record.status === 1) {
            $app
              .$confirm({ title: "确定禁用？" })
              .then(() => {
                const form = cloneDeep(record);
                form.status = 2;
                metaDedupApi.modifyMetaDedup(form).then((result) => {
                  $app.$message.success("禁用成功");
                  loadList();
                  resolve(true);
                });
              })
              .catch(() => {
                reject();
              });
          } else {
            $app
              .$confirm({ title: "确定启用？" })
              .then(() => {
                const form = cloneDeep(record);
                form.status = 1;
                metaDedupApi.modifyMetaDedup(form).then((result) => {
                  $app.$message.success("启用成功");
                  loadList();
                  resolve(true);
                });
              })
              .catch(() => {
                reject();
              });
          }
        });
      },
    },
  },
  { prop: "description", label: "描述", minWidth: 200 },
  { prop: "lastModifiedBy", label: "更新人", width: 100 },
  { prop: "lastModifiedDateRender", label: "更新时间", width: 180 },
  { prop: "createdBy", label: "创建人", width: 100 },
  { prop: "createdDateRender", label: "创建时间", width: 180 },
  { prop: "operation", label: "操作", width: 70, fixed: "right" },
]);
const operations = [
  { type: "read", label: "查看" },
  // { type: "edit", label: t("btn.edit") },
  // { type: "delete", label: t("btn.delete"), btnType: "danger" },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  typeof events[type] == "function" && events[type](record);
};
//列表查询
const loadListData = (data: any) => {
  const { page, size } = data;
  return new Promise((resolve: any) => {
    metaDedupApi.getMetaDedupListPage(data.keywords, data.page, data.size, data.sort).then((result) => {
      resolve(result);
    });
  });
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    x.createdDateRender = timeC.format(x.createdDate, "YYYY-MM-DD hh:mm:ss");
    x.lastModifiedDateRender = timeC.format(x.lastModifiedDate, "YYYY-MM-DD hh:mm:ss");
    return x;
  });
};

/* events */
const events = reactive({
  search: (obj: any) => {
    query.value = assign({}, query.value, obj);
  },
  reset: (obj: any) => {},
  read: (record: any) => {
    proxy.$refs.addRef?.openDialog("read", record);
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  delete: (record: any) => {
    $app
      .$deleteConfirm({
        title: t("tip.deleteConfirmTitle", { n: record.name }),
      })
      .then(() => {
        metaDedupApi.removeMetaDedup(record.id).then((result) => {
          $app.$message.success("删除成功");
          loadList();
        });
      });
  },
});

/* 列表刷新 */
const loadList = () => {
  proxy.$refs.tablePageRef.loadData();
};
</script>

<style lang="scss" scoped></style>
