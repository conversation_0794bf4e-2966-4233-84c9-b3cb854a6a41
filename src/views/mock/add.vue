<template>
    <my-drawer
      class="mock-add"
      v-model="dialogVisible"
      :title="dialogTitle"
      @confirm="handleConfirm"
      @close="handleClose"
    >
      <my-form
        ref="formRef"
        :rules="rules"
        :ruleForm="ruleForm"
        :formItems="formItems">
        <template #file>
          <my-upload
            ref="uploadRef"
            maxSize="10M"
            accept=".xls,.xlsx"
            v-model="ruleForm.file"
            drag
            drag-style
            showDownload
            @download="downloadTemp">
            <template #tips>
                <div class="upload-tips">
                <p>1、文件内容格式请参照模版，一行一条测试数据，一条数据最多支持5000字</p>
                <p>2、每条任务最多支持2000条数据，超出部分不予导入</p>
                <p>3、仅支持扩展名为：.xlsx/xls文件；大小不超过10M</p>
                <p>4、上传后，可在任务列表等待审核结果，仅支持下载查看详情</p>
                </div>
            </template>
          </my-upload>
        </template>
      </my-form>
    </my-drawer>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, computed, nextTick } from "vue";
  import { assign, pick, keys } from "lodash";
  import type { FormRules } from "element-plus";
  import { NAME_RULE } from '@/utils/validate';
  import useValidate from '@/hooks/validate'
  import useStore from "@/store";
  import { useI18n } from "vue-i18n";
  import useCtx from "@/hooks/useCtx";
  const { proxy } = useCtx()
  const { t } = useI18n();
  const { api } = useStore();
  const dialogTitle = computed(() => {
    return (formType.value === 'add' ? t('btn.new') : t('btn.edit')) + t('app.name')
  })
  const isUpdate = computed(() => {
    return formType.value === 'edit'
  })

  // 弹窗相关
  const dialogVisible = ref<boolean>(false)
  const handleClose = () => {
    formRef.value.resetForm();
    dialogVisible.value = false;
  };
  const handleConfirm = () => {
    formRef.value.submitForm((valid: any) => {
      if (valid) {
        // 接口相关业务代码，执行完成后关闭弹窗
        if (isUpdate.value) { // 如果是编辑页面执行
          console.log('params', ruleForm.value)
        } else { // 如果是新增页面执行
          console.log('params', ruleForm.value)
        }
        dialogVisible.value = false
      }
    })
  };
  
  /* 校验 */
  const { validateNameRule } = useValidate()
  // 表单相关
  const formType = ref<string>('add')
  const formRef = ref<any>(null)
  const defaultForm = {
    id: undefined,
    name: "",
    appId: "",
    customText: "",
    city: [],
    date: "",
    age: "",
    enable: false,
    school: "北京大学",
    hobby: [],
    file: null
  };
  let ruleForm = ref<any>(assign({}, defaultForm));
  const rules = reactive<FormRules>({
    name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, '请输入任务名称') }],
    appId: [{ required: true, message: "请选择类型", trigger: "change" }],
    file: [{ required: true, message: "请上传文件", trigger: "change" }],
  });
  // 表单项
  const formItems = ref<any>({
    name: { 
      label: "名称", 
      type: "input",
      attrs: { 
        maxlength: 30,
        placeholder: NAME_RULE
      }
    },
    appId: {
      label: "应用",
      type: "select",
      options: [],
      attrs: { clearable: false },
      events: { 
        change: (val: any) => {
          console.log(val)
        }
      }
    },
    city: { 
      label: "城市", 
      type: "cascader", 
      options: [], 
      attrs: {
        props: {
          checkStrictly: true,
          value: "code",
          label: "name",
          children: "children"
        }
      }
    },
    date: { 
      label: "日期", 
      type: "datePicker"
    },
    age: { 
      label: "年龄", 
      type: "inputNumber", 
      attrs: {min: 1, max: 100, step: 1} 
    },
    enable: { 
      label: "是否启用", 
      type: "switch" 
    },
    school: { 
      label: "学校", 
      type: "radio",
      options: [
        { value: "北京大学", label: "北京大学" },
        { value: "清华大学", label: "清华大学" },
        { value: "复旦大学", label: "复旦大学" },
      ]
    },
    hobby: { 
      label: "爱好", 
      type: "checkbox",
      options: [
        { value: "唱歌", label: "唱歌" },
        { value: "跳舞", label: "跳舞" },
        { value: "羽毛球", label: "羽毛球" },
        { value: "游泳", label: "游泳" },
        { value: "跑步", label: "跑步" },
      ]
    },
    customText: { 
      label: "描述", 
      type: "textarea", 
      attrs: { maxlength: 50  }
    },
    file: { label: '上传文件', type: 'slot', slotName: 'file'}
  });
  
  /* 获取下拉列表数据 */
  const getAppOptions = async (search ? : string) => {
    const result: any = await api.getAppOptions(search)
    formItems.value.appId.options = result.list
    formItems.value.appId.attrs.remote = result.total > api.MAX_TOTAL  // 如果数据总数量大于1000条，则下拉框支持远程搜索，否则只在前端做筛选
  }
  const getCityOptions = async () => {
    const result: any = await api.getCityOptions()
    formItems.value.city.options = result
  }

  const getOptions = () => {
    getAppOptions()
    getCityOptions()
  }

  const openDialog = async (type: string, row: any) => {
    console.log(type, row)
    // 1.打开弹窗
    formType.value = type;
    dialogVisible.value = true;
    /* 业务代码 */
    // 2. 加载相关的下拉列表数据
    getOptions()
    // 3. 回显相关的操作
    nextTick(() => {
      if (type === "edit") {
        ruleForm.value = pick(row, keys(defaultForm)); // 这个写法是将row中 在defaultForm存在的字段值进行返回，多余的不需要
      } else {
        ruleForm.value = assign({}, defaultForm);
        proxy.$refs.uploadRef.clearFiles()
      }
    })
  };
  const emits = defineEmits(["reload"]);

  const downloadTemp = () => {
    console.log('下载模版')
  }
  
  defineExpose({ openDialog });
  </script>
  
  <style lang="scss">
  </style>
  