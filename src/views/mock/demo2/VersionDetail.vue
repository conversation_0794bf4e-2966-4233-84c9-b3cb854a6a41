<template>
  <div>
    <FullscreenPage title="版本详细信息" @close="close">
      <!-- 头部，已内置左侧返回按钮和标题，这部分代码按需添加 -->
      <template #header>
        <div class="center">中间区域内容</div>
        <div class="right">
          <my-button type="primary">保存</my-button>
        </div>
      </template>
      <!-- 全屏页面的内容区域 -->
      <data-preview />
    </FullscreenPage>
  </div>
</template>

<script setup lang="ts">
import FullscreenPage from '@/components/layout/FullscreenPage.vue'
import DataPreview from '@/views/common/preview/DataPreview.vue'
const emits = defineEmits(['close'])
const close = () => {
  emits('close')
};
</script>

<style lang="scss" scoped>

</style>