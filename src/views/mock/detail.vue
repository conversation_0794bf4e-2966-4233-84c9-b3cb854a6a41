<template>
    <page-wrapper :route-name="`${routeName}::`">
        <div style="padding: 24px">
            这是详情页
            <my-button @click="toDetail">进入下一个详情页面</my-button>
        </div>
    </page-wrapper>
</template>

<script setup lang="ts">
import PageWrapper from "@/components/layout/PageWrapper.vue";
import useCtx from "@/hooks/useCtx";
const { $app } = useCtx();
const routeName = 'mockDemo::detail'
const toDetail = () => {
    $app.$router.push({
        name: `${routeName}::detail2`
    })
}
</script>

<style scoped>

</style>