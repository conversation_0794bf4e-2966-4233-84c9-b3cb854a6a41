<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="mock-list">  
      <table-page
        ref="tablePageRef"
        :query="query"
        :columns="columns"
        :operations="operations"
        :loadDataApi="getTemplateList"
        :transformListData="transformListData"
        with-selection
        :selectable="(row: any) =>row.status !== 1"
        @operation="handleOperation"
        @selection-change="handleSelectionChange">
        <!-- 查询 + 操作插槽内容 -->
        <template #query>
          <div class="flexBetweenStart">
            <my-query 
              :queryItems="queryItems"
              :refresh-btn="{ show: true }"
              @search="events.searchQuery"
              @reset="events.resetQuery"/>
            <my-operation :selectedTotal="selectedIds.length">
              <template #buttonGroup>
                <my-button type="add" @click="events.add">新建模版</my-button>
                <tooltip-button 
                  type="delete" 
                  plain 
                  handle="批量删除"
                  :clickable="selectedIds.length > 0"
                  @click="events.batchDelete">
                  批量删除
                </tooltip-button>
              </template>
            </my-operation>
          </div>
        </template>
        <!-- 表格项的插槽内容 -->
        <template #customText="scope">
          <el-icon><Service /></el-icon>
          <span style="display:inline-block; color: red; margin-left: 5px;">{{ scope.row.customText }}</span>
        </template>
      </table-page>
      <AddDialog ref="addRef" @reload="loadList" />
      <!-- <data-preview ref="dataPreviewRef"/> -->
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { assign } from 'lodash'
import { useI18n } from "vue-i18n"
import useCtx from "@/hooks/useCtx";
import { getTemplateList } from '@/api/mock'
import AddDialog from "./add.vue";
import DataPreview from "@/views/common/preview/DataPreview.vue";

const { t } = useI18n();
const { $app, proxy, $router, $route } = useCtx();
const routeName = 'mockDemo'
/* 查询 */
const query = ref<any>({})
const queryItems = ref<any>({
  keywords: {
    type: 'input',
    label: '关键词搜索',
    showAppend: true,     // 后置搜索按钮的文本框
    attrs: {
      placeholder: '请输入关键字进行搜索',
    }
  },
  city: {
    label: '城市', 
    modelValue: [],
    type: "cascader", 
    options: [
      { 
        code: 'anhui', 
        name: "安徽", 
        children: [
          {code: 'hefei', name: '合肥'},
          {code: 'fuyang', name: '阜阳'},
          {code: 'tongning', name: '铜陵'}
        ] 
      },
      { 
        code: 'jiangsu', 
        name: "江苏", 
        children: [
          {code: 'nanjing', name: '南京'},
          {code: 'suzhou', name: '苏州'},
          {code: 'wuxi', name: '无锡'}
        ] 
      },
    ], 
    attrs: {
      props: {
        checkStrictly: true,
        value: "code",
        label: "name",
        children: "children"
      }
    }
  }
})

/* 表格 */
const columns = ref([
  // 设置宽度的时候，需要至少保留一个width不是固定的，可用minWidth代替，否则可能出现大屏表格宽度未到100%的情况
  // 文本可编辑
  { 
    prop: "name", 
    label: "名称", 
    width: 180, 
    showOverflowTooltip: false,
    custom: 'editInput',
    customRender: {
      blur: (val: string, row: any) => {
        console.log('当前文本框的值', val)
        console.log('当前行', row)
        loadList()
      }
    }
  },
  // 文本可复制
  { prop: "appId", label: "APPID", withCopy: true, width: 280, showOverflowTooltip: false},
  // tag标签
  { 
    prop: "type", 
    label: "类型", 
    width: 150,
    custom: "tagStatus",
    customRender: {
      options: {
        1: { type: "danger", name: "黑名单" },
        2: { type: "primary", name: "白名单" },
      }
    }
  },
  // 状态项
  {
    prop: "status",
    label: "状态",
    width: 150,
    custom: "status",
    // 根据状态实际取值来定义
    customRender: {
      options: {
        1: { type: "success", name: "已发布" },
        2: { type: "info", name: "未发布" },
        3: { type: "danger", name: "已驳回" },
        4: { type: "warning", name: "上线审核中" },
        5: { type: "primary", name: "下线审核中" },
      },
      // 支持数组写法 
      // [
      //   { value: "0", label: "上线", color: "success" },
      //   { value: "-1", label: "下线", color: "danger" },
      //   { value: "1", label: "锁定", color: "info" },
      // ]
      reason: (record: any) => record.rejectReason // 驳回原因
    },
  },
  // 开关项
  {
    prop: "enable", 
    label: '是否启用',
    width: 150,
    custom: 'switch',
    customRender: {
      beforeChange: (record: any) => {
        return new Promise((resolve: any, reject: any) => {
          // 禁用需要确认提示，启用直接执行
          if (record.enable === 1) {
            $app.$confirm({title: '确定禁用？'}).then(() => {
              // 在这里调用接口，接口调用成功执行resolve(true) 调用失败执行reject()
              resolve(true)
            }).catch(() => {
              reject()
            })
          } else {
            resolve(true)
          }
        })
      }
    }
  },
  // 可点击项
  { 
    prop: "num", 
    label: "关联数量", 
    width: 100,
    custom: 'link',
    blod: true,
    customRender: {
      click: (row: any) => {
        console.log(row)
      }
    }
  },
  // 自定义内容（插槽）
  { prop: "customText", label: "自定义插槽11", slotName: 'customText', minWidth: 280},
  { prop: "date", label: "更新时间", width: 180 },
  { prop: "operation", label: '操作', width: 220, fixed: 'right' }
]);
const operations = [
  { type: "detail", label: t("btn.detail") },
  { type: "edit", label: t("btn.edit") },
  { type: "delete", label: t("btn.delete"), btnType: "danger", disabled: (record: any) => record.status === 3, disabledTips: '已发布状态不支持删除！' },
  { type: "publish", label: t("btn.publish"), exist: (record: any) => [2, 3].includes(record.status) },
  { type: "offline", label: t("btn.offline"), exist: (record: any) => record.status === 1 },
];
const handleOperation = (data: any) => {
  const { type, record } = data;
  if (type === "detail") {
    events.detail(record)
  } else if (type === "edit") {
    events.edit(record)
  } else if (type === "delete") {
    events.delete(record)
  } else if (type === "publish") {
    events.publish(record)
  }else if (type === "offline") {
    console.log('下线', record)
    // proxy.$refs.dataPreviewRef.openDialog(record)
  } 
};
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    return x;
  });
};

/* events */
const events = reactive({
  searchQuery: (obj: any) => {
    query.value = assign({}, query.value, obj)
  },
  resetQuery: (obj: any) => {
    for (let key in obj) {
      queryItems.value[key].modelValue = obj[key].modelValue
    }
  },
  detail: (record: any) => {
    $router.push({
      name: `${routeName}::detail`,
      params: {
        id: record.id
      }
    })
  },
  add: () => {
    proxy.$refs.addRef?.openDialog("add");
  },
  edit: (record: any) => {
    proxy.$refs.addRef?.openDialog("edit", record);
  },
  delete: (record: any) => {
    $app.$deleteConfirm({
      title: t('tip.deleteConfirmTitle', {t: '模版名称', n: record.name})
    }).then(() => {
      console.log('删除', record)
      $app.$message.warning('接口待提供！')
    })
  },
  publish: (record: any) => {
    $app.$confirm({title: '确定发布当前模版？'})
    .then(() => {
      // 这里执行确认后的操作
      console.log('发布', record)
    }).catch(() => {
      // 这里执行取消后的操作 可以不加catch
    })
  },
  batchDelete: () => {
    $app.$deleteConfirm({
      title: '确认删除选中的模版'
    }).then(() => {
      console.log('删除的项', selectedIds.value)
    })
  }
})

// 多选
const selectedIds = ref<any>([])
const handleSelectionChange = (arr: any) => {
  selectedIds.value = arr.map((v: any) => v.id)
};


/* 列表刷新 */
const loadList = () => {
  proxy.$refs.tablePageRef.loadData();
};
</script>

<style lang="scss" scoped>
</style>