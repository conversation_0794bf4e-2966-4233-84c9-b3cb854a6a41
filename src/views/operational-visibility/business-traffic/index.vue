<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="container">
      <Filter v-model:query="query" ref="filterRef" :getInfoApi="getInfoApi" :show="['appIds','regionCodes']"/>
      <headTitle title="检索请求流量分析" />
      <searchRequestTraffic :data="trafficInfo" />
       <headTitle title="检索请求趋势分析" />
      <searchRequestTrend :data="trafficInfo" />
      <headTitle title="产品方案流量分析" />
      <productPlanTraffic :data="trafficInfo" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import Filter from "../common/filter.vue";
import searchRequestTraffic from "./search-request-traffic.vue";
import searchRequestTrend from "./search-request-trend.vue";
import headTitle from "../common/head-title.vue";
import { getBusinessTraffic } from "@/api/traffic-view";
import productPlanTraffic from "./product-plan-traffic.vue";

const routeName = "business-traffic-analysis";
const query = ref({});
const filterRef = ref()
let trafficInfo = ref({});
/* events */
const events = reactive({});
const getInfoApi =(params)=>{
  return new Promise((resolve)=>{
    getBusinessTraffic(params).then((res) => {
    trafficInfo.value = res.data
  });
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
  overflow: auto;
  ::v-deep {
    .num {
      font-size: 30px;
      font-weight: 400;
    }
    .border {
      padding: 10px;
      border: 1px solid var(--el-border-color);
      margin-right: 10px;
    }
    .sub-name {
      color: rgba(0, 0, 0, 0.427450980392157);
    }
  }
}
</style>
