<template>
  <div class="plan-result-trend">
    <div ref="chartContainer" style="width: 100%; height: 400px"></div>
  </div>
</template>

<script lang="ts" setup>
import echarts from "@/components/echarts/index.ts";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { map, cloneDeep, debounce } from "lodash";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
const { $app, proxy } = useCtx();
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  data: {
    type: Array,
    required: true,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
});
let chartInstance: any = null;
/* events */
const events = reactive({});
const chartContainer = ref(null);

const initChart = () => {
  if (chartInstance !== null) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(chartContainer.value);
  updateChart();
};
const updateChart = () => {
  const minNum = 13;
  const option = {
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let tooltipText = params[0].name + "<br>";
        params.forEach(function (param) {
          const unit = param.data?.unit ? param.data.unit : ""; // 单位
          // 显示系列名称和值
          tooltipText +=
            param.marker +
            param.seriesName +
            ": " +
            util.formatNumber(param.value) +
            unit +
            "<br>";
        });
        return tooltipText;
      },
    },
    grid: {
      left: "8%",
      right: "8%",
    },
    legend: {},
    xAxis: [
      {
        type: "category",
        data: map(props.data, "date"),
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "",
        yAxisIndex: 0
      },
      {
        type: "value",
        axisLabel: {
          formatter: "{value} %",
        },
         yAxisIndex: 1
      },
    ],
    dataZoom: [
      // {
      //   type: "inside",
      //   disabled: props.data.length < minNum,
      // },
      {
        show: props.data.length >= minNum,
        height: 20, 
      },
    ],
    series: [
      {
        name: "有结果",
        type: "bar",
        yAxisIndex: 0,
        smooth: true,
        barMaxWidth:'35',
        stack: "total",
        data: (props.data || []).map((item: any) => item?.hasResultCount),
      },
      {
        name: "无结果",
        type: "bar",
        barMaxWidth:'35',
        yAxisIndex: 0,
        smooth: true,
        stack: "total",
        data: (props.data || []).map((item: any) => item?.noResultCount),
      },
      {
        name: "有结果率",
        type: "line",
        smooth: true,
        yAxisIndex: 1,
        data: (props.data || []).map((item: any) => ({
          value: item.hasResultPercentage,
          unit: "%",
        })),
      },
    ],
  };
  if (props.data.length == 0) {
    option.graphic = {
      type: "text", // 指定类型为文本
      left: "center", // 水平居中
      top: "center", // 垂直居中
      style: {
        text: "暂无数据", // 显示的文本内容
        font: "16px Arial", // 字体样式
        fill: "#999", // 字体颜色
      },
    }; // 显示占位符
    chartInstance.setOption(option, true);
  } else {
    option.graphic = null; // 不显示占位符
    chartInstance.setOption(option, true);
  }
};
const debounceResize = debounce(() => {
  chartInstance.resize();
});
onMounted(() => {
  initChart();
  window.addEventListener("resize", debounceResize);
});
// watch监听
watch(
  () => props.data,
  (nu) => {
    updateChart();
  }
);
onUnmounted(() => {
  window.removeEventListener("resize", debounceResize);
});
</script>

<style lang="scss" scoped>
.search-request-trend {
  display: flex;
  .first,
  .second {
    width: 200px;
  }
}
</style>
