<template>
  <pie-table
    type="custom"
    ref="pieTableRef"
    v-bind="{
      pie: {
        width: '60%',
        data: data.productTrend,
        series: {
          center: ['25%', '55%'],
          radius: ['25%', '60%'],
          label: {
            normal: {
              position: 'inner',
              formatter: '{d}%', // 在这里显示百分比
            },
          },
        },
      },
      table: {
        data: data.productTrend,
        width: '65%',
        columns,
      },
      bar: {
        data: currentRowSon,
        xKey: 'date',
      },
      'sub-name': '产品方案流量占比',
      'sub-son-name': '产品方案版本有结果占比',
      xKey: 'date',
    }"
  />
  <headTitle title="产品方案有结果率趋势分析" />
      <planResultTrend :data="currentRowTrend||[]" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { map, cloneDeep, debounce } from "lodash";
import PieChart from "@/components/echarts/pie.vue";
import stashBarChart from "@/components/echarts/stashBar.vue";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
import planResultTrend from "./plan-result-trend.vue";
import pieTable from "../common/pie-table.vue";
import { computed } from "vue";
import headTitle from "../common/head-title.vue";
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    required: true,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
});
const pieTableRef = ref(null);
//列配置
const columns = ref([
  { prop: "name", label: "", minWidth: 120, slotName: "name" },
  { prop: "value", label: "请求数", minWidth: 80,type:'number' },
  { prop: "hasResultPercentage", label: "有结果率(%)", minWidth: 90 },
  { prop: "trafficPercentage", label: "流量占比(%)", minWidth: 90 },
]);
const currentRowSon = computed(() => {
  return pieTableRef.value?.getTableCurrentRow()?.value?.son || [];
});
const currentRowTrend = computed(() => {
  return pieTableRef.value?.getTableCurrentRow()?.value?.trend || [];
});
/* events */
const events = reactive({});
</script>

<style lang="scss" scoped></style>
