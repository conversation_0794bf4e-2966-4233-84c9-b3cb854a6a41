<template>
  <div class="product-plan-traffic border">
    <div style="width: 62%">
      <div class="sub-name">产品方案流量占比</div>
      <div class="content">
        <div style="width: 60%">
          <PieChart
            :data="data.productTrend || []"
            :series="{
              center: ['25%', '55%'],
              radius: ['25%','60%'],
              label: {
                normal: {
                  position: 'inner',
                  formatter: '{d}%', // 在这里显示百分比
                },
              },
            }"
          />
        </div>
        <clickTable
          ref="clickTableRef"
          width="65%"
          :tableData="data.productTrend"
          :columns="columns"
          rowKey="code"
        >
          <template #name="scope">
            <div>{{ scope.row.name }}</div>
            <div>({{ scope.row.code }})</div>
          </template>
        </clickTable>
      </div>
    </div>
    <div style="width: 38%">
      <div class="sub-name">产品方案版本有结果占比</div>
      <div class="content">
        <stashBarChart
          :data="currentRowSon"
          :yKeys="[
            {
              value: 'hasResultCount',
              label: '有结果数',
              prencentKey: 'hasResultPercentage',
            },
            { value: 'noResultCount', label: '无结果数' },
          ]"
        ></stashBarChart>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { map, cloneDeep, debounce } from "lodash";
import PieChart from "@/components/echarts/pie.vue";
import stashBarChart from "@/components/echarts/stashBar.vue";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
import clickTable from "../common/click-table.vue";
import { computed } from "vue";
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    required: true,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
});
const clickTableRef = ref(null);
let trafficInstance: any = null;
let resultInstance: any = null;
const tableData = ref([]);
//列配置
const columns = ref([
  { prop: "name", label: "", minWidth: 120, slotName: "name" },
  { prop: "value", label: "请求数", minWidth: 80 },
  { prop: "hasResultPercentage", label: "有结果率", minWidth: 90 },
  { prop: "trafficPercentage", label: "流量占比", minWidth: 90 },
]);
const currentRowSon = computed(() => {
  return clickTableRef.value?.getCurrentRow()?.son || [];
});
/* events */
const events = reactive({});
</script>

<style lang="scss" scoped>
.product-plan-traffic {
  height: 300px;
  display: flex;
  > div {
    .content {
      display: flex;
      height: calc(100% - 40px);
    }
  }
}
</style>
