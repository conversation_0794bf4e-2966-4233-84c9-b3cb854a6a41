<template>
  <div class="search-request-traffic">
    <div class="border" style="width: 15%">
      <div class="sub-name">检索请求总量</div>
      <!-- <div class="num">{{ util.formatNumberToChinese(data?.totalRequest?.count ) }}</div> -->
      <Num :number="data?.totalRequest?.count"/>
      <div class="sub-name">日均检索请求量</div>
      <Num :number="data?.totalRequest?.avg"/>
    </div>

    <div class="border" style="width: 25%">
      <span class="sub-name">检索请求成功率</span>
      <div class="content">
        <div class="num">{{  data?.successRequest?.percentage }}%</div>
        <PieChart
          :data="data?.successRequest?.num || []"
          :series="{ center: ['50%', '50%'] }"
          :option="{ legend: false }"
          :color="['#177BF8', '#ebeef5']"
        />
      </div>
    </div>
    <div class="border result" style="width: 25%">
      <span class="sub-name">有结果率</span>
      <div class="num">{{ data.hasResult?.percentage }}%</div>
      <el-progress
        :text-inside="true"
        :stroke-width="26"
        :percentage="data.hasResult?.percentage"
      />
      <div :style="{ 'margin-top': '10px', 'text-align': 'center' }">
        <span
          :class="{
            trangle_red: data.hasResult?.trend?.value > 0,
            trangle_green: data.hasResult?.trend?.value < 0,
          }"
        ></span
        >环比{{ data.hasResult?.trend?.value }}％
      </div>
    </div>
    <div class="border" style="width: 35%">
      <span class="sub-name">业务应用流量占比</span>
      <div class="content" style="width: 100%">
        <PieChart
          :data="data.app || []"
          :series="{
            center: ['20%', '50%'],
            label: {
              normal: {
                position: 'inner',
                formatter: '{d}%', // 在这里显示百分比
              },
            },
          }"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import Filter from "../common/filter.vue";
import Num from "../common/num.vue";
import PieChart from "@/components/echarts/pie.vue";
import * as util from "@/utils/common";
/* events */
const events = reactive({});
const props = defineProps(["data"]);
onMounted(async () => {});
</script>

<style lang="scss" scoped>
.search-request-traffic {
  width: 100%;
  display: flex;
  .border {
    height: 188px;
    .image {
      background: url("@/assets/images/echarts.png") no-repeat;
    }
    span {
      display: inline-block;
      height: 20px;
    }

    .content {
      display: flex;
      height: calc(100% - 20px);
      .num {
        display: inline-block;
        text-align: center; /* 水平居中 */
        line-height: 100px; /* 设置行高等于容器高度实现垂直居中 */
        height: 100px; /* 确定容器的高度 */
        margin: 0 auto;
      }
    }
  }
  .result .num {
    text-align: center; /* 水平居中 */
    height: 70px; /* 确定容器的高度 */
    margin: 0 auto;
  }
}
</style>
