<template>
  <div class="search-request-trend">
    <div class="first">
      <div class="sub-name">累计检索请求总数</div>
      <Num :number="data?.overview?.grandTotal"/>
      <div class="sub-name">检索请求总数</div>
      <Num :number="data?.overview?.conditionGrandTotal"/>
      <div class="sub-name">环比</div>
      <div class="num">
        <span
          :class="{
            trangle_red: data?.overview?.trend?.value > 0,
            trangle_green: data?.overview?.trend?.value < 0,
          }"
        ></span
        >{{ data.overview?.trend?.value }}%
      </div>
    </div>
    <div ref="chartContainer" style="flex: 1; height: 400px"></div>
  </div>
</template>

<script lang="ts" setup>
import echarts from "@/components/echarts/index.ts";
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { map, cloneDeep, debounce } from "lodash";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import Num from "../common/num.vue";
const { $app, proxy } = useCtx();
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    required: true,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
});

let chartInstance: any = null;
/* events */
const events = reactive({});
const chartContainer = ref(null);

const initChart = () => {
  if (chartInstance !== null) {
    chartInstance.dispose();
  }
  chartInstance = echarts.init(chartContainer.value);
  updateChart();
};
const updateChart = () => {
  const minNum = 13;
  const trend = props.data?.trend || [];

  const option = {
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let tooltipText = params[0].name + "<br>";
        params.forEach(function (param) {
          const unit = param.data?.unit ? param.data.unit : ""; // 单位
          // 显示系列名称和值
          tooltipText +=
            param.marker +
            param.seriesName +
            ": " +
            util.formatNumber(param.value) +
            unit +
            "<br>";
        });
        return tooltipText;
      },
    },
    legend: {
      data: ["检索请求总量", "有结果率"],
    },
    xAxis: [
      {
        type: "category",
        data: map(trend, "date"),
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "",
      },
      {
        type: "value",
        axisLabel: {
          formatter: "{value} %",
        },
      },
    ],
    dataZoom: [
      // {
      //   type: "inside",
      //   disabled: trend.length < minNum,
      // },
      {
        show: trend.length >= minNum,
        height: 20, 
      },
    ],
    series: [
      {
        name: "检索请求总量",
        type: "line",
        yAxisIndex: 0,
        smooth: true,
        data: trend.map((item: any) => item?.requestCount),
      },
      {
        name: "有结果率",
        type: "line",
        smooth: true,
        yAxisIndex: 1,
        data: trend.map((item: any) => ({
          value: item.hasResultPercentage,
          unit: "%",
        })),
      },
    ],
  };
  if (trend.length == 0) {
    option.graphic = {
      type: "text", // 指定类型为文本
      left: "center", // 水平居中
      top: "center", // 垂直居中
      style: {
        text: "暂无数据", // 显示的文本内容
        font: "16px Arial", // 字体样式
        fill: "#999", // 字体颜色
      },
    }; // 显示占位符
    chartInstance.setOption(option, true);
  } else {
    option.graphic = null; // 不显示占位符
    chartInstance.setOption(option, true);
  }
};
const debounceResize = debounce(() => {
  chartInstance.resize();
});
onMounted(() => {
  initChart();
  window.addEventListener("resize", debounceResize);
});
// watch监听
watch(
  () => props.data,
  (nu) => {
    updateChart();
  }
);
onUnmounted(() => {
  window.removeEventListener("resize", debounceResize);
});
</script>

<style lang="scss" scoped>
.search-request-trend {
  height: 400px;
  display: flex;
  .first {
    width: 190px;
  }
  .num {
    margin-bottom: 5px;
  }
}
</style>
