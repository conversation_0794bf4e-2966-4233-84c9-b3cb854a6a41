<template>
  <el-table
    :data="table.tableData"
    highlight-current-row
    @current-change="handleCurrentChange"
    :style="{ width: width }"
    :current-row-key="currentRow?.[rowKey]"
    :row-key="rowKey"
    :max-height="height"
    v-bind="{
      border: true,
      'show-overflow-tooltip': {
        placement: 'left',
      },
      ...$attrs,
    }"
    ref="tableRef"
  >
    <el-table-column
      v-for="(item, index) of table.columns"
      :key="index"
      resizable
      v-bind="{
        sortable: false,
        ...item,
      }"
    >
      <!-- 默认项 -->
      <template #default="scope" v-if="!item.custom">
        {{ item.type=='number'?util.formatNumber(scope.row[item.prop]):getText(scope.row[item.prop]) }}
      </template>
      <template #default="scope" v-if="item.slotName">
        <slot :name="item.slotName" :row="scope.row"></slot>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from "vue";
import { copyText, getText } from "@/utils/helpers";
import * as util from "@/utils/common";
const tableRef = ref(null);
const props = defineProps({
  tableData: {
    type: Array,
    default: [],
  },
  rowKey: {
    type: String,
    default: "id",
  },
  columns: {
    type: Array,
    default: [],
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "400",
  },
});
const transformListData = (data) => {
  return data.map((x) => {
    x.hasResultPercentageRender = `${x.hasResultPercentage}%`;
    x.trafficPercentageRender = `${x.trafficPercentage}%`;
    return x;
  });
};
const transformListColumns = (columns) => {
  return columns.map((x) => {
    // if (x.prop == "hasResultPercentage" || x.prop == "trafficPercentage") {
    //   x.prop += "Render";
    // }
    return x;
  });
};
const table = ref({
  columns: transformListColumns(props.columns),
});
const currentRow = ref(null);
const handleCurrentChange = (newVal) => {
  currentRow.value = newVal;
};

// 监听 tableData 的变化，如果变化了，重置 currentRow
watch(
  () => props.tableData,
  (newTableData) => {
    table.value.tableData = transformListData(newTableData);
    if (newTableData.length > 0) {
      currentRow.value = newTableData[0]; // 设置默认当前行为第一行
    }
  },
  { immediate: true, deep: true }
);
const getCurrentRow = () => {
  return currentRow.value;
};
defineExpose({
  getCurrentRow,
});
</script>

<style>
.el-table .current-row {
  background-color: #f5f7fa; /* 自定义高亮颜色 */
}
</style>
