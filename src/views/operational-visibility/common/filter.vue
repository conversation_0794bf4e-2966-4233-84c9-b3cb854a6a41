<template>
  <div>
    <my-query
      :queryItems="queryItems"
      :refreshBtn="{ show: false }"
      @search="events.search"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import moment from "moment";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { useVModel } from "@vueuse/core";
import { getProductPlan, getBusinessApplication } from "@/api/traffic-view";
import useStore from "@/store";
import { cloneDeep } from "lodash";
const { word, api } = useStore();
const props = defineProps({
  getInfoApi: { type: Function, default: (e: any) => e },
  show: { type: Array, default: () => [] },
});
const _query = ref({});
const emit = defineEmits(["change"]);
const shortcuts = [
  {
    text: "本周",
    value: () => {
      const start = moment().startOf("isoWeek").format("YYYY-MM-DD");
      var end = moment().endOf("isoWeek").format("YYYY-MM-DD");
      return [start, end];
    },
  },
  {
    text: "本月",
    value: () => {
      const start = moment().startOf("month").format("YYYY-MM-DD");
      var end = moment().endOf("month").format("YYYY-MM-DD");

      return [start, end];
    },
  },
  {
    text: "全年",
    value: () => {
      const start = moment().startOf("year").format("YYYY-MM-DD");
      var end = moment().endOf("year").format("YYYY-MM-DD");
      return [start, end];
    },
  },
];
const queryItems = ref<any>({
  appIds: {
    modelValue: [],
    type: "select",
    label: "",
    width: "350px",
    options: [],
    attrs: {
      placeholder: "业务应用",
      multiple: true,
      'collapse-tags':false
    },
    hidden: computed(() => !props.show.includes("appIds")).value,
  },
  productIds: {
    modelValue: [],
    type: "select",
    label: "",
    width: "350px",
    options: [],
    attrs: {
      placeholder: "产品方案",
      multiple: true,
      'collapse-tags':false
    },
    hidden: computed(() => !props.show.includes("productIds")).value,
  },
  regionCodes: {
    modelValue: [],
    type: "select",
    label: "",
    custom: "region",
    width: "180px",
    options: [],
    attrs: {
      placeholder: "环境",
      multiple: true,
    },
  },
  date: {
    type: "daterange",
    label: "",
    modelValue: [],
    attrs: {
      placeholder: "时间",
      shortcuts: shortcuts,
      clearable:false
    },
  },
});
const updateEnvirenment = () => {
  word.getAreaList().then((values: any) => {
    queryItems.value.regionCodes.options = values.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.code,
    }));
  });
};
const updateDate = () => {
  const start = moment().subtract(8, "days").format("YYYY-MM-DD");
  const end = moment().subtract(1, "days").format("YYYY-MM-DD");
  _query.value.date = [start, end];
  queryItems.value.date.modelValue = [start, end];
};
const getApplication = () => {
  getBusinessApplication().then((res) => {
    queryItems.value.appIds.options = res.data.map((item: any) => {
      return {
        ...item,
        value: item.appId,
        label:`${item.name}(${item.appId})`,
      };
    });
  });
};
const getPlan = () => {
  getProductPlan().then((res) => {
    queryItems.value.productIds.options = res.data.map((item: any) => ({
      ...item,
      value: item.id,
      label: item.name,
    }));
  });
};
/* events */
const events = reactive({
  search: (val: any) => {
    _query.value = val;
    getInfos();
  },
});
const getParams = () => {
  const params = {
    ..._query.value,
    endDate: _query.value?.date?.[1],
    beginDate: _query.value?.date?.[0],
    regionCodes: _query.value?.regionCodes?.join(","),
    appIds: _query.value?.appIds?.join(","),
    productIds: _query.value?.productIds?.join(","),
  };
  delete params.date;
  Object.keys(params).forEach((key) => {
    if (params[key] === "") {
      delete params[key];
    }
  });
  return params;
};
const getInfos = () => {
  props.getInfoApi(getParams());
};

defineExpose({
  getParams,
});
onMounted(async () => {
  updateDate();
  getApplication();
  getPlan();
  updateEnvirenment();
  getInfos();
});
</script>

<style lang="scss" scoped></style>
