<template>
  <div class="title">
    {{ title }}
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import moment from "moment";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { useVModel } from "@vueuse/core";
import useStore from "@/store";
const { word, api } = useStore();
const props = defineProps(["title"]);

</script>

<style lang="scss" scoped>
.title{
  text-align:center;
  padding: 12px;
  border-bottom: 1px solid var(--el-border-color);
  margin: 10px 0;
  font-weight:bold;
  background-color: rgba(242, 242, 242, 1);
}</style>
