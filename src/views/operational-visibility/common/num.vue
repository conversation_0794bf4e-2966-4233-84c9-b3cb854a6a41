<template>
  <div class="number-container">
    <div class="num">{{util.formatNumberToChinese(number)}}</div>
    <div class="number">({{util.formatNumber(number)}})</div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import moment from "moment";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { useVModel } from "@vueuse/core";
import { getProductPlan, getBusinessApplication } from "@/api/traffic-view";
import useStore from "@/store";
import * as util from "@/utils/common";
import { cloneDeep } from "lodash";
const { word, api } = useStore();
const props = defineProps({
  number: { type: Number },
});
</script>

<style lang="scss" scoped></style>
