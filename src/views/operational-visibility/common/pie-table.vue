<template>
  <div class="product-plan-traffic border">
    <div style="width: 60%">
      <div class="sub-name">{{ subName }}</div>
      <div class="content">
        <div :style="{ width: pie.width }">
          <PieChart :data="pie.data || []" :series="pie.series" />
        </div>
        <clickTable
          ref="clickTableRef"
          :width="table.width"
          :tableData="table.data"
          :columns="table.columns"
          height= "300px"
          rowKey="code"
        >
          <template #name="scope">
            <div>{{ scope.row.name }}</div>
            <div>({{ scope.row.code }})</div>
          </template>
        </clickTable>
      </div>
    </div>
    <div style="width: 38%">
      <div class="sub-name">{{ subSonName }}</div>
      <div style="height: 300px" class="content">
        <stashBarChart1
          v-if="!type"
          :data="bar.data"
          :transfer="bar.transfer"
          :xKey="bar.xKey"
        ></stashBarChart1>
        <stashBarChart
          v-if="type == 'custom'"
          :data="bar.data"
          :yKeys="[
            {
              value: 'hasResultCount',
              label: '有结果数',
              prencentKey: 'hasResultPercentage',
            },
            { value: 'noResultCount', label: '无结果数' },
          ]"
        ></stashBarChart>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { map, cloneDeep, debounce } from "lodash";
import PieChart from "@/components/echarts/pie.vue";
import stashBarChart from "@/components/echarts/stashBar.vue";
import stashBarChart1 from "@/components/echarts/stashBar-1.vue";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
import clickTable from "../common/click-table.vue";
import { computed } from "vue";
const props = defineProps({
  subSonName: {
    type: String,
    default: "",
  },
  subName: {
    type: String,
    default: "",
  },
  pie: {
    type: Object,
    required: true,
  },
  table: {
    type: Object,
    required: true,
  },
  bar: {
    type: Object,
    required: true,
  },
  transfer: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: "",
  },
});
const clickTableRef = ref(null);

const currentRow = computed(() => {
  return clickTableRef.value?.getCurrentRow() || {};
});
const getTableCurrentRow = () => {
  return currentRow;
};
defineExpose({
  getTableCurrentRow,
});
/* events */
const events = reactive({});
</script>

<style lang="scss" scoped>
.product-plan-traffic {
  display: flex;
  justify-content: space-between;
  max-height: 450px;
  > div {
    .content {
      display: flex;
      height: calc(100% - 40px);
    }
  }
}
</style>
