<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="container" >
      <Filter ref="filterRef" :getInfoApi="getInfoApi" :show="['productIds','regionCodes']"/>
      <headTitle title="检索请求流量分析" />
      <searchRequestTotal :data="trafficInfo"/>
      <searchRequestChart :data="trafficInfo" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { getEnvironmentalTraffic } from "@/api/traffic-view";
import Filter from "../common/filter.vue";
import searchRequestTotal from "./search-request-total.vue";
import searchRequestChart from "./search-request-chart.vue";
import headTitle from "../common/head-title.vue";
const routeName = "environmental-traffic-analysis";
const filterRef = ref()
let trafficInfo = ref([]);
/* events */
const events = reactive({});
const getInfoApi =(params)=>{
  return new Promise((resolve)=>{
    getEnvironmentalTraffic(params).then((res) => {
    trafficInfo.value = res.data?.result||[]
  });
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
  overflow: auto;
  ::v-deep {
    .num {
      font-size: 30px;
      font-weight: 400;
    }
    .border {
      padding: 10px;
      border: 1px solid var(--el-border-color);
      margin-right: 10px;
    }
    .sub-name {
      color: rgba(0, 0, 0, 0.427450980392157);
    }
  }
}
</style>
