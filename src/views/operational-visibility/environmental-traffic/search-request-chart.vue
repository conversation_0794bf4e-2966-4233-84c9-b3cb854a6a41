<template>
  <div class="search-request-chart">
    <el-tabs v-model="activeCode" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane
        :label="item.regionName"
        :name="item.regionCode"
        v-for="item in data"
      >
      </el-tab-pane>
    </el-tabs>
    <stashBarChart
      :data="sonData"
      xKey="date"
      :yKeys="[
        {
          value: 'hasResultCount',
          label: '有结果数',
          prencentKey: 'hasResultPercentage',
        },
        { value: 'noResultCount', label: '无结果数' },
      ]"
      :transfer="true"
    ></stashBarChart>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed,watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import Filter from "../common/filter.vue";
import type { TabsPaneContext } from "element-plus";
import PieChart from "@/components/echarts/pie.vue";
import stashBarChart from "@/components/echarts/stashBar.vue";
/* events */
const events = reactive({});
const props = defineProps(["data"]);
const activeCode = ref("hf");
const sonData = computed(()=>{
return props.data.find(item=>item.regionCode==activeCode.value)?.son||[]
})
const handleClick = (tab: TabsPaneContext, event: Event) => {
  activeCode.value = tab.props.name; 
};
// watch监听
watch(
  () => props.data,
  (val) => {
    activeCode.value = val?.[0]?.regionCode||''; 
  },
  {deep:true}
);
onMounted(async () => {});
</script>

<style lang="scss" scoped>
.search-request-chart {
  height: calc(100% - 350px);
}
</style>
