<template>
  <div class="search-request-total">
    <div class="border" v-for="item in data">
      <div class="sub-name">{{ item.regionName }}</div>
      <Num :number="item?.requestCount"/>
      <div class="footer">
        <div><span class="sub-name">有结果数</span><span>{{ util.formatNumber(item?.hasResultCount) || "暂无数据" }}</span></div>
        <div><span class="sub-name">有结果率</span><span>     {{ item?.hasResultPercentage+'%' || "暂无数据" }}</span></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import * as util from "@/utils/common";
import Num from "../common/num.vue";
/* events */
const events = reactive({});
const props = defineProps(["data"]);
onMounted(async () => {});
</script>

<style lang="scss" scoped>
.search-request-total {
  width: 100%;
  display: flex;
  .border {
    flex: 1;
    height: 188px;
    max-width: 300px;
    .image {
      background: url("@/assets/images/echarts.png") no-repeat;
    }
    .footer {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      span{
        margin-right: 10px;
      }
      ::v-deep{
        .number-container{
          display: inline-block;
        }
      }
    }
  }
}
</style>
