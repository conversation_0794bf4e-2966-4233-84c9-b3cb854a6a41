<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="container">
      <!--  -->
      <Filter
        ref="filterRef"
        :getInfoApi="getInfoApi"
        :show="['productIds', 'regionCodes','appIds']"
      />
      <div v-for="item in trafficInfo">
        <headTitle :title="item.name" />
        <component  :is="componentMap[item.model]" :data="item.data" :title="item.name"/>
      </div>     
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { assign } from "lodash";

import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import { getPoint } from "@/api/traffic-view";
import Filter from "../common/filter.vue";
import headTitle from "../common/head-title.vue";
import model1 from "./model-1.vue";
import model2 from "./model-2.vue";
import model3 from "./model-3.vue";
const routeName = "environmental-traffic-analysis";
const filterRef = ref();
let trafficInfo = ref({});
const componentMap = {
  1: model1,
  2: model2,
  3: model3
};
/* events */
const events = reactive({});
const getInfoApi = (params) => {
  return new Promise((resolve) => {
    getPoint(params).then((res) => {
      trafficInfo.value = res.data;
    });
  });
};
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
  overflow: auto;
  ::v-deep {
    .num {
      font-size: 30px;
      font-weight: 400;
    }
    .border {
      padding: 10px;
      border: 1px solid var(--el-border-color);
      margin-right: 10px;
    }
    .sub-name {
      color: rgba(0, 0, 0, 0.427450980392157);
    }
    .flex{
      display: flex;
    }
  }
}
</style>
