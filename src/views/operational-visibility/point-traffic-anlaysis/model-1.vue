<template>
  <pie-table
    v-bind="{
      pie: {
        width: '40%',
        data: item.num,
        series: {
          center: ['30%', '55%'],
          radius: ['17%', '50%'],
          label: {
            normal: {
              position: 'inner',
              formatter: '{d}%', // 在这里显示百分比
            },
          },
        },
      },
      table: {
        data: item.overview,
        width: '60%',
        columns
      },
      bar:{
        data: item.trend,
        xKey:'date',
      },
      'sub-name': item.product,
      'sub-son-name': '按时间分析',
      columns,
      xKey: 'date',
    }"
    v-for="item in data"
  />
</template>

<script lang="ts" setup>
import echarts from "@/components/echarts/index.ts";
import { ref, reactive, onMounted, onUnmounted, watch, computed } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import { map, cloneDeep, debounce } from "lodash";
import pieTable from "../common/pie-table.vue";
import useCtx from "@/hooks/useCtx";
const { $app, proxy } = useCtx();
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  data: {
    type: Array,
    required: true,
  },
  option: {
    type: Object,
    default: () => ({}),
  },
  series: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    default: "",
  },
});
const clickTableRef = ref(null);
//列配置
const columns = computed(()=>{
  return [
  { prop: "name", label: props.title.split('(')[0]||'', minWidth: 80 },
  { prop: "requestCount", label: "请求数", minWidth: 80,type:'number', sortable:true },
  { prop: "hasResultCount", label: "有结果数", minWidth: 90 ,type:'number', sortable:true },
  { prop: "trafficPercentage", label: "流量占比(%)", minWidth: 100 , sortable:true},
  { prop: "hasResultPercentage", label: "有结果占比(%)", minWidth: 120 , sortable:true},
]
});
/* events */
</script>

<style lang="scss" scoped>
.product-plan-traffic {
}
</style>
