<template>
  <div class="product-plan-traffic">
    <el-tabs v-model="activeCode" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane
        :label="item.product"
        :name="item.product"
        v-for="item in data"
      >
      </el-tab-pane>
    </el-tabs>
    <div style="height: 280px">
      <div style="width: 75%">
        <span class="sub-name">总体占比</span>
        <div class="content">
          <div
            v-for="(item,index) in pieData.overview"
            :class="{
              border: true,
              'active-border': activePie == item.name,
              'chart-container': true,
            }"
            @click="handlePieClick(item)"
          >
            <PieChart
              ref="PieChartRef"
              :legendShow="false"
              :title="handleTitle(item.name)"
              :data="item?.num || []"
              :color="[echartOptions.color[index], '#ebeef5']"
              :series="{
                center: ['50%', '50%'],
                radius: ['25%', '60%'],
                label: {
                  normal: {
                    position: 'inner',
                    formatter: '{d}%', // 在这里显示百分比
                  },
                },
              }"
            />
          </div>
        </div>
      </div>
      <div style="width: 25%">
        <span class="sub-name">有结果率</span>
        <div class="num">{{ resultData?.hasResultPercentage }}%</div>
        <el-progress
          :text-inside="true"
          :stroke-width="26"
          :percentage="resultData?.hasResultPercentage"
        />
        <div :style="{ 'margin-top': '10px', 'text-align': 'center' }">
          <span
            :class="{
              trangle_red: resultData?.trend?.value > 0,
              trangle_green: resultData?.trend?.value < 0,
            }"
          ></span
          >环比{{ resultData?.trend?.value }}％
        </div>
      </div>
    </div>
    <div style="height: 400px">
      <stashBar1
        :data="barData"
        xKey="date"
        :yKeys="[
          {
            value: 'hasResultCount',
            label: '有结果数',
            prencentKey: 'hasResultPercentage',
          },
          { value: 'noResultCount', label: '无结果数' },
        ]"
        :transfer="true"
      ></stashBar1>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import Filter from "../common/filter.vue";
import type { TabsPaneContext } from "element-plus";
import PieChart from "@/components/echarts/pie.vue";
import stashBar1 from "@/components/echarts/stashBar-1.vue";
import echartOptions from '@/components/echarts/customized.json'
const { proxy, $app, $auth } = useCtx();
/* events */
const events = reactive({});
const props = defineProps(["data"]);
const activeCode = ref("hf");
const activePie = ref("");

const handleClick = (tab: TabsPaneContext, event: Event) => {
  activeCode.value = tab.props.name;
  nextTick(() => {  
    proxy.$refs.PieChartRef.map(item=>{
      item.resize();
    })
  });
};
const handlePieClick = (data) => {
  activePie.value = data.name;
};
const pieData = computed(() => {
  return props.data.find((item) => item.product == activeCode.value) || [];
});
const resultData = computed(() => {
  return (
    pieData.value.overview.find((item) => item.name == activePie.value) || []
  );
});
const barData = computed(() => {
  return (
    props.data.find((item) => item.product == activeCode.value)?.trend || []
  );
});
const handleTitle = (name) => {
  return {
    text: name,
    left: 0,
    textStyle: { fontSize: 12, fontWeight: "normal" },
  };
};
// watch监听
watch(
  () => props.data,
  (val) => {
    activeCode.value = val?.[0]?.product || "";
    nextTick(() => {  
    proxy.$refs.PieChartRef.map(item=>{
      item.resize();
    })
  });
  },
  { immediate: true, deep: true }
);
// watch监听
watch(
  () => activeCode.value ,
  (val) => {
    const pies = props.data.find(item=>item.product == val)
    activePie.value = pies?.overview?.[0]?.name || "";
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.product-plan-traffic {
  > div {
    display: flex;
    margin-top: 5px;
    .content {
      height: calc(100% - 50px);
      display: flex;
      overflow-x: auto;
      box-sizing: content-box;
      padding-top: 5px;
      .chart-container {
        height: calc(100% - 30px);
        flex: 1;
        min-width: 200px;
        position: relative;
        transition: transform 0.2s;
        border-color: var(--el-border-color);
      }
      .active-border {
        border-color: var(--el-color-primary);
      }
      .chart-container:hover {
        transform: translateY(-3px);
      }
    }
  }
}
</style>
