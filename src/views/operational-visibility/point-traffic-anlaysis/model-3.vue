<template>
  <div class="search-request-chart">
    <el-tabs v-model="activeCode" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane
        :label="item.product"
        :name="item.product"
        v-for="item in data"
      >
      </el-tab-pane>
    </el-tabs>
    <div class="border">
      <div class="flexBetweenStart">
        <span class="sub-name">场景标签总量占比</span>
        <my-query ref="myQueryRef" :queryItems="queryItems"></my-query>
      </div>
      <div class="flexBetweenStart" style="height: 300px">
        <div style="width: 45%">
          <PieChart
            :data="pieData || []"
            :series="{
              center: ['25%', '50%'],
              radius: ['20%', '50%'],
              label: {
                normal: {
                  position: 'inner',
                  formatter: '{d}%', // 在这里显示百分比
                },
              },
            }"
          />
        </div>
        <clickTable
          ref="clickTableRef"
          width="50%"
          :tableData="tableData"
          :columns="columns"
        >
        </clickTable>
      </div>
    </div>
    <div>
      <span class="sub-name">按时间分析</span>
      <div style="height: 340px">
        <stashBar1 :data="barData" xKey="date" :transfer="true"></stashBar1>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import Filter from "../common/filter.vue";
import type { TabsPaneContext } from "element-plus";
import PieChart from "@/components/echarts/pie.vue";
import stashBar1 from "@/components/echarts/stashBar-1.vue";
import clickTable from "../common/click-table.vue";
/* events */
const events = reactive({});
const props = defineProps(["data"]);
const activeCode = ref("hf");
//列配置
const columns = ref([
  { prop: "name", label: "", minWidth: 120 },
  { prop: "trafficPercentage", label: "占比(%)", minWidth: 80, sortable: true },
  { prop: "requestCount", label: "总数", minWidth: 90, sortable: true },
  { prop: "hasResultCount", label: "有结果数", minWidth: 110,type:'number', sortable: true },
  { prop: "hasResultPercentage", label: "有结果占比(%)", minWidth: 120, sortable: true },
]);
const sonData = computed(() => {
  return (
    props.data.find((item) => item.regionCode == activeCode.value)?.son || []
  );
});
const query = ref("oneLevel");
const pieData = computed(() => {
  return (
    props.data.find((item) => item.product == activeCode.value)?.[query.value]
      ?.num || []
  );
});
const tableData = computed(() => {
  return (
    props.data.find((item) => item.product == activeCode.value)?.[query.value]
      ?.overview || []
  );
});
const barData = computed(() => {
  return (
    props.data.find((item) => item.product == activeCode.value)?.[query.value]
      ?.trend || []
  );
});
const queryItems = ref<any>({
  appId: {
    label: "场景标签",
    modelValue: "oneLevel",
    type: "select",
    options: [
      { value: "oneLevel", label: "一级维度" },
      { value: "twoLevel", label: "二级维度" },
      { value: "threeLevel", label: "三级维度" },
    ],
    events: {
      change: (val) => {
        query.value = val;
      },
    },
  },
});
const handleClick = (tab: TabsPaneContext, event: Event) => {
  activeCode.value = tab.props.name;
};
// watch监听
watch(
  () => props.data,
  (val) => {
    activeCode.value = val?.[0]?.product || "";
    console.log(val?.[0]?.product);
    
  },
  { immediate:true,deep: true }
);
</script>

<style lang="scss" scoped>
.search-request-chart {
  .flexBetweenStart {
    > div {
      height: 100%;
    }
  }
}
</style>
