<template>
  <el-card
    style="max-width: 400px; margin: 10px auto; cursor: pointer"
    @click="handleRedirect"
  >
    <template #header v-if="head">
      <div class="card-header">
        <span>{{ head }}</span>
      </div>
    </template>
    <div class="title">
      {{ title }}
    </div>
    <div class="describe">
      {{ describe }}
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
const routeName = "report-anlaysis";
// 全局变量
const { $app } = useCtx();
const props = defineProps({
  title: { type: String }, // 按钮类型
  head: { type: String },
  describe: { type: String },
  href: { type: String },
  mode: { type: String },
  dynamic: { type: Boolean },
  keys: { type: String },
});
/* events */
const handleRedirect = () => {
  $app.$router.push({
    name: `${routeName}::details`,
    query: {
        metaLabel: [props.title],
        type:props.href,
        mode:props.mode,
        keys:props.keys,
        dynamic: props.dynamic
      },
  });
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-card__header {
    background-color: rgba(64, 142, 213, 1);
    color: white;
    font-weight: bold;
  }
}
.title {
  color: var(--el-color-primary);
}
</style>
