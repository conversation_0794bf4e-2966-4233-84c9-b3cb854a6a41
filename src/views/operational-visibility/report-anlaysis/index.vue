<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="container">
      <el-row justify="center">
        <el-col :span="12">
          <Card
            head="报表分析-业务应用"
            title="业务应用流量汇总表"
            describe="按业务应用检索请求总量、有结果数、无结果数、有结果率情况"
            href="app-summary"
             keys=""
          />
          <Card
            href="app-day"
            title="业务应用流量分析日报表"
            describe="实时查询每日业务应用检索请求总量、有结果数、无结果数、有结果率情况"
            keys="date"
          />
        </el-col>
        <el-col :span="12">
          <Card
            href="product-day"
            head=" 报表分析-产品方案"
            title="产品方案流量分析日报表"
            describe="产品方案按日期段查询检索请求总量、有结果数、无结果数、有结果率情况"
             keys="date,app,product"
          />
        </el-col>
      </el-row>
      <el-row justify="center">
        <el-col :span="12"
          ><Card
            v-for="(item,index) in pointReportCard"
            :href="item.code"
            :head="index===0?'报表分析-埋点分析':''"
            :title="item.name"
            :dynamic="item._dynamic"
            mode="point"
            keys="date,product"
        /></el-col>
        <el-col :span="12">
          <Card
            href="app-day-env"
            head="报表分析-环境流量分析"
            title="按环境日期段统计业务应用流量明细"
            describe="按环境统计日期段内业务应用查询检索请求总量、有结果数、无结果数、有结果率情况" 
            keys="date,region"
            />

          <Card
            href="product-day-env"
            title="按环境日期段统计产品方案流量明细"
            describe="按环境统计日期段内产品方案检索请求总量、有结果数、无结果数、有结果率情况"
            keys="date,app,product,productVersion"
        /></el-col>
      </el-row>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from "vue";
import { assign } from "lodash";
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import useCtx from "@/hooks/useCtx";
import Card from "./card.vue";
import { getPointReportCard,getDynamicsBurialType } from "@/api/traffic-view";
const routeName = "report-anlaysis";
const query = ref({});
const filterRef = ref();
let pointReportCard = ref([]);
/* events */
const events = reactive({});
const getPointData = () => {
  getPointReportCard().then((res) => {
    pointReportCard.value = pointReportCard.value.concat(res.data);
  });
  getDynamicsBurialType().then((res) => {
    pointReportCard.value = pointReportCard.value.concat(res.data.map(item=>({
      ...item,
      _dynamic:true
    })));
  });
};
getPointData();
</script>

<style lang="scss" scoped>
.container {
  padding: 12px 16px;
  overflow: auto;
  ::v-deep {
    .el-row {
      margin-bottom: 20px;
    }
  }
}
</style>
