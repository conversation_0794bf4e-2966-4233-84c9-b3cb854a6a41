<template>
  <page-wrapper :route-name="`${routeName}::app-summary::`">
    <div class="report-analysis-container" v-if="keys.length">
      <div class="flexBetweenStart">
        <Filter ref="filterRef" :show="['appIds', 'regionCodes']" :getInfoApi="getInfoApi" />
        <my-operation>
          <template #buttonGroup>
            <my-button type="export" @click="exportExcel" style="margin-left: 10px">导出</my-button>
          </template>
        </my-operation>
      </div>
      <div class="flexBetweenStart report-analysis-content">
        <Tree :treeData="treeData" @updateTable="(data) => treeNode = data" :treeNode="treeNode" :keys="keys" />
        <div class="report-analysis-table">
          <el-card>
            <myTable :withOrder="false" ref="myTableRef" :columns="columns" :tableData="treeNode?.table"
              :transformListData="transformListData" :withPagination="false" :loadImmediately="true">
              <template #query>
              </template>
            </myTable>
          </el-card>
        </div>
      </div>
    </div>
    <div v-else>
      <myTable :withOrder="false" ref="myTableRef" :columns="columns" :tableData="treeData"
        :transformListData="transformListData" :withPagination="false" :loadImmediately="true">
        <template #query>
          <div class="flexBetweenStart">
            <Filter ref="filterRef" :show="['appIds', 'regionCodes']" :getInfoApi="getInfoApi" />
            <my-operation>
              <template #buttonGroup>
                <my-button type="export" @click="exportExcel" style="margin-left: 10px">导出</my-button>
              </template>
            </my-operation>
          </div>
        </template>
      </myTable>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from "vue";
import { cloneDeep } from 'lodash';
import { timeC } from "turing-plugin";
import { useI18n } from "vue-i18n";
import * as util from "@/utils/common";
import { getReport, exportReport, getDynamicsColumn, exportDynamicsReport } from "@/api/traffic-view";
import Tree from "./tree.vue";
import useColumns from "./useColumns";
import Filter from "../common/filter.vue";
import Card from "./card.vue";
import useCtx from "@/hooks/useCtx";
const routeName = "report-anlaysis";
// 全局变量
const { $app, proxy } = useCtx();
const myTableRef = ref();
const query = ref({});
const filterRef = ref();
let treeData = ref([]);
let treeNode = ref({});
let initTableData = ref([]);
const keys = $app.$route.query.keys ? $app.$route.query.keys.split(',') : []
const defaultColumns = ref(useColumns($app.$route.query.type, $app.$route.query.mode).filter(item => !keys.includes(item.prop)) || [])
const columns = ref(keys.length ? [] : defaultColumns.value)
// 转换接口返回的数据
const transformListData = (data: any) => {
  return data.map((x: any) => {
    // x.hasResultPercentageRender = `${x.hasResultPercentage}%`
    // x.trafficPercentageRender = `${x.trafficPercentage}%`
    return x;
  });
};
const getInfoApi = (params) => {
  return new Promise((resolve) => {
    if ($app.$route.query.dynamic === 'true') {
      getDynamicsColumn($app.$route.query.type, params).then((res) => {
        treeData.value = levelData(cloneDeep(res.data))
      })
    } else {
      getReport($app.$route.query.type, params).then((res) => {
        treeData.value = levelData(cloneDeep(res.data))
      });
    }
  });
};
const exportExcel = () => {
  if ($app.$route.query.dynamic === 'true') {
    exportDynamicsReport($app.$route.query.type, proxy.$refs.filterRef.getParams()).then(
      (res) =>
        util.downloadFile(
        res,
        `${$app.$route.query.metaLabel}信息.xlsx`
      )
    );
  } else {
    exportReport($app.$route.query.type, proxy.$refs.filterRef.getParams()).then(
      (res) =>
        util.downloadFile(
          res,
          `${$app.$route.query.metaLabel}信息.xlsx`
        )
    );
  }
};
/* 表格 */
const levelData = (data, level = 0, parentName = '') => {
  return data.map((item, index) => {
    item.level = level;
    item.fullName = parentName + item[keys[level]]
    item.disabled = true
    if (item.details) {
      item.details.forEach((detail) => levelData([detail], level + 1, item.fullName));
    }
    if (level === keys.length - 1) {
      item.disabled = false
      item.table = item.details.map((x:any)=>{
        return {
          ...x,
          ...x.burialItemMap
        }
      })
      delete item.details
    }
    return item;
  });
}
watch(treeNode, () => {
  if ($app.$route.query.dynamic === 'true') {
    columns.value = util.generateTableColumns(treeNode.value.table,'burialItemMap').concat(defaultColumns.value)
  }else{
    columns.value = defaultColumns.value
  }
})
</script>

<style lang="scss" scoped>
.report-analysis-container {
  padding: 12px 16px;
  overflow: auto;

  .report-analysis-content {
    height: calc(100% - 50px);

    ::v-deep {
      .el-card {
        height: 100%;

        .el-card__body {
          height: 100%;
        }
      }

      .left-card {
        overflow: auto;
      }
    }

    .report-analysis-table {
      width: calc(100% - 383px);
      height: 100%;
      margin-left: 10px;

      ::v-deep {
        .table-page-wrapper>div {
          padding: 0;
        }

        .table-wrapper {
          padding: 0;
        }

        .el-table {
          width: 100%;
        }
      }
    }


  }
}
</style>
