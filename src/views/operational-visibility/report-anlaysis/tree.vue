<template>
  <el-card class="left-card" >
    <el-tree
      ref="treeRef"
      style="max-width: 600px"
      :data="treeData"
      node-key="fullName"
      :default-expanded-keys="expandArr"
      :props="defaultProps"
      :auto-expand-parent="true"
      :expand-on-click-node="false"
      @node-expand="handleNodeExpand"
    >
      <template #default="{ node, data }">
        <span :class="{ 'custom-tree-node': true,active:data.fullName === treeNode.fullName}" @click="setCurrentKey(data)" :title="data[keys[node.level-1]]">
          <span class="text">
            {{ data[keys[node.level-1]] }}
          </span>
        </span>
      </template>
    </el-tree>
  </el-card>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick, defineEmits, onMounted } from "vue";
import { ElTree } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import useCtx from "@/hooks/useCtx";
import * as metaWordApi from "@/api/eval-manage";
import { computed } from "vue";
import { cloneDeep } from "lodash";
const { $app, proxy, $router } = useCtx();
const props = defineProps({
  treeData: { type: Array, default: [] },
  treeNode: { type: Object, default: {} },
  keys: { type: Array, default: [] },
});
const emits = defineEmits(["updateTree", "updateTable"]);
const expandArr = ref([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const defaultProps = {
  children: "details",
  label: "name",
  disabled: "disabled"
};

const getCurrentNode = () => {
  return treeRef.value?.getCurrentNode();
};
const handleNodeExpand = (data: any, node: any, el: any) => {
  const expand: boolean = node.expand;
  if (expand) {
    expandArr.value.push(data.id);
  } else {
    expandArr.value.splice(expandArr.value.indexOf(node.id), 1);
  }
};
const setCurrentKey = (data: any) => {
  if(!data?.details){
    emits("updateTable", cloneDeep(data))
  }
};
const firstNode = computed(() => {
  const findFirstLeafNode = (nodes: any[]): any => {
    for (const node of nodes) {
      if (!node.details || node.details.length === 0) {
        return node;
      }
      const result = findFirstLeafNode(node.details);
      if (result) {
        return result;
      }
    }
    return null;
  };

  if (props.treeData && props.treeData.length > 0) {
    return findFirstLeafNode(props.treeData); 
  }
  return null;
});
watch(()=>props.treeData,(val)=>{
  if(firstNode.value){
    setCurrentKey(firstNode.value)
    expandArr.value.push(firstNode.value.fullName)
  }else{
    setCurrentKey({table:[]})

  }

},{
  deep:true,
  immediate:true
})
defineExpose({
  getCurrentNode,
});
</script>

<style lang="scss" scoped>
.left-card {
  width: 375px;
  flex-shrink: 0;
  height: 100%;
  .title {
    font-weight: 550;
    display: flex;
    justify-content: space-between;
    i {
      margin-left: 5px;
    }
  }
  .layout-tab-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    .sub-title {
      margin-right: 8px;
    }
  }
  .custom-tree-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    padding: 0 3px;
    .status {
      display: inline-block;
      height: 8px;
      width: 8px;
      border-radius: 8px;
      vertical-align: middle;
      margin-right: 2px;
      &.active {
        background-color: var(--el-color-primary);
      }
      &.error {
        background-color: var(--el-color-danger);
      }
    }
    .text {
      display: inline-block;
      align-content: center;
      @include no-wrap();
      width: 270px;
      word-break: break-all;
    }
  }
  .active{
    background-color: var(--el-color-primary-light-9);
  }
  ::v-deep(.disabled-node) {
  cursor: not-allowed;
  pointer-events: none; /* 禁止鼠标事件 */
}
  ::v-deep {
    .el-input {
      margin: 12px 0 10px 0;
    }
  }
}
</style>