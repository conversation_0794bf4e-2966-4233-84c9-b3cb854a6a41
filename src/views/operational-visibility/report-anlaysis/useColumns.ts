import { ref, onMounted, onUnmounted } from "vue";
// 定义公共列宽度
const commonWidths = {
  app: 220,
  requestCount: 180,
  hasResultCount: 180,
  noResultCount: 180,
  hasResultPercentage: 180,
  trafficPercentage: 180,
  date: 150,
  product: 220,
  productVersion: 220,
};
const commonColumns = [
  {
    prop: "requestCount",
    label: "请求总数",
    type:'number',
    width: commonWidths.requestCount,
    sortable: true,
  },
  {
    prop: "hasResultCount",
    label: "有结果",
    type:'number',
    width: commonWidths.hasResultCount,
    sortable: true,
  },
  {
    prop: "noResultCount",
    label: "无结果",
    type:'number',
    width: commonWidths.noResultCount,
    sortable: true,
  },
  {
    prop: "hasResultPercentage",
    label: "有结果占比（%）",
    width: commonWidths.hasResultPercentage,
    sortable: true,
  },
  {
    prop: "trafficPercentage",
    label: "流量占比（%）",
    width: commonWidths.trafficPercentage,
    sortable: true,
  },
];
const columns = {
  // 业务应用流量汇总表
  "app-summary-columns": [
    { prop: "app", label: "业务应用", minWidth: commonWidths.app },
    ...commonColumns,
  ],
  // 业务应用流量分析日报表
  "app-day-columns": [
    {
      prop: "date",
      label: "日期",
      custom: "link",
      minWidth: 150,
    },
    { prop: "app", label: "业务应用", minWidth: commonWidths.app },
    ...commonColumns,
  ],
  // 按环境日期段统计业务应用流量明细
  "app-day-env-columns": [
    {
      prop: "date",
      label: "日期",
      custom: "link",
      width: 200,
    },
    { prop: "region", label: "环境", width: 150 },
    { prop: "app", label: "业务应用", minWidth: commonWidths.app },
    ...commonColumns,
  ],
  // 按环境日期段统计产品方案流量明细
  "product-day-env-columns": [
    {
      prop: "date",
      label: "日期",
      custom: "link",
      minWidth: 200,
      sortable: true,
    },
    { prop: "app", label: "业务应用", width: 220 },
    {
      prop: "product",
      label: "产品方案",
      minWidth: commonWidths.product,
    },
    {
      prop: "productVersion",
      label: "产品方案版本",
      minWidth: commonWidths.productVersion,
    },
    {
      prop: "region",
      label: "环境",
      minWidth: 112,
      sortable: true,
    },
    ...commonColumns,
  ],
  // 产品方案流量分析日报表
  "product-day-columns": [
    {
      prop: "date",
      label: "日期",
      custom: "link",
      minWidth: 200,
    },
    { prop: "app", label: "业务应用", width: 220 },
    {
      prop: "product",
      label: "产品方案",
      minWidth: commonWidths.product,
    },
    {
      prop: "productVersion",
      label: "产品方案版本",
      minWidth: commonWidths.productVersion,
    },
    ...commonColumns,
  ],
  "point-columns": [
    {
      prop: "date",
      label: "日期",
      custom: "link",
      minWidth: 200,
    },
    {
      prop: "product",
      label: "产品方案",
      minWidth: commonWidths.product,
    },
    {
      prop: "burialItem",
      label: "埋点项",
      minWidth: commonWidths.productVersion,
    },
    ...commonColumns,
  ],
};
function useColumns(type: string, mode?: string) {
  return mode ? columns[mode + "-columns"] : columns[type + "-columns"];
}

export default useColumns;
