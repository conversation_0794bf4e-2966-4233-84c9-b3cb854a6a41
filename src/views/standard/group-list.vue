<template>
    <page-wrapper :route-name="`${routeName}::`">
        <div class="compVersion-list height-adaptive" style="padding: 10px">
            <el-card class="info-card">
                <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
                    <el-collapse-item :name="1">
                        <template #title>
                            <span>基本信息</span>&nbsp;
                            <!-- <el-button link type="primary" @click.native.stop="events.editComp(productDetail)">
                <el-icon size="18"><Edit /></el-icon>
              </el-button> -->
                            <!-- <my-button link type="primary" @click="events.editComp(productDetail)" :stopPropagation="true"
                                operationAuth="/base/#/product/edit"><el-icon size="18">
                                    <Edit />
                                </el-icon></my-button> -->
                        </template>
                        <el-descriptions :column="2">
                            <el-descriptions-item label-class-name="bold" label="标准名称：">{{ getText(productDetail.name)
                            }}</el-descriptions-item>
                            <el-descriptions-item label-class-name="bold" label="所属目录">{{ getText(productDetail.catalogName)
                            }}</el-descriptions-item>
                            <el-descriptions-item label-class-name="bold" label="描述：">{{
                                getText(productDetail.description) }}</el-descriptions-item>

                        </el-descriptions>
                    </el-collapse-item>
                </el-collapse>
            </el-card>

            <el-card class="table-card" style="flex: 1">
                <div class="flex">
                    <el-descriptions>
                        <template #title>
                            维度详情
                        </template>
                    </el-descriptions>
                    <el-button type="primary" @click="events.add" :icon="CirclePlus" :disabled="productDetail.enabled"
                        style="margin-left: auto">新建选项</el-button>
                </div>
                <div style="height: calc(100% - 40px);">
                    <el-table :data="productDetail.dimsTree" 
                    style="width: 100%; margin-bottom: 20px" 
                    row-key="code"
                    default-expand-all 
                    border
                    height="100%"
                    :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                    >
                        <el-table-column prop="name" label="名称" width="200"/>
                        <el-table-column prop="code" label="编码" width="100"/>
                        <el-table-column prop="definition" label="定义" min-width="100"/>
                        
                        <el-table-column prop="sample" label="示例"  min-width="100">
                        <template #default="scope">
                            <span v-html="(scope.row.sample || '').replace(/\n/g, '<br>')"></span>
                        </template>
                        </el-table-column>
                        <el-table-column prop="description" label="备注" width="200">
                        <template #default="scope">
                            <span v-html="(scope.row.description || '').replace(/\n/g, '<br>')"></span>
                        </template>
                        </el-table-column>
                        <el-table-column prop="optType" label="选项类型" :formatter="formatOptType" width="100"/>
                        <el-table-column prop="required" label="是否必填" width="100"/>
                        <!-- 操作列 -->
                        <el-table-column label="操作" width="200" fixed="right">
                            <template #default="scope">
                                <el-button type="primary" size="small" :disabled="productDetail.enabled"
                                    @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                                <el-button type="danger" size="small" :disabled="productDetail.enabled"
                                    @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                        <el-table-column prop="optEvalFields" label="选项测评字段" width="200"/>
                    </el-table>

                </div>
            </el-card>


            <my-drawer class="component-add" v-model="addOptiondialogVisible" :title="dialogTitle" :width="800"
                @confirm="handleConfirm" @close="handleClose">
                <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" :disabled="disabled">

                    <template #optEvalFieldList>
                        <el-button link type="primary" @click="events.insertOptEvalField"
                            v-if="dataC.isEmpty(ruleForm.optEvalFieldList)">
                            <el-icon size="18px">
                                <CirclePlus />
                            </el-icon>
                        </el-button>
                        <el-col>
                            <el-row v-for="(item, index) in ruleForm.optEvalFieldList" gutter="10" class="item-row">
                                <el-col :span="21">
                                    <el-form-item label="" label-width="0" :prop="`optEvalFieldList[${index}]`"
                                        :rules="{ required: true, message: '必填', trigger: 'blur' }">
                                        <el-input :disabled="productDetail.enabled"
                                            v-model="ruleForm.optEvalFieldList[index]"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="3">
                                    <el-button link type="danger" @click="events.removeOptEvalField(index)"
                                        style="margin-left: 10px">
                                        <el-icon size="18px">
                                            <Delete />
                                        </el-icon>
                                    </el-button>
                                    <el-button link type="primary" @click="events.insertOptEvalField"
                                        v-if="ruleForm.optEvalFieldList.length == index + 1" style="margin-left: 10px">
                                        <el-icon size="18px">
                                            <CirclePlus />
                                        </el-icon>
                                    </el-button>
                                </el-col>
                            </el-row>
                        </el-col>
                    </template>
                    <template #required>
                        <el-switch :disabled="productDetail.enabled" v-model="ruleForm.required" :active-value="true"
                            :inactive-value="false"> </el-switch>
                    </template>

                    <template #optValueList>
                        <div v-if="ruleForm.optType">
                            <el-row v-for="(item, index) in ruleForm.optValueList" :key="index" gutter="10"
                                class="item-row">
                                <el-col :span="10">
                                    <el-form-item label-width="" :prop="`optValueList[${index}].label`"
                                        :rules="{ required: true, message: '必填', trigger: 'blur' }">
                                        <el-input :disabled="productDetail.enabled"
                                            v-model="ruleForm.optValueList[index].label" placeholder="如：A"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="10">
                                    <el-form-item label-width="0" :prop="`optValueList[${index}].value`"
                                        :rules="{ required: true, message: '必填', trigger: 'blur' }">
                                        <el-input :disabled="productDetail.enabled"
                                            v-model="ruleForm.optValueList[index].value" placeholder="定义"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="1">
                                    <el-button link type="danger" @click="events.removeOptValue(index)"
                                        style="margin-left: 10px">
                                        <el-icon size="18px">
                                            <Delete />
                                        </el-icon>
                                    </el-button>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="24" style="text-align: right">
                                    <el-button link type="primary" @click="events.addOptValue" style="margin-top: 10px">
                                        <el-icon size="18px">
                                            <CirclePlus />
                                        </el-icon>
                                    </el-button>
                                </el-col>
                            </el-row>
                        </div>
                    </template>


                    <template #defaultValue>
                        <el-select clearable v-model="ruleForm.defaultValue" placeholder="请选择"
                            :disabled="!ruleForm.optValueList || ruleForm.optValueList.length === 0 || productDetail.enabled"
                            style="width: 100%">
                            <el-option v-for="item in ruleForm.optValueList" :key="item.label" :label="item.label"
                                :value="item.label"></el-option>
                        </el-select>
                    </template>


                </my-form>
            </my-drawer>
        </div>
    </page-wrapper>
</template>
  
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { useI18n } from "vue-i18n";
import { getText } from "@/utils/helpers";
import type { FormRules } from "element-plus";
import useValidate from '@/hooks/validate'
import useCtx from "@/hooks/useCtx";
import { CirclePlus, Edit } from "@element-plus/icons-vue";
import { useRoute } from 'vue-router';
import { dataC, timeC } from "turing-plugin";

import * as markGroupApi from "@/api/eval-mark-group";

const { t } = useI18n();
const { $app, proxy, $router, $route, $auth } = useCtx();
const { validateNameRule, validateCodeRule } = useValidate()
const routeName = "standard-dim-group::details";
const productDetail = ref<any>({});
const getDetail = async (id: string) => {
    const res = await markGroupApi.findById(id);
    productDetail.value = res || {};
};
const activeCollapse = ref([1]);
/* 查询 */
const query = ref<any>({
    opened: undefined,
    id: undefined,
});

const dialogTitle = ref<string> ("")

const currentEditRecord = ref<any>(null);

const defaultForm = {
    id: '',
    name: "",
    code: "",
    description: '',
    level: '',
    required: true,
    optType: ''
};
const extraForm = {
    // 选项测评字段
    optEvalFieldList: [],
    // 选项值配置
    optValueList: [],
    // 默认值
    defaultValue: ''
};
let ruleForm = ref<any>(assign({}, defaultForm, extraForm));


// 格式化 optType 的值
const formatOptType = (row: any, column: any, cellValue: any) => {
    return cellValue === 1 ? "单选" : cellValue === 2 ? "多选" : "-";
};

const addOptiondialogVisible = ref(false)
/* events */
const events = reactive({
    // 点击新建选项打开弹框
    add: () => {
        dialogTitle.value = "新增";
        resetForm()
        addOptiondialogVisible.value = true
    },

    // 新建选项表单里的添加选项测评字段
    insertOptEvalField: () => {
        ruleForm.value.optEvalFieldList.push("");
    },
    // 新建选项表单里的移除选项测评字段
    removeOptEvalField: (index) => {
        ruleForm.value.optEvalFieldList.splice(index, 1);
    },

    // 添加选项值
    addOptValue: () => {

        // 检查最后一个选项的label是否重复
        const lastLabel = ruleForm.value.optValueList[ruleForm.value.optValueList.length - 1]?.label;
        if (checkLabelDuplicate(lastLabel)) {
            $app.$message.error("选项值已重复，请重新输入！");
            return;
        }
        ruleForm.value.optValueList.push({
            label: '',
            value: ''
        });
    },
    // 移除选项值
    removeOptValue: (index) => {
        ruleForm.value.optValueList.splice(index, 1);
    },


});


// 校验选项是否重复
function checkLabelDuplicate(label: string) {
    return ruleForm.value.optValueList.some((item, index) => {
        // 排除当前项自身
        return ruleForm.value.optValueList.length > index + 1 ? item.label === label : false;
    });
}

// 新建选项表单项
const formItems = ref<any>({
    name: {
        label: "名称",
        type: "input",
        attrs: {
            maxlength: 20,
            placeholder: '请输入名称',

        },
        disabled: () => {
            return productDetail.value.enabled;
        },
    },
    definition: {
        label: "定义",
        type: "input",
        attrs: {
            maxlength: 20,
            placeholder: '请输入定义'

        }
    },
    sample: {
        label: "示例",
        type: "textarea",
        attrs: {
            maxlength: 600,
            placeholder: '请输入示例'
        }
    },
    description: {
        label: "备注",
        type: "textarea",
        attrs: {
            maxlength: 600,
            placeholder: '请输入备注'
        }
    },
    optEvalFieldList: { label: "选项测评字段", type: "slot", slotName: "optEvalFieldList" },
    required: { label: "是否必填", type: "slot", slotName: "required" },

    optType: {
        label: "选项类型",
        type: "select",
        options: [{
            label: '单选',
            value: 1

        }, {
            label: '多选',
            value: 2

        }],
        attrs: {
            clearable: false,
        },
        disabled: () => {
            return productDetail.value.enabled;
        },
    },

    optValueList: { label: "选项值配置", type: "slot", slotName: "optValueList" },
    defaultValue: { label: "默认值", type: "slot", slotName: "defaultValue" }

});


const rules = reactive<FormRules>({
    name: [{ required: true, trigger: "blur", message: "请输入名称" }],
    definition: [{ required: true, trigger: "blur",  message: "请输入定义"}],
    required: [{ required: true, trigger: "blur",  message: "请选择是否必填"}],
    optType: [{ required: true, trigger: "blur",  message: "选项类型必填"}],
    // description: [{ required: true, message: "请输入产品描述", trigger: "blur" }],
});


onMounted(async () => {

    const route = useRoute();
    // 获取路由参数
    const id = route.query.recordId;
    const metaLabel = route.query.metaLabel;
    getDetail(id);
});


// 编辑选项
function handleEdit(index: any, record: any) {
    console.log("edit:", index, record)
    dialogTitle.value="编辑";
    resetForm()
    // 回显值
    ruleForm.value.name = record.name;
    ruleForm.value.definition = record.definition
    ruleForm.value.sample = record.sample
    ruleForm.value.description = record.description
    ruleForm.value.optEvalFieldList = record.optEvalFields?.split(',')
    ruleForm.value.required = record.required
    ruleForm.value.optType = record.optType
    ruleForm.value.defaultValue = record.defaultValue
    if (record.children) {
        ruleForm.value.optValueList = []
        record.children.forEach((item) => {
            ruleForm.value.optValueList.push({ label: item.name, value: item.definition });
        })
    }

    // 深拷贝 record 对象，避免引用问题
    currentEditRecord.value = cloneDeep(record)
    addOptiondialogVisible.value = true
}

// 删除选项
function handleDelete(index: any, record: any) {
    console.log(record)
    let markGroup = {
        id: productDetail.value.id,
        name: productDetail.value.name,
        catalogCode: productDetail.value.catalogCode,
        description: productDetail.value.description,
        enabled: productDetail.value.enabled,
        dimsTree: productDetail.value.dimsTree

    };
    deleteNodeByCode(markGroup.dimsTree, record.code)
    markGroupApi.save(markGroup);
    $app.$message.success("操作成功");
}


async function handleConfirm() {


    console.log("当前record:", currentEditRecord.value)
    let markGroup = {
        id: productDetail.value.id,
        name: productDetail.value.name,
        catalogCode: productDetail.value.catalogCode,
        description: productDetail.value.description,
        enabled: productDetail.value.enabled,
        dimsTree: productDetail.value.dimsTree

    };

    // 如果当前记录值不存在，说明为新增维度，level=0， 即点击新建选项的接口
    if (!currentEditRecord.value) {
        let tree = convertToTree(ruleForm.value)
        markGroup.dimsTree.push(tree)
        await markGroupApi.save(markGroup);
    } else {

        if (currentEditRecord.value.level === 3) {
            $app.$message.error("层级至多4层!");
            currentEditRecord.value = null
            return
        }
        currentEditRecord.value.name = ruleForm.value.name;
        currentEditRecord.value.description = ruleForm.value.description;
        currentEditRecord.value.required = ruleForm.value.required;
        currentEditRecord.value.optType = ruleForm.value.optType;
        currentEditRecord.value.optEvalFields = ruleForm.value.optEvalFieldList?.join(',');
        currentEditRecord.value.defaultValue = ruleForm.value.defaultValue;
        currentEditRecord.value.definition = ruleForm.value.definition;
        currentEditRecord.value.sample = ruleForm.value.sample;

        // 处理子节点：保持已有的、删除多余的、添加新的
        ruleForm.value.optValueList.forEach(item => {
            const existingChildIndex = currentEditRecord.value.children.findIndex(child => child.name === item.label);
            if (existingChildIndex >= 0) {
                // 只更新 definition 字段，其余字段保持子节点自身内容
                currentEditRecord.value.children[existingChildIndex].definition = item.value;
            } else {
                // 新增子节点时，继承父节点的 description、required 等属性
                const childNode = {
                    name: item.label,
                    definition: item.value,
                    code: generateRandomCode(),
                    description: '', // 新增子节点 description 置空或自定义
                    required: false, // 新增子节点 required 置为默认值
                    optType: '', // 新增子节点 optType 置为默认值
                    optEvalFields: '',
                    defaultValue: '',
                    sample: '',
                    level: currentEditRecord.value.level + 1,
                    children: []
                };
                currentEditRecord.value.children.push(childNode);
            }
        });

        // 删除 ruleForm.value.optValueList 中不存在的子节点
        let newChildNames = ruleForm.value.optValueList.map(item => item.label);
        currentEditRecord.value.children = currentEditRecord.value.children.filter(child => newChildNames.includes(child.name));

        console.log("全量树:", markGroup.dimsTree);
        findAndUpdateNode(markGroup.dimsTree, currentEditRecord.value.code, currentEditRecord.value);
        await markGroupApi.save(markGroup);


    }


    // markGroupApi.save(markGroup);
    addOptiondialogVisible.value = false
    $app.$message.success("操作成功");
    currentEditRecord.value = null
}



// 递归查找并替换全量树中的对应节点
function findAndUpdateNode(tree: any[], code: string, newNode: any) {
    for (let i = 0; i < tree.length; i++) {
        if (tree[i].code === code) {
            // 找到匹配的节点，用 newNode 替换
            tree[i] = newNode;
            return true;
        }
        // 递归查找子节点
        if (tree[i].children && tree[i].children.length > 0) {
            if (findAndUpdateNode(tree[i].children, code, newNode)) {
                return true;
            }
        }
    }
    return false;
}


function handleClose() {
    currentEditRecord.value = null
    addOptiondialogVisible.value = false
}



function deleteNodeByCode(tree: any[], targetCode: string): boolean {
    for (let i = 0; i < tree.length; i++) {
        if (tree[i].code === targetCode) {
            // 找到目标节点，从数组中移除
            tree.splice(i, 1);
            return true;
        }

        // 递归检查子节点
        if (tree[i].children && tree[i].children.length > 0) {
            if (deleteNodeByCode(tree[i].children, targetCode)) {
                // 如果子树中删除了节点，检查当前节点是否还有子节点，如果没有则也可以考虑删除当前节点
                if (tree[i].children.length === 0) {
                    // tree.splice(i, 1);
                }
                return true;
            }
        }
    }
    return false;
}

function convertToTree(data: any) {
    // 创建父节点
    const parentNode = {
        name: data.name,
        code: generateRandomCode(),
        description: data.description,
        required: data.required,
        optType: data.optType,
        optEvalFields: data.optEvalFieldList.join(','),
        defaultValue: data.defaultValue,
        definition: data.definition,
        sample: data.sample,
        children: [],
        level: 0
    };

    // 创建子节点并添加到父节点的 children 数组中
    data.optValueList.forEach(item => {
        // 子节点只保留自身的 name 和 definition 字段
        const childNode = {
            name: item.label,
            definition: item.value,
            code: generateRandomCode(),
            description: '',
            required: false,
            optType: '',
            optEvalFields: '',
            defaultValue: '',
            sample: '',
            level: 1,
            children: []
        };
        parentNode.children.push(childNode);
    });
    return parentNode;
}





// 生成8位字母数字组合的编码
function generateRandomCode(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
}


// 重置表单为初始状态
function resetForm() {
    extraForm.optEvalFieldList = []
    extraForm.optValueList = []
    extraForm.defaultValue = ''
    ruleForm.value = assign({}, defaultForm, extraForm);
}

</script>
  
<style lang="scss" scoped>
.compVersion-list {
    padding: 10px;

    .el-card {
        :deep(.el-card__body) {
            height: 100%;
        }
    }

    .info-card {
        height: auto;
        margin-bottom: 10px;

        :deep(.el-descriptions__label.bold) {
            font-weight: bold;
            background: #fff !important;
        }
    }

    :deep(.query-wrapper),
    :deep(.table-wrapper) {
        padding: 0;
    }
}
</style>