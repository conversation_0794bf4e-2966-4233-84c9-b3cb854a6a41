<template>
  <div class="mark-standard-detail">
    <div class="mark-standard-main">
      <ModuleTree
        ref="treeRef"
        @treeNodeClick="events.treeNodeClick"
        :module="module"
        class="left-tree"
      />
      <div class="right-content">
        <MarkStandardTable
          ref="myWordTableRef"
          :treeNode="currentNode"
          @add-data="handleAddData"
          @edit-data="handleEditData"
        />
        <MarkStandardEdit
          ref="markStandardEditRef"
          :treeNode="currentNode"
          @reload-table="handleReloadTable"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from "vue";
import ModuleTree from "./module/ModuleTree.vue";
import ModuleNode from "./module/ModuleNode.vue";
import MarkStandardTable from "./markStandardTable.vue";
import MarkStandardEdit from "./markStandardEdit.vue";
import * as markGroupApi from "@/api/eval-mark-group";

import useCtx from "@/hooks/useCtx";

const { proxy, $router } = useCtx();

// 组件引用
const treeRef = ref();
const treeNodeRef = ref();
const currentNode = ref();
const markStandardEditRef = ref()
const myWordTableRef = ref();
const module = ref("CPBZ")


const handleAddData = (data) => {
    if (markStandardEditRef.value) {
        markStandardEditRef.value.openDialog(data.mode, data.data);
    }

};

const handleEditData = (data) => {
    if (markStandardEditRef.value) {
        markStandardEditRef.value.openDialog(data.mode, data.data);
    }
};


const catalogData = computed(() => {
    const query = $router.currentRoute.value.query;
    return treeRef.value?.getNode(query.catalog)?.data || {};
});


// 事件处理
const events = {
    treeNodeClick: () => {
        currentNode.value = $router.currentRoute.value.query.catalog; // 获取当前选中树结点
        // 调用子组件的方法获取表格内容数据
        // myWordTableRef.value.loadList();
    },

    activeKindChange: async (kindCode: string) => {
        const currentRoute = $router.currentRoute.value;
        await $router.push({
            name: currentRoute.name,
            query: { ...currentRoute.query, kind: kindCode },
        });
        proxy.$refs[`tableRef-${kindCode}`]?.[0]?.loadList();
        window.dispatchEvent(new Event("resize"));
    },
    refreshCurrentNode: () => {
        treeRef.value?.setCurrentKey($router.currentRoute.value.query.catalog);
    },
    openEditFolderWindow: () => {
        treeNodeRef.value.openWindow("edit", catalogData.value);
    },
    editFolder: (newData: any) => {
        treeRef.value?.editFolder(newData);
    },
    getCatalogName: (catalogCode: string, callback: (name: string) => void) => {
        const catalogName = treeRef.value?.getCatalogName(catalogCode);
        callback(catalogName);
    },
};

// 处理 MarkStandardEdit 中的 reload-table 事件
const handleReloadTable = () => {
    console.log("Reloading table data...");
  myWordTableRef.value?.loadList();
};

// 生命周期
onMounted(() => {
});
</script>
  
<style lang="scss" scoped>
.mark-standard-detail {
  height: 100%;
  .mark-standard-main {
    display: flex;
    height: 100%;
    padding: 20px;
    box-sizing: border-box;
    .left-tree {
      width: 300px;
      min-width: 220px;
      max-width: 400px;
      background: #fff;
      border-radius: 6px;
      margin-right: 16px;
      overflow-y: auto;
    }
    .right-content {
      flex: 1;
      min-width: 0;
      background: #fff;
      border-radius: 6px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
<style lang="scss">
.primary-color {
  color: var(--el-color-primary) !important;
}

.warning-color {
  color: var(--el-color-warning) !important;
}

.success-color {
  color: var(--el-color-success) !important;
}

.danger-color {
  color: var(--el-color-danger) !important;
}</style>