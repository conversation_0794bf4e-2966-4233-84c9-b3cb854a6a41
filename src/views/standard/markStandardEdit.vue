<template>
  <my-drawer class="dataset-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm"
    @close="handleClose" size="800px">



    <my-form ref="formRef" :rules="rules" :ruleForm="ruleForm" :formItems="formItems" @submit="submit"
      label-width="138px">
      <template #badUrl>
        <div style="width: 100%;">
          <div v-for="(item, badIndex) in ruleForm.urlConfig.bad" :key="badIndex">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(dim, index) in ruleForm.dims" :key="index" class="margin-bottom-10">
                <el-form-item :prop="'urlConfig.bad.' + badIndex + '.' + dim"
                  :rules="{ required: true, validator: validatePass2 }">
                  <el-select v-model="ruleForm.urlConfig.bad[badIndex][dim]" multiple style="width: 100%;">
                    <el-option v-for="sub in getUrlOption(dim)" :key="sub.value" :label="sub.label" :value="sub.value"
                      :disabled="(ruleForm.urlConfig.good[dim] || []).includes(sub.value)">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="5">
                <el-button link type="danger" @click="removeBadGroup(badIndex)">
                  <el-icon size="18px">
                    <Delete />
                  </el-icon>
                </el-button>
                <el-button link type="primary" @click="addNewBadGroup">
                  <el-icon size="18px">
                    <CirclePlus />
                  </el-icon>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <template #goodUrl>
        <div style="width: 100%;">
          <div v-for="(item, goodIndex) in ruleForm.urlConfig.good" :key="goodIndex">
            <el-row :gutter="20">
              <el-col :span="8" v-for="(dim, index) in ruleForm.dims" :key="index" class="margin-bottom-10">
                <el-form-item :prop="'urlConfig.good.' + goodIndex + '.' + dim"
                  :rules="{ required: true, validator: validatePass2 }">
                  <el-select v-model="ruleForm.urlConfig.good[goodIndex][dim]" multiple style="width: 100%;">
                    <el-option v-for="sub in getUrlOption(dim)" :key="sub.value" :label="sub.label" :value="sub.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="5">
                <el-button link type="danger" @click="removeGoodGroup(goodIndex)">
                  <el-icon size="18px">
                    <Delete />
                  </el-icon>
                </el-button>
                <el-button link type="primary" @click="addNewGoodGroup">
                  <el-icon size="18px">
                    <CirclePlus />
                  </el-icon>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>

      <template #customizeConfig>
        <div class="strategy-config-container">
          <div class="button-add">
            <my-button type="primary" text="primary" @click="handleCustomeAddStr">添加字段</my-button>
          </div>
          <div v-for="(item, index) in ruleForm.customizeConfig" class="strategy-config-item">
            <el-row>
              <el-col :span="22">
                <el-row :gutter="10">
                  <el-col :span="6">
                    <el-form-item :prop="'customizeConfig.' + index + '.name'" :rules="{
                      required: true,
                      validator: validatePass1,
                    }">
                      <el-input v-model="ruleForm.customizeConfig[index]['name']" placeholder="请输入字段名称">
                      </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="18">
                    <el-row :gutter="10">
                      <el-col :span="8" v-for="key in ruleForm.dims">
                        <el-form-item :prop="'customizeConfig.' + index + '.' + key" :rules="{
                          required: true,
                          validator: validatePass2,
                        }">
                          <el-select v-model="ruleForm.customizeConfig[index][key]" multiple>
                            <el-option v-for="sub in getUrlOption(key)" :key="sub.value" :label="sub.label"
                              :value="sub.value">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="2">
                <my-button type="danger" text="danger" @click="handleCustomeDeleteStr(index)"
                  v-if="ruleForm.customizeConfig.length"><el-icon :size="20">
                    <Remove />
                  </el-icon></my-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
    </my-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import { assign, pick, keys, cloneDeep } from "lodash";
import useCtx from "@/hooks/useCtx";
import useStore from "@/store";
import * as metaWordApi from "@/api/eval-manage";
import * as markGroupApi from "@/api/eval-mark-group";
import * as markStandardApi from "@/api/eval-mark-standard";

// 定义表单选择的bad、good标准组
const badGroup = ref([] as string[]);
const goodGroup = ref([])


const props = defineProps({
  treeData: { type: Array },
  treeNode: { type: Object },
  type: { type: String },
  anasisList: { type: Array },
});
const { $app, proxy, $router } = useCtx();
const { common } = useStore();
//弹窗相关
const dialogTitle = computed(() => {
  return `${formType.value === "add" ? "新增" : "编辑"}`;
});
const dialogVisible = ref<boolean>(false);
// 表单相关
const formType = ref<string>("add");

// 定义一个响应式变量来保存标准列表
const standardGroupList = ref([]);

const formRef = ref<any>(null);
const handleClose = () => {
  formRef.value.resetForm();
  dialogVisible.value = false;
};

const handleConfirm = async () => {

  let param = {
    id: ruleForm.value.id,
    name: ruleForm.value.name,
    catalogCode: props.treeNode,
    description: ruleForm.value.description,
    recallDimsGroupId: ruleForm.value.recallDimsGroupId,
    chatDimsGroupId: ruleForm.value.chatDimsGroupId,
    ignoreQueryDimsGroupId: ruleForm.value.ignoreQueryDimsGroupId,
    ignoreDocDimsGroupId: ruleForm.value.ignoreDocDimsGroupId,
    enabled: false,
    classifyRules: []
  }

  // 处理 good
  // 处理 good
  if (ruleForm.value.urlConfig.good && ruleForm.value.urlConfig.good.length > 0) {
    const goodEntry = {
      name: "good",
      ruleGroup: []
    };
    ruleForm.value.urlConfig.good.forEach(item => {
      const ruleGroupItem = {
        dimOptions: []
      };
      Object.entries(item).forEach(([key, value]) => {
        ruleGroupItem.dimOptions.push({
          dimName: key,
          optionNames: value
        });
      });
      goodEntry.ruleGroup.push(ruleGroupItem);
    });
    param.classifyRules.push(goodEntry);
  }

  // 处理 bad
  if (ruleForm.value.urlConfig.bad && ruleForm.value.urlConfig.bad.length > 0) {
    const badEntry = {
      name: "bad",
      ruleGroup: []
    };
    ruleForm.value.urlConfig.bad.forEach(item => {
      const ruleGroupItem = {
        dimOptions: []
      };
      Object.entries(item).forEach(([key, value]) => {
        ruleGroupItem.dimOptions.push({
          dimName: key,
          optionNames: value
        });
      });
      badEntry.ruleGroup.push(ruleGroupItem);
    });
    param.classifyRules.push(badEntry);
  }

  // 处理 customizeConfig
  if (ruleForm.value.customizeConfig && ruleForm.value.customizeConfig.length > 0) {
    ruleForm.value.customizeConfig.forEach(item => {
      const customizeEntry = {
        name: item.name, // 使用每个 customizeConfig 元素的 name 属性
        ruleGroup: []
      };

      const ruleGroupItem = {
        dimOptions: []
      };

      // 遍历 item 中的键值对（排除 name 字段）
      Object.entries(item).forEach(([key, value]) => {
        if (key !== 'name') {
          ruleGroupItem.dimOptions.push({
            dimName: key,
            optionNames: value
          });
        }
      });

      customizeEntry.ruleGroup.push(ruleGroupItem);
      param.classifyRules.push(customizeEntry);
    });
  }


  console.log(param)
  await markStandardApi.save(param);
  dialogVisible.value = false
  $app.$message.success("操作成功");
  // 提交逻辑
  emit("reload-table");

};



const handleCustomeAddStr = () => {
  if (ruleForm.value.customizeConfig) {
    ruleForm.value.customizeConfig.push({});
  } else {
    ruleForm.value.customizeConfig = [{}];
  }
};
const handleCustomeDeleteStr = (index) => {
  ruleForm.value.customizeConfig.splice(index, 1);
};
//是否为更新
const isUpdate = computed(() => {
  return formType.value === "edit";
});

const defaultForm = {
  id: "",
  parentId: 0,
  name: "",
  recallDimsGroupId: "",
  dims: [],
  urlConfig: {
    good: [{}],
    bad: [{}],
  },
  level: 0,
  strategyConfig: [],
  customizeConfig: [],
  badGroup: [{}]
};
let ruleForm = ref<any>(assign({}, defaultForm));
const validatePass1 = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error("请输入"));
  } else {
    callback();
  }
};
const validatePass2 = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error("请至少选择一个选项"));
  } else {
    callback();
  }
};
const validatePass = (rule: any, value: any, callback: any) => {
  callback();
};
const rules = computed(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "change" }],
  dims: [
    {
      required: ruleForm.value.level == 1,
      message: "标注维度不能为空",
      trigger: "change",
    },
  ],
  goodUrl: [
    {
      required: ruleForm.value.level == 1,
      validator: validatePass,
      trigger: "change",
    },
  ],
  badUrl: [
    {
      required: ruleForm.value.level == 1,
      validator: validatePass,
      trigger: "change",
    },
  ],
}));

const getUrlOption = (key) => {
  return (
    formItems.value.dims.options.find((item) => item.value == key)?.options ||
    []
  );
};
// 表单项
const formItems = ref<any>({
  // parentId: {
  //   label: "所属目录",
  //   type: "select",
  //   options: [],
  //   // hidden: () => {
  //   //   return ruleForm.value.parentId == "0";
  //   // },
  //   // attrs: {
  //   //   disabled: true,
  //   //   maxlength: 255,
  //   //   placeholder: "请输入父级菜单",
  //   // },
  // },

  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 255,
      placeholder: "请输入名称",
    },
  },

  description: {
    label: "描述",
    type: "textarea",
    attrs: { maxlength: 255 },
  },

  recallDimsGroupId: {
    label: "标注标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: false },
    // disabled: () => isUpdate.value || ruleForm.value.level == 2,
    events: {
      change: (val) => {
        // ruleForm.value.urlConfig.good[val] = [];
        markGroupApi.findById(val).then((res) => {
          formItems.value.dims.options = res.dimsTree.map((item: any) => ({
            value: item.name,
            label: item.name,
            options: item.children.map(i => ({
              value: i.name,
              label: i.name,
            }))
          }))
          ruleForm.value.dims = res.dimsTree.map((item: any) => item.name)
        });
      },
      placeholder: "请输入标注维度",
    },
    disabled: () => {
      return ruleForm.value.enabled;
    },
    // hidden: () => {
    //   return ruleForm.value.parentId == "0";
    // },
  },
  dims: {
    label: "标注维度",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: true },
    disabled: () => true,
    events: {
      change: (val) => {
        // ruleForm.value.urlConfig.good[val] = [];
      },
      placeholder: "请输入标注维度",
    },
    hidden: () => {
      return !ruleForm.value.recallDimsGroupId;
    },
  },
  goodUrl: {
    label: "good定义",
    hidden: () => {
      return (
        !ruleForm.value.dims ||
        !ruleForm.value.dims.length
      );
    },
    type: "slot",
    slotName: "goodUrl",
    disabled: () => isUpdate.value
    
  },
  badUrl: {
    label: "bad定义",
    hidden: () => {
      return (

        !ruleForm.value.dims ||
        !ruleForm.value.dims.length
      );
    },
    type: "slot",
    slotName: "badUrl",
    disabled: () => isUpdate.value,
  },

  ignoreQueryDimsGroupId: {
    label: "query类弃标标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: false },
    disabled: () => {
      return ruleForm.value.enabled;
    },

  },

  ignoreDocDimsGroupId: {
    label: "doc类弃标标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: false },
    disabled: () => {
      return ruleForm.value.enabled;
    }
  },

  chatDimsGroupId: {
    label: "chat标注标准",
    type: "select",
    options: [],
    attrs: { clearable: false, multiple: false },
    disabled: () => {
      return ruleForm.value.enabled;
    }
  },


  customizeConfig: {
    label: "数据分析自定义字段",
    type: "slot",
    slotName: "customizeConfig",
    disabled: () => isUpdate.value,
  },
});



const revertData = (data: any) => {
  console.log('revertData:', data)
  const original = cloneDeep(data);
  original.recallDimsGroupId = data?.standardConfig?.id || ''
  // 将 ascribeMode 转换为字符串类型
  if (original.strategyConfig && original.strategyConfig.length > 0) {
    original.strategyConfig.forEach(item => {
      if (typeof item.ascribeMode === 'number') {
        item.ascribeMode = String(item.ascribeMode); // 转换为字符串类型
      }
    });
  }

  original.dims = data?.standardConfig?.dimensionList?.map((item: any) => item.name) || []
  original.urlConfig = {
    good: [{}],
    bad: [{}]
  }
  // original.urlConfig.good = (data?.classifyRules || []).find((item: any) => item.name === 'good')?.rule?.reduce((sum: any, item: any) => {
  //   sum[item.dimName] = item.optionNames
  //   return sum
  // }, {})
  // original.urlConfig.bad = (data?.classifyRules || []).find((item: any) => item.name === 'bad')?.rule?.reduce((sum: any, item: any) => {
  //   sum[item.dimName] = item.optionNames
  //   return sum
  // }, {})
  original.customizeConfig = (data?.classifyRules || []).filter((item: any) => item.name !== 'good' && item.name !== 'bad').map((item: any) => {
    item.name = item.name
    Object.assign(item, item.rule.reduce((sum: any, item: any) => {
      sum[item.dimName] = item.optionNames
      return sum
    }, {}))
    return item
  })
  return original;
};


function addNewBadGroup() {
  ruleForm.value.urlConfig.bad.push({});
};
function removeBadGroup(badIndex: any) {
  if (ruleForm.value.urlConfig.bad.length > 1) {
    ruleForm.value.urlConfig.bad.splice(badIndex, 1);
  }
};

function addNewGoodGroup() {
  ruleForm.value.urlConfig.good.push({});
};
function removeGoodGroup(goodIndex: any) {
  if (ruleForm.value.urlConfig.good.length > 1) {
    ruleForm.value.urlConfig.good.splice(goodIndex, 1);
  }
};
//打开窗口
const openDialog = async (type: string, row: any) => {

  formType.value = type;
  dialogVisible.value = true;
  nextTick(() => {
    if (type === "edit") {
      // ruleForm.value = pick(
      //   { ...defaultForm, ...revertData(row) },
      //   keys(assign({}, defaultForm))
      // );
      console.log(row)
      ruleForm.value.id = row.id
      ruleForm.value.name = row.name
      ruleForm.value.description = row.description
      ruleForm.value.recallDimsGroupId = row.recallDimsGroupId
      markGroupApi.findById(row.recallDimsGroupId).then((res) => {
        formItems.value.dims.options = res.dimsTree.map((item: any) => ({
          value: item.name,
          label: item.name,
          options: item.children.map(i => ({
            value: i.name,
            label: i.name,
          }))
        }))
        ruleForm.value.dims = res.dimsTree.map((item: any) => item.name)
      });
      ruleForm.value.enabled = row.enabled

      // 处理goodUrl和badUrl
      // 处理goodUrl和badUrl
      ruleForm.value.urlConfig = {
        good: [],
        bad: []
      };
      // 处理good
      const goodData = (row?.classifyRules || []).find((item: any) => item.name === 'good');
      if (goodData && goodData.ruleGroup && goodData.ruleGroup.length > 0) {
        goodData.ruleGroup.forEach((group: any) => {
          const goodItem: any = {};
          group.dimOptions.forEach((dim: any) => {
            goodItem[dim.dimName] = dim.optionNames;
          });
          ruleForm.value.urlConfig.good.push(goodItem);
        });
      } else {
        ruleForm.value.urlConfig.good.push({});
      }

      // 处理bad
      const badData = (row?.classifyRules || []).find((item: any) => item.name === 'bad');
      if (badData && badData.ruleGroup && badData.ruleGroup.length > 0) {
        badData.ruleGroup.forEach((group: any) => {
          const badItem: any = {};
          group.dimOptions.forEach((dim: any) => {
            badItem[dim.dimName] = dim.optionNames;
          });
          ruleForm.value.urlConfig.bad.push(badItem);
        });
      } else {
        ruleForm.value.urlConfig.bad.push({});
      }

      // 处理customizeConfig
      ruleForm.value.customizeConfig = (row?.classifyRules || []).filter((item: any) => item.name !== 'good' && item.name !== 'bad').map((item: any) => {
        const customizeItem: any = { name: item.name };
        item.ruleGroup.forEach((group: any) => {
          group.dimOptions.forEach((dim: any) => {
            customizeItem[dim.dimName] = dim.optionNames;
          });
        });
        return customizeItem;
      });
      ruleForm.value.ignoreQueryDimsGroupId = row.ignoreQueryDimsGroupId
      ruleForm.value.ignoreDocDimsGroupId = row.ignoreDocDimsGroupId
      ruleForm.value.chatDimsGroupId = row.chatDimsGroupId




    } else {
      const row1 = revertData(row);
      const keysEnum = [
        ["level", "parentId"],
        ["recallDimsGroupId", "dims", "level", "urlConfig", "parentId", "customizeConfig"],
        ["recallDimsGroupId", "dims", "level", "urlConfig", "parentId", "standardConfig"],
      ];

      ruleForm.value = pick({ ...defaultForm, ...row1 }, keysEnum[row.level]);
      ruleForm.value.urlConfig = { good: [{}], bad: [{}] };
      ruleForm.value.customizeConfig = [];
      // if (row.level == 2) {
      //   handleAddStr();
      // }
    }
  });
};
const transformData = (data) => {
  const transformed = cloneDeep(data);
  if (transformed.dims) {
    transformed.dims = transformed.dims.join(",");
  }

  // 将 ascribeMode 转换为数值类型
  if (transformed.strategyConfig && transformed.strategyConfig.length > 0) {
    transformed.strategyConfig.forEach(item => {
      if (typeof item.ascribeMode === 'string') {
        item.ascribeMode = Number(item.ascribeMode); // 转换为数值类型
      }
    });
  }

  // 转换 bad 对象（拼接字符串）
  if (data.urlConfig?.good) {
    transformed.urlConfig.good = Object.entries(data.urlConfig.good).reduce(
      (acc, [key, arr]) => {
        if (key !== "name") {
          acc[key] = (arr || []).join(",");
        } else {
          acc[key] = arr
        }
        return acc;
      },
      {}
    );
  }

  if (data.urlConfig?.bad) {
    // 转换 bad 对象（拼接字符串）
    transformed.urlConfig.bad = Object.entries(data.urlConfig.bad).reduce(
      (acc, [key, arr]) => {
        acc[key] = arr.join(",");
        return acc;
      },
      {}
    );
  }
  if (data?.customizeConfig) {
    // 转换 bad 对象（拼接字符串）
    transformed.customizeConfig = data?.customizeConfig.map((item) => {
      item = Object.entries(item).reduce((acc, [key, arr]) => {
        acc[key] = Array.isArray(arr) ? arr.join(",") : arr;
        return acc;
      }, {});
      return item;
    });
  }
  if (data.level != 2) {
    delete transformed.strategyConfig;
  }
  return transformed;
};
// 转换函数
const transformDataRule = (data) => {
  let transformed: any[] = [];
  // 转换 bad 对象（拼接字符串）
  if (data.urlConfig?.good) {
    transformed.push({
      name: 'good',
      rule: []
    })
    transformed[0].rule = Object.entries(data.urlConfig.good).reduce((sum, [key, value]) => {
      sum.push({
        dimName: key,
        optionNames: value
      })
      return sum;
    },
      []
    );
  }
  if (data.urlConfig?.bad) {
    transformed.push({
      name: 'bad',
      rule: []
    })
    // 转换 bad 对象（拼接字符串）
    transformed[1].rule = Object.entries(data.urlConfig.bad).reduce(
      (sum, [key, value]) => {
        sum.push({
          dimName: key,
          optionNames: value
        })
        return sum;
      },
      []
    );
  }
  if (data?.customizeConfig) {
    transformed = transformed.concat(data.customizeConfig.map(item => ({
      name: item.name,
      rule: Object.keys(item)
        .filter(key => key !== 'name')
        .map(dimKey => ({
          dimName: dimKey,
          optionNames: item[dimKey]
        }))
    })))
  }
  if (data.level != 2) {
    delete transformed.strategyConfig;
  }
  return transformed;
};
function hasDuplicatesValue(arr) {
  const seen = new Set(); // 使用Set记录已存在的键值组合
  for (const obj of arr) {
    // 提取除name外的所有字段值，按字段名排序后拼接成唯一键
    const keys = Object.keys(obj).filter(k => k !== 'name').sort();
    const key = keys.map(k => `${k}:${obj[k]}`).join('|'); // 例如："age:25|city:New York"

    if (seen.has(key)) {
      return true; // 发现重复
    }
    seen.add(key);
  }
  return false; // 无重复
}
function hasDuplicateName(arr) {
  const names = arr.map(item => item.name);
  return new Set(names).size !== names.length;
}
const hasRepeat = (data) => {
  const form = transformData(data);
  let arr: any[] = [form.urlConfig.good, form.urlConfig.bad]
  if (form.customizeConfig) {
    arr = [...arr, ...form.customizeConfig]
  }
  return hasDuplicatesValue(arr)
}
//提交数据
const submit = (form: any) => {
  console.log(form);
  const params = transformData(form);
  // 选项值有重复
  if (params.level == 1 && hasRepeat(form)) {
    $app.$message.warning("自定义选项的值有重复");
    return
  }
  if (params.level == 1 && hasDuplicateName(form.customizeConfig)) {
    $app.$message.warning("自定义选项的名称有重复");
    return
  }
  let apiParams = {
    name: form.name,
    parentId: form.parentId,
    level: form.level,
    recallDimsGroupId: form.recallDimsGroupId,
    classifyRule: transformDataRule(form),
    id: ''
  }
  if (form.level == 2) {
    apiParams.strategyConfig = form.strategyConfig
    apiParams.standardConfig = form.standardConfig
  }
  if (isUpdate.value) {
    apiParams.id = form.id
    metaWordApi.editTree(apiParams).then(() => {
      $app.$message.success("修改成功");
      emit("reload", props.treeNode);
      handleClose();
    });
  } else {
    metaWordApi[`createTree${params.level}`](apiParams).then(() => {
      $app.$message.success("新增成功");
      emit("reload");
      handleClose();
    });
  }
};
const flattenTree = (tree: any[]) => {
  let result: any[] = [];

  function flattenNode(node: any) {
    result.push({ label: node.name, value: node.id }); // 将当前节点添加到结果数组中
    if (node.children && node.children.length > 0) {
      // 如果节点有子节点，递归扁平化子节点
      node.children.forEach(flattenNode);
    }
  }

  tree.forEach(flattenNode); // 对树中的每个节点应用扁平化
  return result;
};
// watch监听
watch(
  () => props.treeData,
  (val) => {
    formItems.value.parentId.options = flattenTree(val as any[]);
  },
  { deep: true }
);

//初始化
onMounted(() => {

  markGroupApi.getList().then((res) => {
    standardGroupList.value = res.data.map((item: any) => ({
      value: item.id.toString(),
      label: item.name,
    }));
    formItems.value.recallDimsGroupId.options = standardGroupList.value;
    formItems.value.chatDimsGroupId.options = standardGroupList.value;
    formItems.value.ignoreQueryDimsGroupId.options = standardGroupList.value;
    formItems.value.ignoreDocDimsGroupId.options = standardGroupList.value;

  }).catch((error) => {
    console.error('获取标准列表失败:', error);
    // 处理错误，例如提示用户
  });
});
//事件声明
const emit = defineEmits(["reload", "reload-table"]);
//接口暴露
defineExpose({
  openDialog,
});
</script>
<style lang="scss" scoped>
.button-add {
  height: 15px;

  ::v-deep {
    .el-button {
      float: right;
    }
  }
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

::v-deep {
  .el-row {
    width: 100%;
  }

  .el-form-item__content {
    .strategy-config-container {
      width: 100%;

      .strategy-config-item {
        .el-form-item {
          margin-bottom: 18px;
        }
      }
    }
  }
}
</style>