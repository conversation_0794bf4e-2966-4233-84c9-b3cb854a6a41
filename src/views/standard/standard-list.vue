<template>
  <page-wrapper :route-name="`${routeName}::`">
    <div class="compVersion-list height-adaptive" style="padding: 10px">
      <el-card class="info-card">
        <el-collapse v-model="activeCollapse" @change="events.collapseChange" class="collapse">
          <el-collapse-item :name="1">
            <template #title>
              <span>基本信息</span>&nbsp;
              <!-- <el-button link type="primary" @click.native.stop="events.editComp(productDetail)">
                <el-icon size="18"><Edit /></el-icon>
              </el-button> -->
              <!-- <my-button link type="primary" @click="events.editComp(productDetail)" :stopPropagation="true"
                                operationAuth="/base/#/product/edit"><el-icon size="18">
                                    <Edit />
                                </el-icon></my-button> -->
            </template>
            <el-descriptions :column="2">
              <el-descriptions-item label-class-name="bold" label="标准名称：">{{ getText(productDetail.name) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="所属目录">{{ getText(productDetail.catalogName) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="描述：">{{ getText(productDetail.description) }}</el-descriptions-item>

              <el-descriptions-item label-class-name="bold" label="维度标准:">{{ getText(productDetail.recallDimsGroupName) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="goodUrl定义:">{{ getText(goodUrl) }}</el-descriptions-item>
              <el-descriptions-item label-class-name="bold" label="badUrl定义:">{{ getText(badUrl) }}</el-descriptions-item>

              <el-descriptions-item label-class-name="bold" label="recall标注标准:">
                <span @click="refreshDimsTree(productDetail.recallDimsGroupId)" style="color: #409eff; cursor: pointer">
                  {{ getText(productDetail.recallDimsGroupName) }}
                </span>
              </el-descriptions-item>

              <el-descriptions-item label-class-name="bold" label="query类弃标标准:">
                <span @click="refreshDimsTree(productDetail.ignoreQueryDimsGroupId)" style="color: #409eff; cursor: pointer">
                  {{ getText(productDetail.ignoreQueryDimsGroupName) }}
                </span>
              </el-descriptions-item>

              <!-- 添加点击事件 -->
              <el-descriptions-item label-class-name="bold" label="doc类弃标标准:">
                <span @click="refreshDimsTree(productDetail.ignoreDocDimsGroupId)" style="color: #409eff; cursor: pointer">
                  {{ getText(productDetail.ignoreDocDimsGroupName) }}
                </span>
              </el-descriptions-item>

              <!-- 添加点击事件 -->
              <el-descriptions-item label-class-name="bold" label="chat标注标准:">
                <span @click="refreshDimsTree(productDetail.chatDimsGroupId)" style="color: #409eff; cursor: pointer">
                  {{ getText(productDetail.chatDimsGroupName) }}
                </span>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-card>

      <el-card class="table-card" style="flex: 1">
        <div class="flex">
          <el-descriptions>
            <template #title>
              <span>维度标准详情</span>
              <!-- <div class="flex">
                            

                                <el-input v-model="query.id" placeholder="名称或编码" clearable
                                    style="width: 240px; margin-left: 10px"></el-input>
                            </div> -->
            </template>
          </el-descriptions>
        </div>
        <div style="height: calc(100% - 40px)">
          <el-table :data="markGroupObj.dimsTree" 
          style="width: 100%; margin-bottom: 20px" 
          row-key="code" 
          default-expand-all 
          border
          height="100%">
            <el-table-column prop="name" label="名称" width="200" />
            <el-table-column prop="code" label="编码" width="100" />
            <el-table-column prop="definition" label="定义" min-width="100" />

            <el-table-column prop="sample" label="示例" min-width="100">
              <template #default="scope">
                <span v-html="(scope.row.sample || '').replace(/\n/g, '<br>')"></span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="备注" width="250">
              <template #default="scope">
                <span v-html="(scope.row.description || '').replace(/\n/g, '<br>')"></span>
              </template>
            </el-table-column>
            <el-table-column prop="optType" label="选项类型" :formatter="formatOptType" width="100" />
            <el-table-column prop="required" label="是否必填" width="100" />
            <el-table-column prop="optEvalFields" label="选项测评字段" width="200" />
          </el-table>
        </div>
      </el-card>
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { assign, cloneDeep } from "lodash";
import { useI18n } from "vue-i18n";
import { getText } from "@/utils/helpers";
import type { FormRules } from "element-plus";
import useValidate from "@/hooks/validate";
import useCtx from "@/hooks/useCtx";
import { CirclePlus, Edit } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { dataC, timeC } from "turing-plugin";

import * as markStandardApi from "@/api/eval-mark-standard";
import * as markGroupApi from "@/api/eval-mark-group";

const { t } = useI18n();
const { $app, proxy, $router, $route, $auth } = useCtx();
const { validateNameRule, validateCodeRule } = useValidate();
const routeName = "mark-standard::details";
const productDetail = ref<any>({});
const markGroupObj = ref<any>({});
const getDetail = async (id: string) => {
  const res = await markStandardApi.findById(id);
  productDetail.value = res || {};
  const markGroup = await markGroupApi.findById(res.recallDimsGroupId);
  markGroupObj.value = markGroup || {};
};
const activeCollapse = ref([1]);
const goodUrl = ref(null);
const badUrl = ref(null);

/* 查询 */
const query = ref<any>({
  opened: undefined,
  id: undefined,
});

const currentEditRecord = ref<any>(null);

const defaultForm = {
  id: "",
  name: "",
  code: "",
  description: "",
  level: "",
  required: true,
  optType: "",
};
const extraForm = {
  // 选项测评字段
  optEvalFieldList: [],
  // 选项值配置
  optValueList: [],
  // 默认值
  defaultValue: "",
};
let ruleForm = ref<any>(assign({}, defaultForm, extraForm));

// 格式化 optType 的值
const formatOptType = (row: any, column: any, cellValue: any) => {
  return cellValue === 1 ? "单选" : cellValue === 2 ? "多选" : "-";
};

const addOptiondialogVisible = ref(false);
/* events */
const events = reactive({
  // 点击新建选项打开弹框
  add: () => {
    resetForm();
    addOptiondialogVisible.value = true;
  },

  // 新建选项表单里的添加选项测评字段
  insertOptEvalField: () => {
    ruleForm.value.optEvalFieldList.push("");
  },
  // 新建选项表单里的移除选项测评字段
  removeOptEvalField: (index) => {
    ruleForm.value.optEvalFieldList.splice(index, 1);
  },

  // 添加选项值
  addOptValue: () => {
    ruleForm.value.optValueList.push({
      label: "",
      value: "",
    });
  },
  // 移除选项值
  removeOptValue: (index) => {
    if (ruleForm.value.optValueList.length > 1) {
      ruleForm.value.optValueList.splice(index, 1);
    } else {
      $app.message.info("至少保留一个选项值");
    }
  },
});

// 新建选项表单项
const formItems = ref<any>({
  name: {
    label: "名称",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: "请输入名称",
    },
  },
  definition: {
    label: "定义",
    type: "input",
    attrs: {
      maxlength: 20,
      placeholder: "请输入定义",
    },
  },
  sample: {
    label: "示例",
    type: "textarea",
    attrs: {
      maxlength: 600,
      placeholder: "请输入示例",
    },
  },
  description: {
    label: "备注",
    type: "textarea",
    attrs: {
      maxlength: 600,
      placeholder: "请输入备注",
    },
  },
  optEvalFieldList: { label: "选项测评字段", type: "slot", slotName: "optEvalFieldList" },
  required: { label: "是否必填", type: "slot", slotName: "required" },

  optType: {
    label: "选项类型",
    type: "select",
    options: [
      {
        label: "单选",
        value: 1,
      },
      {
        label: "多选",
        value: 2,
      },
    ],
    attrs: {
      clearable: false,
    },
  },

  optValueList: { label: "选项值配置", type: "slot", slotName: "optValueList" },
  defaultValue: { label: "默认值", type: "slot", slotName: "defaultValue" },
});

const rules = reactive<FormRules>({
  name: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateNameRule(rule, value, callback, "请输入产品名称") }],
  code: [{ required: true, trigger: "blur", validator: (rule: any, value: any, callback: any) => validateCodeRule(rule, value, callback, "请输入产品编码") }],
  // description: [{ required: true, message: "请输入产品描述", trigger: "blur" }],
});

onMounted(async () => {
  const route = useRoute();
  // 获取路由参数
  const id = route.query.recordId;
  const metaLabel = route.query.metaLabel;
  goodUrl.value = route.query.goodUrl;
  badUrl.value = route.query.badUrl;
  getDetail(id);
});

// 编辑选项
function handleEdit(index: any, record: any) {
  console.log(record);
  resetForm();
  // 回显值
  ruleForm.value.name = record.name;
  ruleForm.value.definition = record.definition;
  ruleForm.value.sample = record.sample;
  ruleForm.value.description = record.description;
  ruleForm.value.optEvalFieldList = record.optEvalFields.split(",");
  ruleForm.value.required = record.required;
  ruleForm.value.optType = record.optType;
  if (record.children) {
    ruleForm.value.optValueList = [];
    record.children.forEach((item) => {
      ruleForm.value.optValueList.push({ label: item.name, value: item.definition });
    });
  }

  // 深拷贝 record 对象，避免引用问题
  currentEditRecord.value = JSON.parse(JSON.stringify(record));
  addOptiondialogVisible.value = true;
}

// 删除选项
function handleDelete(index: any, record: any) {
  console.log(record);
  let markGroup = {
    id: productDetail.value.id,
    name: productDetail.value.name,
    catalogCode: productDetail.value.catalogCode,
    description: productDetail.value.description,
    enabled: productDetail.value.enabled,
    dimsTree: productDetail.value.dimsTree,
  };
  deleteNodeByCode(markGroup.dimsTree, record.code);
  markGroupApi.save(markGroup);
  $app.$message.success("操作成功");
}

function handleConfirm() {
  console.log("当前record:", currentEditRecord.value);
  let markGroup = {
    id: productDetail.value.id,
    name: productDetail.value.name,
    catalogCode: productDetail.value.catalogCode,
    description: productDetail.value.description,
    enabled: productDetail.value.enabled,
    dimsTree: productDetail.value.dimsTree,
  };

  // 如果当前记录值不存在，说明为新增维度，level=0， 即点击新建选项的接口
  if (!currentEditRecord.value) {
    let tree = convertToTree(ruleForm.value);
    markGroup.dimsTree.push(tree);
    markGroupApi.save(markGroup);
  } else {
    // 其余都是点击编辑按钮，需要调整拼接树结构
    currentEditRecord.value.name = ruleForm.value.name;
    currentEditRecord.value.description = ruleForm.value.description;
    currentEditRecord.value.required = ruleForm.value.required;
    currentEditRecord.value.optType = ruleForm.value.optType;
    currentEditRecord.value.optEvalFields = ruleForm.value.optEvalFieldList.join(",");
    currentEditRecord.value.defaultValue = ruleForm.value.defaultValue;
    currentEditRecord.value.definition = ruleForm.value.definition;
    currentEditRecord.value.sample = ruleForm.value.sample;
    currentEditRecord.value.children = [];
    let level = currentEditRecord.value.level;
    // 创建子节点并添加到父节点的 children 数组中
    ruleForm.value.optValueList.forEach((item) => {
      // 子节点继承父节点的大部分属性
      const childNode = {
        name: item.label, // 使用 label 作为子节点的 name
        definition: item.value, // 使用 value 作为子节点的 definition
        code: generateRandomCode(),
        description: currentEditRecord.value.description, // 继承父节点的 description
        required: currentEditRecord.value.required, // 继承父节点的 required
        optType: currentEditRecord.value.optType, // 继承父节点的 optType
        optEvalFields: currentEditRecord.value.optEvalFields, // 继承父节点的 optEvalFieldList
        defaultValue: currentEditRecord.value.defaultValue, // 继承父节点的 defaultValue
        sample: currentEditRecord.value.sample, // 继承父节点的 sample
        level: level + 1,
        children: [],
      };
      currentEditRecord.value.children.push(childNode);
    });

    console.log("全量树:", markGroup.dimsTree);

    // 从全量树中匹配当前结点并替换当前结点为currentEditRecord
    findAndUpdateNode(markGroup.dimsTree, currentEditRecord.value.code, currentEditRecord.value);
    markGroupApi.save(markGroup);
  }

  // markGroupApi.save(markGroup);
  addOptiondialogVisible.value = false;
  $app.$message.success("操作成功");
  currentEditRecord.value = null;
}

// 递归查找并替换全量树中的对应节点
function findAndUpdateNode(tree: any[], code: string, newNode: any) {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].code === code) {
      // 找到匹配的节点，用 newNode 替换
      tree[i] = newNode;
      return true;
    }
    // 递归查找子节点
    if (tree[i].children && tree[i].children.length > 0) {
      if (findAndUpdateNode(tree[i].children, code, newNode)) {
        return true;
      }
    }
  }
  return false;
}

function deleteNodeByCode(tree: any[], targetCode: string): boolean {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].code === targetCode) {
      // 找到目标节点，从数组中移除
      tree.splice(i, 1);
      return true;
    }

    // 递归检查子节点
    if (tree[i].children && tree[i].children.length > 0) {
      if (deleteNodeByCode(tree[i].children, targetCode)) {
        // 如果子树中删除了节点，检查当前节点是否还有子节点，如果没有则也可以考虑删除当前节点
        if (tree[i].children.length === 0) {
          tree.splice(i, 1);
        }
        return true;
      }
    }
  }
  return false;
}

function convertToTree(data: any) {
  // 创建父节点
  const parentNode = {
    name: data.name,
    code: generateRandomCode(),
    description: data.description,
    required: data.required,
    optType: data.optType,
    optEvalFields: data.optEvalFieldList.join(","),
    defaultValue: data.defaultValue,
    definition: data.definition,
    sample: data.sample,
    children: [],
    level: 0,
  };

  // 创建子节点并添加到父节点的 children 数组中
  data.optValueList.forEach((item) => {
    // 子节点继承父节点的大部分属性
    const childNode = {
      name: item.label, // 使用 label 作为子节点的 name
      definition: item.value, // 使用 value 作为子节点的 definition
      code: generateRandomCode(),
      description: parentNode.description, // 继承父节点的 description
      required: parentNode.required, // 继承父节点的 required
      optType: parentNode.optType, // 继承父节点的 optType
      optEvalFields: parentNode.optEvalFields, // 继承父节点的 optEvalFieldList
      defaultValue: parentNode.defaultValue, // 继承父节点的 defaultValue
      sample: parentNode.sample, // 继承父节点的 sample
      level: 1,
      children: [],
    };
    parentNode.children.push(childNode);
  });

  return parentNode;
}

// 生成8位字母数字组合的编码
function generateRandomCode(length = 8) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let code = "";
  for (let i = 0; i < length; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return code;
}

// 重置表单为初始状态
function resetForm() {
  ruleForm.value = assign({}, defaultForm, extraForm);
}

// 刷新 markGroupObj.dimsTree 的点击事件处理函数
function refreshDimsTree(id: string) {
  if (!id) return;
  // 调用 API 获取对应标准的详情
  markGroupApi
    .findById(id)
    .then((res) => {
      markGroupObj.value = res || {};
      $app.$message.success("刷新成功");
    })
    .catch((error) => {
      console.error("刷新失败:", error);
      $app.$message.error("刷新失败");
    });
}
</script>

<style lang="scss" scoped>
.compVersion-list {
  padding: 10px;

  .el-card {
    :deep(.el-card__body) {
      height: 100%;
    }
  }

  .info-card {
    height: auto;
    margin-bottom: 10px;

    :deep(.el-descriptions__label.bold) {
      font-weight: bold;
      background: #fff !important;
    }
  }

  :deep(.query-wrapper),
  :deep(.table-wrapper) {
    padding: 0;
  }
}
</style>