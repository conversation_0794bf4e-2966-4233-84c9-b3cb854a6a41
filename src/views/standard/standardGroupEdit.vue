<template>
  <my-drawer class="data-bucket-edit" v-model="dialogVisible" :title="dialogTitle" @confirm="handleConfirm" @close="handleClose" size="800">
    <el-form ref="formRef" :model="ruleForm" :rules="rules" label-width="100px">
      <el-col :span="24">
        <el-form-item label="名称 :" prop="name">
          <el-input v-model="ruleForm.name" placeholder="请输入名称" clearable />
        </el-form-item>
     
        <el-form-item label="描述 :" prop="description">
          <el-input type="textarea" v-model="ruleForm.description" placeholder="请输入描述" />
        </el-form-item>
      </el-col>
    </el-form>
  </my-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { cloneDeep } from "lodash";
import * as markGroupApi from "@/api/eval-mark-group";
import useCtx from "@/hooks/useCtx";


// 事件声明
const emit = defineEmits(["reload","reload-table"]);

const { $app, proxy, $router } = useCtx();

const props = defineProps({
  treeNode: { type: String, default: '' },
});

// 类型定义
type FormData = {
  id: string;
  name: string;
  description: string;
 
};

type KindType = "WEB" | "DOC" | "SET" | "IDX";

// 响应式数据
const dialogVisible = ref(false);
const formType = ref<"add" | "edit">("add");
const formRef = ref();
const originForm = ref<{ kindCode: KindType; code: string }>({ kindCode: "WEB", code: "" });

const ruleForm = reactive<FormData>({
  id: "",
  name: "",
  description: "",
});


// 计算属性
const isUpdate = computed(() => formType.value === "edit");
const dialogTitle = computed(() => {
  return `${isUpdate.value ? "编辑" : "新建"}` + '维度标准';
});
// 表单规则
const rules = computed(() => ({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
}));

// 事件对象
const events = {
  
};

// 方法
const openWindow = async (type: "add" | "edit", row: any) => {
  
  formType.value = type;
  originForm.value = row;
  dialogVisible.value = true;
  // 初始化表单数据
  if (type === "add") {
    Object.assign(ruleForm, {
      ...ruleForm,
      id: "",
      name: "",
      description: "",
    });
  } else {
    const formData = { ...ruleForm, ...row };
    Object.assign(ruleForm, formData);
    // 新增：保留原有 dimsTree
    ruleForm.dimsTree = row.dimsTree || [];
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  setTimeout(() => {
    formRef.value?.resetFields();
  }, 500);
};

const handleConfirm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    const formData = cloneDeep({
      ...originForm.value,
      ...ruleForm
    });
    const param = {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      catalogCode: props.treeNode,
      dimsTree: formData.dimsTree || [] // 这里提交原有 dimsTree
    }
    await markGroupApi.save(param);
    dialogVisible.value = false;
    $app.$message.success("操作成功");
    emit("reload-table");
  });
};




const initDsTree = (node: any) => {
  node.isDir = true;
  node.disabled = true;
  if (!node.children) return;

  node.children.forEach((child) => {
    child.parentCode = node.code;
    initDsTree(child);
  });
};



// 初始化数据
const initData = async () => {
  
};

// 初始化
onMounted(initData);

// 暴露接口
defineExpose({ openWindow });
</script>

<style lang="scss" scoped>
.data-bucket-edit {
  .item-row {
    margin-bottom: 12px;
  }

  .action-buttons {
    .el-button {
      margin-left: 10px;
    }
  }

  .ttl-input,
  .ttl-select {
    width: 150px;
    margin-left: 25px;
  }

  .template-select {
    margin-bottom: 12px;
  }
}
</style>